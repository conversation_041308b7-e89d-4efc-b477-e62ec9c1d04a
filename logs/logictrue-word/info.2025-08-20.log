09:41:27.816 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 97151 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
09:41:27.819 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
09:41:28.869 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
09:41:28.870 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:41:28.870 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
09:41:28.921 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:41:29.611 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
09:41:29.907 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
09:41:30.634 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
09:41:30.650 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.33 seconds (JVM running for 4.748)
09:41:52.635 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:42:28.212 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
09:42:28.212 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检验记录表
09:42:28.431 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,569] - 已设置文档为横向纸张
09:42:28.481 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,83] - 接收到表头数据，共2行
09:42:28.482 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,86] - 第1行表头，共8列
09:42:28.482 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,90] -   列0: content='检查工序名称', rowspan=2, colspan=1
09:42:28.482 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,90] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
09:42:28.482 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,90] -   列2: content='实际检查结果', rowspan=2, colspan=1
09:42:28.482 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='完工', rowspan=1, colspan=2
09:42:28.482 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,90] -   列5: content='操作员', rowspan=2, colspan=1
09:42:28.482 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,90] -   列6: content='班组长', rowspan=2, colspan=1
09:42:28.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,90] -   列7: content='检验员', rowspan=2, colspan=1
09:42:28.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,86] - 第2行表头，共8列
09:42:28.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='月', rowspan=1, colspan=1
09:42:28.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,90] -   列4: content='日', rowspan=1, colspan=1
09:42:28.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,110] - 创建表格: 3行 x 8列
09:42:28.519 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,155] - 开始处理表头，共2行
09:42:28.519 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第1行表头，共8列
09:42:28.548 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第2行表头，共8列
09:42:28.550 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,170] - 表头处理完成，共处理2行
09:42:28.550 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:42:40.707 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:42:42.733 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processDataRow,337] - 处理混合内容，mathMLMap大小: 1
09:42:42.734 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [insertMixedContent,434] - 插入混合内容，内容: 你好
__MATH_FORMULA_0__
海, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
09:42:42.736 [http-nio-9550-exec-7] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
09:42:43.139 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:42:44.235 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:42:45.841 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:42:47.141 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:42:48.037 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:42:48.938 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,144] - 表格创建完成
09:42:49.017 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 2997 bytes
09:42:49.024 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_094249.docx, 大小: 2997 bytes
09:45:38.171 [http-nio-9550-exec-8] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
09:45:38.172 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检验记录表
09:45:38.173 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [setPageOrientation,569] - 已设置文档为横向纸张
09:45:38.174 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,83] - 接收到表头数据，共2行
09:45:38.175 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,86] - 第1行表头，共8列
09:45:38.175 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,90] -   列0: content='检查工序名称', rowspan=2, colspan=1
09:45:38.175 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,90] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
09:45:38.175 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,90] -   列2: content='实际检查结果', rowspan=2, colspan=1
09:45:38.175 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='完工', rowspan=1, colspan=2
09:45:38.175 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,90] -   列5: content='操作员', rowspan=2, colspan=1
09:45:38.175 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,90] -   列6: content='班组长', rowspan=2, colspan=1
09:45:38.175 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,90] -   列7: content='检验员', rowspan=2, colspan=1
09:45:38.176 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,86] - 第2行表头，共8列
09:45:38.176 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='月', rowspan=1, colspan=1
09:45:38.176 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,90] -   列4: content='日', rowspan=1, colspan=1
09:45:38.176 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,110] - 创建表格: 3行 x 8列
09:45:38.178 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,155] - 开始处理表头，共2行
09:45:38.178 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第1行表头，共8列
09:45:38.185 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第2行表头，共8列
09:45:38.186 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,170] - 表头处理完成，共处理2行
09:45:38.186 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:45:38.187 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:45:38.187 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processDataRow,337] - 处理混合内容，mathMLMap大小: 1
09:45:38.187 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [insertMixedContent,434] - 插入混合内容，内容: 你好
__MATH_FORMULA_0__
海, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
09:48:07.745 [http-nio-9550-exec-8] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
09:48:10.081 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:48:10.082 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:48:10.082 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:48:10.082 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:48:10.082 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:48:10.084 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,144] - 表格创建完成
09:48:10.091 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 2997 bytes
09:48:10.094 [http-nio-9550-exec-8] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_094810.docx, 大小: 2997 bytes
09:49:04.679 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
09:49:04.682 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
09:49:08.782 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 107509 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
09:49:08.784 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
09:49:09.617 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
09:49:09.618 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:49:09.618 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
09:49:09.661 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:49:10.170 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
09:49:10.434 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
09:49:11.033 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
09:49:11.049 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.653 seconds (JVM running for 3.286)
09:49:13.697 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:49:13.796 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
09:49:13.797 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检验记录表
09:49:14.118 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,569] - 已设置文档为横向纸张
09:49:14.159 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,83] - 接收到表头数据，共2行
09:49:14.160 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,86] - 第1行表头，共8列
09:49:14.160 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列0: content='检查工序名称', rowspan=2, colspan=1
09:49:14.160 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
09:49:14.160 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列2: content='实际检查结果', rowspan=2, colspan=1
09:49:14.160 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='完工', rowspan=1, colspan=2
09:49:14.160 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列5: content='操作员', rowspan=2, colspan=1
09:49:14.160 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列6: content='班组长', rowspan=2, colspan=1
09:49:14.161 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列7: content='检验员', rowspan=2, colspan=1
09:49:14.161 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,86] - 第2行表头，共8列
09:49:14.161 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='月', rowspan=1, colspan=1
09:49:14.161 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列4: content='日', rowspan=1, colspan=1
09:49:14.161 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,110] - 创建表格: 3行 x 8列
09:49:14.195 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,155] - 开始处理表头，共2行
09:49:14.195 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第1行表头，共8列
09:49:14.221 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第2行表头，共8列
09:49:14.223 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,170] - 表头处理完成，共处理2行
09:49:14.224 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:49:14.224 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:49:14.225 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,337] - 处理混合内容，mathMLMap大小: 1
09:49:14.225 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,434] - 插入混合内容，内容: 你好
__MATH_FORMULA_0__
海, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
09:49:18.052 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
09:49:18.343 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:49:18.344 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:49:18.345 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:49:18.346 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:49:18.346 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:49:18.354 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,144] - 表格创建完成
09:49:18.407 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 3001 bytes
09:49:18.416 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_094918.docx, 大小: 3001 bytes
09:50:29.070 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
09:50:29.070 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检验记录表
09:50:29.072 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,569] - 已设置文档为横向纸张
09:50:29.073 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,83] - 接收到表头数据，共2行
09:50:29.073 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,86] - 第1行表头，共8列
09:50:29.073 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列0: content='检查工序名称', rowspan=2, colspan=1
09:50:29.073 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
09:50:29.073 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列2: content='实际检查结果', rowspan=2, colspan=1
09:50:29.073 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='完工', rowspan=1, colspan=2
09:50:29.074 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列5: content='操作员', rowspan=2, colspan=1
09:50:29.074 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列6: content='班组长', rowspan=2, colspan=1
09:50:29.074 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列7: content='检验员', rowspan=2, colspan=1
09:50:29.074 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,86] - 第2行表头，共8列
09:50:29.074 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='月', rowspan=1, colspan=1
09:50:29.074 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列4: content='日', rowspan=1, colspan=1
09:50:29.074 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,110] - 创建表格: 3行 x 8列
09:50:29.078 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,155] - 开始处理表头，共2行
09:50:29.078 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第1行表头，共8列
09:50:29.085 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第2行表头，共8列
09:50:29.086 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,170] - 表头处理完成，共处理2行
09:50:29.087 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:50:29.087 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:50:29.087 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,337] - 处理混合内容，mathMLMap大小: 1
09:50:29.087 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [insertMixedContent,434] - 插入混合内容，内容: 你好
__MATH_FORMULA_0__
海
不错

嘿嘿
而已
111
432
, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
09:50:29.088 [http-nio-9550-exec-2] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
09:50:29.186 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:50:29.187 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:50:29.187 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:50:29.187 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:50:29.187 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:50:29.189 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,144] - 表格创建完成
09:50:29.195 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 3045 bytes
09:50:29.198 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_095029.docx, 大小: 3045 bytes
10:34:18.116 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检查记录表
10:34:18.117 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检查记录表
10:34:18.120 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,569] - 已设置文档为横向纸张
10:34:18.134 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 2402 bytes
10:34:18.139 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E6%9F%A5%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_103418.docx, 大小: 2402 bytes
10:35:25.048 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检查记录表
10:35:25.048 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检查记录表
10:35:25.049 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,569] - 已设置文档为横向纸张
10:35:25.056 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 2402 bytes
10:35:25.065 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E6%9F%A5%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_103525.docx, 大小: 2402 bytes
10:39:21.792 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检查记录表
10:39:21.792 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检查记录表
10:39:21.794 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [setPageOrientation,569] - 已设置文档为横向纸张
10:39:21.801 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 2402 bytes
10:39:21.806 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E6%9F%A5%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_103921.docx, 大小: 2402 bytes
10:44:32.183 [http-nio-9550-exec-10] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
10:44:32.183 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检验记录表
10:44:32.185 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [setPageOrientation,569] - 已设置文档为横向纸张
10:44:32.186 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,83] - 接收到表头数据，共2行
10:44:32.186 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,86] - 第1行表头，共8列
10:44:32.186 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列0: content='检查工序名称', rowspan=2, colspan=1
10:44:32.186 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
10:44:32.187 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列2: content='实际检查结果', rowspan=2, colspan=1
10:44:32.187 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='完工', rowspan=1, colspan=2
10:44:32.187 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列5: content='操作员', rowspan=2, colspan=1
10:44:32.187 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列6: content='班组长', rowspan=2, colspan=1
10:44:32.187 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列7: content='检验员', rowspan=2, colspan=1
10:44:32.187 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,86] - 第2行表头，共8列
10:44:32.188 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='月', rowspan=1, colspan=1
10:44:32.188 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列4: content='日', rowspan=1, colspan=1
10:44:32.188 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,110] - 创建表格: 7行 x 8列
10:44:32.194 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,155] - 开始处理表头，共2行
10:44:32.194 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第1行表头，共8列
10:44:32.206 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第2行表头，共8列
10:44:32.209 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,170] - 表头处理完成，共处理2行
10:44:32.210 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:32.210 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:32.211 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,337] - 处理混合内容，mathMLMap大小: 1
10:44:32.212 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [insertMixedContent,434] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mi>E</mi><mo>=</mo><mrow><mi>m</mi><mo>&#x2062;</mo><msup><mi>c</mi><mn>2</mn></msup></mrow></math>}
10:44:32.212 [http-nio-9550-exec-10] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
10:44:32.463 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:32.463 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:32.464 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:32.465 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:32.466 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:32.467 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:32.468 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:32.468 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,337] - 处理混合内容，mathMLMap大小: 1
10:44:32.468 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [insertMixedContent,434] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><msubsup><mo>&#x222B;</mo><mn>0</mn><mi mathvariant="normal">&#x221E;</mi></msubsup><msup><mi>e</mi><mrow><mo>&#x2212;</mo><msup><mi>x</mi><mn>2</mn></msup></mrow></msup><mrow><mi>d</mi><mi>x</mi></mrow></mrow><mo>=</mo><mfrac><msqrt><mi>&#x3C0;</mi></msqrt><mn>2</mn></mfrac></math>}
10:44:32.469 [http-nio-9550-exec-10] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
10:44:32.763 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:32.765 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:32.766 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:32.767 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:32.768 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:32.768 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:32.768 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:32.769 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,337] - 处理混合内容，mathMLMap大小: 1
10:44:32.769 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [insertMixedContent,434] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><munderover><mo>&#x2211;</mo><mrow><mi>n</mi><mo>=</mo><mn>1</mn></mrow><mi mathvariant="normal">&#x221E;</mi></munderover><mfrac><mn>1</mn><msup><mi>n</mi><mn>2</mn></msup></mfrac></mrow><mo>=</mo><mfrac><msup><mi>&#x3C0;</mi><mn>2</mn></msup><mn>6</mn></mfrac></math>}
10:44:32.769 [http-nio-9550-exec-10] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
10:44:32.983 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:32.984 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:32.985 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:32.985 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:32.986 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:32.986 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:32.987 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:32.987 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,337] - 处理混合内容，mathMLMap大小: 1
10:44:32.987 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [insertMixedContent,434] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><munder><mo movablelimits="true">lim</mo><mrow><mi>x</mi><mo accent="false" stretchy="false">&#x2192;</mo><mn>0</mn></mrow></munder><mo>&#x2061;</mo><mfrac><mrow><mi>sin</mi><mo>&#x2061;</mo><mi>x</mi></mrow><mi>x</mi></mfrac></mrow><mo>=</mo><mn>1</mn></math>}
10:44:32.988 [http-nio-9550-exec-10] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
10:44:33.225 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:33.226 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:33.227 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:33.227 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:33.228 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:33.228 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:33.229 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:33.229 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,337] - 处理混合内容，mathMLMap大小: 1
10:44:33.229 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [insertMixedContent,434] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mrow><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup></mrow></msqrt></math>}
10:44:33.229 [http-nio-9550-exec-10] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
10:44:33.489 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:33.490 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:33.491 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:33.492 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:33.492 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
10:44:33.496 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,144] - 表格创建完成
10:44:33.503 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 3541 bytes
10:44:33.507 [http-nio-9550-exec-10] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_104433.docx, 大小: 3541 bytes
13:33:24.958 [http-nio-9550-exec-10] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
13:33:24.962 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检验记录表
13:33:24.969 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [setPageOrientation,569] - 已设置文档为横向纸张
13:33:24.972 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,83] - 接收到表头数据，共2行
13:33:24.972 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,86] - 第1行表头，共8列
13:33:24.972 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列0: content='检查工序名称', rowspan=2, colspan=1
13:33:24.973 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
13:33:24.973 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列2: content='实际检查结果', rowspan=2, colspan=1
13:33:24.973 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='完工', rowspan=1, colspan=2
13:33:24.973 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列5: content='操作员', rowspan=2, colspan=1
13:33:24.973 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列6: content='班组长', rowspan=2, colspan=1
13:33:24.973 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列7: content='检验员', rowspan=2, colspan=1
13:33:24.973 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,86] - 第2行表头，共8列
13:33:24.974 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='月', rowspan=1, colspan=1
13:33:24.974 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列4: content='日', rowspan=1, colspan=1
13:33:24.974 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,110] - 创建表格: 3行 x 8列
13:33:24.978 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,155] - 开始处理表头，共2行
13:33:24.978 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第1行表头，共8列
13:33:24.985 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第2行表头，共8列
13:33:24.987 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,170] - 表头处理完成，共处理2行
13:33:24.987 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
13:33:24.988 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,337] - 处理混合内容，mathMLMap大小: 1
13:33:24.989 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [insertMixedContent,434] - 插入混合内容，内容: 你好
ok
哈哈
谢谢你
__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
13:33:24.991 [http-nio-9550-exec-10] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
13:33:25.226 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
13:33:25.227 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
13:33:25.228 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
13:33:25.228 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
13:33:25.229 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
13:33:25.229 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
13:33:25.232 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,144] - 表格创建完成
13:33:25.246 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 3008 bytes
13:33:25.255 [http-nio-9550-exec-10] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_133325.docx, 大小: 3008 bytes
13:38:22.972 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
13:38:22.978 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
13:38:29.086 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 387764 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
13:38:29.087 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
13:38:29.982 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
13:38:29.982 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:38:29.983 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
13:38:30.030 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:38:30.580 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
13:38:30.853 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
13:38:31.491 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
13:38:31.506 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.88 seconds (JVM running for 3.549)
13:38:42.163 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:38:54.595 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
13:38:54.595 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检验记录表
13:38:54.863 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [setPageOrientation,662] - 已设置文档为横向纸张
13:38:54.917 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTable,83] - 接收到表头数据，共2行
13:38:54.917 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTable,86] - 第1行表头，共8列
13:38:54.918 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTable,90] -   列0: content='检查工序名称', rowspan=2, colspan=1
13:38:54.918 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTable,90] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
13:38:54.918 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTable,90] -   列2: content='实际检查结果', rowspan=2, colspan=1
13:38:54.918 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='完工', rowspan=1, colspan=2
13:38:54.918 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTable,90] -   列5: content='操作员', rowspan=2, colspan=1
13:38:54.918 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTable,90] -   列6: content='班组长', rowspan=2, colspan=1
13:38:54.918 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTable,90] -   列7: content='检验员', rowspan=2, colspan=1
13:38:54.918 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTable,86] - 第2行表头，共8列
13:38:54.919 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='月', rowspan=1, colspan=1
13:38:54.919 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTable,90] -   列4: content='日', rowspan=1, colspan=1
13:38:54.919 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTable,110] - 创建表格: 3行 x 8列
13:38:54.958 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,427] - 设置表格总宽度: 850px (12750twips)
13:38:54.967 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,444] - 设置列宽完成: [100, 300, 200, 50, 50, 50, 50, 50]
13:38:54.968 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,157] - 开始处理表头，共2行
13:38:54.968 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第1行表头，共8列
13:38:54.998 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第2行表头，共8列
13:38:55.000 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,172] - 表头处理完成，共处理2行
13:38:55.001 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processDataRow,353] - 处理普通文本内容
13:38:55.001 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processDataRow,339] - 处理混合内容，mathMLMap大小: 1
13:38:55.002 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [insertMixedContent,527] - 插入混合内容，内容: hah
__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
13:38:55.005 [http-nio-9550-exec-5] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
13:38:55.365 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processDataRow,353] - 处理普通文本内容
13:38:55.366 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processDataRow,353] - 处理普通文本内容
13:38:55.366 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processDataRow,353] - 处理普通文本内容
13:38:55.367 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processDataRow,353] - 处理普通文本内容
13:38:55.368 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processDataRow,353] - 处理普通文本内容
13:38:55.369 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processDataRow,353] - 处理普通文本内容
13:38:55.377 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTable,146] - 表格创建完成
13:38:55.455 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 2975 bytes
13:38:55.464 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_133855.docx, 大小: 2975 bytes
13:47:05.275 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
13:47:05.278 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
13:47:09.487 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 398744 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
13:47:09.488 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
13:47:10.293 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
13:47:10.294 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:47:10.294 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
13:47:10.337 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:47:10.822 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
13:47:11.070 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
13:47:11.648 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
13:47:11.662 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.535 seconds (JVM running for 3.111)
13:47:21.263 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:47:21.381 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
13:47:21.382 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检验记录表
13:47:21.722 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,689] - 已设置文档为横向纸张
13:47:21.769 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,83] - 接收到表头数据，共2行
13:47:21.769 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,86] - 第1行表头，共8列
13:47:21.769 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列0: content='检查工序名称', rowspan=2, colspan=1
13:47:21.769 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
13:47:21.769 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列2: content='实际检查结果', rowspan=2, colspan=1
13:47:21.769 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='完工', rowspan=1, colspan=2
13:47:21.769 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列5: content='操作员', rowspan=2, colspan=1
13:47:21.770 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列6: content='班组长', rowspan=2, colspan=1
13:47:21.770 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列7: content='检验员', rowspan=2, colspan=1
13:47:21.770 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,86] - 第2行表头，共8列
13:47:21.770 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='月', rowspan=1, colspan=1
13:47:21.770 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列4: content='日', rowspan=1, colspan=1
13:47:21.770 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,110] - 创建表格: 3行 x 8列
13:47:21.806 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,385] - 从数据行获取列宽: [100, 300, 200, 50, 50, 50, 50, 50]
13:47:21.806 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,407] - 设置表格总宽度: 850px (12750twips)
13:47:21.811 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setDataRowsColumnWidth,451] - 数据行列宽设置完成
13:47:21.811 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,415] - 设置列宽完成: [100, 300, 200, 50, 50, 50, 50, 50]
13:47:21.811 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,157] - 开始处理表头，共2行
13:47:21.812 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第1行表头，共8列
13:47:21.837 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第2行表头，共8列
13:47:21.839 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,172] - 表头处理完成，共处理2行
13:47:21.840 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,353] - 处理普通文本内容
13:47:21.840 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,339] - 处理混合内容，mathMLMap大小: 1
13:47:21.840 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,554] - 插入混合内容，内容: hah
__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
13:47:21.843 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
13:47:22.145 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,353] - 处理普通文本内容
13:47:22.145 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,353] - 处理普通文本内容
13:47:22.145 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,353] - 处理普通文本内容
13:47:22.146 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,353] - 处理普通文本内容
13:47:22.147 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,353] - 处理普通文本内容
13:47:22.147 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,353] - 处理普通文本内容
13:47:22.153 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,146] - 表格创建完成
13:47:22.210 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 2971 bytes
13:47:22.221 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_134722.docx, 大小: 2971 bytes
13:59:35.891 [http-nio-9550-exec-10] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
13:59:35.892 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检验记录表
13:59:35.895 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [setPageOrientation,689] - 已设置文档为横向纸张
13:59:35.896 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,83] - 接收到表头数据，共2行
13:59:35.897 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,86] - 第1行表头，共8列
13:59:35.897 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列0: content='检查工序名称', rowspan=2, colspan=1
13:59:35.897 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
13:59:35.897 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列2: content='实际检查结果', rowspan=2, colspan=1
13:59:35.897 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='完工', rowspan=1, colspan=2
13:59:35.897 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列5: content='操作员', rowspan=2, colspan=1
13:59:35.897 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列6: content='班组长', rowspan=2, colspan=1
13:59:35.897 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列7: content='检验员', rowspan=2, colspan=1
13:59:35.898 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,86] - 第2行表头，共8列
13:59:35.898 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='月', rowspan=1, colspan=1
13:59:35.898 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列4: content='日', rowspan=1, colspan=1
13:59:35.898 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,110] - 创建表格: 2行 x 8列
13:59:35.900 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,393] - 使用默认列宽: [150, 200, 150, 80, 80, 120, 120, 120]
13:59:35.901 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,407] - 设置表格总宽度: 1020px (15300twips)
13:59:35.901 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [setDataRowsColumnWidth,451] - 数据行列宽设置完成
13:59:35.901 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,415] - 设置列宽完成: [150, 200, 150, 80, 80, 120, 120, 120]
13:59:35.901 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,157] - 开始处理表头，共2行
13:59:35.901 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第1行表头，共8列
13:59:35.912 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第2行表头，共8列
13:59:35.913 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,172] - 表头处理完成，共处理2行
13:59:35.914 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,146] - 表格创建完成
13:59:35.923 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 2816 bytes
13:59:35.927 [http-nio-9550-exec-10] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_135935.docx, 大小: 2816 bytes
14:13:42.228 [http-nio-9550-exec-8] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
14:13:42.228 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检验记录表
14:13:42.229 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [setPageOrientation,689] - 已设置文档为横向纸张
14:13:42.230 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,83] - 接收到表头数据，共2行
14:13:42.230 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,86] - 第1行表头，共8列
14:13:42.230 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,90] -   列0: content='检查工序名称', rowspan=2, colspan=1
14:13:42.230 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,90] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
14:13:42.230 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,90] -   列2: content='实际检查结果', rowspan=2, colspan=1
14:13:42.231 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='完工', rowspan=1, colspan=2
14:13:42.231 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,90] -   列5: content='操作员', rowspan=2, colspan=1
14:13:42.231 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,90] -   列6: content='班组长', rowspan=2, colspan=1
14:13:42.231 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,90] -   列7: content='检验员', rowspan=2, colspan=1
14:13:42.231 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,86] - 第2行表头，共8列
14:13:42.231 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='月', rowspan=1, colspan=1
14:13:42.231 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,90] -   列4: content='日', rowspan=1, colspan=1
14:13:42.232 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,110] - 创建表格: 3行 x 8列
14:13:42.233 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,385] - 从数据行获取列宽: [150, 200, 150, 80, 80, 120, 120, 120]
14:13:42.233 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,407] - 设置表格总宽度: 1020px (15300twips)
14:13:42.234 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [setDataRowsColumnWidth,451] - 数据行列宽设置完成
14:13:42.234 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,415] - 设置列宽完成: [150, 200, 150, 80, 80, 120, 120, 120]
14:13:42.234 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,157] - 开始处理表头，共2行
14:13:42.234 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第1行表头，共8列
14:13:42.237 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第2行表头，共8列
14:13:42.239 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,172] - 表头处理完成，共处理2行
14:13:42.239 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processDataRow,353] - 处理普通文本内容
14:13:42.239 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processDataRow,353] - 处理普通文本内容
14:13:42.240 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processDataRow,339] - 处理混合内容，mathMLMap大小: 1
14:13:42.240 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [insertMixedContent,554] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
14:13:42.240 [http-nio-9550-exec-8] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
14:13:42.361 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processDataRow,353] - 处理普通文本内容
14:13:42.362 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processDataRow,353] - 处理普通文本内容
14:13:42.362 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processDataRow,353] - 处理普通文本内容
14:13:42.362 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processDataRow,353] - 处理普通文本内容
14:13:42.362 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processDataRow,353] - 处理普通文本内容
14:13:42.365 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,146] - 表格创建完成
14:13:42.370 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 2977 bytes
14:13:42.373 [http-nio-9550-exec-8] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_141342.docx, 大小: 2977 bytes
14:20:15.801 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
14:20:15.806 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
14:20:20.322 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 439366 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
14:20:20.323 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
14:20:21.328 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
14:20:21.329 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:20:21.330 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
14:20:21.382 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:20:21.971 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
14:20:22.267 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
14:20:23.022 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
14:20:23.042 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.18 seconds (JVM running for 3.828)
14:20:40.799 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:20:40.928 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
14:20:40.929 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检验记录表
14:20:41.307 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,752] - 已设置文档为横向纸张
14:20:41.354 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,83] - 接收到表头数据，共2行
14:20:41.355 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,86] - 第1行表头，共8列
14:20:41.355 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列0: content='检查工序名称', rowspan=2, colspan=1
14:20:41.355 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
14:20:41.355 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列2: content='实际检查结果', rowspan=2, colspan=1
14:20:41.355 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='完工', rowspan=1, colspan=2
14:20:41.355 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列5: content='操作员', rowspan=2, colspan=1
14:20:41.355 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列6: content='班组长', rowspan=2, colspan=1
14:20:41.355 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列7: content='检验员', rowspan=2, colspan=1
14:20:41.355 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,86] - 第2行表头，共8列
14:20:41.356 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='月', rowspan=1, colspan=1
14:20:41.356 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列4: content='日', rowspan=1, colspan=1
14:20:41.356 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,110] - 创建表格: 3行 x 8列
14:20:41.393 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,448] - 从数据行获取列宽: [150, 200, 150, 80, 80, 120, 120, 120]
14:20:41.394 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,470] - 设置表格总宽度: 1020px (15300twips)
14:20:41.399 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setDataRowsColumnWidth,514] - 数据行列宽设置完成
14:20:41.399 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,478] - 设置列宽完成: [150, 200, 150, 80, 80, 120, 120, 120]
14:20:41.400 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,157] - 开始处理表头，共2行
14:20:41.400 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第1行表头，共8列
14:20:41.428 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setHeaderCellContent,311] - 设置纵向文字: '操作员' (3个字符，每字符一行)
14:20:41.429 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setHeaderCellContent,311] - 设置纵向文字: '班组长' (3个字符，每字符一行)
14:20:41.431 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setHeaderCellContent,311] - 设置纵向文字: '检验员' (3个字符，每字符一行)
14:20:41.433 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第2行表头，共8列
14:20:41.437 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,172] - 表头处理完成，共处理2行
14:20:41.438 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,416] - 处理普通文本内容
14:20:41.438 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,416] - 处理普通文本内容
14:20:41.438 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,402] - 处理混合内容，mathMLMap大小: 1
14:20:41.439 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,617] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
14:20:41.440 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
14:20:41.738 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,416] - 处理普通文本内容
14:20:41.738 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,416] - 处理普通文本内容
14:20:41.739 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,416] - 处理普通文本内容
14:20:41.740 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,416] - 处理普通文本内容
14:20:41.741 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,416] - 处理普通文本内容
14:20:41.748 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,146] - 表格创建完成
14:20:41.809 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 3005 bytes
14:20:41.819 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_142041.docx, 大小: 3005 bytes
14:24:20.116 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
14:24:20.116 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检验记录表
14:24:20.119 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,752] - 已设置文档为横向纸张
14:24:20.121 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,83] - 接收到表头数据，共2行
14:24:20.121 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,86] - 第1行表头，共8列
14:24:20.121 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,90] -   列0: content='检查工序<br>名称', rowspan=2, colspan=1
14:24:20.121 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,90] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
14:24:20.122 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,90] -   列2: content='实际检查结果', rowspan=2, colspan=1
14:24:20.122 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='完工', rowspan=1, colspan=2
14:24:20.122 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,90] -   列5: content='操作员', rowspan=2, colspan=1
14:24:20.122 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,90] -   列6: content='班组长', rowspan=2, colspan=1
14:24:20.122 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,90] -   列7: content='检验员', rowspan=2, colspan=1
14:24:20.122 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,86] - 第2行表头，共8列
14:24:20.122 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='月', rowspan=1, colspan=1
14:24:20.123 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,90] -   列4: content='日', rowspan=1, colspan=1
14:24:20.123 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,110] - 创建表格: 3行 x 8列
14:24:20.126 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,448] - 从数据行获取列宽: [150, 200, 150, 80, 80, 120, 120, 120]
14:24:20.127 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,470] - 设置表格总宽度: 1020px (15300twips)
14:24:20.128 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [setDataRowsColumnWidth,514] - 数据行列宽设置完成
14:24:20.128 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,478] - 设置列宽完成: [150, 200, 150, 80, 80, 120, 120, 120]
14:24:20.129 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,157] - 开始处理表头，共2行
14:24:20.129 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第1行表头，共8列
14:24:20.135 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [setHeaderCellContent,311] - 设置纵向文字: '操作员' (3个字符，每字符一行)
14:24:20.136 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [setHeaderCellContent,311] - 设置纵向文字: '班组长' (3个字符，每字符一行)
14:24:20.137 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [setHeaderCellContent,311] - 设置纵向文字: '检验员' (3个字符，每字符一行)
14:24:20.139 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第2行表头，共8列
14:24:20.140 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,172] - 表头处理完成，共处理2行
14:24:20.141 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processDataRow,416] - 处理普通文本内容
14:24:20.141 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processDataRow,416] - 处理普通文本内容
14:24:20.141 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processDataRow,416] - 处理普通文本内容
14:24:20.141 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processDataRow,416] - 处理普通文本内容
14:24:20.142 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processDataRow,416] - 处理普通文本内容
14:24:20.142 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processDataRow,416] - 处理普通文本内容
14:24:20.142 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processDataRow,416] - 处理普通文本内容
14:24:20.142 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processDataRow,416] - 处理普通文本内容
14:24:20.143 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,146] - 表格创建完成
14:24:20.152 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 2897 bytes
14:24:20.158 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_142420.docx, 大小: 2897 bytes
14:28:23.242 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
14:28:23.245 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
14:28:27.480 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 449764 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
14:28:27.481 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
14:28:28.422 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
14:28:28.422 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:28:28.423 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
14:28:28.470 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:28:29.054 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
14:28:29.325 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
14:28:29.995 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
14:28:30.013 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.971 seconds (JVM running for 3.598)
14:28:36.675 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:28:36.797 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
14:28:36.798 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检验记录表
14:28:37.169 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,798] - 已设置文档为横向纸张
14:28:37.219 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,83] - 接收到表头数据，共2行
14:28:37.220 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,86] - 第1行表头，共8列
14:28:37.220 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列0: content='检查工序<br>名称', rowspan=2, colspan=1
14:28:37.220 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
14:28:37.220 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列2: content='实际检查结果', rowspan=2, colspan=1
14:28:37.220 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='完工', rowspan=1, colspan=2
14:28:37.220 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列5: content='操作员', rowspan=2, colspan=1
14:28:37.221 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列6: content='班组长', rowspan=2, colspan=1
14:28:37.221 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列7: content='检验员', rowspan=2, colspan=1
14:28:37.221 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,86] - 第2行表头，共8列
14:28:37.221 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='月', rowspan=1, colspan=1
14:28:37.221 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列4: content='日', rowspan=1, colspan=1
14:28:37.221 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,110] - 创建表格: 2行 x 8列
14:28:37.260 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,502] - 使用默认列宽: [150, 200, 150, 80, 80, 120, 120, 120]
14:28:37.261 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,516] - 设置表格总宽度: 1020px (15300twips)
14:28:37.261 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setDataRowsColumnWidth,560] - 数据行列宽设置完成
14:28:37.261 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,524] - 设置列宽完成: [150, 200, 150, 80, 80, 120, 120, 120]
14:28:37.261 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,157] - 开始处理表头，共2行
14:28:37.262 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第1行表头，共8列
14:28:37.296 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setHeaderCellContent,312] - 设置纵向文字: '操作员' -> '操作员' (3个字符，每字符一行)
14:28:37.298 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setHeaderCellContent,312] - 设置纵向文字: '班组长' -> '班组长' (3个字符，每字符一行)
14:28:37.300 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setHeaderCellContent,312] - 设置纵向文字: '检验员' -> '检验员' (3个字符，每字符一行)
14:28:37.301 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第2行表头，共8列
14:28:37.304 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,172] - 表头处理完成，共处理2行
14:28:37.312 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,146] - 表格创建完成
14:28:37.400 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 2851 bytes
14:28:37.414 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_142837.docx, 大小: 2851 bytes
14:29:43.976 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
14:29:43.976 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检验记录表
14:29:43.978 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,798] - 已设置文档为横向纸张
14:29:43.980 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,83] - 接收到表头数据，共2行
14:29:43.980 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,86] - 第1行表头，共8列
14:29:43.980 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列0: content='检查工序<br>名称', rowspan=2, colspan=1
14:29:43.980 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
14:29:43.980 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列2: content='实际检查结果', rowspan=2, colspan=1
14:29:43.980 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='完工', rowspan=1, colspan=2
14:29:43.981 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列5: content='操作员', rowspan=2, colspan=1
14:29:43.981 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列6: content='班组长', rowspan=2, colspan=1
14:29:43.981 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列7: content='检验员', rowspan=2, colspan=1
14:29:43.981 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,86] - 第2行表头，共8列
14:29:43.981 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='月', rowspan=1, colspan=1
14:29:43.981 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列4: content='日', rowspan=1, colspan=1
14:29:43.981 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,110] - 创建表格: 3行 x 8列
14:29:43.983 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,494] - 从数据行获取列宽: [150, 200, 150, 80, 80, 120, 120, 120]
14:29:43.984 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,516] - 设置表格总宽度: 1020px (15300twips)
14:29:43.987 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setDataRowsColumnWidth,560] - 数据行列宽设置完成
14:29:43.987 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,524] - 设置列宽完成: [150, 200, 150, 80, 80, 120, 120, 120]
14:29:43.987 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,157] - 开始处理表头，共2行
14:29:43.988 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第1行表头，共8列
14:29:43.994 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setHeaderCellContent,312] - 设置纵向文字: '操作员' -> '操作员' (3个字符，每字符一行)
14:29:43.995 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setHeaderCellContent,312] - 设置纵向文字: '班组长' -> '班组长' (3个字符，每字符一行)
14:29:43.996 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setHeaderCellContent,312] - 设置纵向文字: '检验员' -> '检验员' (3个字符，每字符一行)
14:29:43.998 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第2行表头，共8列
14:29:43.999 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,172] - 表头处理完成，共处理2行
14:29:44.000 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,462] - 处理普通文本内容
14:29:44.000 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,462] - 处理普通文本内容
14:29:44.001 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,462] - 处理普通文本内容
14:29:44.001 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,462] - 处理普通文本内容
14:29:44.001 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,462] - 处理普通文本内容
14:29:44.001 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,448] - 处理混合内容，mathMLMap大小: 1
14:29:44.001 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [insertMixedContent,663] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
14:29:44.003 [http-nio-9550-exec-2] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
14:29:44.292 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,462] - 处理普通文本内容
14:29:44.293 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,462] - 处理普通文本内容
14:29:44.293 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,146] - 表格创建完成
14:29:44.300 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 2998 bytes
14:29:44.303 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_142944.docx, 大小: 2998 bytes
14:30:50.697 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
14:30:50.698 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检验记录表
14:30:50.699 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,798] - 已设置文档为横向纸张
14:30:50.699 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,83] - 接收到表头数据，共2行
14:30:50.700 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,86] - 第1行表头，共8列
14:30:50.700 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,90] -   列0: content='检查工序<br>名称', rowspan=2, colspan=1
14:30:50.700 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,90] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
14:30:50.700 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,90] -   列2: content='实际检查结果', rowspan=2, colspan=1
14:30:50.700 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='完工', rowspan=1, colspan=2
14:30:50.700 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,90] -   列5: content='操作员', rowspan=2, colspan=1
14:30:50.700 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,90] -   列6: content='班组长', rowspan=2, colspan=1
14:30:50.700 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,90] -   列7: content='检验员', rowspan=2, colspan=1
14:30:50.700 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,86] - 第2行表头，共8列
14:30:50.701 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='月', rowspan=1, colspan=1
14:30:50.701 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,90] -   列4: content='日', rowspan=1, colspan=1
14:30:50.701 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,110] - 创建表格: 3行 x 8列
14:30:50.702 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,494] - 从数据行获取列宽: [150, 450, 150, 80, 80, 30, 30, 30]
14:30:50.702 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,516] - 设置表格总宽度: 1000px (15000twips)
14:30:50.703 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setDataRowsColumnWidth,560] - 数据行列宽设置完成
14:30:50.703 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,524] - 设置列宽完成: [150, 450, 150, 80, 80, 30, 30, 30]
14:30:50.703 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,157] - 开始处理表头，共2行
14:30:50.703 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第1行表头，共8列
14:30:50.705 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setHeaderCellContent,312] - 设置纵向文字: '操作员' -> '操作员' (3个字符，每字符一行)
14:30:50.706 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setHeaderCellContent,312] - 设置纵向文字: '班组长' -> '班组长' (3个字符，每字符一行)
14:30:50.706 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setHeaderCellContent,312] - 设置纵向文字: '检验员' -> '检验员' (3个字符，每字符一行)
14:30:50.707 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第2行表头，共8列
14:30:50.709 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,172] - 表头处理完成，共处理2行
14:30:50.709 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRow,462] - 处理普通文本内容
14:30:50.709 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRow,462] - 处理普通文本内容
14:30:50.709 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRow,462] - 处理普通文本内容
14:30:50.710 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRow,462] - 处理普通文本内容
14:30:50.710 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRow,462] - 处理普通文本内容
14:30:50.710 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRow,448] - 处理混合内容，mathMLMap大小: 1
14:30:50.710 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [insertMixedContent,663] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
14:30:50.711 [http-nio-9550-exec-3] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
14:30:50.803 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRow,462] - 处理普通文本内容
14:30:50.804 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRow,462] - 处理普通文本内容
14:30:50.806 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,146] - 表格创建完成
14:30:50.812 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 2995 bytes
14:30:50.815 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_143050.docx, 大小: 2995 bytes
15:30:06.108 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
15:30:06.116 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
15:38:12.725 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 534835 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
15:38:12.726 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
15:38:13.705 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
15:38:13.706 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:38:13.706 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
15:38:13.763 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:38:14.420 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:38:14.778 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:38:16.215 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
15:38:16.239 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.967 seconds (JVM running for 4.671)
15:39:03.095 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
15:39:03.098 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
15:39:07.657 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 536152 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
15:39:07.658 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
15:39:08.561 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
15:39:08.562 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:39:08.563 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
15:39:08.611 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:39:09.233 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:39:09.572 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:39:10.980 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
15:39:11.010 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.763 seconds (JVM running for 4.373)
15:39:29.563 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:39:29.660 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
15:39:29.660 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
15:39:29.661 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,434] - JSON请求转换完成，表头数量: 8, 数据行数量: 3, 合并单元格数量: 1
15:39:29.661 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,883] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
15:39:29.661 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 合并单元格数量: 1
15:39:29.980 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,814] - 已设置文档为横向纸张
15:39:30.023 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,938] - 创建表格，总行数: 4, 总列数: 8
15:39:30.127 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1052] - 开始应用 1 个合并单元格
15:39:30.205 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,912] - 包含合并单元格的Word文档导出完成，文件大小: 3077 bytes
15:39:30.214 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_153930.docx, 大小: 3077 bytes
15:40:06.912 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
15:40:06.913 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 0
15:40:06.913 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,434] - JSON请求转换完成，表头数量: 8, 数据行数量: 3, 合并单元格数量: 0
15:40:06.913 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,883] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
15:40:06.914 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 合并单元格数量: 0
15:40:06.915 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,814] - 已设置文档为横向纸张
15:40:06.916 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableWithMerges,938] - 创建表格，总行数: 4, 总列数: 8
15:40:06.940 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,912] - 包含合并单元格的Word文档导出完成，文件大小: 2989 bytes
15:40:06.945 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_154006.docx, 大小: 2989 bytes
15:41:20.554 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
15:41:20.554 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 0
15:41:20.554 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,434] - JSON请求转换完成，表头数量: 0, 数据行数量: 0, 合并单元格数量: 0
15:41:20.555 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,883] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
15:41:20.555 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 合并单元格数量: 0
15:41:20.556 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,814] - 已设置文档为横向纸张
15:41:20.557 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTableWithMerges,938] - 创建表格，总行数: 0, 总列数: 0
15:41:20.562 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,912] - 包含合并单元格的Word文档导出完成，文件大小: 2520 bytes
15:41:20.565 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_154120.docx, 大小: 2520 bytes
15:42:00.240 [http-nio-9550-exec-8] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
15:42:00.240 [http-nio-9550-exec-8] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 0
15:42:00.241 [http-nio-9550-exec-8] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,434] - JSON请求转换完成，表头数量: 0, 数据行数量: 0, 合并单元格数量: 0
15:42:00.241 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,883] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
15:42:00.241 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 合并单元格数量: 0
15:42:00.242 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [setPageOrientation,814] - 已设置文档为横向纸张
15:42:00.242 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTableWithMerges,938] - 创建表格，总行数: 0, 总列数: 0
15:42:00.247 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,912] - 包含合并单元格的Word文档导出完成，文件大小: 2520 bytes
15:42:00.251 [http-nio-9550-exec-8] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_154200.docx, 大小: 2520 bytes
15:43:26.764 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
15:43:26.764 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检验记录表
15:43:26.765 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,814] - 已设置文档为横向纸张
15:43:26.765 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,83] - 接收到表头数据，共2行
15:43:26.765 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,86] - 第1行表头，共8列
15:43:26.766 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,90] -   列0: content='检查工序<br>名称', rowspan=2, colspan=1
15:43:26.766 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,90] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
15:43:26.766 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,90] -   列2: content='实际检查结果', rowspan=2, colspan=1
15:43:26.766 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='完工', rowspan=1, colspan=2
15:43:26.766 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,90] -   列5: content='操作员', rowspan=2, colspan=1
15:43:26.766 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,90] -   列6: content='班组长', rowspan=2, colspan=1
15:43:26.766 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,90] -   列7: content='检验员', rowspan=2, colspan=1
15:43:26.766 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,86] - 第2行表头，共8列
15:43:26.766 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='月', rowspan=1, colspan=1
15:43:26.766 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,90] -   列4: content='日', rowspan=1, colspan=1
15:43:26.767 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,110] - 创建表格: 3行 x 8列
15:43:26.767 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,494] - 从数据行获取列宽: [150, 200, 150, 50, 50, 80, 80, 80]
15:43:26.768 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,516] - 设置表格总宽度: 840px (12600twips)
15:43:26.768 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setDataRowsColumnWidth,560] - 数据行列宽设置完成
15:43:26.768 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,524] - 设置列宽完成: [150, 200, 150, 50, 50, 80, 80, 80]
15:43:26.768 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,157] - 开始处理表头，共2行
15:43:26.768 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第1行表头，共8列
15:43:26.774 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setHeaderCellContent,312] - 设置纵向文字: '操作员' -> '操作员' (3个字符，每字符一行)
15:43:26.775 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setHeaderCellContent,312] - 设置纵向文字: '班组长' -> '班组长' (3个字符，每字符一行)
15:43:26.776 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setHeaderCellContent,312] - 设置纵向文字: '检验员' -> '检验员' (3个字符，每字符一行)
15:43:26.776 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第2行表头，共8列
15:43:26.777 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,172] - 表头处理完成，共处理2行
15:43:26.777 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRow,462] - 处理普通文本内容
15:43:26.777 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRow,462] - 处理普通文本内容
15:43:26.777 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRow,462] - 处理普通文本内容
15:43:26.777 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRow,448] - 处理混合内容，mathMLMap大小: 1
15:43:26.778 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [insertMixedContent,679] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
15:43:26.779 [http-nio-9550-exec-3] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
15:43:27.065 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRow,462] - 处理普通文本内容
15:43:27.066 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRow,462] - 处理普通文本内容
15:43:27.066 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRow,462] - 处理普通文本内容
15:43:27.066 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [processDataRow,462] - 处理普通文本内容
15:43:27.073 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTable,146] - 表格创建完成
15:43:27.077 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 3033 bytes
15:43:27.080 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_154327.docx, 大小: 3033 bytes
16:08:17.548 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
16:08:17.551 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
16:08:21.827 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 571307 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
16:08:21.828 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
16:08:22.684 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
16:08:22.685 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:08:22.685 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
16:08:22.734 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:08:23.278 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
16:08:23.545 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
16:08:24.141 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
16:08:24.156 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.707 seconds (JVM running for 3.312)
16:16:38.679 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
16:16:38.681 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
16:18:20.087 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 583515 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
16:18:20.089 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
16:18:20.830 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
16:18:20.831 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:18:20.831 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
16:18:20.871 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:18:21.310 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
16:18:21.525 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
16:18:22.064 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
16:18:22.078 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.388 seconds (JVM running for 2.741)
16:18:30.976 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:18:41.356 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
16:18:41.357 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
16:18:41.358 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,457] - JSON请求转换完成，表头数量: 2, 数据行数量: 3, 合并单元格数量: 1
16:18:41.358 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,883] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
16:18:41.358 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 合并单元格数量: 1
16:18:41.546 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [setPageOrientation,814] - 已设置文档为横向纸张
16:18:41.594 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTableWithMerges,939] - 创建表格，总行数: 5, 总列数: 8
16:18:41.689 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTableWithMerges,970] - 应用表头合并单元格，数量: 7
16:18:41.689 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyMerges,1064] - 开始应用 7 个合并单元格，表头行数: 0
16:18:41.693 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTableWithMerges,978] - 应用数据行合并单元格，数量: 1，表头偏移: 2
16:18:41.693 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyMerges,1064] - 开始应用 1 个合并单元格，表头行数: 2
16:18:41.756 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,912] - 包含合并单元格的Word文档导出完成，文件大小: 3148 bytes
16:18:41.764 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_161841.docx, 大小: 3148 bytes
16:26:16.897 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
16:26:16.898 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
16:26:16.898 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,457] - JSON请求转换完成，表头数量: 2, 数据行数量: 3, 合并单元格数量: 1
16:26:16.898 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,883] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
16:26:16.898 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 合并单元格数量: 1
16:26:16.899 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,814] - 已设置文档为横向纸张
16:26:16.900 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTableWithMerges,939] - 创建表格，总行数: 5, 总列数: 8
16:26:16.943 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTableWithMerges,970] - 应用表头合并单元格，数量: 7
16:26:16.943 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyMerges,1064] - 开始应用 7 个合并单元格，表头行数: 0
16:26:16.943 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTableWithMerges,978] - 应用数据行合并单元格，数量: 1，表头偏移: 2
16:26:16.943 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyMerges,1064] - 开始应用 1 个合并单元格，表头行数: 2
16:26:16.950 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,912] - 包含合并单元格的Word文档导出完成，文件大小: 3255 bytes
16:26:16.953 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_162616.docx, 大小: 3255 bytes
16:54:21.109 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
16:54:21.117 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
16:54:26.019 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 626791 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
16:54:26.021 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
16:54:26.692 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
16:54:26.693 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:54:26.693 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
16:54:26.724 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:54:27.136 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
16:54:27.336 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
16:54:27.823 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
16:54:27.837 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.194 seconds (JVM running for 2.534)
16:54:56.747 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:54:56.821 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
16:54:56.822 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
16:54:56.822 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,493] - JSON请求转换完成，表头数量: 2, 数据行数量: 4, 合并单元格数量: 1
16:54:56.823 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,883] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
16:54:56.823 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 合并单元格数量: 1
16:54:57.074 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,814] - 已设置文档为横向纸张
16:54:57.105 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,939] - 创建表格，总行数: 6, 总列数: 8
16:54:57.166 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,679] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mi>E</mi><mo>=</mo><mrow><mi>m</mi><mo>&#x2062;</mo><msup><mi>c</mi><mn>2</mn></msup></mrow></math>}
16:54:57.167 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
16:54:58.216 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,679] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mi>&#x3C0;</mi><mo>&#x2062;</mo><msup><mi>r</mi><mn>2</mn></msup></math>}
16:54:58.217 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
16:54:58.284 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,679] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><munderover><mo>&#x2211;</mo><mrow><mi>i</mi><mo>=</mo><mn>1</mn></mrow><mi>n</mi></munderover><msub><mi>x</mi><mi>i</mi></msub></math>}
16:54:58.285 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
16:54:58.356 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,679] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><msubsup><mo>&#x222B;</mo><mn>0</mn><mn>1</mn></msubsup><mi>x</mi><mrow><mi>d</mi><mi>x</mi></mrow></mrow><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></math>}
16:54:58.356 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
16:54:58.432 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,970] - 应用表头合并单元格，数量: 7
16:54:58.433 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1072] - 开始应用 7 个合并单元格，表头行数: 0
16:54:58.436 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,978] - 应用数据行合并单元格，数量: 1，表头偏移: 2
16:54:58.437 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1072] - 开始应用 1 个合并单元格，表头行数: 2
16:54:58.493 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,912] - 包含合并单元格的Word文档导出完成，文件大小: 3568 bytes
16:54:58.501 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_165458.docx, 大小: 3568 bytes
16:55:36.749 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
16:55:36.750 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
16:55:36.750 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,493] - JSON请求转换完成，表头数量: 2, 数据行数量: 3, 合并单元格数量: 1
16:55:36.750 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,883] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
16:55:36.750 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 合并单元格数量: 1
16:55:36.752 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,814] - 已设置文档为横向纸张
16:55:36.752 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableWithMerges,939] - 创建表格，总行数: 5, 总列数: 8
16:55:36.764 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableWithMerges,970] - 应用表头合并单元格，数量: 7
16:55:36.764 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerges,1072] - 开始应用 7 个合并单元格，表头行数: 0
16:55:36.764 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableWithMerges,978] - 应用数据行合并单元格，数量: 1，表头偏移: 2
16:55:36.765 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerges,1072] - 开始应用 1 个合并单元格，表头行数: 2
16:55:36.770 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,912] - 包含合并单元格的Word文档导出完成，文件大小: 3176 bytes
16:55:36.773 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_165536.docx, 大小: 3176 bytes
16:58:59.586 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
16:58:59.586 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
16:58:59.586 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,493] - JSON请求转换完成，表头数量: 2, 数据行数量: 4, 合并单元格数量: 1
16:58:59.587 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,883] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
16:58:59.587 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 合并单元格数量: 1
16:58:59.587 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,814] - 已设置文档为横向纸张
16:58:59.588 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTableWithMerges,939] - 创建表格，总行数: 6, 总列数: 8
16:58:59.594 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [insertMixedContent,679] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mi>E</mi><mo>=</mo><mrow><mi>m</mi><mo>&#x2062;</mo><msup><mi>c</mi><mn>2</mn></msup></mrow></math>}
16:58:59.594 [http-nio-9550-exec-3] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
16:58:59.656 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [insertMixedContent,679] - 插入混合内容，内容: 不错
__MATH_FORMULA_0__
可以
1, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mi>&#x3C0;</mi><mo>&#x2062;</mo><msup><mi>r</mi><mn>2</mn></msup></math>}
16:58:59.656 [http-nio-9550-exec-3] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
16:58:59.713 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [insertMixedContent,679] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><munderover><mo>&#x2211;</mo><mrow><mi>i</mi><mo>=</mo><mn>1</mn></mrow><mi>n</mi></munderover><msub><mi>x</mi><mi>i</mi></msub></math>}
16:58:59.713 [http-nio-9550-exec-3] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
16:58:59.767 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [insertMixedContent,679] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><msubsup><mo>&#x222B;</mo><mn>0</mn><mn>1</mn></msubsup><mi>x</mi><mrow><mi>d</mi><mi>x</mi></mrow></mrow><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></math>}
16:58:59.768 [http-nio-9550-exec-3] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
16:58:59.834 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTableWithMerges,970] - 应用表头合并单元格，数量: 7
16:58:59.836 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyMerges,1072] - 开始应用 7 个合并单元格，表头行数: 0
16:58:59.838 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTableWithMerges,978] - 应用数据行合并单元格，数量: 1，表头偏移: 2
16:58:59.838 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyMerges,1072] - 开始应用 1 个合并单元格，表头行数: 2
16:58:59.847 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,912] - 包含合并单元格的Word文档导出完成，文件大小: 3594 bytes
16:58:59.850 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_165859.docx, 大小: 3594 bytes
17:01:46.683 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 635784 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
17:01:46.685 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
17:01:47.329 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
17:01:47.330 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:01:47.330 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
17:01:47.363 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:01:47.753 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
17:01:47.942 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
17:01:48.506 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
17:01:48.623 [main] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
17:01:48.625 [main] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
17:01:48.628 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9550"]
17:01:48.629 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
17:01:48.632 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9550"]
17:01:48.633 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9550"]
17:01:54.031 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
17:01:54.034 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
17:01:55.781 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 636182 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
17:01:55.785 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
17:01:56.869 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
17:01:56.869 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:01:56.869 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
17:01:56.905 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:01:57.315 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
17:01:57.514 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
17:01:58.001 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
17:01:58.014 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.775 seconds (JVM running for 3.109)
17:02:01.852 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:02:01.917 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
17:02:01.918 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
17:02:01.918 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,493] - JSON请求转换完成，表头数量: 2, 数据行数量: 4, 合并单元格数量: 1
17:02:01.918 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,885] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
17:02:01.919 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,886] - 合并单元格数量: 1
17:02:02.136 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,814] - 已设置文档为横向纸张
17:02:02.169 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,941] - 创建表格，总行数: 6, 总列数: 8
17:02:02.229 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,679] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mi>E</mi><mo>=</mo><mrow><mi>m</mi><mo>&#x2062;</mo><msup><mi>c</mi><mn>2</mn></msup></mrow></math>}
17:02:02.230 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
17:02:02.468 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,679] - 插入混合内容，内容: 不错
__MATH_FORMULA_0__
可以
1, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mi>&#x3C0;</mi><mo>&#x2062;</mo><msup><mi>r</mi><mn>2</mn></msup></math>}
17:02:02.468 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
17:02:02.541 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,679] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><munderover><mo>&#x2211;</mo><mrow><mi>i</mi><mo>=</mo><mn>1</mn></mrow><mi>n</mi></munderover><msub><mi>x</mi><mi>i</mi></msub></math>}
17:02:02.541 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
17:02:02.607 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,679] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><msubsup><mo>&#x222B;</mo><mn>0</mn><mn>1</mn></msubsup><mi>x</mi><mrow><mi>d</mi><mi>x</mi></mrow></mrow><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></math>}
17:02:02.607 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
17:02:02.675 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,972] - 应用表头合并单元格，数量: 7
17:02:02.675 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1074] - 开始应用 7 个合并单元格，表头行数: 0
17:02:02.680 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,980] - 应用数据行合并单元格，数量: 1，表头偏移: 2
17:02:02.680 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1074] - 开始应用 1 个合并单元格，表头行数: 2
17:02:02.731 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,914] - 包含合并单元格的Word文档导出完成，文件大小: 3589 bytes
17:02:02.740 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_170202.docx, 大小: 3589 bytes
20:36:37.876 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
20:36:37.883 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
20:36:43.724 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 701571 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
20:36:43.727 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
20:36:44.519 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
20:36:44.520 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:36:44.520 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
20:36:44.557 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:36:45.009 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
20:36:45.208 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
20:36:45.722 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
20:36:45.737 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.453 seconds (JVM running for 2.852)
20:38:12.054 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:50:29.484 [http-nio-9550-exec-10] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
20:50:29.484 [http-nio-9550-exec-10] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
20:50:29.485 [http-nio-9550-exec-10] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,493] - JSON请求转换完成，表头数量: 2, 数据行数量: 3, 合并单元格数量: 1
20:50:29.485 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,883] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
20:50:29.485 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 合并单元格数量: 1
20:50:29.656 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [setPageOrientation,814] - 已设置文档为横向纸张
20:50:29.698 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTableWithMerges,939] - 创建表格，总行数: 5, 总列数: 8
20:50:29.788 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [insertMixedContent,679] - 插入混合内容，内容: 性能测试：__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><mi>C</mi><mo>&#x2062;</mo><mi>P</mi><mo>&#x2062;</mo><mi>U</mi></mrow><mo>=</mo><mrow><mn>95</mn><mi mathvariant="normal">%</mi></mrow></math>}
20:50:29.790 [http-nio-9550-exec-10] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
20:50:30.040 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTableWithMerges,970] - 应用表头合并单元格，数量: 7
20:50:30.040 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [applyMerges,1072] - 开始应用 7 个合并单元格，表头行数: 0
20:50:30.044 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTableWithMerges,978] - 应用数据行合并单元格，数量: 1，表头偏移: 2
20:50:30.044 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [applyMerges,1072] - 开始应用 1 个合并单元格，表头行数: 2
20:50:30.098 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,912] - 包含合并单元格的Word文档导出完成，文件大小: 3391 bytes
20:50:30.105 [http-nio-9550-exec-10] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_205030.docx, 大小: 3391 bytes
21:03:56.482 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
21:03:56.485 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
21:04:02.235 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 733903 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
21:04:02.238 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
21:04:03.093 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
21:04:03.094 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
21:04:03.095 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
21:04:03.132 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
21:04:03.595 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
21:04:03.819 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
21:04:04.390 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
21:04:04.405 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.739 seconds (JVM running for 3.343)
21:04:09.061 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:04:26.415 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
21:04:26.416 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
21:04:26.417 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,513] - JSON请求转换完成，表头数量: 2, 数据行数量: 3, 合并单元格数量: 1
21:04:26.417 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,883] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
21:04:26.417 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 合并单元格数量: 1
21:04:28.966 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [setPageOrientation,814] - 已设置文档为横向纸张
21:04:29.006 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTableWithMerges,940] - 创建表格，总行数: 5, 总列数: 8
21:04:29.051 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1653] - 应用表头宽度配置: 列宽=[280, 150, 120, 100, 100, 90, 90, 90], 行高=[60, 40]
21:04:29.104 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [insertMixedContent,679] - 插入混合内容，内容: 性能测试：__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><mi>C</mi><mo>&#x2062;</mo><mi>P</mi><mo>&#x2062;</mo><mi>U</mi></mrow><mo>=</mo><mrow><mn>95</mn><mi mathvariant="normal">%</mi></mrow></math>}
21:04:29.107 [http-nio-9550-exec-5] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
21:04:29.462 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTableWithMerges,976] - 应用表头合并单元格，数量: 5
21:04:29.463 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyMerges,1078] - 开始应用 5 个合并单元格，表头行数: 0
21:04:29.467 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTableWithMerges,984] - 应用数据行合并单元格，数量: 1，表头偏移: 2
21:04:29.467 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyMerges,1078] - 开始应用 1 个合并单元格，表头行数: 2
21:04:29.527 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,912] - 包含合并单元格的Word文档导出完成，文件大小: 3427 bytes
21:04:29.535 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_210429.docx, 大小: 3427 bytes
21:06:21.034 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
21:06:21.035 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
21:06:21.035 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,513] - JSON请求转换完成，表头数量: 2, 数据行数量: 3, 合并单元格数量: 1
21:06:21.035 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,883] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
21:06:21.035 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 合并单元格数量: 1
21:06:21.037 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,814] - 已设置文档为横向纸张
21:06:21.038 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTableWithMerges,940] - 创建表格，总行数: 5, 总列数: 8
21:06:21.040 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1653] - 应用表头宽度配置: 列宽=[380, 100, 100, 100, 100, 90, 90, 90], 行高=[60, 40]
21:06:21.053 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [insertMixedContent,679] - 插入混合内容，内容: 性能测试：__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><mi>C</mi><mo>&#x2062;</mo><mi>P</mi><mo>&#x2062;</mo><mi>U</mi></mrow><mo>=</mo><mrow><mn>95</mn><mi mathvariant="normal">%</mi></mrow></math>}
21:06:21.053 [http-nio-9550-exec-6] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
21:06:21.144 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTableWithMerges,976] - 应用表头合并单元格，数量: 5
21:06:21.144 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyMerges,1078] - 开始应用 5 个合并单元格，表头行数: 0
21:06:21.145 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTableWithMerges,984] - 应用数据行合并单元格，数量: 1，表头偏移: 2
21:06:21.146 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyMerges,1078] - 开始应用 1 个合并单元格，表头行数: 2
21:06:21.151 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,912] - 包含合并单元格的Word文档导出完成，文件大小: 3417 bytes
21:06:21.154 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_210621.docx, 大小: 3417 bytes
22:11:11.513 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
22:11:11.517 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
22:11:11.518 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,513] - JSON请求转换完成，表头数量: 2, 数据行数量: 3, 合并单元格数量: 1
22:11:11.518 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,883] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
22:11:11.519 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 合并单元格数量: 1
22:11:11.527 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,814] - 已设置文档为横向纸张
22:11:11.531 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTableWithMerges,940] - 创建表格，总行数: 5, 总列数: 8
22:11:11.537 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1653] - 应用表头宽度配置: 列宽=[280, 150, 220, 100, 100, 90, 90, 90], 行高=[60, 40]
22:11:11.564 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [insertMixedContent,679] - 插入混合内容，内容: 性能测试：__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><mi>C</mi><mo>&#x2062;</mo><mi>P</mi><mo>&#x2062;</mo><mi>U</mi></mrow><mo>=</mo><mrow><mn>95</mn><mi mathvariant="normal">%</mi></mrow></math>}
22:11:11.564 [http-nio-9550-exec-3] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
22:11:11.780 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTableWithMerges,976] - 应用表头合并单元格，数量: 5
22:11:11.780 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyMerges,1078] - 开始应用 5 个合并单元格，表头行数: 0
22:11:11.781 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTableWithMerges,984] - 应用数据行合并单元格，数量: 1，表头偏移: 2
22:11:11.782 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyMerges,1078] - 开始应用 1 个合并单元格，表头行数: 2
22:11:11.805 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,912] - 包含合并单元格的Word文档导出完成，文件大小: 3429 bytes
22:11:11.820 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_221111.docx, 大小: 3429 bytes
22:48:30.038 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
22:48:30.042 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
22:48:30.043 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,513] - JSON请求转换完成，表头数量: 2, 数据行数量: 3, 合并单元格数量: 1
22:48:30.044 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,883] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
22:48:30.044 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 合并单元格数量: 1
22:48:30.048 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,814] - 已设置文档为横向纸张
22:48:30.051 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTableWithMerges,940] - 创建表格，总行数: 5, 总列数: 8
22:48:30.056 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1653] - 应用表头宽度配置: 列宽=[130, 150, 230, 100, 100, 90, 90, 90], 行高=[60, 40]
22:48:30.079 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [insertMixedContent,679] - 插入混合内容，内容: 性能测试：__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><mi>C</mi><mo>&#x2062;</mo><mi>P</mi><mo>&#x2062;</mo><mi>U</mi></mrow><mo>=</mo><mrow><mn>95</mn><mi mathvariant="normal">%</mi></mrow></math>}
22:48:30.080 [http-nio-9550-exec-6] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
22:48:30.546 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTableWithMerges,976] - 应用表头合并单元格，数量: 5
22:48:30.547 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyMerges,1078] - 开始应用 5 个合并单元格，表头行数: 0
22:48:30.548 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTableWithMerges,984] - 应用数据行合并单元格，数量: 1，表头偏移: 2
22:48:30.548 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyMerges,1078] - 开始应用 1 个合并单元格，表头行数: 2
22:48:30.563 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,912] - 包含合并单元格的Word文档导出完成，文件大小: 3424 bytes
22:48:30.570 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_224830.docx, 大小: 3424 bytes
22:49:25.828 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
22:49:25.828 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 0
22:49:25.829 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,513] - JSON请求转换完成，表头数量: 2, 数据行数量: 3, 合并单元格数量: 0
22:49:25.829 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,883] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
22:49:25.829 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 合并单元格数量: 0
22:49:25.830 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,814] - 已设置文档为横向纸张
22:49:25.831 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTableWithMerges,940] - 创建表格，总行数: 5, 总列数: 8
22:49:25.833 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1653] - 应用表头宽度配置: 列宽=[150, 200, 150, 50, 50, 80, 80, 80], 行高=[50, 50]
22:49:25.858 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTableWithMerges,976] - 应用表头合并单元格，数量: 7
22:49:25.858 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [applyMerges,1078] - 开始应用 7 个合并单元格，表头行数: 0
22:49:25.870 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,912] - 包含合并单元格的Word文档导出完成，文件大小: 3112 bytes
22:49:25.877 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_224925.docx, 大小: 3112 bytes
22:49:41.770 [http-nio-9550-exec-8] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
22:49:41.771 [http-nio-9550-exec-8] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 0
22:49:41.771 [http-nio-9550-exec-8] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,513] - JSON请求转换完成，表头数量: 2, 数据行数量: 3, 合并单元格数量: 0
22:49:41.771 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,883] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
22:49:41.771 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 合并单元格数量: 0
22:49:41.772 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [setPageOrientation,814] - 已设置文档为横向纸张
22:49:41.773 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTableWithMerges,940] - 创建表格，总行数: 5, 总列数: 8
22:49:41.775 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1653] - 应用表头宽度配置: 列宽=[150, 200, 150, 50, 50, 80, 80, 80], 行高=[50, 50]
22:49:41.793 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTableWithMerges,976] - 应用表头合并单元格，数量: 7
22:49:41.793 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [applyMerges,1078] - 开始应用 7 个合并单元格，表头行数: 0
22:49:41.803 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,912] - 包含合并单元格的Word文档导出完成，文件大小: 3112 bytes
22:49:41.810 [http-nio-9550-exec-8] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_224941.docx, 大小: 3112 bytes
22:51:37.227 [http-nio-9550-exec-9] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
22:51:37.227 [http-nio-9550-exec-9] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 0
22:51:37.227 [http-nio-9550-exec-9] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,513] - JSON请求转换完成，表头数量: 2, 数据行数量: 3, 合并单元格数量: 0
22:51:37.228 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,883] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
22:51:37.228 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 合并单元格数量: 0
22:51:37.229 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,814] - 已设置文档为横向纸张
22:51:37.230 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTableWithMerges,940] - 创建表格，总行数: 5, 总列数: 8
22:51:37.231 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1653] - 应用表头宽度配置: 列宽=[150, 200, 150, 50, 50, 80, 80, 80], 行高=[50, 50]
22:51:37.252 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTableWithMerges,976] - 应用表头合并单元格，数量: 7
22:51:37.252 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [applyMerges,1078] - 开始应用 7 个合并单元格，表头行数: 0
22:51:37.262 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,912] - 包含合并单元格的Word文档导出完成，文件大小: 3112 bytes
22:51:37.268 [http-nio-9550-exec-9] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_225137.docx, 大小: 3112 bytes
22:52:56.980 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
22:52:56.981 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
22:52:56.981 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,513] - JSON请求转换完成，表头数量: 2, 数据行数量: 3, 合并单元格数量: 1
22:52:56.981 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,883] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
22:52:56.981 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 合并单元格数量: 1
22:52:56.982 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,814] - 已设置文档为横向纸张
22:52:56.982 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,940] - 创建表格，总行数: 5, 总列数: 8
22:52:56.984 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1653] - 应用表头宽度配置: 列宽=[180, 150, 390, 100, 100, 90, 90, 90], 行高=[60, 40]
22:52:56.999 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,679] - 插入混合内容，内容: 性能测试：__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><mi>C</mi><mo>&#x2062;</mo><mi>P</mi><mo>&#x2062;</mo><mi>U</mi></mrow><mo>=</mo><mrow><mn>95</mn><mi mathvariant="normal">%</mi></mrow></math>}
22:52:57.000 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
22:52:57.262 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,976] - 应用表头合并单元格，数量: 5
22:52:57.263 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1078] - 开始应用 5 个合并单元格，表头行数: 0
22:52:57.264 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,984] - 应用数据行合并单元格，数量: 1，表头偏移: 2
22:52:57.264 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1078] - 开始应用 1 个合并单元格，表头行数: 2
22:52:57.274 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,912] - 包含合并单元格的Word文档导出完成，文件大小: 3427 bytes
22:52:57.283 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_225257.docx, 大小: 3427 bytes
22:55:30.458 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
22:55:30.459 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检验记录表
22:55:30.460 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [setPageOrientation,814] - 已设置文档为横向纸张
22:55:30.461 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTable,83] - 接收到表头数据，共2行
22:55:30.461 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTable,86] - 第1行表头，共8列
22:55:30.461 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTable,90] -   列0: content='检查工序<br>名称', rowspan=2, colspan=1
22:55:30.461 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTable,90] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
22:55:30.461 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTable,90] -   列2: content='实际检查结果', rowspan=2, colspan=1
22:55:30.461 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='完工', rowspan=1, colspan=2
22:55:30.462 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTable,90] -   列5: content='操作员', rowspan=2, colspan=1
22:55:30.462 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTable,90] -   列6: content='班组长', rowspan=2, colspan=1
22:55:30.462 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTable,90] -   列7: content='检验员', rowspan=2, colspan=1
22:55:30.462 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTable,86] - 第2行表头，共8列
22:55:30.462 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='月', rowspan=1, colspan=1
22:55:30.462 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTable,90] -   列4: content='日', rowspan=1, colspan=1
22:55:30.462 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTable,110] - 创建表格: 3行 x 8列
22:55:30.463 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,494] - 从数据行获取列宽: [150, 400, 150, 50, 50, 80, 80, 80]
22:55:30.464 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,516] - 设置表格总宽度: 1040px (15600twips)
22:55:30.465 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [setDataRowsColumnWidth,560] - 数据行列宽设置完成
22:55:30.466 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [setTableWidthAndColumns,524] - 设置列宽完成: [150, 400, 150, 50, 50, 80, 80, 80]
22:55:30.466 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,157] - 开始处理表头，共2行
22:55:30.466 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第1行表头，共8列
22:55:30.468 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [setHeaderCellContent,312] - 设置纵向文字: '操作员' -> '操作员' (3个字符，每字符一行)
22:55:30.469 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [setHeaderCellContent,312] - 设置纵向文字: '班组长' -> '班组长' (3个字符，每字符一行)
22:55:30.470 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [setHeaderCellContent,312] - 设置纵向文字: '检验员' -> '检验员' (3个字符，每字符一行)
22:55:30.470 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第2行表头，共8列
22:55:30.471 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,172] - 表头处理完成，共处理2行
22:55:30.471 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processDataRow,462] - 处理普通文本内容
22:55:30.472 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processDataRow,448] - 处理混合内容，mathMLMap大小: 1
22:55:30.472 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [insertMixedContent,679] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
22:55:30.472 [http-nio-9550-exec-5] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
22:55:30.770 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processDataRow,462] - 处理普通文本内容
22:55:30.771 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processDataRow,462] - 处理普通文本内容
22:55:30.772 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processDataRow,462] - 处理普通文本内容
22:55:30.772 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processDataRow,462] - 处理普通文本内容
22:55:30.773 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processDataRow,462] - 处理普通文本内容
22:55:30.773 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [processDataRow,462] - 处理普通文本内容
22:55:30.781 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTable,146] - 表格创建完成
22:55:30.790 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 3009 bytes
22:55:30.794 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_225530.docx, 大小: 3009 bytes
23:29:01.785 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
23:29:01.791 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
23:29:08.874 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 904144 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
23:29:08.877 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
23:29:09.898 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
23:29:09.898 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:29:09.898 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
23:29:09.951 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:29:10.546 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
23:29:10.783 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
23:29:11.516 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
23:29:11.533 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.188 seconds (JVM running for 3.694)
23:29:17.158 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:29:50.745 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
23:29:50.746 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
23:29:50.746 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,519] - JSON请求转换完成，表头数量: 2, 数据行数量: 3, 合并单元格数量: 1
23:29:50.747 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,886] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
23:29:50.747 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,887] - 合并单元格数量: 1
23:29:50.927 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [setPageOrientation,817] - 已设置文档为横向纸张
23:29:50.976 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTableWithMerges,943] - 创建表格，总行数: 5, 总列数: 8
23:29:51.026 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1687] - 应用表头宽度配置: 列宽=[180, 150, 320, 100, 100, 30, 90, 90], 行高=[60, 40]
23:29:51.028 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1739] - 开始应用列宽配置: [180, 150, 320, 100, 100, 30, 90, 90]
23:29:51.037 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1751] - 列宽配置应用完成
23:29:51.083 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [insertMixedContent,682] - 插入混合内容，内容: 性能测试：__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><mi>C</mi><mo>&#x2062;</mo><mi>P</mi><mo>&#x2062;</mo><mi>U</mi></mrow><mo>=</mo><mrow><mn>95</mn><mi mathvariant="normal">%</mi></mrow></math>}
23:29:51.085 [http-nio-9550-exec-5] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
23:29:51.363 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTableWithMerges,979] - 应用表头合并单元格，数量: 5
23:29:51.363 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyMerges,1083] - 开始应用 5 个合并单元格，表头行数: 0
23:29:51.370 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTableWithMerges,987] - 应用数据行合并单元格，数量: 1，表头偏移: 2
23:29:51.371 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyMerges,1083] - 开始应用 1 个合并单元格，表头行数: 2
23:29:51.429 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,915] - 包含合并单元格的Word文档导出完成，文件大小: 3478 bytes
23:29:51.437 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_232951.docx, 大小: 3478 bytes
23:39:36.185 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
23:39:36.189 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
23:40:30.789 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 917491 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
23:40:30.792 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
23:40:31.731 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
23:40:31.732 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:40:31.732 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
23:40:31.774 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:40:32.300 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
23:40:32.548 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
23:40:33.198 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
23:40:33.216 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.837 seconds (JVM running for 3.254)
23:40:38.003 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:40:38.108 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
23:40:38.108 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
23:40:38.109 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,519] - JSON请求转换完成，表头数量: 2, 数据行数量: 3, 合并单元格数量: 1
23:40:38.110 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,886] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
23:40:38.110 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,887] - 合并单元格数量: 1
23:40:38.384 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,817] - 已设置文档为横向纸张
23:40:38.422 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,943] - 创建表格，总行数: 5, 总列数: 8
23:40:38.460 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1687] - 应用表头宽度配置: 列宽=[180, 150, 320, 100, 100, 30, 90, 90], 行高=[60, 40]
23:40:38.463 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1739] - 开始应用列宽配置: [180, 150, 320, 100, 100, 30, 90, 90]
23:40:38.468 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1751] - 列宽配置应用完成
23:40:38.513 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,682] - 插入混合内容，内容: 性能测试：__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><mi>C</mi><mo>&#x2062;</mo><mi>P</mi><mo>&#x2062;</mo><mi>U</mi></mrow><mo>=</mo><mrow><mn>95</mn><mi mathvariant="normal">%</mi></mrow></math>}
23:40:38.516 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
23:40:38.763 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,979] - 应用表头合并单元格，数量: 5
23:40:38.764 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1083] - 开始应用 5 个合并单元格，表头行数: 0
23:40:38.768 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,987] - 应用数据行合并单元格，数量: 1，表头偏移: 2
23:40:38.768 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1083] - 开始应用 1 个合并单元格，表头行数: 2
23:40:38.821 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,915] - 包含合并单元格的Word文档导出完成，文件大小: 3481 bytes
23:40:38.830 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_234038.docx, 大小: 3481 bytes
23:46:25.602 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
23:46:25.603 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
23:46:25.604 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,519] - JSON请求转换完成，表头数量: 2, 数据行数量: 3, 合并单元格数量: 1
23:46:25.604 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,886] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
23:46:25.604 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,887] - 合并单元格数量: 1
23:46:25.607 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,817] - 已设置文档为横向纸张
23:46:25.608 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableWithMerges,943] - 创建表格，总行数: 5, 总列数: 8
23:46:25.611 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1687] - 应用表头宽度配置: 列宽=[180, 150, 320, 100, 100, 30, 90, 90], 行高=[60, 40]
23:46:25.611 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1739] - 开始应用列宽配置: [180, 150, 320, 100, 100, 30, 90, 90]
23:46:25.612 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1751] - 列宽配置应用完成
23:46:25.626 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [insertMixedContent,682] - 插入混合内容，内容: 性能测试：__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><mi>C</mi><mo>&#x2062;</mo><mi>P</mi><mo>&#x2062;</mo><mi>U</mi></mrow><mo>=</mo><mrow><mn>95</mn><mi mathvariant="normal">%</mi></mrow></math>}
23:46:25.627 [http-nio-9550-exec-2] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
23:46:25.722 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableWithMerges,979] - 应用表头合并单元格，数量: 5
23:46:25.722 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerges,1083] - 开始应用 5 个合并单元格，表头行数: 0
23:46:25.723 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTableWithMerges,987] - 应用数据行合并单元格，数量: 1，表头偏移: 2
23:46:25.723 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [applyMerges,1083] - 开始应用 1 个合并单元格，表头行数: 2
23:46:25.731 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,915] - 包含合并单元格的Word文档导出完成，文件大小: 3482 bytes
23:46:25.734 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_234625.docx, 大小: 3482 bytes
23:46:48.968 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
23:46:48.971 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
23:46:54.060 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 925295 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
23:46:54.063 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
23:46:54.856 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
23:46:54.857 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:46:54.857 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
23:46:54.893 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:46:55.378 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
23:46:55.617 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
23:46:56.281 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
23:46:56.296 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.62 seconds (JVM running for 3.004)
23:46:59.862 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:46:59.954 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
23:46:59.955 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
23:46:59.957 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,519] - JSON请求转换完成，表头数量: 2, 数据行数量: 3, 合并单元格数量: 1
23:46:59.957 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,886] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
23:46:59.957 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,887] - 合并单元格数量: 1
23:47:00.258 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,817] - 已设置文档为横向纸张
23:47:00.298 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,943] - 创建表格，总行数: 5, 总列数: 8
23:47:00.356 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1692] - 应用表头宽度配置: 列宽=[180, 150, 320, 100, 100, 30, 90, 90], 行高=[60, 40]
23:47:00.358 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1744] - 开始应用列宽配置: [180, 150, 320, 100, 100, 30, 90, 90]
23:47:00.362 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1756] - 列宽配置应用完成
23:47:00.404 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,682] - 插入混合内容，内容: 性能测试：__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><mi>C</mi><mo>&#x2062;</mo><mi>P</mi><mo>&#x2062;</mo><mi>U</mi></mrow><mo>=</mo><mrow><mn>95</mn><mi mathvariant="normal">%</mi></mrow></math>}
23:47:00.406 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
23:47:00.662 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,979] - 应用表头合并单元格，数量: 5
23:47:00.662 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1088] - 开始应用 5 个合并单元格，表头行数: 0
23:47:00.667 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,987] - 应用数据行合并单元格，数量: 1，表头偏移: 2
23:47:00.668 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1088] - 开始应用 1 个合并单元格，表头行数: 2
23:47:00.718 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,915] - 包含合并单元格的Word文档导出完成，文件大小: 3448 bytes
23:47:00.730 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_234700.docx, 大小: 3448 bytes
23:48:48.430 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
23:48:48.432 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
23:48:54.353 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 927833 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
23:48:54.356 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
23:48:55.225 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
23:48:55.226 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
23:48:55.226 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
23:48:55.263 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
23:48:55.789 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
23:48:56.034 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
23:48:56.654 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
23:48:56.669 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.778 seconds (JVM running for 3.238)
23:49:03.468 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
23:49:03.567 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,73] - 接收到包含合并单元格的表格导出请求，标题: 检验记录表
23:49:03.568 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,74] - 合并单元格数量: 1
23:49:03.569 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [convertJsonRequestToTableRequest,519] - JSON请求转换完成，表头数量: 2, 数据行数量: 3, 合并单元格数量: 1
23:49:03.570 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,884] - 开始导出包含合并单元格的Word文档，表格标题: 检验记录表
23:49:03.570 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,885] - 合并单元格数量: 1
23:49:03.874 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,815] - 已设置文档为横向纸张
23:49:03.923 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,941] - 创建表格，总行数: 5, 总列数: 8
23:49:03.963 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyHeaderWidthConfig,1677] - 应用表头宽度配置: 列宽=[180, 150, 320, 100, 100, 30, 90, 90], 行高=[60, 40]
23:49:03.965 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1729] - 开始应用列宽配置: [180, 150, 320, 100, 100, 30, 90, 90]
23:49:03.969 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyColumnWidths,1741] - 列宽配置应用完成
23:49:04.011 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,680] - 插入混合内容，内容: 性能测试：__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><mi>C</mi><mo>&#x2062;</mo><mi>P</mi><mo>&#x2062;</mo><mi>U</mi></mrow><mo>=</mo><mrow><mn>95</mn><mi mathvariant="normal">%</mi></mrow></math>}
23:49:04.013 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
23:49:04.285 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,977] - 应用表头合并单元格，数量: 5
23:49:04.285 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1073] - 开始应用 5 个合并单元格，表头行数: 0
23:49:04.290 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTableWithMerges,985] - 应用数据行合并单元格，数量: 1，表头偏移: 2
23:49:04.290 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [applyMerges,1073] - 开始应用 1 个合并单元格，表头行数: 2
23:49:04.347 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWordWithMerges,913] - 包含合并单元格的Word文档导出完成，文件大小: 3448 bytes
23:49:04.355 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTableWithMerges,91] - 包含合并单元格的表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_234904.docx, 大小: 3448 bytes
