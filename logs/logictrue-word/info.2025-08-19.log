00:17:17.140 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 1219560 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
00:17:17.154 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:17:18.832 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
00:17:18.833 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:17:18.833 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:17:18.886 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:17:19.526 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:17:19.759 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:17:21.001 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
00:17:21.127 [main] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
00:17:21.130 [main] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
00:17:21.134 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9550"]
00:17:21.134 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
00:17:21.138 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9550"]
00:17:21.139 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9550"]
00:17:55.618 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 1220542 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
00:17:55.620 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:17:56.684 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
00:17:56.684 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:17:56.685 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:17:56.728 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:17:57.364 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:17:57.625 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:17:58.445 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
00:17:58.466 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.322 seconds (JVM running for 4.037)
00:18:17.267 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:21:54.137 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
00:21:54.139 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
00:21:58.297 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 1225727 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
00:21:58.299 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
00:21:59.308 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
00:21:59.308 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
00:21:59.309 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
00:21:59.352 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
00:21:59.961 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
00:22:00.223 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
00:22:00.949 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
00:22:00.970 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.127 seconds (JVM running for 3.795)
00:22:07.244 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
00:22:07.266 [http-nio-9550-exec-1] INFO  c.l.w.c.MathFormulaTestController - [downloadWordWithXSLT,357] - GET请求：导出Word文档（XSLT数学公式转换）
00:22:07.266 [http-nio-9550-exec-1] INFO  c.l.w.s.AdvancedWordExportService - [exportTableToWordWithXSLT,44] - 开始导出Word文档（XSLT数学公式转换）: XSLT转换测试文档
00:22:08.359 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,35] - 开始导出Word文档，表格标题: XSLT转换测试文档
00:22:08.564 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,495] - 已设置文档为横向纸张
00:22:08.612 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,108] - 创建表格: 3行 x 8列
00:22:24.873 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,142] - 表格创建完成
00:22:24.939 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,60] - Word文档导出完成，文件大小: 2757 bytes
00:22:24.951 [http-nio-9550-exec-1] INFO  c.l.w.c.MathFormulaTestController - [downloadWordWithXSLT,376] - Word文档导出成功（XSLT转换GET），文件大小: 2757 bytes
01:06:11.036 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
01:06:11.065 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
09:38:10.338 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 79249 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
09:38:10.341 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
09:38:11.315 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
09:38:11.316 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:38:11.316 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
09:38:11.360 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:38:11.960 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
09:38:12.209 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
09:38:12.984 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
09:38:13.003 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.152 seconds (JVM running for 3.807)
09:42:14.043 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:00:09.130 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 44512 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
13:00:09.134 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
13:00:09.917 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
13:00:09.918 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:00:09.918 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
13:00:09.959 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:00:10.496 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
13:00:10.747 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
13:00:11.352 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
13:00:11.365 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.605 seconds (JVM running for 2.97)
13:00:39.271 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:01:01.940 [http-nio-9550-exec-9] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
13:01:01.940 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检验记录表
13:01:02.118 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,528] - 已设置文档为横向纸张
13:01:02.158 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,83] - 接收到表头数据，共2行
13:01:02.159 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,86] - 第1行表头，共8列
13:01:02.159 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,90] -   列0: content='检查工序名称', rowspan=2, colspan=1
13:01:02.159 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,90] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
13:01:02.159 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,90] -   列2: content='实际检查结果', rowspan=2, colspan=1
13:01:02.159 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='完工', rowspan=1, colspan=2
13:01:02.159 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,90] -   列5: content='操作员', rowspan=2, colspan=1
13:01:02.159 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,90] -   列6: content='班组长', rowspan=2, colspan=1
13:01:02.159 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,90] -   列7: content='检验员', rowspan=2, colspan=1
13:01:02.160 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,86] - 第2行表头，共8列
13:01:02.160 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='月', rowspan=1, colspan=1
13:01:02.160 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,90] -   列4: content='日', rowspan=1, colspan=1
13:01:02.160 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,110] - 创建表格: 3行 x 8列
13:01:02.188 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,155] - 开始处理表头，共2行
13:01:02.189 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第1行表头，共8列
13:01:02.212 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第2行表头，共8列
13:01:02.214 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,170] - 表头处理完成，共处理2行
13:01:02.240 [http-nio-9550-exec-9] INFO  org.docx4j.XmlUtils - [<clinit>,196] - setProperty com.sun.org.apache.xerces.internal.jaxp.SAXParserFactoryImpl
13:01:02.240 [http-nio-9550-exec-9] INFO  org.docx4j.XmlUtils - [<clinit>,249] - setProperty com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderFactoryImpl
13:01:02.241 [http-nio-9550-exec-9] INFO  o.d.jaxb.Context - [<clinit>,87] - java.vendor=Oracle Corporation
13:01:02.241 [http-nio-9550-exec-9] INFO  o.d.jaxb.Context - [<clinit>,88] - java.version=1.8.0_181
13:01:02.242 [http-nio-9550-exec-9] INFO  o.d.jaxb.Context - [<clinit>,89] - java.vm.name=Java HotSpot(TM) 64-Bit Server VM
13:01:03.854 [http-nio-9550-exec-9] INFO  o.d.jaxb.Context - [<clinit>,173] - JAXB Reference Implementation is in use.
13:01:03.969 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,144] - 表格创建完成
13:01:04.034 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 2898 bytes
13:01:04.040 [http-nio-9550-exec-9] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250819_130104.docx, 大小: 2898 bytes
13:02:35.808 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
13:02:35.810 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
13:02:39.279 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 48154 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
13:02:39.281 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
13:02:39.979 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
13:02:39.980 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:02:39.980 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
13:02:40.028 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:02:40.455 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
13:02:40.657 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
13:02:41.181 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
13:02:41.195 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.222 seconds (JVM running for 2.547)
13:02:53.768 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:02:53.832 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
13:02:53.833 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,35] - 开始导出Word文档，表格标题: 检验记录表
13:02:54.053 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,492] - 已设置文档为横向纸张
13:02:54.085 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,81] - 接收到表头数据，共2行
13:02:54.085 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,84] - 第1行表头，共8列
13:02:54.085 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] -   列0: content='检查工序名称', rowspan=2, colspan=1
13:02:54.085 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
13:02:54.085 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] -   列2: content='实际检查结果', rowspan=2, colspan=1
13:02:54.086 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] -   列3: content='完工', rowspan=1, colspan=2
13:02:54.086 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] -   列5: content='操作员', rowspan=2, colspan=1
13:02:54.086 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] -   列6: content='班组长', rowspan=2, colspan=1
13:02:54.086 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] -   列7: content='检验员', rowspan=2, colspan=1
13:02:54.086 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,84] - 第2行表头，共8列
13:02:54.086 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] -   列3: content='月', rowspan=1, colspan=1
13:02:54.086 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] -   列4: content='日', rowspan=1, colspan=1
13:02:54.086 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,108] - 创建表格: 3行 x 8列
13:02:54.110 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,153] - 开始处理表头，共2行
13:02:54.111 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,159] - 处理第1行表头，共8列
13:02:54.131 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,159] - 处理第2行表头，共8列
13:02:54.133 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,168] - 表头处理完成，共处理2行
13:02:54.157 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,142] - 表格创建完成
13:02:54.211 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,60] - Word文档导出完成，文件大小: 2947 bytes
13:02:54.219 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250819_130254.docx, 大小: 2947 bytes
13:11:44.918 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 60040 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
13:11:44.922 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
13:11:45.901 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
13:11:45.902 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:11:45.902 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
13:11:45.964 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:11:46.520 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
13:11:46.772 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
13:11:47.390 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
13:11:47.504 [main] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
13:11:47.506 [main] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
13:11:47.508 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9550"]
13:11:47.508 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
13:11:47.510 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9550"]
13:11:47.511 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9550"]
13:12:04.051 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 60932 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
13:12:04.053 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
13:12:04.768 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
13:12:04.768 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:12:04.769 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
13:12:04.817 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:12:05.264 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
13:12:05.466 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
13:12:05.983 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
13:12:05.997 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.255 seconds (JVM running for 2.588)
13:12:12.392 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:12:12.404 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [testExport,105] - 开始测试Word导出...
13:12:12.405 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,39] - 开始导出Word文档，表格标题: 测试检验记录表
13:12:12.543 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,494] - 已设置文档为横向纸张
13:12:12.574 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,85] - 接收到表头数据，共2行
13:12:12.574 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] - 第1行表头，共8列
13:12:12.574 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列0: content='检查工序名称', rowspan=2, colspan=1
13:12:12.574 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
13:12:12.574 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列2: content='实际检查结果', rowspan=2, colspan=1
13:12:12.574 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列3: content='完工', rowspan=1, colspan=2
13:12:12.574 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列5: content='操作员', rowspan=2, colspan=1
13:12:12.575 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列6: content='班组长', rowspan=2, colspan=1
13:12:12.575 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列7: content='检验员', rowspan=2, colspan=1
13:12:12.575 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] - 第2行表头，共8列
13:12:12.575 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列3: content='月', rowspan=1, colspan=1
13:12:12.575 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列4: content='日', rowspan=1, colspan=1
13:12:12.575 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,112] - 创建表格: 5行 x 8列
13:12:12.695 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,157] - 开始处理表头，共2行
13:12:12.696 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第1行表头，共8列
13:12:12.720 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第2行表头，共8列
13:12:12.722 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,172] - 表头处理完成，共处理2行
13:12:12.743 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertMathMLToOMML,71] - OMML转换完成（自定义实现）: org.dom4j.tree.DefaultDocument@63cef2cb [Document: name null]
13:12:12.755 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertMathMLToOMML,71] - OMML转换完成（自定义实现）: org.dom4j.tree.DefaultDocument@3151ac96 [Document: name null]
13:12:12.764 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,146] - 表格创建完成
13:12:12.818 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,64] - Word文档导出完成，文件大小: 3084 bytes
13:12:12.828 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [testExport,122] - 测试导出成功，文件名: %E6%B5%8B%E8%AF%95%E5%AF%BC%E5%87%BA_20250819_131212.docx, 大小: 3084 bytes
13:12:16.622 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
13:12:16.622 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWord,39] - 开始导出Word文档，表格标题: 检验记录表
13:12:16.624 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,494] - 已设置文档为横向纸张
13:12:16.625 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,85] - 接收到表头数据，共2行
13:12:16.625 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,88] - 第1行表头，共8列
13:12:16.625 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,92] -   列0: content='检查工序名称', rowspan=2, colspan=1
13:12:16.626 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,92] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
13:12:16.626 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,92] -   列2: content='实际检查结果', rowspan=2, colspan=1
13:12:16.626 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,92] -   列3: content='完工', rowspan=1, colspan=2
13:12:16.626 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,92] -   列5: content='操作员', rowspan=2, colspan=1
13:12:16.626 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,92] -   列6: content='班组长', rowspan=2, colspan=1
13:12:16.626 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,92] -   列7: content='检验员', rowspan=2, colspan=1
13:12:16.626 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,88] - 第2行表头，共8列
13:12:16.626 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,92] -   列3: content='月', rowspan=1, colspan=1
13:12:16.626 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,92] -   列4: content='日', rowspan=1, colspan=1
13:12:16.626 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,112] - 创建表格: 3行 x 8列
13:12:16.628 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,157] - 开始处理表头，共2行
13:12:16.628 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第1行表头，共8列
13:12:16.632 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第2行表头，共8列
13:12:16.633 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,172] - 表头处理完成，共处理2行
13:12:16.637 [http-nio-9550-exec-2] INFO  c.l.w.s.MathMLToOMMLService - [convertMathMLToOMML,71] - OMML转换完成（自定义实现）: org.dom4j.tree.DefaultDocument@334e3288 [Document: name null]
13:12:16.639 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,146] - 表格创建完成
13:12:16.646 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWord,64] - Word文档导出完成，文件大小: 2893 bytes
13:12:16.649 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250819_131216.docx, 大小: 2893 bytes
13:13:52.695 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
13:13:52.697 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
13:13:56.572 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 63446 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
13:13:56.574 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
13:13:57.269 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
13:13:57.269 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:13:57.270 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
13:13:57.316 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:13:57.751 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
13:13:57.954 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
13:13:58.496 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
13:13:58.509 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.236 seconds (JVM running for 2.577)
13:14:04.273 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:14:04.339 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
13:14:04.340 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,39] - 开始导出Word文档，表格标题: 检验记录表
13:14:04.562 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,495] - 已设置文档为横向纸张
13:14:04.594 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,85] - 接收到表头数据，共2行
13:14:04.594 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] - 第1行表头，共8列
13:14:04.594 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列0: content='检查工序名称', rowspan=2, colspan=1
13:14:04.594 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
13:14:04.594 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列2: content='实际检查结果', rowspan=2, colspan=1
13:14:04.594 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列3: content='完工', rowspan=1, colspan=2
13:14:04.594 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列5: content='操作员', rowspan=2, colspan=1
13:14:04.595 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列6: content='班组长', rowspan=2, colspan=1
13:14:04.595 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列7: content='检验员', rowspan=2, colspan=1
13:14:04.595 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] - 第2行表头，共8列
13:14:04.595 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列3: content='月', rowspan=1, colspan=1
13:14:04.595 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列4: content='日', rowspan=1, colspan=1
13:14:04.595 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,112] - 创建表格: 3行 x 8列
13:14:04.619 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,157] - 开始处理表头，共2行
13:14:04.620 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第1行表头，共8列
13:14:04.639 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第2行表头，共8列
13:14:04.642 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,172] - 表头处理完成，共处理2行
13:14:04.661 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertMathMLToOMML,71] - OMML转换完成（自定义实现）: <?xml version="1.0" encoding="UTF-8"?>
<m:oMath xmlns:m="http://schemas.openxmlformats.org/officeDocument/2006/math"><m:rad><m:e><m:r><m:t>x</m:t></m:r></m:e></m:rad></m:oMath>
13:14:04.679 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,146] - 表格创建完成
13:14:04.734 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,64] - Word文档导出完成，文件大小: 2921 bytes
13:14:04.743 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250819_131404.docx, 大小: 2921 bytes
13:15:28.691 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
13:15:28.693 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
13:15:31.888 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 65661 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
13:15:31.890 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
13:15:32.632 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
13:15:32.633 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:15:32.633 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
13:15:32.678 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:15:33.091 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
13:15:33.290 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
13:15:33.822 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
13:15:33.836 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.255 seconds (JVM running for 2.58)
13:15:37.215 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:15:37.283 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
13:15:37.283 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,39] - 开始导出Word文档，表格标题: 检验记录表
13:15:37.507 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,495] - 已设置文档为横向纸张
13:15:37.542 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,85] - 接收到表头数据，共2行
13:15:37.542 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] - 第1行表头，共8列
13:15:37.543 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列0: content='检查工序名称', rowspan=2, colspan=1
13:15:37.543 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
13:15:37.543 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列2: content='实际检查结果', rowspan=2, colspan=1
13:15:37.543 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列3: content='完工', rowspan=1, colspan=2
13:15:37.543 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列5: content='操作员', rowspan=2, colspan=1
13:15:37.543 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列6: content='班组长', rowspan=2, colspan=1
13:15:37.543 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列7: content='检验员', rowspan=2, colspan=1
13:15:37.543 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] - 第2行表头，共8列
13:15:37.544 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列3: content='月', rowspan=1, colspan=1
13:15:37.544 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列4: content='日', rowspan=1, colspan=1
13:15:37.544 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,112] - 创建表格: 3行 x 8列
13:15:37.568 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,157] - 开始处理表头，共2行
13:15:37.568 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第1行表头，共8列
13:15:37.589 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第2行表头，共8列
13:15:37.591 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,172] - 表头处理完成，共处理2行
13:15:37.628 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,146] - 表格创建完成
13:15:37.682 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,64] - Word文档导出完成，文件大小: 2935 bytes
13:15:37.690 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250819_131537.docx, 大小: 2935 bytes
13:35:31.739 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
13:35:31.742 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
13:35:35.373 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 90408 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
13:35:35.376 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
13:35:36.041 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
13:35:36.042 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:35:36.042 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
13:35:36.082 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:35:36.500 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
13:35:36.703 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
13:35:37.206 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
13:35:37.220 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.153 seconds (JVM running for 2.511)
13:35:45.863 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:35:45.929 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
13:35:45.929 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,39] - 开始导出Word文档，表格标题: 检验记录表
13:35:46.179 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,476] - 已设置文档为横向纸张
13:35:46.211 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,85] - 接收到表头数据，共2行
13:35:46.212 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] - 第1行表头，共8列
13:35:46.212 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列0: content='检查工序名称', rowspan=2, colspan=1
13:35:46.212 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
13:35:46.212 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列2: content='实际检查结果', rowspan=2, colspan=1
13:35:46.212 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列3: content='完工', rowspan=1, colspan=2
13:35:46.212 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列5: content='操作员', rowspan=2, colspan=1
13:35:46.213 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列6: content='班组长', rowspan=2, colspan=1
13:35:46.213 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列7: content='检验员', rowspan=2, colspan=1
13:35:46.213 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] - 第2行表头，共8列
13:35:46.213 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列3: content='月', rowspan=1, colspan=1
13:35:46.213 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列4: content='日', rowspan=1, colspan=1
13:35:46.213 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,112] - 创建表格: 3行 x 8列
13:35:46.239 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,157] - 开始处理表头，共2行
13:35:46.239 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第1行表头，共8列
13:35:46.261 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第2行表头，共8列
13:35:46.263 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,172] - 表头处理完成，共处理2行
13:35:46.264 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,37] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/MML2OMML.XSL
13:35:46.307 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,146] - 表格创建完成
13:35:46.360 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,64] - Word文档导出完成，文件大小: 2894 bytes
13:35:46.368 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250819_133546.docx, 大小: 2894 bytes
13:37:45.235 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
13:37:45.237 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
13:37:48.214 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 93341 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
13:37:48.217 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
13:37:48.924 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
13:37:48.924 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:37:48.925 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
13:37:48.972 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:37:49.402 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
13:37:49.604 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
13:37:50.120 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
13:37:50.132 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.219 seconds (JVM running for 2.54)
13:37:53.478 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:37:53.544 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
13:37:53.545 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,39] - 开始导出Word文档，表格标题: 检验记录表
13:37:53.760 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,476] - 已设置文档为横向纸张
13:37:53.791 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,85] - 接收到表头数据，共2行
13:37:53.792 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] - 第1行表头，共8列
13:37:53.792 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列0: content='检查工序名称', rowspan=2, colspan=1
13:37:53.792 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
13:37:53.792 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列2: content='实际检查结果', rowspan=2, colspan=1
13:37:53.792 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列3: content='完工', rowspan=1, colspan=2
13:37:53.792 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列5: content='操作员', rowspan=2, colspan=1
13:37:53.792 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列6: content='班组长', rowspan=2, colspan=1
13:37:53.792 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列7: content='检验员', rowspan=2, colspan=1
13:37:53.792 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] - 第2行表头，共8列
13:37:53.793 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列3: content='月', rowspan=1, colspan=1
13:37:53.793 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列4: content='日', rowspan=1, colspan=1
13:37:53.793 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,112] - 创建表格: 3行 x 8列
13:37:53.816 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,157] - 开始处理表头，共2行
13:37:53.816 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第1行表头，共8列
13:37:53.836 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第2行表头，共8列
13:37:53.838 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,172] - 表头处理完成，共处理2行
13:37:53.839 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,46] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
13:37:53.977 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,146] - 表格创建完成
13:37:54.038 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,64] - Word文档导出完成，文件大小: 2923 bytes
13:37:54.049 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250819_133754.docx, 大小: 2923 bytes
13:38:22.517 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
13:38:22.519 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
13:38:28.299 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 94435 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
13:38:28.301 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
13:38:29.176 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
13:38:29.176 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:38:29.177 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
13:38:29.225 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:38:29.649 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
13:38:29.853 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
13:38:30.389 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
13:38:30.403 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.48 seconds (JVM running for 2.863)
13:38:36.491 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:38:36.557 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
13:38:36.557 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,39] - 开始导出Word文档，表格标题: 检验记录表
13:38:36.773 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,476] - 已设置文档为横向纸张
13:38:36.805 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,85] - 接收到表头数据，共2行
13:38:36.806 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] - 第1行表头，共8列
13:38:36.806 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列0: content='检查工序名称', rowspan=2, colspan=1
13:38:36.806 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
13:38:36.806 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列2: content='实际检查结果', rowspan=2, colspan=1
13:38:36.806 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列3: content='完工', rowspan=1, colspan=2
13:38:36.806 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列5: content='操作员', rowspan=2, colspan=1
13:38:36.807 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列6: content='班组长', rowspan=2, colspan=1
13:38:36.807 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列7: content='检验员', rowspan=2, colspan=1
13:38:36.807 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] - 第2行表头，共8列
13:38:36.807 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列3: content='月', rowspan=1, colspan=1
13:38:36.807 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列4: content='日', rowspan=1, colspan=1
13:38:36.807 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,112] - 创建表格: 3行 x 8列
13:38:36.830 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,157] - 开始处理表头，共2行
13:38:36.831 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第1行表头，共8列
13:38:36.852 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第2行表头，共8列
13:38:36.854 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,172] - 表头处理完成，共处理2行
13:38:36.855 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,46] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
13:38:36.977 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,146] - 表格创建完成
13:38:37.027 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,64] - Word文档导出完成，文件大小: 2923 bytes
13:38:37.037 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250819_133837.docx, 大小: 2923 bytes
13:39:21.236 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
13:39:21.238 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
13:39:24.570 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 95779 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
13:39:24.571 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
13:39:25.542 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
13:39:25.543 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:39:25.543 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
13:39:25.592 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:39:26.192 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
13:39:26.447 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
13:39:27.098 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
13:39:27.114 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.959 seconds (JVM running for 3.928)
13:39:37.256 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:39:37.344 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
13:39:37.344 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,39] - 开始导出Word文档，表格标题: 检验记录表
13:39:37.627 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,476] - 已设置文档为横向纸张
13:39:37.665 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,85] - 接收到表头数据，共2行
13:39:37.665 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] - 第1行表头，共8列
13:39:37.665 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列0: content='检查工序名称', rowspan=2, colspan=1
13:39:37.665 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
13:39:37.666 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列2: content='实际检查结果', rowspan=2, colspan=1
13:39:37.666 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列3: content='完工', rowspan=1, colspan=2
13:39:37.666 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列5: content='操作员', rowspan=2, colspan=1
13:39:37.666 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列6: content='班组长', rowspan=2, colspan=1
13:39:37.666 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列7: content='检验员', rowspan=2, colspan=1
13:39:37.666 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] - 第2行表头，共8列
13:39:37.666 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列3: content='月', rowspan=1, colspan=1
13:39:37.666 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列4: content='日', rowspan=1, colspan=1
13:39:37.667 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,112] - 创建表格: 3行 x 8列
13:39:37.696 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,157] - 开始处理表头，共2行
13:39:37.697 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第1行表头，共8列
13:39:37.722 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第2行表头，共8列
13:39:37.724 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,172] - 表头处理完成，共处理2行
13:39:37.726 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,46] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
13:43:08.766 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,146] - 表格创建完成
13:43:08.820 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,64] - Word文档导出完成，文件大小: 2923 bytes
13:43:08.829 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250819_134308.docx, 大小: 2923 bytes
13:46:54.461 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
13:46:54.463 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
13:46:56.854 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 105209 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
13:46:56.856 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
13:46:57.853 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
13:46:57.854 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:46:57.854 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
13:46:57.904 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:46:58.485 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
13:46:58.736 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
13:46:59.393 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
13:46:59.410 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.129 seconds (JVM running for 3.929)
13:47:08.468 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:47:08.558 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
13:47:08.559 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,39] - 开始导出Word文档，表格标题: 检验记录表
13:47:08.860 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,476] - 已设置文档为横向纸张
13:47:08.898 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,85] - 接收到表头数据，共2行
13:47:08.898 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] - 第1行表头，共8列
13:47:08.898 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列0: content='检查工序名称', rowspan=2, colspan=1
13:47:08.898 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
13:47:08.898 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列2: content='实际检查结果', rowspan=2, colspan=1
13:47:08.899 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列3: content='完工', rowspan=1, colspan=2
13:47:08.899 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列5: content='操作员', rowspan=2, colspan=1
13:47:08.899 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列6: content='班组长', rowspan=2, colspan=1
13:47:08.899 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列7: content='检验员', rowspan=2, colspan=1
13:47:08.899 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,88] - 第2行表头，共8列
13:47:08.899 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列3: content='月', rowspan=1, colspan=1
13:47:08.899 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,92] -   列4: content='日', rowspan=1, colspan=1
13:47:08.899 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,112] - 创建表格: 2行 x 8列
13:47:08.930 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,157] - 开始处理表头，共2行
13:47:08.931 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第1行表头，共8列
13:47:08.956 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第2行表头，共8列
13:47:08.959 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,172] - 表头处理完成，共处理2行
13:47:08.964 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,146] - 表格创建完成
13:47:09.033 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,64] - Word文档导出完成，文件大小: 2823 bytes
13:47:09.043 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250819_134709.docx, 大小: 2823 bytes
13:47:44.905 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
13:47:44.906 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWord,39] - 开始导出Word文档，表格标题: 检验记录表
13:47:44.908 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,476] - 已设置文档为横向纸张
13:47:44.908 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,85] - 接收到表头数据，共2行
13:47:44.909 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,88] - 第1行表头，共8列
13:47:44.909 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,92] -   列0: content='检查工序名称', rowspan=2, colspan=1
13:47:44.909 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,92] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
13:47:44.909 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,92] -   列2: content='实际检查结果', rowspan=2, colspan=1
13:47:44.909 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,92] -   列3: content='完工', rowspan=1, colspan=2
13:47:44.909 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,92] -   列5: content='操作员', rowspan=2, colspan=1
13:47:44.909 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,92] -   列6: content='班组长', rowspan=2, colspan=1
13:47:44.909 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,92] -   列7: content='检验员', rowspan=2, colspan=1
13:47:44.910 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,88] - 第2行表头，共8列
13:47:44.910 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,92] -   列3: content='月', rowspan=1, colspan=1
13:47:44.910 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,92] -   列4: content='日', rowspan=1, colspan=1
13:47:44.910 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,112] - 创建表格: 3行 x 8列
13:47:44.912 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,157] - 开始处理表头，共2行
13:47:44.912 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第1行表头，共8列
13:47:44.922 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第2行表头，共8列
13:47:44.924 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,172] - 表头处理完成，共处理2行
13:47:44.926 [http-nio-9550-exec-2] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,46] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
13:48:04.563 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,146] - 表格创建完成
13:48:04.573 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWord,64] - Word文档导出完成，文件大小: 2894 bytes
13:48:04.579 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250819_134804.docx, 大小: 2894 bytes
14:42:15.245 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
14:42:15.253 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
14:42:20.306 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 172591 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
14:42:20.307 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
14:42:21.291 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
14:42:21.292 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:42:21.292 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
14:42:21.345 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:42:21.944 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
14:42:22.217 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
14:42:22.880 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
14:42:22.897 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.081 seconds (JVM running for 3.817)
14:43:28.714 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:43:41.909 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
14:43:41.909 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportTableToWord,39] - 开始导出Word文档，表格标题: 检验记录表
14:43:42.087 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,454] - 已设置文档为横向纸张
14:43:42.128 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,85] - 接收到表头数据，共2行
14:43:42.129 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,88] - 第1行表头，共8列
14:43:42.129 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,92] -   列0: content='检查工序名称', rowspan=2, colspan=1
14:43:42.129 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,92] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
14:43:42.129 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,92] -   列2: content='实际检查结果', rowspan=2, colspan=1
14:43:42.129 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,92] -   列3: content='完工', rowspan=1, colspan=2
14:43:42.129 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,92] -   列5: content='操作员', rowspan=2, colspan=1
14:43:42.129 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,92] -   列6: content='班组长', rowspan=2, colspan=1
14:43:42.130 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,92] -   列7: content='检验员', rowspan=2, colspan=1
14:43:42.130 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,88] - 第2行表头，共8列
14:43:42.130 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,92] -   列3: content='月', rowspan=1, colspan=1
14:43:42.130 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,92] -   列4: content='日', rowspan=1, colspan=1
14:43:42.130 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,112] - 创建表格: 3行 x 8列
14:43:42.169 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,157] - 开始处理表头，共2行
14:43:42.169 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第1行表头，共8列
14:43:42.201 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,163] - 处理第2行表头，共8列
14:43:42.203 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,172] - 表头处理完成，共处理2行
14:43:42.204 [http-nio-9550-exec-7] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,46] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
14:43:42.393 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,146] - 表格创建完成
14:43:42.459 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportTableToWord,64] - Word文档导出完成，文件大小: 2894 bytes
14:43:42.466 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250819_144342.docx, 大小: 2894 bytes
14:46:09.959 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
14:46:09.962 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
14:46:13.869 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 177680 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
14:46:13.870 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
14:46:14.848 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
14:46:14.849 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:46:14.850 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
14:46:14.908 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:46:15.501 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
14:46:15.773 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
14:46:16.220 [main] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
14:46:16.223 [main] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
14:46:16.226 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
14:46:44.665 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 178544 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
14:46:44.666 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
14:46:45.534 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
14:46:45.535 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:46:45.535 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
14:46:45.584 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:46:46.174 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
14:46:46.430 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
14:46:47.119 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
14:46:47.135 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.886 seconds (JVM running for 3.498)
14:46:52.015 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:46:52.127 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
14:46:52.128 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,38] - 开始导出Word文档，表格标题: 检验记录表
14:46:52.461 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,453] - 已设置文档为横向纸张
14:46:52.501 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,84] - 接收到表头数据，共2行
14:46:52.502 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] - 第1行表头，共8列
14:46:52.502 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列0: content='检查工序名称', rowspan=2, colspan=1
14:46:52.502 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
14:46:52.502 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列2: content='实际检查结果', rowspan=2, colspan=1
14:46:52.502 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列3: content='完工', rowspan=1, colspan=2
14:46:52.502 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列5: content='操作员', rowspan=2, colspan=1
14:46:52.502 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列6: content='班组长', rowspan=2, colspan=1
14:46:52.502 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列7: content='检验员', rowspan=2, colspan=1
14:46:52.503 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] - 第2行表头，共8列
14:46:52.503 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列3: content='月', rowspan=1, colspan=1
14:46:52.503 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列4: content='日', rowspan=1, colspan=1
14:46:52.503 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,111] - 创建表格: 3行 x 8列
14:46:52.538 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,156] - 开始处理表头，共2行
14:46:52.538 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,162] - 处理第1行表头，共8列
14:46:52.565 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,162] - 处理第2行表头，共8列
14:46:52.567 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,171] - 表头处理完成，共处理2行
14:46:52.569 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,45] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
14:46:52.761 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,145] - 表格创建完成
14:46:52.820 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,63] - Word文档导出完成，文件大小: 2894 bytes
14:46:52.829 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250819_144652.docx, 大小: 2894 bytes
14:49:32.688 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
14:49:32.692 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
14:49:36.450 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 182295 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
14:49:36.451 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
14:49:37.305 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
14:49:37.305 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:49:37.306 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
14:49:37.356 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:49:37.916 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
14:49:38.167 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
14:49:38.869 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
14:49:38.885 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.861 seconds (JVM running for 3.514)
14:49:48.658 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:49:48.749 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
14:49:48.749 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,38] - 开始导出Word文档，表格标题: 检验记录表
14:49:49.038 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,453] - 已设置文档为横向纸张
14:49:49.075 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,84] - 接收到表头数据，共2行
14:49:49.075 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] - 第1行表头，共8列
14:49:49.075 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列0: content='检查工序名称', rowspan=2, colspan=1
14:49:49.075 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
14:49:49.075 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列2: content='实际检查结果', rowspan=2, colspan=1
14:49:49.075 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列3: content='完工', rowspan=1, colspan=2
14:49:49.076 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列5: content='操作员', rowspan=2, colspan=1
14:49:49.076 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列6: content='班组长', rowspan=2, colspan=1
14:49:49.076 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列7: content='检验员', rowspan=2, colspan=1
14:49:49.076 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] - 第2行表头，共8列
14:49:49.076 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列3: content='月', rowspan=1, colspan=1
14:49:49.076 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列4: content='日', rowspan=1, colspan=1
14:49:49.076 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,111] - 创建表格: 3行 x 8列
14:49:49.106 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,156] - 开始处理表头，共2行
14:49:49.106 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,162] - 处理第1行表头，共8列
14:49:49.132 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,162] - 处理第2行表头，共8列
14:49:49.135 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,171] - 表头处理完成，共处理2行
14:49:49.136 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMathFormula,405] - 插入数学公式，MathML: <math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>
14:49:49.138 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,45] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
14:49:49.327 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,145] - 表格创建完成
14:49:49.392 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,63] - Word文档导出完成，文件大小: 2894 bytes
14:49:49.402 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250819_144949.docx, 大小: 2894 bytes
14:51:19.885 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
14:51:19.885 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWord,38] - 开始导出Word文档，表格标题: 检验记录表
14:51:19.886 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,453] - 已设置文档为横向纸张
14:51:19.887 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,84] - 接收到表头数据，共2行
14:51:19.887 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] - 第1行表头，共8列
14:51:19.888 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,91] -   列0: content='检查工序名称', rowspan=2, colspan=1
14:51:19.888 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,91] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
14:51:19.888 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,91] -   列2: content='实际检查结果', rowspan=2, colspan=1
14:51:19.888 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,91] -   列3: content='完工', rowspan=1, colspan=2
14:51:19.888 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,91] -   列5: content='操作员', rowspan=2, colspan=1
14:51:19.888 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,91] -   列6: content='班组长', rowspan=2, colspan=1
14:51:19.888 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,91] -   列7: content='检验员', rowspan=2, colspan=1
14:51:19.888 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] - 第2行表头，共8列
14:51:19.888 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,91] -   列3: content='月', rowspan=1, colspan=1
14:51:19.888 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,91] -   列4: content='日', rowspan=1, colspan=1
14:51:19.889 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,111] - 创建表格: 3行 x 8列
14:51:19.890 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,156] - 开始处理表头，共2行
14:51:19.890 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,162] - 处理第1行表头，共8列
14:51:19.895 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,162] - 处理第2行表头，共8列
14:51:19.896 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,171] - 表头处理完成，共处理2行
14:51:19.897 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [insertMathFormula,405] - 插入数学公式，MathML: <math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup></mrow><mo>=</mo><msup><mi>r</mi><mn>2</mn></msup></math>
14:51:19.897 [http-nio-9550-exec-2] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,45] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
14:51:25.167 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,145] - 表格创建完成
14:51:25.173 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWord,63] - Word文档导出完成，文件大小: 3000 bytes
14:51:25.176 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250819_145125.docx, 大小: 3000 bytes
15:23:48.810 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 224874 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
15:23:48.812 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
15:23:49.565 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
15:23:49.566 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:23:49.566 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
15:23:49.610 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:23:50.055 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:23:50.274 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:23:50.852 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
15:23:50.967 [main] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
15:23:50.969 [main] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
15:23:50.971 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9550"]
15:23:50.971 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
15:23:50.973 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9550"]
15:23:50.974 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9550"]
15:24:02.333 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 225304 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
15:24:02.335 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
15:24:03.114 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
15:24:03.114 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:24:03.114 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
15:24:03.180 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:24:03.661 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:24:03.894 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:24:04.415 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
15:24:04.432 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.519 seconds (JVM running for 2.878)
15:25:14.729 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:25:14.797 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
15:25:14.798 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,38] - 开始导出Word文档，表格标题: 检验记录表
15:25:14.938 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,453] - 已设置文档为横向纸张
15:25:15.050 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,84] - 接收到表头数据，共2行
15:25:15.051 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] - 第1行表头，共8列
15:25:15.051 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列0: content='检查工序名称', rowspan=2, colspan=1
15:25:15.051 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
15:25:15.051 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列2: content='实际检查结果', rowspan=2, colspan=1
15:25:15.051 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列3: content='完工', rowspan=1, colspan=2
15:25:15.051 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列5: content='操作员', rowspan=2, colspan=1
15:25:15.052 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列6: content='班组长', rowspan=2, colspan=1
15:25:15.052 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列7: content='检验员', rowspan=2, colspan=1
15:25:15.052 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] - 第2行表头，共8列
15:25:15.052 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列3: content='月', rowspan=1, colspan=1
15:25:15.052 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列4: content='日', rowspan=1, colspan=1
15:25:15.052 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,111] - 创建表格: 3行 x 8列
15:25:15.077 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,156] - 开始处理表头，共2行
15:25:15.077 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,162] - 处理第1行表头，共8列
15:25:15.099 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,162] - 处理第2行表头，共8列
15:25:15.101 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,171] - 表头处理完成，共处理2行
15:25:15.102 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMathFormula,405] - 插入数学公式，MathML: <math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><mo>$</mo></mrow><mo>&#x2062;</mo><mrow><munderover><mo>&#x2211;</mo><mrow><mi>i</mi><mo>=</mo><mn>1</mn></mrow><mi>n</mi></munderover><mrow><msub><mi>x</mi><mi>i</mi></msub><mo>&#x2062;</mo><mrow><mo>$</mo></mrow><mo>&#x2062;</mo><mi mathvariant="normal">&#x4E0D;</mi><mo>&#x2062;</mo><mi mathvariant="normal">&#x9519;</mi><mo>&#x2062;</mo><mi mathvariant="normal">&#x7684;</mi></mrow></mrow></math>
15:25:15.103 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,45] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
15:25:15.267 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMathFormula,405] - 插入数学公式，MathML: <math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>
15:25:15.267 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,45] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
15:25:15.291 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMathFormula,405] - 插入数学公式，MathML: <math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup></mrow><mo>=</mo><msup><mi>r</mi><mn>2</mn></msup></math>
15:25:15.291 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,45] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
15:25:15.313 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMathFormula,405] - 插入数学公式，MathML: <math xmlns="http://www.w3.org/1998/Math/MathML"><mi mathvariant="normal">&#x8FD9;</mi><mo>&#x2062;</mo><mi mathvariant="normal">&#x662F;</mi><mo>&#x2062;</mo><mi mathvariant="normal">&#x5206;</mi><mo>&#x2062;</mo><mi mathvariant="normal">&#x6570;</mi><mo>&#x2062;</mo><mrow><mo>$</mo></mrow><mo>&#x2062;</mo><mfrac><mi>a</mi><mi>b</mi></mfrac><mo>&#x2062;</mo><mrow><mo>$</mo></mrow></math>
15:25:15.313 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,45] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
15:25:15.342 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,145] - 表格创建完成
15:25:15.388 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,63] - Word文档导出完成，文件大小: 3256 bytes
15:25:15.396 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250819_152515.docx, 大小: 3256 bytes
15:26:03.571 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
15:26:03.571 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWord,38] - 开始导出Word文档，表格标题: 检验记录表
15:26:03.574 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,453] - 已设置文档为横向纸张
15:26:03.575 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,84] - 接收到表头数据，共2行
15:26:03.575 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] - 第1行表头，共8列
15:26:03.576 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,91] -   列0: content='检查工序名称', rowspan=2, colspan=1
15:26:03.576 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,91] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
15:26:03.576 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,91] -   列2: content='实际检查结果', rowspan=2, colspan=1
15:26:03.576 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,91] -   列3: content='完工', rowspan=1, colspan=2
15:26:03.576 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,91] -   列5: content='操作员', rowspan=2, colspan=1
15:26:03.576 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,91] -   列6: content='班组长', rowspan=2, colspan=1
15:26:03.576 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,91] -   列7: content='检验员', rowspan=2, colspan=1
15:26:03.576 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,87] - 第2行表头，共8列
15:26:03.577 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,91] -   列3: content='月', rowspan=1, colspan=1
15:26:03.577 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,91] -   列4: content='日', rowspan=1, colspan=1
15:26:03.577 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,111] - 创建表格: 3行 x 8列
15:26:03.579 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,156] - 开始处理表头，共2行
15:26:03.580 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,162] - 处理第1行表头，共8列
15:26:03.585 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,162] - 处理第2行表头，共8列
15:26:03.586 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,171] - 表头处理完成，共处理2行
15:26:03.587 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [insertMathFormula,405] - 插入数学公式，MathML: <math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><mo>$</mo></mrow><mo>&#x2062;</mo><mrow><munderover><mo>&#x2211;</mo><mrow><mi>i</mi><mo>=</mo><mn>1</mn></mrow><mi>n</mi></munderover><mrow><msub><mi>x</mi><mi>i</mi></msub><mo>&#x2062;</mo><mrow><mo>$</mo></mrow><mo>&#x2062;</mo><mi mathvariant="normal">&#x4E0D;</mi><mo>&#x2062;</mo><mi mathvariant="normal">&#x9519;</mi><mo>&#x2062;</mo><mi mathvariant="normal">&#x7684;</mi></mrow></mrow></math>
15:26:03.587 [http-nio-9550-exec-2] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,45] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
15:26:03.609 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [insertMathFormula,405] - 插入数学公式，MathML: <math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>
15:26:03.610 [http-nio-9550-exec-2] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,45] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
15:26:03.623 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [insertMathFormula,405] - 插入数学公式，MathML: <math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup></mrow><mo>=</mo><msup><mi>r</mi><mn>2</mn></msup></math>
15:26:03.624 [http-nio-9550-exec-2] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,45] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
15:26:03.638 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [insertMathFormula,405] - 插入数学公式，MathML: <math xmlns="http://www.w3.org/1998/Math/MathML"><mi mathvariant="normal">&#x8FD9;</mi><mo>&#x2062;</mo><mi mathvariant="normal">&#x662F;</mi><mo>&#x2062;</mo><mi mathvariant="normal">&#x5206;</mi><mo>&#x2062;</mo><mi mathvariant="normal">&#x6570;</mi><mo>&#x2062;</mo><mrow><mo>$</mo></mrow><mo>&#x2062;</mo><mfrac><mi>a</mi><mi>b</mi></mfrac><mo>&#x2062;</mo><mrow><mo>$</mo></mrow></math>
15:26:03.639 [http-nio-9550-exec-2] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,45] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
15:26:03.656 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,145] - 表格创建完成
15:26:03.663 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWord,63] - Word文档导出完成，文件大小: 3256 bytes
15:26:03.666 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250819_152603.docx, 大小: 3256 bytes
15:32:40.596 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
15:32:40.598 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
15:32:44.420 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 236562 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
15:32:44.421 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
15:32:45.342 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
15:32:45.343 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:32:45.343 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
15:32:45.394 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:32:45.990 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:32:46.242 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:32:46.976 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
15:32:47.000 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.99 seconds (JVM running for 4.211)
15:33:05.677 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:33:05.772 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
15:33:05.772 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,38] - 开始导出Word文档，表格标题: 检验记录表
15:33:06.095 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,453] - 已设置文档为横向纸张
15:33:06.132 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,84] - 接收到表头数据，共2行
15:33:06.133 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] - 第1行表头，共8列
15:33:06.133 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列0: content='检查工序名称', rowspan=2, colspan=1
15:33:06.133 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
15:33:06.133 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列2: content='实际检查结果', rowspan=2, colspan=1
15:33:06.133 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列3: content='完工', rowspan=1, colspan=2
15:33:06.133 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列5: content='操作员', rowspan=2, colspan=1
15:33:06.133 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列6: content='班组长', rowspan=2, colspan=1
15:33:06.133 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列7: content='检验员', rowspan=2, colspan=1
15:33:06.134 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,87] - 第2行表头，共8列
15:33:06.134 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列3: content='月', rowspan=1, colspan=1
15:33:06.134 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,91] -   列4: content='日', rowspan=1, colspan=1
15:33:06.134 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,111] - 创建表格: 3行 x 8列
15:33:06.163 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,156] - 开始处理表头，共2行
15:33:06.164 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,162] - 处理第1行表头，共8列
15:33:06.189 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,162] - 处理第2行表头，共8列
15:33:06.191 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,171] - 表头处理完成，共处理2行
15:33:57.093 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMathFormula,405] - 插入数学公式，MathML: <math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><mo>$</mo></mrow><mo>&#x2062;</mo><mrow><munderover><mo>&#x2211;</mo><mrow><mi>i</mi><mo>=</mo><mn>1</mn></mrow><mi>n</mi></munderover><mrow><msub><mi>x</mi><mi>i</mi></msub><mo>&#x2062;</mo><mrow><mo>$</mo></mrow><mo>&#x2062;</mo><mi mathvariant="normal">&#x4E0D;</mi><mo>&#x2062;</mo><mi mathvariant="normal">&#x9519;</mi><mo>&#x2062;</mo><mi mathvariant="normal">&#x7684;</mi></mrow></mrow></math>
15:42:08.802 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,45] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
15:42:11.560 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMathFormula,405] - 插入数学公式，MathML: <math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>
15:42:11.561 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,45] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
15:42:11.596 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMathFormula,405] - 插入数学公式，MathML: <math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup></mrow><mo>=</mo><msup><mi>r</mi><mn>2</mn></msup></math>
15:42:11.596 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,45] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
15:42:11.622 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMathFormula,405] - 插入数学公式，MathML: <math xmlns="http://www.w3.org/1998/Math/MathML"><mi mathvariant="normal">&#x8FD9;</mi><mo>&#x2062;</mo><mi mathvariant="normal">&#x662F;</mi><mo>&#x2062;</mo><mi mathvariant="normal">&#x5206;</mi><mo>&#x2062;</mo><mi mathvariant="normal">&#x6570;</mi><mo>&#x2062;</mo><mrow><mo>$</mo></mrow><mo>&#x2062;</mo><mfrac><mi>a</mi><mi>b</mi></mfrac><mo>&#x2062;</mo><mrow><mo>$</mo></mrow></math>
15:42:11.622 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,45] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
15:42:11.659 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,145] - 表格创建完成
15:42:11.716 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,63] - Word文档导出完成，文件大小: 3256 bytes
15:42:11.726 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250819_154211.docx, 大小: 3256 bytes
15:44:40.922 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
15:44:40.924 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
15:44:44.689 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 251533 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
15:44:44.690 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
15:44:45.559 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
15:44:45.560 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:44:45.560 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
15:44:45.610 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:44:46.231 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
15:44:46.475 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
15:44:47.116 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
15:44:47.132 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.861 seconds (JVM running for 3.558)
15:44:53.472 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:44:53.558 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
15:44:53.558 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,36] - 开始导出Word文档，表格标题: 检验记录表
15:44:53.866 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,446] - 已设置文档为横向纸张
15:44:53.914 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,82] - 接收到表头数据，共2行
15:44:53.914 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,85] - 第1行表头，共8列
15:44:53.914 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,89] -   列0: content='检查工序名称', rowspan=2, colspan=1
15:44:53.914 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,89] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
15:44:53.914 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,89] -   列2: content='实际检查结果', rowspan=2, colspan=1
15:44:53.914 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,89] -   列3: content='完工', rowspan=1, colspan=2
15:44:53.915 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,89] -   列5: content='操作员', rowspan=2, colspan=1
15:44:53.915 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,89] -   列6: content='班组长', rowspan=2, colspan=1
15:44:53.915 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,89] -   列7: content='检验员', rowspan=2, colspan=1
15:44:53.915 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,85] - 第2行表头，共8列
15:44:53.915 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,89] -   列3: content='月', rowspan=1, colspan=1
15:44:53.915 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,89] -   列4: content='日', rowspan=1, colspan=1
15:44:53.915 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,109] - 创建表格: 3行 x 8列
15:44:53.946 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,154] - 开始处理表头，共2行
15:44:53.946 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,160] - 处理第1行表头，共8列
15:44:53.971 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,160] - 处理第2行表头，共8列
15:44:53.973 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,169] - 表头处理完成，共处理2行
15:44:53.973 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMathFormula,398] - 插入数学公式，MathML: <math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><mo>$</mo></mrow><mo>&#x2062;</mo><mrow><munderover><mo>&#x2211;</mo><mrow><mi>i</mi><mo>=</mo><mn>1</mn></mrow><mi>n</mi></munderover><mrow><msub><mi>x</mi><mi>i</mi></msub><mo>&#x2062;</mo><mrow><mo>$</mo></mrow><mo>&#x2062;</mo><mi mathvariant="normal">&#x4E0D;</mi><mo>&#x2062;</mo><mi mathvariant="normal">&#x9519;</mi><mo>&#x2062;</mo><mi mathvariant="normal">&#x7684;</mi></mrow></mrow></math>
15:44:58.683 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,45] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
15:44:58.890 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMathFormula,398] - 插入数学公式，MathML: <math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>
15:44:58.890 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,45] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
15:44:58.923 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMathFormula,398] - 插入数学公式，MathML: <math xmlns="http://www.w3.org/1998/Math/MathML"><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><msup><mi>y</mi><mn>2</mn></msup></mrow><mo>=</mo><msup><mi>r</mi><mn>2</mn></msup></math>
15:44:58.923 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,45] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
15:44:58.950 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMathFormula,398] - 插入数学公式，MathML: <math xmlns="http://www.w3.org/1998/Math/MathML"><mi mathvariant="normal">&#x8FD9;</mi><mo>&#x2062;</mo><mi mathvariant="normal">&#x662F;</mi><mo>&#x2062;</mo><mi mathvariant="normal">&#x5206;</mi><mo>&#x2062;</mo><mi mathvariant="normal">&#x6570;</mi><mo>&#x2062;</mo><mrow><mo>$</mo></mrow><mo>&#x2062;</mo><mfrac><mi>a</mi><mi>b</mi></mfrac><mo>&#x2062;</mo><mrow><mo>$</mo></mrow></math>
15:44:58.950 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,45] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
15:44:58.991 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,143] - 表格创建完成
15:44:59.044 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,61] - Word文档导出完成，文件大小: 3250 bytes
15:44:59.053 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250819_154459.docx, 大小: 3250 bytes
16:32:22.717 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
16:32:22.725 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
16:32:47.703 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 311577 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
16:32:47.704 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
16:32:48.614 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
16:32:48.615 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:32:48.615 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
16:32:48.662 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:32:49.223 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
16:32:49.507 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
16:32:50.140 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
16:32:50.156 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.871 seconds (JVM running for 3.526)
16:48:29.037 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
16:48:29.039 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
16:48:32.863 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 331314 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
16:48:32.864 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
16:48:33.749 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
16:48:33.750 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:48:33.750 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
16:48:33.796 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:48:34.323 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
16:48:34.593 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
16:48:35.223 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
16:48:35.239 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.824 seconds (JVM running for 3.481)
16:48:43.312 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:50:38.132 [http-nio-9550-exec-9] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
16:50:38.133 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检验记录表
16:50:38.319 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [setPageOrientation,532] - 已设置文档为横向纸张
16:50:38.367 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,83] - 接收到表头数据，共2行
16:50:38.368 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,86] - 第1行表头，共8列
16:50:38.369 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,90] -   列0: content='检查工序名称', rowspan=2, colspan=1
16:50:38.369 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,90] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
16:50:38.370 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,90] -   列2: content='实际检查结果', rowspan=2, colspan=1
16:50:38.370 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='完工', rowspan=1, colspan=2
16:50:38.370 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,90] -   列5: content='操作员', rowspan=2, colspan=1
16:50:38.371 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,90] -   列6: content='班组长', rowspan=2, colspan=1
16:50:38.371 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,90] -   列7: content='检验员', rowspan=2, colspan=1
16:50:38.371 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,86] - 第2行表头，共8列
16:50:38.372 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='月', rowspan=1, colspan=1
16:50:38.372 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,90] -   列4: content='日', rowspan=1, colspan=1
16:50:38.372 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,110] - 创建表格: 3行 x 8列
16:50:38.420 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,155] - 开始处理表头，共2行
16:50:38.421 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第1行表头，共8列
16:50:38.447 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第2行表头，共8列
16:50:38.449 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,170] - 表头处理完成，共处理2行
16:50:38.450 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [insertMixedContent,433] - 插入混合内容，内容: 你好__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
16:50:38.451 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [insertMixedContent,433] - 插入混合内容，内容: __MATH_FORMULA_0__好好__MATH_FORMULA_1__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>, __MATH_FORMULA_1__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
16:50:38.462 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [createTable,144] - 表格创建完成
16:50:38.541 [http-nio-9550-exec-9] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 2878 bytes
16:50:38.548 [http-nio-9550-exec-9] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250819_165038.docx, 大小: 2878 bytes
16:52:17.931 [http-nio-9550-exec-10] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
16:52:17.932 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检验记录表
16:52:17.933 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [setPageOrientation,532] - 已设置文档为横向纸张
16:52:17.934 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,83] - 接收到表头数据，共2行
16:52:17.934 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,86] - 第1行表头，共8列
16:52:17.934 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列0: content='检查工序名称', rowspan=2, colspan=1
16:52:17.934 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
16:52:17.935 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列2: content='实际检查结果', rowspan=2, colspan=1
16:52:17.935 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='完工', rowspan=1, colspan=2
16:52:17.935 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列5: content='操作员', rowspan=2, colspan=1
16:52:17.935 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列6: content='班组长', rowspan=2, colspan=1
16:52:17.935 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列7: content='检验员', rowspan=2, colspan=1
16:52:17.935 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,86] - 第2行表头，共8列
16:52:17.935 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='月', rowspan=1, colspan=1
16:52:17.935 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,90] -   列4: content='日', rowspan=1, colspan=1
16:52:17.935 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,110] - 创建表格: 3行 x 8列
16:52:17.937 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,155] - 开始处理表头，共2行
16:52:17.937 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第1行表头，共8列
16:52:17.942 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第2行表头，共8列
16:52:17.943 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,170] - 表头处理完成，共处理2行
16:52:17.944 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [insertMixedContent,433] - 插入混合内容，内容: 你好__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
16:52:17.944 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [insertMixedContent,433] - 插入混合内容，内容: __MATH_FORMULA_0__好好__MATH_FORMULA_1__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>, __MATH_FORMULA_1__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
16:52:17.945 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [insertMixedContent,433] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
16:52:17.946 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [createTable,144] - 表格创建完成
16:52:17.952 [http-nio-9550-exec-10] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 2881 bytes
16:52:17.955 [http-nio-9550-exec-10] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250819_165217.docx, 大小: 2881 bytes
17:02:02.022 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
17:02:02.024 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
17:02:06.553 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 348287 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
17:02:06.555 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
17:02:07.537 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
17:02:07.537 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:02:07.537 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
17:02:07.586 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:02:08.255 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
17:02:08.545 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
17:02:09.246 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
17:02:09.273 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.172 seconds (JVM running for 3.871)
17:04:13.281 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:04:13.389 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
17:04:13.390 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检验记录表
17:04:13.719 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,585] - 已设置文档为横向纸张
17:04:13.761 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,83] - 接收到表头数据，共2行
17:04:13.761 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,86] - 第1行表头，共8列
17:04:13.761 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列0: content='检查工序名称', rowspan=2, colspan=1
17:04:13.762 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
17:04:13.762 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列2: content='实际检查结果', rowspan=2, colspan=1
17:04:13.762 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='完工', rowspan=1, colspan=2
17:04:13.762 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列5: content='操作员', rowspan=2, colspan=1
17:04:13.762 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列6: content='班组长', rowspan=2, colspan=1
17:04:13.762 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列7: content='检验员', rowspan=2, colspan=1
17:04:13.762 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,86] - 第2行表头，共8列
17:04:13.762 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='月', rowspan=1, colspan=1
17:04:13.762 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列4: content='日', rowspan=1, colspan=1
17:04:13.763 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,110] - 创建表格: 3行 x 8列
17:04:13.793 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,155] - 开始处理表头，共2行
17:04:13.794 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第1行表头，共8列
17:04:13.819 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第2行表头，共8列
17:04:13.821 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,170] - 表头处理完成，共处理2行
17:04:13.821 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,354] - 处理普通文本内容
17:04:13.822 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,354] - 处理普通文本内容
17:04:13.822 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,337] - 处理混合内容，mathMLMap大小: 1
17:04:13.823 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,440] - 插入混合内容，内容: 你好__MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:04:13.826 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
17:04:14.103 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,337] - 处理混合内容，mathMLMap大小: 2
17:04:14.103 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,440] - 插入混合内容，内容: __MATH_FORMULA_0__好好__MATH_FORMULA_1__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>, __MATH_FORMULA_1__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:04:14.103 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
17:04:14.188 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
17:04:14.281 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,337] - 处理混合内容，mathMLMap大小: 1
17:04:14.282 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,440] - 插入混合内容，内容: __MATH_FORMULA_0__, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:04:14.282 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
17:04:14.354 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,354] - 处理普通文本内容
17:04:14.356 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,354] - 处理普通文本内容
17:04:14.358 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,354] - 处理普通文本内容
17:04:14.367 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,144] - 表格创建完成
17:04:14.422 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 3008 bytes
17:04:14.433 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250819_170414.docx, 大小: 3008 bytes
17:25:18.693 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
17:25:18.693 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检验记录表
17:25:18.695 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,585] - 已设置文档为横向纸张
17:25:18.697 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,83] - 接收到表头数据，共2行
17:25:18.697 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,86] - 第1行表头，共8列
17:25:18.697 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,90] -   列0: content='检查工序名称', rowspan=2, colspan=1
17:25:18.697 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,90] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
17:25:18.698 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,90] -   列2: content='实际检查结果', rowspan=2, colspan=1
17:25:18.698 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='完工', rowspan=1, colspan=2
17:25:18.698 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,90] -   列5: content='操作员', rowspan=2, colspan=1
17:25:18.698 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,90] -   列6: content='班组长', rowspan=2, colspan=1
17:25:18.698 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,90] -   列7: content='检验员', rowspan=2, colspan=1
17:25:18.698 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,86] - 第2行表头，共8列
17:25:18.699 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='月', rowspan=1, colspan=1
17:25:18.699 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,90] -   列4: content='日', rowspan=1, colspan=1
17:25:18.699 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,110] - 创建表格: 3行 x 8列
17:25:18.702 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,155] - 开始处理表头，共2行
17:25:18.703 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第1行表头，共8列
17:25:18.711 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第2行表头，共8列
17:25:18.713 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,170] - 表头处理完成，共处理2行
17:25:18.714 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processDataRow,354] - 处理普通文本内容
17:25:18.715 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processDataRow,354] - 处理普通文本内容
17:25:18.716 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processDataRow,337] - 处理混合内容，mathMLMap大小: 1
17:25:18.716 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [insertMixedContent,440] - 插入混合内容，内容: 你好
__MATH_FORMULA_0__
不错, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:25:18.752 [http-nio-9550-exec-6] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
17:25:18.828 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processDataRow,354] - 处理普通文本内容
17:25:18.829 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processDataRow,354] - 处理普通文本内容
17:25:18.830 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processDataRow,354] - 处理普通文本内容
17:25:18.831 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processDataRow,354] - 处理普通文本内容
17:25:18.831 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [processDataRow,354] - 处理普通文本内容
17:25:18.833 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTable,144] - 表格创建完成
17:25:18.839 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 3015 bytes
17:25:18.842 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250819_172518.docx, 大小: 3015 bytes
17:30:07.684 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
17:30:07.687 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
17:30:13.487 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 383549 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
17:30:13.489 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
17:30:14.601 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
17:30:14.601 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:30:14.602 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
17:30:14.658 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:30:15.328 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
17:30:15.680 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
17:30:16.481 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
17:30:16.501 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.782 seconds (JVM running for 4.617)
17:30:24.118 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:30:24.215 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
17:30:24.216 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检验记录表
17:30:24.534 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,569] - 已设置文档为横向纸张
17:30:24.577 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,83] - 接收到表头数据，共2行
17:30:24.577 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,86] - 第1行表头，共8列
17:30:24.577 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列0: content='检查工序名称', rowspan=2, colspan=1
17:30:24.577 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
17:30:24.577 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列2: content='实际检查结果', rowspan=2, colspan=1
17:30:24.577 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='完工', rowspan=1, colspan=2
17:30:24.578 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列5: content='操作员', rowspan=2, colspan=1
17:30:24.578 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列6: content='班组长', rowspan=2, colspan=1
17:30:24.578 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列7: content='检验员', rowspan=2, colspan=1
17:30:24.578 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,86] - 第2行表头，共8列
17:30:24.578 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='月', rowspan=1, colspan=1
17:30:24.578 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列4: content='日', rowspan=1, colspan=1
17:30:24.578 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,110] - 创建表格: 3行 x 8列
17:30:24.611 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,155] - 开始处理表头，共2行
17:30:24.612 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第1行表头，共8列
17:30:24.640 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第2行表头，共8列
17:30:24.643 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,170] - 表头处理完成，共处理2行
17:30:24.643 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
17:30:24.644 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
17:30:24.645 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,337] - 处理混合内容，mathMLMap大小: 1
17:30:24.645 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,434] - 插入混合内容，内容: 你好
__MATH_FORMULA_0__
不错, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:30:24.647 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
17:30:24.942 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
17:30:24.943 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
17:30:24.944 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
17:30:24.946 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
17:30:24.947 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
17:30:24.961 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,144] - 表格创建完成
17:30:25.042 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 2999 bytes
17:30:25.060 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250819_173025.docx, 大小: 2999 bytes
17:30:54.912 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
17:30:54.913 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检验记录表
17:30:54.914 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,569] - 已设置文档为横向纸张
17:30:54.916 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,83] - 接收到表头数据，共2行
17:30:54.917 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,86] - 第1行表头，共8列
17:30:54.917 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列0: content='检查工序名称', rowspan=2, colspan=1
17:30:54.917 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
17:30:54.917 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列2: content='实际检查结果', rowspan=2, colspan=1
17:30:54.917 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='完工', rowspan=1, colspan=2
17:30:54.917 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列5: content='操作员', rowspan=2, colspan=1
17:30:54.917 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列6: content='班组长', rowspan=2, colspan=1
17:30:54.917 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列7: content='检验员', rowspan=2, colspan=1
17:30:54.917 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,86] - 第2行表头，共8列
17:30:54.918 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='月', rowspan=1, colspan=1
17:30:54.918 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列4: content='日', rowspan=1, colspan=1
17:30:54.918 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,110] - 创建表格: 3行 x 8列
17:30:54.920 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,155] - 开始处理表头，共2行
17:30:54.920 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第1行表头，共8列
17:30:54.930 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第2行表头，共8列
17:30:54.932 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,170] - 表头处理完成，共处理2行
17:30:54.933 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
17:30:54.933 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
17:30:54.934 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,337] - 处理混合内容，mathMLMap大小: 1
17:30:54.934 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [insertMixedContent,434] - 插入混合内容，内容: 你好
__MATH_FORMULA_0__
不错, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
17:30:54.934 [http-nio-9550-exec-2] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
17:30:55.030 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
17:30:55.031 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
17:30:55.031 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
17:30:55.031 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
17:30:55.031 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
17:30:55.033 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,144] - 表格创建完成
17:30:55.039 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 2999 bytes
17:30:55.043 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250819_173055.docx, 大小: 2999 bytes
