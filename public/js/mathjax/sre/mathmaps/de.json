{"de/messages/alphabets.min": {"kind": "alphabets", "locale": "de", "messages": {"latinSmall": ["a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z"], "latinCap": ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"], "greekSmall": ["nabla", "alpha", "beta", "gamma", "delta", "epsilon", "zeta", "eta", "theta", "iota", "kappa", "lambda", "my", "ny", "xi", "omikron", "pi", "rho", "abschließendes sigma", "sigma", "tau", "ypsilon", "phi", "chi", "psi", "omega", "partielle Ableitung", "epsilon", "theta", "kappa", "phi", "rho", "pi"], "greekCap": ["Alpha", "Beta", "Gamma", "Delta", "Epsilon", "Zeta", "Eta", "Theta", "Iota", "Kappa", "Lambda", "My", "Ny", "Xi", "Omikron", "Pi", "Rho", "Theta", "Sigma", "Tau", "Ypsilon", "Phi", "<PERSON>", "Psi", "Omega"], "capPrefix": {"default": "großes"}, "smallPrefix": {"default": ""}, "digitPrefix": {"default": "s"}}}, "de/messages/messages.min": {"kind": "messages", "locale": "de", "messages": {"MS": {"START": "<PERSON><PERSON><PERSON>", "FRAC_V": "Bru<PERSON>", "FRAC_B": "Bru<PERSON>", "FRAC_S": "Bru<PERSON>", "END": "<PERSON><PERSON>", "FRAC_OVER": "durch", "TWICE": "Twice", "NEST_FRAC": "geschachtelt", "ENDFRAC": "<PERSON><PERSON>", "SUPER": "hoch", "SUB": "Index", "SUP": "hoch", "SUPERSCRIPT": "hoch", "SUBSCRIPT": "Index", "BASELINE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BASE": "<PERSON><PERSON><PERSON>", "NESTED": "geschachtelte", "NEST_ROOT": "geschachtelte", "STARTROOT": "<PERSON><PERSON><PERSON>", "ENDROOT": "<PERSON><PERSON>", "ROOTINDEX": "Wurzelexponent", "ROOT": "<PERSON><PERSON><PERSON>", "INDEX": "Exponent", "UNDER": "Unter", "UNDERSCRIPT": "Unterschrift", "OVER": "<PERSON><PERSON>", "OVERSCRIPT": "Überschrift", "ENDSCRIPTS": ""}, "MSroots": {"2": "Quadrat", "3": "Kubik"}, "font": {"bold": "fettes", "bold-fraktur": "fettes Fraktur", "bold-italic": "fettkurs<PERSON>", "bold-script": "fettes Schreibschrift", "caligraphic": "kalligrafisches", "caligraphic-bold": "fettes kalligrafisches", "double-struck": ["<PERSON> <PERSON>rich", "germanPostfix"], "double-struck-italic": ["kursiv mit Doppelstrich", "germanPostfix"], "fraktur": "Fraktur", "fullwidth": "vollbreites", "italic": "kurs<PERSON>", "monospace": "nichtproportionales", "normal": "normales", "oldstyle": "antiquiertes", "oldstyle-bold": "antiquiertes fettes", "script": "Schreibschrift", "sans-serif": "serifenloses", "sans-serif-italic": "serifenloses kurs<PERSON>", "sans-serif-bold": "serifenloses fettes", "sans-serif-bold-italic": "serifenloses fettkursives", "unknown": "unbekannt"}, "embellish": {"super": "hoch", "sub": "Index", "circled": "eingekreistes", "parenthesized": "eingeklammertes", "period": ["<PERSON><PERSON>", "germanPostfix"], "negative-circled": "schwarz eingekreistes", "double-circled": "doppelt eingekreis<PERSON>", "circled-sans-serif": "eingekreistes serifenloses", "negative-circled-sans-serif": "schwarz eingekreistes serifenloses", "comma": ["<PERSON><PERSON>", "germanPostfix"], "squared": "<PERSON><PERSON><PERSON><PERSON>", "negative-squared": "schwarz umrahmtes"}, "role": {"addition": "Addition", "multiplication": "Multiplikation", "subtraction": "Subtraktion", "division": "Division", "equality": "Gleichung", "inequality": "Ungleichung", "element": "Element", "arrow": "Pfeil", "determinant": "Determinante", "rowvector": "Zeilenvektor", "binomial": "Binomialkoeffizient", "squarematrix": "quadratische Matrize", "multiline": "mehrzeiligem Ausdruck", "matrix": "Matrize", "vector": "Vektor", "cases": "Fallunterscheidung", "table": "<PERSON><PERSON><PERSON>", "unknown": "unbekannt"}, "enclose": {"longdiv": "langer Bru<PERSON><PERSON>h", "actuarial": "Bilanzsumme", "radical": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "box": "rechteckige Umrandung", "roundedbox": "abgerundete rechteckige Umrandung", "circle": "kreisähnliche Umrandung", "left": "senkrechte Linie links", "right": "senkrechte Linie rechts", "top": "waagerechte Linie ober<PERSON>b", "bottom": "waagerechte Lin<PERSON> un<PERSON>b", "updiagonalstrike": "durchgestrichen", "downdiagonalstrike": "durchgestrichen", "verticalstrike": "senkrecht durchgestrichen", "horizontalstrike": "durchgestrichen", "madruwb": "arabisches Fakultätssymbol", "updiagonalarrow": "<PERSON><PERSON><PERSON> von links unten nach rechts oben", "phasorangle": "phasor angle", "unknown": "langer Bru<PERSON><PERSON>h"}, "navigate": {"COLLAPSIBLE": "kollabierbar", "EXPANDABLE": "ausfaltbar", "LEVEL": "Niveau"}, "regexp": {"TEXT": "a-zA-ZäöüÄÖÜß", "NUMBER": "((\\d{1,3})(?=(.| ))((.| )\\d{3})*(\\,\\d+)?)|^\\d*\\,\\d+|^\\d+", "DECIMAL_MARK": ",", "DIGIT_GROUP": "\\.", "JOINER_SUBSUPER": " ", "JOINER_FRAC": " "}, "unitTimes": ""}}, "de/messages/numbers.min": {"kind": "numbers", "locale": "de", "messages": {"zero": "null", "ones": ["", "eins", "zwei", "drei", "vier", "f<PERSON>nf", "sechs", "sieben", "acht", "neun", "zehn", "elf", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "vierzehn", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "tens": ["", "", "zwanzig", "d<PERSON><PERSON><PERSON><PERSON>", "vierzig", "fünfzig", "sechzig", "sieb<PERSON>", "a<PERSON>zig", "<PERSON><PERSON><PERSON>"], "large": ["", "tausend", "million", "milliarde", "billion", "billiarde", "trillion", "trilliard", "quadrillion", "quadrilliard", "quintillion", "quintillia<PERSON>", "sextillion", "sextillia<PERSON>"], "vulgarSep": " ", "numSep": ""}}, "de/si/prefixes.min": [{"Y": "<PERSON><PERSON>", "Z": "<PERSON><PERSON>", "E": "Exa", "P": "<PERSON><PERSON>", "T": "<PERSON><PERSON>", "G": "Giga", "M": "Mega", "k": "<PERSON><PERSON>", "h": "Hekto", "da": "<PERSON><PERSON>", "d": "<PERSON><PERSON>", "c": "<PERSON><PERSON>", "m": "<PERSON><PERSON>", "µ": "<PERSON><PERSON><PERSON>", "μ": "<PERSON><PERSON><PERSON>", "n": "<PERSON><PERSON>", "p": "<PERSON><PERSON>", "f": "<PERSON><PERSON><PERSON>", "a": "Atto", "z": "Zepto", "y": "<PERSON><PERSON><PERSON>"}], "de/functions/algebra.min": [{"locale": "de"}, {"key": "deg", "mappings": {"default": {"default": "Grad"}}}, {"key": "det", "mappings": {"default": {"default": "Determinante"}}}, {"key": "dim", "mappings": {"default": {"default": "Dimension"}}}, {"key": "hom", "mappings": {"default": {"default": "Homomorphismus"}, "mathspeak": {"default": "hom"}, "clearspeak": {"default": "hom"}}}, {"key": "ker", "mappings": {"default": {"default": "<PERSON>"}}}, {"key": "Tr", "mappings": {"default": {"default": "Spur"}}}], "de/functions/elementary.min": [{"locale": "de"}, {"key": "log", "mappings": {"default": {"default": "Logarith<PERSON>"}}}, {"key": "ln", "mappings": {"default": {"default": "natürlicher Logarithmus"}, "clearspeak": {"default": "l n", "Log_LnAsNaturalLog": "natürlicher Logarithmus"}}}, {"key": "lg", "mappings": {"default": {"default": "Logarithmus zur Basis 10"}}}, {"key": "exp", "mappings": {"default": {"default": "Exponent"}, "mathspeak": {"default": "exp"}, "clearspeak": {"default": "exp"}}}, {"key": "gcd", "mappings": {"default": {"default": "gr<PERSON><PERSON><PERSON> gemeinsamer <PERSON>"}, "mathspeak": {"default": "ggt"}, "clearspeak": {"default": "ggt"}}, "names": ["ggt", "ggT"]}, {"key": "lcm", "mappings": {"default": {"default": "kleinstes gemeinsames Vielfaches"}, "mathspeak": {"default": "kgv"}, "clearspeak": {"default": "kgv"}}, "names": ["kgv", "kgV"]}, {"key": "arg", "mappings": {"default": {"default": "Argument"}}}, {"key": "im", "mappings": {"default": {"default": "der Imaginärteil der komplexen Zahl"}}}, {"key": "re", "mappings": {"default": {"default": "der Realteil der komplexen Zahl"}}}, {"key": "mod", "mappings": {"default": {"default": "modulo"}}}, {"key": "inf", "mappings": {"default": {"default": "Infimum"}}}, {"key": "lim", "mappings": {"default": {"default": "Limes"}}}, {"key": "liminf", "mappings": {"default": {"default": "Limes inferior"}}}, {"key": "limsup", "mappings": {"default": {"default": "Limes superior"}}}, {"key": "max", "mappings": {"default": {"default": "Maximum"}}}, {"key": "min", "mappings": {"default": {"default": "Minimum"}}}, {"key": "sup", "mappings": {"default": {"default": "Supremum"}}}, {"key": "<PERSON><PERSON><PERSON>", "mappings": {"default": {"default": "Colimes"}}}, {"key": "proj<PERSON>", "mappings": {"default": {"default": "projektiver Limes"}}}, {"key": "Pr", "mappings": {"default": {"default": "Wahrscheinlichkeit"}}}], "de/functions/hyperbolic.min": [{"locale": "de"}, {"key": "cosh", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> hyperbolicus"}}}, {"key": "coth", "mappings": {"default": {"default": "Kotangens hyperbolicus"}}}, {"key": "csch", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> hyperbolicus"}}}, {"key": "sech", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> hyperbolic<PERSON>"}}}, {"key": "sinh", "mappings": {"default": {"default": "Sinus hyperbolicus"}}}, {"key": "tanh", "mappings": {"default": {"default": "<PERSON><PERSON> hyperbolicus"}}}, {"key": "arcosh", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> hyperbolicus"}}}, {"key": "arcoth", "mappings": {"default": {"default": "Areakotangens hyperbolicus"}}}, {"key": "<PERSON><PERSON>", "mappings": {"default": {"default": "Areakosekans hyperbolicus"}}}, {"key": "arsech", "mappings": {"default": {"default": "Areas<PERSON><PERSON> hyperbolicus"}}}, {"key": "a<PERSON><PERSON><PERSON>", "mappings": {"default": {"default": "<PERSON><PERSON> hyperbolicus"}}}, {"key": "artanh", "mappings": {"default": {"default": "Areatangens hyperbolicus "}}}], "de/functions/trigonometry.min": [{"locale": "de"}, {"key": "cos", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "cot", "mappings": {"default": {"default": "Kotangens"}}}, {"key": "csc", "mappings": {"default": {"default": "Kosekan<PERSON>"}}}, {"key": "sec", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "sin", "mappings": {"default": {"default": "Sinus"}}}, {"key": "tan", "mappings": {"default": {"default": "Tangens"}}}, {"key": "arccos", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}, "clearspeak": {"Trig_TrigInverse": "<PERSON><PERSON> <PERSON><PERSON><PERSON>"}}}, {"key": "<PERSON><PERSON>", "mappings": {"default": {"default": "Arkuskotangens"}, "clearspeak": {"Trig_TrigInverse": "<PERSON>r <PERSON>"}}}, {"key": "arccsc", "mappings": {"default": {"default": "Arkuskosekans"}, "clearspeak": {"Trig_TrigInverse": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "arcsec", "mappings": {"default": {"default": "Arkussekans"}, "clearspeak": {"Trig_TrigInverse": "<PERSON><PERSON> <PERSON><PERSON><PERSON>"}}}, {"key": "arcsin", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}, "clearspeak": {"Trig_TrigInverse": "<PERSON><PERSON>"}}}, {"key": "arctan", "mappings": {"default": {"default": "<PERSON>ust<PERSON><PERSON>"}, "clearspeak": {"Trig_TrigInverse": "inverser <PERSON><PERSON>"}}}], "de/symbols/digits_rest.min": [{"locale": "de"}, {"key": "00B2", "mappings": {"default": {"default": "Quadrat"}, "clearspeak": {"Exponent_Exponent": "mit Exponent 2", "Exponent_OrdinalPower": "zur zweiten Potenz"}}}, {"key": "00B3", "mappings": {"default": {"default": "Kubik"}, "clearspeak": {"Exponent_Exponent": "mit Exponent 3", "Exponent_OrdinalPower": "zur dritten Potenz"}}}, {"key": "00BC", "mappings": {"default": {"default": "ein Viertel"}}}, {"key": "00BD", "mappings": {"default": {"default": "ein <PERSON>"}}}, {"key": "00BE", "mappings": {"default": {"default": "dre<PERSON>"}}}, {"key": "2150", "mappings": {"default": {"default": "ein siebtel"}}}, {"key": "2151", "mappings": {"default": {"default": "ein neuntel"}}}, {"key": "2152", "mappings": {"default": {"default": "ein zehntel"}}}, {"key": "2153", "mappings": {"default": {"default": "ein drittel"}}}, {"key": "2154", "mappings": {"default": {"default": "zwei drittel"}}}, {"key": "2155", "mappings": {"default": {"default": "ein fünftel"}}}, {"key": "2156", "mappings": {"default": {"default": "zwei fünftel"}}}, {"key": "2157", "mappings": {"default": {"default": "drei fün<PERSON>el"}}}, {"key": "2158", "mappings": {"default": {"default": "vier fünftel"}}}, {"key": "2159", "mappings": {"default": {"default": "ein sechstel"}}}, {"key": "215A", "mappings": {"default": {"default": "f<PERSON><PERSON><PERSON>"}}}, {"key": "215B", "mappings": {"default": {"default": "ein achtel"}}}, {"key": "215C", "mappings": {"default": {"default": "drei achtel"}}}, {"key": "215D", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "215E", "mappings": {"default": {"default": "sieben achtel"}}}, {"key": "215F", "mappings": {"default": {"default": "Bru<PERSON>zä<PERSON> eins"}}}, {"key": "2189", "mappings": {"default": {"default": "null drittel"}}}, {"key": "3248", "mappings": {"default": {"default": "eingekreiste Zehn auf schwarzem Quadrat"}}}, {"key": "3249", "mappings": {"default": {"default": "eingekreiste Zwanzig auf schwarzem Quadrat"}}}, {"key": "324A", "mappings": {"default": {"default": "eingekreiste Dreißig auf schwarzem Quadrat"}}}, {"key": "324B", "mappings": {"default": {"default": "eingekreiste Vierzig auf schwarzem Quadrat"}}}, {"key": "324C", "mappings": {"default": {"default": "eingekreiste Fünfzig auf schwarzem Quadrat"}}}, {"key": "324D", "mappings": {"default": {"default": "eingekreiste Sechzig auf schwarzem Quadrat"}}}, {"key": "324E", "mappings": {"default": {"default": "eingekreiste Siebzig auf schwarzem Quadrat"}}}, {"key": "324F", "mappings": {"default": {"default": "eingekreiste Achtzig auf schwarzem Quadrat"}}}], "de/symbols/greek-rest.min": [{"locale": "de"}, {"key": "0394", "mappings": {"clearspeak": {"default": "<PERSON><PERSON><PERSON>", "TriangleSymbol_Delta": "großes Delta"}}}], "de/symbols/greek-scripts.min": [{"locale": "de"}, {"key": "1D26", "mappings": {"default": {"default": "Kapitälchen Gamma"}}}, {"key": "1D27", "mappings": {"default": {"default": "Kapitälchen Lamda"}}}, {"key": "1D28", "mappings": {"default": {"default": "Kapitälchen Pi"}}}, {"key": "1D29", "mappings": {"default": {"default": "Kapitälchen Rho"}}}, {"key": "1D2A", "mappings": {"default": {"default": "Kapitälchen Psi"}}}, {"key": "1D5E", "mappings": {"default": {"default": "hochgestelltes gamma"}}}, {"key": "1D60", "mappings": {"default": {"default": "hochgestelltes phi"}}}, {"key": "1D66", "mappings": {"default": {"default": "tiefgestelltes beta"}}}, {"key": "1D67", "mappings": {"default": {"default": "tiefgestelltes gamma"}}}, {"key": "1D68", "mappings": {"default": {"default": "tiefgestelltes rho"}}}, {"key": "1D69", "mappings": {"default": {"default": "tiefgestelltes phi"}}}, {"key": "1D6A", "mappings": {"default": {"default": "tiefgestelltes chi"}}}], "de/symbols/greek-symbols.min": [{"locale": "de"}, {"key": "03D0", "mappings": {"default": {"default": "beta"}}}, {"key": "03D7", "mappings": {"default": {"default": "kai"}}}, {"key": "03F6", "mappings": {"default": {"default": "umgekehrtes epsilon"}}}, {"key": "1D7CA", "mappings": {"default": {"default": "fettes großes Digamma"}}}, {"key": "1D7CB", "mappings": {"default": {"default": "fettes digamma"}}}], "de/symbols/hebrew_letters.min": [{"locale": "de"}, {"key": "2135", "mappings": {"default": {"default": "Alef-Symbol"}}}, {"key": "2136", "mappings": {"default": {"default": "Bet-Symbol"}}}, {"key": "2137", "mappings": {"default": {"default": "Gimel-Symbol"}}}, {"key": "2138", "mappings": {"default": {"default": "Dalet-Symbol"}}}], "de/symbols/latin-lower-double-accent.min": [{"locale": "de"}, {"key": "01D6", "mappings": {"default": {"default": "u mit Diärese und Makron"}}}, {"key": "01D8", "mappings": {"default": {"default": "u mit Diärese und Akut"}}}, {"key": "01DA", "mappings": {"default": {"default": "u mit Diärese und Caron"}}}, {"key": "01DC", "mappings": {"default": {"default": "u mit Diärese und Grab"}}}, {"key": "01DF", "mappings": {"default": {"default": "a mit Diärese und Makron"}}}, {"key": "01E1", "mappings": {"default": {"default": "a mit Dot Above und Makron"}}}, {"key": "01ED", "mappings": {"default": {"default": "o mit Ogonek und Makron"}}}, {"key": "01FB", "mappings": {"default": {"default": "a mit Ring oben und akut"}}}, {"key": "022B", "mappings": {"default": {"default": "o mit Diärese und Makron"}}}, {"key": "022D", "mappings": {"default": {"default": "o mit Tilde und Makron"}}}, {"key": "0231", "mappings": {"default": {"default": "o mit <PERSON>t oben und Makron"}}}, {"key": "1E09", "mappings": {"default": {"default": "c mit <PERSON><PERSON><PERSON> und Akut"}}}, {"key": "1E15", "mappings": {"default": {"default": "e mit Makron und Grab"}}}, {"key": "1E17", "mappings": {"default": {"default": "e mit Makron und Akut"}}}, {"key": "1E1D", "mappings": {"default": {"default": "e mit Cedilla und Breve"}}}, {"key": "1E2F", "mappings": {"default": {"default": "i mit Diärese und Akut"}}}, {"key": "1E39", "mappings": {"default": {"default": "l mit <PERSON>t unten und Makron"}}}, {"key": "1E4D", "mappings": {"default": {"default": "o mit Tilde und Akut"}}}, {"key": "1E4F", "mappings": {"default": {"default": "o mit Tilde und Diärese"}}}, {"key": "1E51", "mappings": {"default": {"default": "o mit Mak<PERSON> und Grab"}}}, {"key": "1E53", "mappings": {"default": {"default": "o mit Makron und Akut"}}}, {"key": "1E5D", "mappings": {"default": {"default": "r mit <PERSON>t unten und Makron"}}}, {"key": "1E65", "mappings": {"default": {"default": "s mit Aku<PERSON> und Punkt oben"}}}, {"key": "1E67", "mappings": {"default": {"default": "s mit <PERSON><PERSON> und Punkt oben"}}}, {"key": "1E69", "mappings": {"default": {"default": "s mit <PERSON>t unten und <PERSON>t oben"}}}, {"key": "1E79", "mappings": {"default": {"default": "u mit Tilde und Akut"}}}, {"key": "1E7B", "mappings": {"default": {"default": "u mit Makron und Diärese"}}}, {"key": "1EA5", "mappings": {"default": {"default": "a mit Zirkumflex und Akut"}}}, {"key": "1EA7", "mappings": {"default": {"default": "a mit Zirkumflex und Grab"}}}, {"key": "1EA9", "mappings": {"default": {"default": "a mit Zirkumflex und Haken oben"}}}, {"key": "1EAB", "mappings": {"default": {"default": "a mit Zirkumflex und Tilde"}}}, {"key": "1EAD", "mappings": {"default": {"default": "a mit Zirkumflex und Punkt darunter"}}}, {"key": "1EAF", "mappings": {"default": {"default": "a mit Breve und Akut"}}}, {"key": "1EB1", "mappings": {"default": {"default": "a mit Breve und Grab"}}}, {"key": "1EB3", "mappings": {"default": {"default": "a mit Breve und Haken oben"}}}, {"key": "1EB5", "mappings": {"default": {"default": "a mit Breve und Tilde"}}}, {"key": "1EB7", "mappings": {"default": {"default": "a mit Breve und Dot Below"}}}, {"key": "1EBF", "mappings": {"default": {"default": "e mit Zirkumflex und Akut"}}}, {"key": "1EC1", "mappings": {"default": {"default": "e mit Zirkumflex und Grab"}}}, {"key": "1EC3", "mappings": {"default": {"default": "e mit Zirkumflex und Haken oben"}}}, {"key": "1EC5", "mappings": {"default": {"default": "e mit Zirkumflex und Tilde"}}}, {"key": "1EC7", "mappings": {"default": {"default": "e mit Zirkumflex und <PERSON>t darunter"}}}, {"key": "1ED1", "mappings": {"default": {"default": "o mit Zirkumflex und Akut"}}}, {"key": "1ED3", "mappings": {"default": {"default": "o mit Zirkumflex und Grab"}}}, {"key": "1ED5", "mappings": {"default": {"default": "o mit Zirkumflex und Haken oben"}}}, {"key": "1ED7", "mappings": {"default": {"default": "o mit Zirkumflex und Tilde"}}}, {"key": "1ED9", "mappings": {"default": {"default": "o mit Zirkumflex und <PERSON>run<PERSON>"}}}, {"key": "1EDB", "mappings": {"default": {"default": "o mit Horn und Akut"}}}, {"key": "1EDD", "mappings": {"default": {"default": "o mit Horn und Grab"}}}, {"key": "1EDF", "mappings": {"default": {"default": "o mit Horn und Haken oben"}}}, {"key": "1EE1", "mappings": {"default": {"default": "o mit Horn und Tilde"}}}, {"key": "1EE3", "mappings": {"default": {"default": "o mit Horn und Punkt"}}}, {"key": "1EE9", "mappings": {"default": {"default": "u mit Horn und Akut"}}}, {"key": "1EEB", "mappings": {"default": {"default": "u mit Horn und Grab"}}}, {"key": "1EED", "mappings": {"default": {"default": "u mit Horn und Haken oben"}}}, {"key": "1EEF", "mappings": {"default": {"default": "u mit Horn und Tilde"}}}, {"key": "1EF1", "mappings": {"default": {"default": "u mit Horn und Punkt"}}}], "de/symbols/latin-lower-phonetic.min": [{"locale": "de"}, {"key": "00F8", "mappings": {"default": {"default": "o mit <PERSON>"}}}, {"key": "0111", "mappings": {"default": {"default": "d mit <PERSON><PERSON>"}}}, {"key": "0127", "mappings": {"default": {"default": "h mit <PERSON>"}}}, {"key": "0142", "mappings": {"default": {"default": "l <PERSON>"}}}, {"key": "0167", "mappings": {"default": {"default": "t mit <PERSON>"}}}, {"key": "0180", "mappings": {"default": {"default": "b <PERSON>"}}}, {"key": "019B", "mappings": {"default": {"default": "lambda mit Strich"}}}, {"key": "01B6", "mappings": {"default": {"default": "z mit <PERSON>"}}}, {"key": "01BE", "mappings": {"default": {"default": "umgekehrter Glottalanschlag mit Strich"}}}, {"key": "01E5", "mappings": {"default": {"default": "g mit <PERSON>"}}}, {"key": "01FF", "mappings": {"default": {"default": "o mit Strich und Akut"}}}, {"key": "023C", "mappings": {"default": {"default": "c <PERSON>"}}}, {"key": "0247", "mappings": {"default": {"default": "e mit <PERSON>"}}}, {"key": "0249", "mappings": {"default": {"default": "j <PERSON>"}}}, {"key": "024D", "mappings": {"default": {"default": "r <PERSON>"}}}, {"key": "024F", "mappings": {"default": {"default": "y mit <PERSON>"}}}, {"key": "025F", "mappings": {"default": {"default": "punktloses j mit <PERSON>rich"}}}, {"key": "0268", "mappings": {"default": {"default": "i mit Strich"}}}, {"key": "0284", "mappings": {"default": {"default": "punktloses j mit Strich und Haken"}}}, {"key": "02A1", "mappings": {"default": {"default": "Glottalanschlag mit Strich"}}}, {"key": "02A2", "mappings": {"default": {"default": "umgekehrter Glottalanschlag mit Strich"}}}, {"key": "1D13", "mappings": {"default": {"default": "liegendes O mit Strich"}}}, {"key": "1D7C", "mappings": {"default": {"default": "iota mit Strich"}}}, {"key": "1D7D", "mappings": {"default": {"default": "p <PERSON>"}}}, {"key": "1D7F", "mappings": {"default": {"default": "upsilon mit Strich"}}}, {"key": "1E9C", "mappings": {"default": {"default": "lang mit diagonalem Anschlag"}}}, {"key": "1E9D", "mappings": {"default": {"default": "lang S mit hohem Hub"}}}, {"key": "018D", "mappings": {"default": {"default": "wurde <PERSON> gedreht"}}}, {"key": "1E9B", "mappings": {"default": {"default": "lang mit <PERSON>t oben"}}}, {"key": "1E9F", "mappings": {"default": {"default": "delta"}}}, {"key": "0138", "mappings": {"default": {"default": "kra"}}}, {"key": "017F", "mappings": {"default": {"default": "lang S"}}}, {"key": "0183", "mappings": {"default": {"default": "b mit Oberle<PERSON><PERSON>"}}}, {"key": "0185", "mappings": {"default": {"default": "ton sechs"}}}, {"key": "0188", "mappings": {"default": {"default": "c mit <PERSON>"}}}, {"key": "018C", "mappings": {"default": {"default": "d mit Oberleiste"}}}, {"key": "0192", "mappings": {"default": {"default": "f mit <PERSON>ken"}}}, {"key": "0195", "mappings": {"default": {"default": "hv"}}}, {"key": "0199", "mappings": {"default": {"default": "k mit <PERSON>ken"}}}, {"key": "019A", "mappings": {"default": {"default": "l <PERSON>"}}}, {"key": "019E", "mappings": {"default": {"default": "n mit langem rechtem Bein"}}}, {"key": "01A1", "mappings": {"default": {"default": "o mit Horn"}}}, {"key": "01A3", "mappings": {"default": {"default": "oi"}}}, {"key": "01A5", "mappings": {"default": {"default": "p mit <PERSON><PERSON>"}}}, {"key": "01A8", "mappings": {"default": {"default": "ton zwei"}}}, {"key": "01AA", "mappings": {"default": {"default": "umgedrehte esh Schleife"}}}, {"key": "01AB", "mappings": {"default": {"default": "t mit palatal <PERSON>"}}}, {"key": "01AD", "mappings": {"default": {"default": "t mit <PERSON>ken"}}}, {"key": "01B0", "mappings": {"default": {"default": "u mit Horn"}}}, {"key": "01B4", "mappings": {"default": {"default": "y mit <PERSON>ken"}}}, {"key": "01B9", "mappings": {"default": {"default": "umgekehrtes ezh"}}}, {"key": "01BA", "mappings": {"default": {"default": "ezh mit <PERSON>nz"}}}, {"key": "01BD", "mappings": {"default": {"default": "ton fünf"}}}, {"key": "01BF", "mappings": {"default": {"default": "wynn"}}}, {"key": "01C6", "mappings": {"default": {"default": "dz mit <PERSON><PERSON>"}}}, {"key": "01C9", "mappings": {"default": {"default": "lj"}}}, {"key": "01CC", "mappings": {"default": {"default": "nj"}}}, {"key": "01E3", "mappings": {"default": {"default": "ae mit Ma<PERSON>ron"}}}, {"key": "01EF", "mappings": {"default": {"default": "ezh mit Caron"}}}, {"key": "01F3", "mappings": {"default": {"default": "dz"}}}, {"key": "021D", "mappings": {"default": {"default": "yogh"}}}, {"key": "026E", "mappings": {"default": {"default": "lezh"}}}, {"key": "0292", "mappings": {"default": {"default": "ezh"}}}, {"key": "0293", "mappings": {"default": {"default": "ezh mit Kringel"}}}, {"key": "02A4", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "01DD", "mappings": {"default": {"default": "g<PERSON><PERSON><PERSON>"}}}, {"key": "01FD", "mappings": {"default": {"default": "ae mit Akut"}}}, {"key": "0221", "mappings": {"default": {"default": "d mit <PERSON>"}}}, {"key": "0223", "mappings": {"default": {"default": "ou"}}}, {"key": "0225", "mappings": {"default": {"default": "z mit Haken"}}}, {"key": "0234", "mappings": {"default": {"default": "l <PERSON>"}}}, {"key": "0235", "mappings": {"default": {"default": "n mit K<PERSON>el"}}}, {"key": "0236", "mappings": {"default": {"default": "t mit <PERSON>"}}}, {"key": "0238", "mappings": {"default": {"default": "db"}}}, {"key": "0239", "mappings": {"default": {"default": "qp Digraph"}}}, {"key": "023F", "mappings": {"default": {"default": "s mit <PERSON><PERSON>"}}}, {"key": "0240", "mappings": {"default": {"default": "z mit <PERSON><PERSON>"}}}, {"key": "0242", "mappings": {"default": {"default": "Glottalanschlag"}}}, {"key": "024B", "mappings": {"default": {"default": "q mit Ha<PERSON><PERSON>wanz"}}}, {"key": "0250", "mappings": {"default": {"default": "ged<PERSON><PERSON>"}}}, {"key": "0251", "mappings": {"default": {"default": "alpha"}}}, {"key": "0252", "mappings": {"default": {"default": "g<PERSON><PERSON>ht<PERSON> Alpha"}}}, {"key": "0253", "mappings": {"default": {"default": "b mit <PERSON><PERSON>"}}}, {"key": "0254", "mappings": {"default": {"default": "open O"}}}, {"key": "0255", "mappings": {"default": {"default": "c <PERSON>"}}}, {"key": "0256", "mappings": {"default": {"default": "d mit <PERSON>"}}}, {"key": "0257", "mappings": {"default": {"default": "d mit <PERSON><PERSON>"}}}, {"key": "0258", "mappings": {"default": {"default": "umgedreht E"}}}, {"key": "0259", "mappings": {"default": {"default": "schwa"}}}, {"key": "025A", "mappings": {"default": {"default": "schwa mit Haken"}}}, {"key": "025B", "mappings": {"default": {"default": "offenes e"}}}, {"key": "025C", "mappings": {"default": {"default": "umgedrehtes offenes e"}}}, {"key": "025D", "mappings": {"default": {"default": "umgekehrtes offenes e mit Haken"}}}, {"key": "025E", "mappings": {"default": {"default": "umgekehrtes geschlossenes epsilon"}}}, {"key": "0260", "mappings": {"default": {"default": "g mit <PERSON><PERSON>"}}}, {"key": "0261", "mappings": {"default": {"default": "g"}}}, {"key": "0263", "mappings": {"default": {"default": "gamma"}}}, {"key": "0264", "mappings": {"default": {"default": "mini gamma"}}}, {"key": "0265", "mappings": {"default": {"default": "ged<PERSON><PERSON><PERSON> h"}}}, {"key": "0266", "mappings": {"default": {"default": "h mit <PERSON>ken"}}}, {"key": "0267", "mappings": {"default": {"default": "h mit <PERSON>ken"}}}, {"key": "0269", "mappings": {"default": {"default": "iota"}}}, {"key": "026B", "mappings": {"default": {"default": "l mit mittlerer Tilde"}}}, {"key": "026C", "mappings": {"default": {"default": "l mit Gurt"}}}, {"key": "026D", "mappings": {"default": {"default": "l mit Retroflexhaken"}}}, {"key": "026F", "mappings": {"default": {"default": "g<PERSON><PERSON><PERSON>"}}}, {"key": "0270", "mappings": {"default": {"default": "g<PERSON><PERSON><PERSON><PERSON> M mit langem Bein"}}}, {"key": "0271", "mappings": {"default": {"default": "m mit <PERSON>ken"}}}, {"key": "0272", "mappings": {"default": {"default": "n mit linkem Haken"}}}, {"key": "0273", "mappings": {"default": {"default": "n mit Retroflexhaken"}}}, {"key": "0275", "mappings": {"default": {"default": "o mit balken"}}}, {"key": "0277", "mappings": {"default": {"default": "geschlossenes omega"}}}, {"key": "0278", "mappings": {"default": {"default": "phi"}}}, {"key": "0279", "mappings": {"default": {"default": "ged<PERSON><PERSON><PERSON> r"}}}, {"key": "027A", "mappings": {"default": {"default": "ged<PERSON><PERSON><PERSON> r mit langem Bein"}}}, {"key": "027B", "mappings": {"default": {"default": "gedrehtes r mit langem Bein und Haken"}}}, {"key": "027C", "mappings": {"default": {"default": "r mit langem Bein"}}}, {"key": "027D", "mappings": {"default": {"default": "r mit Endstück"}}}, {"key": "027E", "mappings": {"default": {"default": "r mit <PERSON>"}}}, {"key": "027F", "mappings": {"default": {"default": "umgedrehtes R mit Angelhaken"}}}, {"key": "0282", "mappings": {"default": {"default": "s mit <PERSON>ken"}}}, {"key": "0283", "mappings": {"default": {"default": "esh"}}}, {"key": "0285", "mappings": {"default": {"default": "squat umgekehrt"}}}, {"key": "0286", "mappings": {"default": {"default": "esh mit K<PERSON>el"}}}, {"key": "0287", "mappings": {"default": {"default": "g<PERSON><PERSON><PERSON>"}}}, {"key": "0288", "mappings": {"default": {"default": "t mit <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "0289", "mappings": {"default": {"default": "u Bar"}}}, {"key": "028A", "mappings": {"default": {"default": "upsilon"}}}, {"key": "028B", "mappings": {"default": {"default": "v mit Haken"}}}, {"key": "028C", "mappings": {"default": {"default": "gedrehtes v"}}}, {"key": "028D", "mappings": {"default": {"default": "ged<PERSON><PERSON><PERSON> w"}}}, {"key": "028E", "mappings": {"default": {"default": "ged<PERSON><PERSON>es y"}}}, {"key": "0290", "mappings": {"default": {"default": "z mit Retroflexhaken"}}}, {"key": "0291", "mappings": {"default": {"default": "z mit K<PERSON>el"}}}, {"key": "0295", "mappings": {"default": {"default": "pharyngal<PERSON> stimm<PERSON>"}}}, {"key": "0296", "mappings": {"default": {"default": "invertierter Glottalanschlag"}}}, {"key": "0297", "mappings": {"default": {"default": "gestrecktes C"}}}, {"key": "0298", "mappings": {"default": {"default": "bilabial<PERSON>"}}}, {"key": "029A", "mappings": {"default": {"default": "geschlossenes offenes e"}}}, {"key": "029E", "mappings": {"default": {"default": "gedrehtes k"}}}, {"key": "02A0", "mappings": {"default": {"default": "q mit Haken"}}}, {"key": "02A3", "mappings": {"default": {"default": "dz Digraph"}}}, {"key": "02A5", "mappings": {"default": {"default": "dz Digraph mit Kringel"}}}, {"key": "02A6", "mappings": {"default": {"default": "ts <PERSON><PERSON>"}}}, {"key": "02A7", "mappings": {"default": {"default": "tesh Digraph"}}}, {"key": "02A8", "mappings": {"default": {"default": "tc Digraph mit Kringel"}}}, {"key": "02A9", "mappings": {"default": {"default": "feng Di<PERSON>"}}}, {"key": "02AA", "mappings": {"default": {"default": "ls Digraph"}}}, {"key": "02AB", "mappings": {"default": {"default": "lz Digraph"}}}, {"key": "02AC", "mappings": {"default": {"default": "bilabiales Perkussiv"}}}, {"key": "02AD", "mappings": {"default": {"default": "bid<PERSON><PERSON>"}}}, {"key": "02AE", "mappings": {"default": {"default": "ged<PERSON><PERSON><PERSON> h mit <PERSON>haken"}}}, {"key": "02AF", "mappings": {"default": {"default": "ged<PERSON>ht<PERSON> h mit Angelhaken und Schwanz"}}}, {"key": "1D02", "mappings": {"default": {"default": "gedrehtes ae"}}}, {"key": "1D08", "mappings": {"default": {"default": "gedrehtes e"}}}, {"key": "1D09", "mappings": {"default": {"default": "ged<PERSON><PERSON><PERSON> i"}}}, {"key": "1D11", "mappings": {"default": {"default": "liegendes O"}}}, {"key": "1D12", "mappings": {"default": {"default": "liegendes geöffnet O"}}}, {"key": "1D14", "mappings": {"default": {"default": "g<PERSON><PERSON><PERSON>"}}}, {"key": "1D16", "mappings": {"default": {"default": "obere Hälfte O"}}}, {"key": "1D17", "mappings": {"default": {"default": "untere Hälfte O"}}}, {"key": "1D1D", "mappings": {"default": {"default": "liegendes U"}}}, {"key": "1D1E", "mappings": {"default": {"default": "liegendes u Umlaut"}}}, {"key": "1D1F", "mappings": {"default": {"default": "liegendes m"}}}, {"key": "1D24", "mappings": {"default": {"default": "stimm<PERSON> laryngaler Frikativ"}}}, {"key": "1D25", "mappings": {"default": {"default": "Ain"}}}, {"key": "1D6B", "mappings": {"default": {"default": "ue"}}}, {"key": "1D6C", "mappings": {"default": {"default": "b mit mittler<PERSON> <PERSON><PERSON>"}}}, {"key": "1D6D", "mappings": {"default": {"default": "d mit mittlerer Tilde"}}}, {"key": "1D6E", "mappings": {"default": {"default": "f mit mittlerer Tilde"}}}, {"key": "1D6F", "mappings": {"default": {"default": "m mit mittlerer Tilde"}}}, {"key": "1D70", "mappings": {"default": {"default": "n mit mittlerer Tilde"}}}, {"key": "1D71", "mappings": {"default": {"default": "p mit mittlerer Tilde"}}}, {"key": "1D72", "mappings": {"default": {"default": "r mit mittlerer Tilde"}}}, {"key": "1D73", "mappings": {"default": {"default": "r mit <PERSON> und mittlerer Tilde"}}}, {"key": "1D74", "mappings": {"default": {"default": "s mit mittlerer Tilde"}}}, {"key": "1D75", "mappings": {"default": {"default": "t mit mittlerer Tilde"}}}, {"key": "1D76", "mappings": {"default": {"default": "z mit mittlerer Tilde"}}}, {"key": "1D77", "mappings": {"default": {"default": "g<PERSON><PERSON><PERSON> G"}}}, {"key": "1D79", "mappings": {"default": {"default": "insular G"}}}, {"key": "1D7A", "mappings": {"default": {"default": "th mit Durchgestrichen"}}}, {"key": "1D80", "mappings": {"default": {"default": "b mit <PERSON><PERSON>"}}}, {"key": "1D81", "mappings": {"default": {"default": "d mit <PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "1D82", "mappings": {"default": {"default": "f mit <PERSON><PERSON>"}}}, {"key": "1D83", "mappings": {"default": {"default": "g mit <PERSON><PERSON>"}}}, {"key": "1D84", "mappings": {"default": {"default": "k <PERSON>"}}}, {"key": "1D85", "mappings": {"default": {"default": "l mit <PERSON><PERSON><PERSON>"}}}, {"key": "1D86", "mappings": {"default": {"default": "m mit <PERSON><PERSON><PERSON>"}}}, {"key": "1D87", "mappings": {"default": {"default": "n mit <PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "1D88", "mappings": {"default": {"default": "p mit <PERSON><PERSON>"}}}, {"key": "1D89", "mappings": {"default": {"default": "r mit <PERSON>"}}}, {"key": "1D8A", "mappings": {"default": {"default": "s mit <PERSON><PERSON>"}}}, {"key": "1D8B", "mappings": {"default": {"default": "esh mit <PERSON><PERSON>en"}}}, {"key": "1D8C", "mappings": {"default": {"default": "v mit <PERSON><PERSON><PERSON>"}}}, {"key": "1D8D", "mappings": {"default": {"default": "x mit <PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "1D8E", "mappings": {"default": {"default": "z mit <PERSON>"}}}, {"key": "1D8F", "mappings": {"default": {"default": "a mit Retroflexhaken"}}}, {"key": "1D90", "mappings": {"default": {"default": "alpha mit Retroflexhaken"}}}, {"key": "1D91", "mappings": {"default": {"default": "d mit <PERSON><PERSON> und <PERSON>hwanz"}}}, {"key": "1D92", "mappings": {"default": {"default": "e mit Retroflexhaken"}}}, {"key": "1D93", "mappings": {"default": {"default": "offenes e mit Retroflexhaken"}}}, {"key": "1D94", "mappings": {"default": {"default": "umgedrehtes offenes e mit Retroflexhaken"}}}, {"key": "1D95", "mappings": {"default": {"default": "schwa mit Retroflexhaken"}}}, {"key": "1D96", "mappings": {"default": {"default": "i mit Retroflexhaken"}}}, {"key": "1D97", "mappings": {"default": {"default": "offenes o mit Retroflexhaken"}}}, {"key": "1D98", "mappings": {"default": {"default": "esh mit Retroflexhaken"}}}, {"key": "1D99", "mappings": {"default": {"default": "u mit Retroflexhaken"}}}, {"key": "1D9A", "mappings": {"default": {"default": "ezh mit Retroflexhaken"}}}, {"key": "0149", "mappings": {"default": {"default": "n mit Apostroph davor"}}}, {"key": "014B", "mappings": {"default": {"default": "eng"}}}], "de/symbols/latin-lower-single-accent.min": [{"locale": "de"}, {"key": "00E0", "mappings": {"default": {"default": "a mit Grave"}}}, {"key": "00E1", "mappings": {"default": {"default": "a mit Aigu"}}}, {"key": "00E2", "mappings": {"default": {"default": "a mit Zirkumflex"}}}, {"key": "00E3", "mappings": {"default": {"default": "a mit Tilde"}}}, {"key": "00E4", "mappings": {"default": {"default": "a mit Diärese"}}}, {"key": "00E5", "mappings": {"default": {"default": "a mit Ring darüber"}}}, {"key": "00E7", "mappings": {"default": {"default": "c mit <PERSON><PERSON><PERSON>"}}}, {"key": "00E8", "mappings": {"default": {"default": "e mit Grave"}}}, {"key": "00E9", "mappings": {"default": {"default": "e mit Aigu"}}}, {"key": "00EA", "mappings": {"default": {"default": "e mit Zirkumflex"}}}, {"key": "00EB", "mappings": {"default": {"default": "e mit Diärese"}}}, {"key": "00EC", "mappings": {"default": {"default": "i mit Grave"}}}, {"key": "00ED", "mappings": {"default": {"default": "i mit Aigu"}}}, {"key": "00EE", "mappings": {"default": {"default": "i mit Zirkumflex"}}}, {"key": "00EF", "mappings": {"default": {"default": "i mit Diärese"}}}, {"key": "00F1", "mappings": {"default": {"default": "n mit Tilde"}}}, {"key": "00F2", "mappings": {"default": {"default": "o mit Grave"}}}, {"key": "00F3", "mappings": {"default": {"default": "o mit Aigu"}}}, {"key": "00F4", "mappings": {"default": {"default": "o mit Zirkumflex"}}}, {"key": "00F5", "mappings": {"default": {"default": "o mit Tilde"}}}, {"key": "00F6", "mappings": {"default": {"default": "o mit Diärese"}}}, {"key": "00F9", "mappings": {"default": {"default": "u mit Grave"}}}, {"key": "00FA", "mappings": {"default": {"default": "u mit Aigu"}}}, {"key": "00FB", "mappings": {"default": {"default": "u mit Zirkumflex"}}}, {"key": "00FC", "mappings": {"default": {"default": "u mit Diärese"}}}, {"key": "00FD", "mappings": {"default": {"default": "y mit Aigu"}}}, {"key": "00FF", "mappings": {"default": {"default": "y mit Diärese"}}}, {"key": "0101", "mappings": {"default": {"default": "a mit Ma<PERSON>ron"}}}, {"key": "0103", "mappings": {"default": {"default": "a mit Breve"}}}, {"key": "0105", "mappings": {"default": {"default": "a mit O<PERSON>ek"}}}, {"key": "0107", "mappings": {"default": {"default": "c mit <PERSON><PERSON>"}}}, {"key": "0109", "mappings": {"default": {"default": "c mit <PERSON>"}}}, {"key": "010B", "mappings": {"default": {"default": "c mit <PERSON><PERSON>"}}}, {"key": "010D", "mappings": {"default": {"default": "c <PERSON><PERSON>"}}}, {"key": "010F", "mappings": {"default": {"default": "d mit <PERSON><PERSON>"}}}, {"key": "0113", "mappings": {"default": {"default": "e mit <PERSON><PERSON>"}}}, {"key": "0115", "mappings": {"default": {"default": "e mit Breve"}}}, {"key": "0117", "mappings": {"default": {"default": "e mit <PERSON> darüber"}}}, {"key": "0119", "mappings": {"default": {"default": "e mit Ogonek"}}}, {"key": "011B", "mappings": {"default": {"default": "e mit <PERSON><PERSON>"}}}, {"key": "011D", "mappings": {"default": {"default": "g mit Zirkumflex"}}}, {"key": "011F", "mappings": {"default": {"default": "g mit Breve"}}}, {"key": "0121", "mappings": {"default": {"default": "g mit <PERSON><PERSON> darüber"}}}, {"key": "0123", "mappings": {"default": {"default": "g mit <PERSON><PERSON><PERSON>"}}}, {"key": "0125", "mappings": {"default": {"default": "h mit Zirkumflex"}}}, {"key": "0129", "mappings": {"default": {"default": "i mit Tilde"}}}, {"key": "012B", "mappings": {"default": {"default": "i mit Ma<PERSON>ron"}}}, {"key": "012D", "mappings": {"default": {"default": "i mit Breve"}}}, {"key": "012F", "mappings": {"default": {"default": "i mit Ogonek"}}}, {"key": "0131", "mappings": {"default": {"default": "i ohne punkt"}}}, {"key": "0135", "mappings": {"default": {"default": "j <PERSON>"}}}, {"key": "0137", "mappings": {"default": {"default": "k mit <PERSON><PERSON>"}}}, {"key": "013A", "mappings": {"default": {"default": "l mit <PERSON>gu"}}}, {"key": "013C", "mappings": {"default": {"default": "l mit Cedille"}}}, {"key": "013E", "mappings": {"default": {"default": "l <PERSON>"}}}, {"key": "0140", "mappings": {"default": {"default": "l mit Punkt in der Mitte"}}}, {"key": "0144", "mappings": {"default": {"default": "n mit Aigu"}}}, {"key": "0146", "mappings": {"default": {"default": "n mit Cedille"}}}, {"key": "0148", "mappings": {"default": {"default": "n mit <PERSON>on"}}}, {"key": "014D", "mappings": {"default": {"default": "o mit <PERSON><PERSON>"}}}, {"key": "014F", "mappings": {"default": {"default": "o mit Breve"}}}, {"key": "0151", "mappings": {"default": {"default": "o mit doppel<PERSON>"}}}, {"key": "0155", "mappings": {"default": {"default": "r mit <PERSON>"}}}, {"key": "0157", "mappings": {"default": {"default": "r mit <PERSON>"}}}, {"key": "0159", "mappings": {"default": {"default": "r <PERSON>"}}}, {"key": "015B", "mappings": {"default": {"default": "s mit <PERSON>gu"}}}, {"key": "015D", "mappings": {"default": {"default": "s mit Zirkumflex"}}}, {"key": "015F", "mappings": {"default": {"default": "s mit <PERSON><PERSON><PERSON>"}}}, {"key": "0161", "mappings": {"default": {"default": "s <PERSON><PERSON>"}}}, {"key": "0163", "mappings": {"default": {"default": "t mit <PERSON><PERSON><PERSON>"}}}, {"key": "0165", "mappings": {"default": {"default": "t mit <PERSON><PERSON>"}}}, {"key": "0169", "mappings": {"default": {"default": "u mit Tilde"}}}, {"key": "016B", "mappings": {"default": {"default": "u mit <PERSON><PERSON>"}}}, {"key": "016D", "mappings": {"default": {"default": "u mit Breve"}}}, {"key": "016F", "mappings": {"default": {"default": "u mit Ring darüber"}}}, {"key": "0171", "mappings": {"default": {"default": "u mit doppeltem <PERSON>"}}}, {"key": "0173", "mappings": {"default": {"default": "u mit Ogonek"}}}, {"key": "0175", "mappings": {"default": {"default": "w mit Zirkumflex"}}}, {"key": "0177", "mappings": {"default": {"default": "y mit Zirkumflex"}}}, {"key": "017A", "mappings": {"default": {"default": "z mit Aigu"}}}, {"key": "017C", "mappings": {"default": {"default": "z mit <PERSON>t darüber"}}}, {"key": "017E", "mappings": {"default": {"default": "z mit <PERSON>"}}}, {"key": "01CE", "mappings": {"default": {"default": "a mit <PERSON>on"}}}, {"key": "01D0", "mappings": {"default": {"default": "i mit Caron"}}}, {"key": "01D2", "mappings": {"default": {"default": "o mit <PERSON>on"}}}, {"key": "01D4", "mappings": {"default": {"default": "u mit <PERSON><PERSON>"}}}, {"key": "01E7", "mappings": {"default": {"default": "g <PERSON>"}}}, {"key": "01E9", "mappings": {"default": {"default": "k <PERSON>"}}}, {"key": "01EB", "mappings": {"default": {"default": "o mit <PERSON><PERSON>"}}}, {"key": "01F0", "mappings": {"default": {"default": "j <PERSON>"}}}, {"key": "01F5", "mappings": {"default": {"default": "g mit <PERSON><PERSON>"}}}, {"key": "01F9", "mappings": {"default": {"default": "n mit Grab"}}}, {"key": "0201", "mappings": {"default": {"default": "a mit <PERSON>"}}}, {"key": "0203", "mappings": {"default": {"default": "a mit umgekehrter Breve"}}}, {"key": "0205", "mappings": {"default": {"default": "e mit <PERSON>grab"}}}, {"key": "0207", "mappings": {"default": {"default": "e mit umgekehrter Breve"}}}, {"key": "0209", "mappings": {"default": {"default": "i mit Doppelgrab"}}}, {"key": "020B", "mappings": {"default": {"default": "i mit umgekehrter Breve"}}}, {"key": "020D", "mappings": {"default": {"default": "o mit <PERSON>"}}}, {"key": "020F", "mappings": {"default": {"default": "o mit umgekehrter Breve"}}}, {"key": "0211", "mappings": {"default": {"default": "r <PERSON>"}}}, {"key": "0213", "mappings": {"default": {"default": "r mit umgekehrter Breve"}}}, {"key": "0215", "mappings": {"default": {"default": "u mit <PERSON>"}}}, {"key": "0217", "mappings": {"default": {"default": "u mit umgekehrter Breve"}}}, {"key": "0219", "mappings": {"default": {"default": "s mit untenste<PERSON><PERSON>"}}}, {"key": "021B", "mappings": {"default": {"default": "t mit untenste<PERSON><PERSON>"}}}, {"key": "021F", "mappings": {"default": {"default": "h mit <PERSON><PERSON>"}}}, {"key": "0227", "mappings": {"default": {"default": "a mit <PERSON> oben"}}}, {"key": "0229", "mappings": {"default": {"default": "e mit Cedilla"}}}, {"key": "022F", "mappings": {"default": {"default": "o mit Punkt oben"}}}, {"key": "0233", "mappings": {"default": {"default": "y mit <PERSON>"}}}, {"key": "0237", "mappings": {"default": {"default": "dotless J"}}}, {"key": "1E01", "mappings": {"default": {"default": "a mit Ring unten"}}}, {"key": "1E03", "mappings": {"default": {"default": "b mit <PERSON><PERSON> oben"}}}, {"key": "1E05", "mappings": {"default": {"default": "b mit untens<PERSON><PERSON><PERSON>"}}}, {"key": "1E07", "mappings": {"default": {"default": "b mit <PERSON><PERSON><PERSON>"}}}, {"key": "1E0B", "mappings": {"default": {"default": "d mit <PERSON> oben"}}}, {"key": "1E0D", "mappings": {"default": {"default": "d mit untens<PERSON><PERSON><PERSON>"}}}, {"key": "1E0F", "mappings": {"default": {"default": "d mit <PERSON><PERSON><PERSON> da<PERSON>ter"}}}, {"key": "1E11", "mappings": {"default": {"default": "d mit <PERSON><PERSON><PERSON>"}}}, {"key": "1E13", "mappings": {"default": {"default": "d mit darun<PERSON>em Zirkumflex"}}}, {"key": "1E19", "mappings": {"default": {"default": "e mit darunterem Zirkumflex"}}}, {"key": "1E1B", "mappings": {"default": {"default": "e mit darunterliegender Tilde"}}}, {"key": "1E1F", "mappings": {"default": {"default": "f mit <PERSON>t oben"}}}, {"key": "1E21", "mappings": {"default": {"default": "g mit <PERSON>"}}}, {"key": "1E23", "mappings": {"default": {"default": "h mit <PERSON> oben"}}}, {"key": "1E25", "mappings": {"default": {"default": "h mit untenste<PERSON><PERSON>"}}}, {"key": "1E27", "mappings": {"default": {"default": "h mit Diärese"}}}, {"key": "1E29", "mappings": {"default": {"default": "h mit <PERSON><PERSON><PERSON>"}}}, {"key": "1E2B", "mappings": {"default": {"default": "h mit <PERSON>reve unten"}}}, {"key": "1E2D", "mappings": {"default": {"default": "i mit darunterliegender Tilde"}}}, {"key": "1E31", "mappings": {"default": {"default": "k mit <PERSON>kut"}}}, {"key": "1E33", "mappings": {"default": {"default": "k mit untenste<PERSON><PERSON> Punkt"}}}, {"key": "1E35", "mappings": {"default": {"default": "k mit <PERSON><PERSON><PERSON> da<PERSON>ter"}}}, {"key": "1E37", "mappings": {"default": {"default": "l mit <PERSON> unten"}}}, {"key": "1E3B", "mappings": {"default": {"default": "l mit <PERSON><PERSON><PERSON> da<PERSON>ter"}}}, {"key": "1E3D", "mappings": {"default": {"default": "l mit darunterem Zirkumflex"}}}, {"key": "1E3F", "mappings": {"default": {"default": "m mit <PERSON><PERSON><PERSON>"}}}, {"key": "1E41", "mappings": {"default": {"default": "m mit <PERSON> oben"}}}, {"key": "1E43", "mappings": {"default": {"default": "m mit untens<PERSON><PERSON><PERSON>"}}}, {"key": "1E45", "mappings": {"default": {"default": "n mit <PERSON> oben"}}}, {"key": "1E47", "mappings": {"default": {"default": "n mit untenstehendem Punkt"}}}, {"key": "1E49", "mappings": {"default": {"default": "n mit <PERSON><PERSON>e da<PERSON>ter"}}}, {"key": "1E4B", "mappings": {"default": {"default": "n mit darunterem Zirkumflex"}}}, {"key": "1E55", "mappings": {"default": {"default": "p mit <PERSON><PERSON><PERSON>"}}}, {"key": "1E57", "mappings": {"default": {"default": "p mit <PERSON> oben"}}}, {"key": "1E59", "mappings": {"default": {"default": "r mit <PERSON> oben"}}}, {"key": "1E5B", "mappings": {"default": {"default": "r mit untens<PERSON><PERSON><PERSON>"}}}, {"key": "1E5F", "mappings": {"default": {"default": "r mit <PERSON><PERSON><PERSON>"}}}, {"key": "1E61", "mappings": {"default": {"default": "s mit <PERSON>t oben"}}}, {"key": "1E63", "mappings": {"default": {"default": "s mit untenste<PERSON><PERSON>"}}}, {"key": "1E6B", "mappings": {"default": {"default": "t mit <PERSON>t oben"}}}, {"key": "1E6D", "mappings": {"default": {"default": "t mit untenste<PERSON><PERSON>"}}}, {"key": "1E6F", "mappings": {"default": {"default": "t mit <PERSON><PERSON><PERSON>"}}}, {"key": "1E71", "mappings": {"default": {"default": "t mit darun<PERSON><PERSON> Zirkumflex"}}}, {"key": "1E73", "mappings": {"default": {"default": "u mit untenstehender Diärese"}}}, {"key": "1E75", "mappings": {"default": {"default": "u mit darunterliegender Tilde"}}}, {"key": "1E77", "mappings": {"default": {"default": "u mit darunterem Zirkumflex"}}}, {"key": "1E7D", "mappings": {"default": {"default": "v mit Tilde"}}}, {"key": "1E7F", "mappings": {"default": {"default": "v mit untenstehendem Punkt"}}}, {"key": "1E81", "mappings": {"default": {"default": "w mit Grab"}}}, {"key": "1E83", "mappings": {"default": {"default": "w mit Akut"}}}, {"key": "1E85", "mappings": {"default": {"default": "w mit Diärese"}}}, {"key": "1E87", "mappings": {"default": {"default": "w mit <PERSON>t oben"}}}, {"key": "1E89", "mappings": {"default": {"default": "w mit <PERSON>t unten"}}}, {"key": "1E8B", "mappings": {"default": {"default": "x mit <PERSON>t oben"}}}, {"key": "1E8D", "mappings": {"default": {"default": "x mit Diärese"}}}, {"key": "1E8F", "mappings": {"default": {"default": "y mit Punkt oben"}}}, {"key": "1E91", "mappings": {"default": {"default": "z mit Zirkumflex"}}}, {"key": "1E93", "mappings": {"default": {"default": "z mit untenstehendem Punkt"}}}, {"key": "1E95", "mappings": {"default": {"default": "z mit <PERSON><PERSON><PERSON> da<PERSON>ter"}}}, {"key": "1E96", "mappings": {"default": {"default": "h mit <PERSON><PERSON><PERSON>"}}}, {"key": "1E97", "mappings": {"default": {"default": "t mit <PERSON>ä<PERSON>e"}}}, {"key": "1E98", "mappings": {"default": {"default": "w mit Ring oben"}}}, {"key": "1E99", "mappings": {"default": {"default": "y mit Ring oben"}}}, {"key": "1E9A", "mappings": {"default": {"default": "a mit rechtem Halbring"}}}, {"key": "1EA1", "mappings": {"default": {"default": "a mit <PERSON> unten"}}}, {"key": "1EA3", "mappings": {"default": {"default": "a mit Haken oben"}}}, {"key": "1EB9", "mappings": {"default": {"default": "e mit untenste<PERSON>em Punkt"}}}, {"key": "1EBB", "mappings": {"default": {"default": "e mit Haken oben"}}}, {"key": "1EBD", "mappings": {"default": {"default": "e mit Tilde"}}}, {"key": "1EC9", "mappings": {"default": {"default": "i mit Haken oben"}}}, {"key": "1ECB", "mappings": {"default": {"default": "i mit untenste<PERSON><PERSON> Punkt"}}}, {"key": "1ECD", "mappings": {"default": {"default": "o mit untenste<PERSON><PERSON> Punkt"}}}, {"key": "1ECF", "mappings": {"default": {"default": "o mit Haken oben"}}}, {"key": "1EE5", "mappings": {"default": {"default": "u mit untenstehendem Punkt"}}}, {"key": "1EE7", "mappings": {"default": {"default": "u mit Haken oben"}}}, {"key": "1EF3", "mappings": {"default": {"default": "y mit Grab"}}}, {"key": "1EF5", "mappings": {"default": {"default": "y mit untenste<PERSON><PERSON>"}}}, {"key": "1EF7", "mappings": {"default": {"default": "y mit Haken oben"}}}, {"key": "1EF9", "mappings": {"default": {"default": "y mit Tilde"}}}], "de/symbols/latin-rest.min": [{"locale": "de"}, {"key": "210E", "mappings": {"default": {"physics": "Planck-Ko<PERSON>ante"}}}, {"key": "0363", "mappings": {"default": {"default": "kombinierendes a"}}}, {"key": "0364", "mappings": {"default": {"default": "kombinierendes e"}}}, {"key": "0365", "mappings": {"default": {"default": "kombinierendes i"}}}, {"key": "0366", "mappings": {"default": {"default": "kombinierendes o"}}}, {"key": "0367", "mappings": {"default": {"default": "kombinierendes u"}}}, {"key": "0368", "mappings": {"default": {"default": "komb<PERSON>erendes c"}}}, {"key": "0369", "mappings": {"default": {"default": "kombinierendes d"}}}, {"key": "036A", "mappings": {"default": {"default": "komb<PERSON>erendes h"}}}, {"key": "036B", "mappings": {"default": {"default": "kombinierendes m"}}}, {"key": "036C", "mappings": {"default": {"default": "kombinierendes r"}}}, {"key": "036D", "mappings": {"default": {"default": "komb<PERSON>erendes t"}}}, {"key": "036E", "mappings": {"default": {"default": "kombinierendes v"}}}, {"key": "036F", "mappings": {"default": {"default": "kombinierendes x"}}}, {"key": "1D62", "mappings": {"default": {"default": "tiefgestelltes i"}}}, {"key": "1D63", "mappings": {"default": {"default": "tiefgestelltes r"}}}, {"key": "1D64", "mappings": {"default": {"default": "tiefgestelltes u"}}}, {"key": "1D65", "mappings": {"default": {"default": "tiefgestelltes v"}}}, {"key": "1DCA", "mappings": {"default": {"default": "tiefgestelltes r"}}}, {"key": "1DD3", "mappings": {"default": {"default": "kombinierendes oberes w"}}}, {"key": "1DD4", "mappings": {"default": {"default": "kombinierendes ae"}}}, {"key": "1DD5", "mappings": {"default": {"default": "kombinierendes ao"}}}, {"key": "1DD6", "mappings": {"default": {"default": "kombinierendes av"}}}, {"key": "1DD7", "mappings": {"default": {"default": "komb<PERSON>erendes c mit <PERSON><PERSON>"}}}, {"key": "1DD8", "mappings": {"default": {"default": "kombinierendes insulares d"}}}, {"key": "1DD9", "mappings": {"default": {"default": "kombinierendes eth"}}}, {"key": "1DDA", "mappings": {"default": {"default": "kombinierendes g"}}}, {"key": "1DDB", "mappings": {"default": {"default": "kombinierendes Kapitälchen g"}}}, {"key": "1DDC", "mappings": {"default": {"default": "kombinierendes k"}}}, {"key": "1DDD", "mappings": {"default": {"default": "kombinierendes l"}}}, {"key": "1DDE", "mappings": {"default": {"default": "kombinierendes Kapitälchen L"}}}, {"key": "1DDF", "mappings": {"default": {"default": "kombinierendes Kapitälchen M"}}}, {"key": "1DE0", "mappings": {"default": {"default": "kombinierendes n"}}}, {"key": "1DE1", "mappings": {"default": {"default": "kombinierendes Kapitälchen N"}}}, {"key": "1DE2", "mappings": {"default": {"default": "kombinierendes lateinisches Kapitälchen R"}}}, {"key": "1DE3", "mappings": {"default": {"default": "kombinierendes r rotunda"}}}, {"key": "1DE4", "mappings": {"default": {"default": "komb<PERSON><PERSON><PERSON> s"}}}, {"key": "1DE5", "mappings": {"default": {"default": "kombinierendes langes s"}}}, {"key": "1DE6", "mappings": {"default": {"default": "kombinierendes z"}}}, {"key": "2071", "mappings": {"default": {"default": "hochgestelltes i"}}}, {"key": "207F", "mappings": {"default": {"default": "hochgestelltes n"}}}, {"key": "2090", "mappings": {"default": {"default": "tiefgestelltes a"}}}, {"key": "2091", "mappings": {"default": {"default": "tiefgestelltes e"}}}, {"key": "2092", "mappings": {"default": {"default": "tiefgestelltes o"}}}, {"key": "2093", "mappings": {"default": {"default": "tiefgestelltes x"}}}, {"key": "2094", "mappings": {"default": {"default": "tiefgestelltes schwa"}}}, {"key": "2095", "mappings": {"default": {"default": "tiefgestelltes h"}}}, {"key": "2096", "mappings": {"default": {"default": "tiefgestelltes k"}}}, {"key": "2097", "mappings": {"default": {"default": "tiefgestelltes l"}}}, {"key": "2098", "mappings": {"default": {"default": "tiefgestelltes m"}}}, {"key": "2099", "mappings": {"default": {"default": "tiefgestelltes n"}}}, {"key": "209A", "mappings": {"default": {"default": "tiefgestelltes p"}}}, {"key": "209B", "mappings": {"default": {"default": "tiefgestelltes s"}}}, {"key": "209C", "mappings": {"default": {"default": "tiefgestelltes t"}}}, {"key": "2C7C", "mappings": {"default": {"default": "tiefgestelltes j"}}}, {"key": "1F12A", "mappings": {"default": {"default": "groß S in stumpfen Klammern"}}}, {"key": "1F12B", "mappings": {"default": {"default": "eingekreistes kursives groß C"}}}, {"key": "1F12C", "mappings": {"default": {"default": "eingekreistes kursives groß R"}}}, {"key": "1F18A", "mappings": {"default": {"default": "durchgestrichenes P auf schwarzen Quadrat"}}}], "de/symbols/latin-upper-double-accent.min": [{"locale": "de"}, {"key": "01D5", "mappings": {"default": {"default": "großes U mit Diärese und Makron"}}}, {"key": "01D7", "mappings": {"default": {"default": "großes U mit Diärese und Akut"}}}, {"key": "01D9", "mappings": {"default": {"default": "großes U mit Diärese und Caron"}}}, {"key": "01DB", "mappings": {"default": {"default": "großes U mit Diärese und Grab"}}}, {"key": "01DE", "mappings": {"default": {"default": "großes A mit Diärese und Makron"}}}, {"key": "01E0", "mappings": {"default": {"default": "großes A mit Dot Above und Makron"}}}, {"key": "01EC", "mappings": {"default": {"default": "großes O mit Ogonek und Makron"}}}, {"key": "01FA", "mappings": {"default": {"default": "großes A mit Ring oben und akut"}}}, {"key": "022A", "mappings": {"default": {"default": "großes O mit Diärese und Makron"}}}, {"key": "022C", "mappings": {"default": {"default": "großes O mit Tilde und Makron"}}}, {"key": "0230", "mappings": {"default": {"default": "großes O mit Punkt oben und Makron"}}}, {"key": "1E08", "mappings": {"default": {"default": "großes C mit Cedilla und Akut"}}}, {"key": "1E14", "mappings": {"default": {"default": "großes E mit Makron und Grab"}}}, {"key": "1E16", "mappings": {"default": {"default": "großes E mit Makron und Akut"}}}, {"key": "1E1C", "mappings": {"default": {"default": "großes E mit Cedilla und Breve"}}}, {"key": "1E2E", "mappings": {"default": {"default": "großes I mit Diärese und Akut"}}}, {"key": "1E38", "mappings": {"default": {"default": "großes L mit Dot Below und Makron"}}}, {"key": "1E4C", "mappings": {"default": {"default": "großes O mit Tilde und Akut"}}}, {"key": "1E4E", "mappings": {"default": {"default": "großes O mit Tilde und Diärese"}}}, {"key": "1E50", "mappings": {"default": {"default": "großes O mit Makron und Grab"}}}, {"key": "1E52", "mappings": {"default": {"default": "großes O mit Makron und Akut"}}}, {"key": "1E5C", "mappings": {"default": {"default": "großes R mit Punkt unten und Makron"}}}, {"key": "1E64", "mappings": {"default": {"default": "großes S mit Akute und Punkt oben"}}}, {"key": "1E66", "mappings": {"default": {"default": "großes S mit Caron und Punkt oben"}}}, {"key": "1E68", "mappings": {"default": {"default": "großes S mit untenstehendem Punkt und oben stehendem Punkt"}}}, {"key": "1E78", "mappings": {"default": {"default": "großes U mit Tilde und Akut"}}}, {"key": "1E7A", "mappings": {"default": {"default": "großes U mit Makron und Diärese"}}}, {"key": "1EA4", "mappings": {"default": {"default": "großes A mit Zirkumflex und Akut"}}}, {"key": "1EA6", "mappings": {"default": {"default": "großes A mit Zirkumflex und Grab"}}}, {"key": "1EA8", "mappings": {"default": {"default": "großes A mit Zirkumflex und Haken oben"}}}, {"key": "1EAA", "mappings": {"default": {"default": "großes A mit Circumflex und Tilde"}}}, {"key": "1EAC", "mappings": {"default": {"default": "großes A mit Zirkumflex und Punkt darunter"}}}, {"key": "1EAE", "mappings": {"default": {"default": "großes A mit Breve und Akut"}}}, {"key": "1EB0", "mappings": {"default": {"default": "großes A mit Breve und Grab"}}}, {"key": "1EB2", "mappings": {"default": {"default": "großes A mit Breve und Hook oben"}}}, {"key": "1EB4", "mappings": {"default": {"default": "großes A mit Breve und Tilde"}}}, {"key": "1EB6", "mappings": {"default": {"default": "großes A mit Breve und Dot Below"}}}, {"key": "1EBE", "mappings": {"default": {"default": "großes E mit Zirkumflex und Akut"}}}, {"key": "1EC0", "mappings": {"default": {"default": "großes E mit Zirkumflex und Grab"}}}, {"key": "1EC2", "mappings": {"default": {"default": "großes E mit Zirkumflex und Haken oben"}}}, {"key": "1EC4", "mappings": {"default": {"default": "großes E mit Zirkumflex und Tilde"}}}, {"key": "1EC6", "mappings": {"default": {"default": "großes E mit Zirkumflex und Punkt darunter"}}}, {"key": "1ED0", "mappings": {"default": {"default": "großes O mit Circumflex und Akut"}}}, {"key": "1ED2", "mappings": {"default": {"default": "großes O mit Zirkumflex und Grab"}}}, {"key": "1ED4", "mappings": {"default": {"default": "großes O mit Zirkumflex und Haken oben"}}}, {"key": "1ED6", "mappings": {"default": {"default": "großes O mit Zirkumflex und Tilde"}}}, {"key": "1ED8", "mappings": {"default": {"default": "großes O mit Zirkumflex und Punkt darunter"}}}, {"key": "1EDA", "mappings": {"default": {"default": "großes O mit Horn und Akut"}}}, {"key": "1EDC", "mappings": {"default": {"default": "großes O mit Horn und Grab"}}}, {"key": "1EDE", "mappings": {"default": {"default": "großes O mit Horn und Haken oben"}}}, {"key": "1EE0", "mappings": {"default": {"default": "großes O mit Horn und Tilde"}}}, {"key": "1EE2", "mappings": {"default": {"default": "großes O mit Horn und Punkt"}}}, {"key": "1EE8", "mappings": {"default": {"default": "großes U mit Horn und Akut"}}}, {"key": "1EEA", "mappings": {"default": {"default": "großes U mit Horn und Grab"}}}, {"key": "1EEC", "mappings": {"default": {"default": "großes U mit Horn und Haken oben"}}}, {"key": "1EEE", "mappings": {"default": {"default": "großes U mit Horn und Tilde"}}}, {"key": "1EF0", "mappings": {"default": {"default": "großes U mit Horn und Punkt"}}}], "de/symbols/latin-upper-single-accent.min": [{"locale": "de"}, {"key": "00C0", "mappings": {"default": {"default": "großes A mit Gravis"}}}, {"key": "00C1", "mappings": {"default": {"default": "großes A mit Aigu"}}}, {"key": "00C2", "mappings": {"default": {"default": "großes A mit Zirkumflex"}}}, {"key": "00C3", "mappings": {"default": {"default": "großes A mit Tilde"}}}, {"key": "00C4", "mappings": {"default": {"default": "großes A mit Diärese"}}}, {"key": "00C5", "mappings": {"default": {"default": "großes A mit Ring darüber"}}}, {"key": "00C7", "mappings": {"default": {"default": "großes C mit Cedille"}}}, {"key": "00C8", "mappings": {"default": {"default": "großes E mit Gravis"}}}, {"key": "00C9", "mappings": {"default": {"default": "großes E mit Aigu"}}}, {"key": "00CA", "mappings": {"default": {"default": "großes E mit Zirkumflex"}}}, {"key": "00CB", "mappings": {"default": {"default": "großes E mit Diärese"}}}, {"key": "00CC", "mappings": {"default": {"default": "großes I mit Gravis"}}}, {"key": "00CD", "mappings": {"default": {"default": "großes I mit Aigu"}}}, {"key": "00CE", "mappings": {"default": {"default": "großes I mit Zirkumflex"}}}, {"key": "00CF", "mappings": {"default": {"default": "großes I mit Diärese"}}}, {"key": "00D1", "mappings": {"default": {"default": "großes n mit Tilde"}}}, {"key": "00D2", "mappings": {"default": {"default": "großes o mit Gravis"}}}, {"key": "00D3", "mappings": {"default": {"default": "großes O mit Aigu"}}}, {"key": "00D4", "mappings": {"default": {"default": "großes O mit Zirkumflex"}}}, {"key": "00D5", "mappings": {"default": {"default": "großes O mit Tilde"}}}, {"key": "00D6", "mappings": {"default": {"default": "großes O mit Diärese"}}}, {"key": "00D9", "mappings": {"default": {"default": "großes U mit Gravis"}}}, {"key": "00DA", "mappings": {"default": {"default": "großes U mit Aigu"}}}, {"key": "00DB", "mappings": {"default": {"default": "großes U mit Zirkumflex"}}}, {"key": "00DC", "mappings": {"default": {"default": "großes U mit Diärese"}}}, {"key": "00DD", "mappings": {"default": {"default": "großes Y mit Aigu"}}}, {"key": "0100", "mappings": {"default": {"default": "großes A mit Makron"}}}, {"key": "0102", "mappings": {"default": {"default": "großes A mit Breve"}}}, {"key": "0104", "mappings": {"default": {"default": "großes A mit Ogonek"}}}, {"key": "0106", "mappings": {"default": {"default": "großes C mit Aigu"}}}, {"key": "0108", "mappings": {"default": {"default": "großes C mit Zirkumflex"}}}, {"key": "010A", "mappings": {"default": {"default": "großes C mit Punkt darüber"}}}, {"key": "010C", "mappings": {"default": {"default": "großes C mit Caron"}}}, {"key": "010E", "mappings": {"default": {"default": "großes D mit Caron"}}}, {"key": "0112", "mappings": {"default": {"default": "großes E mit Makron"}}}, {"key": "0114", "mappings": {"default": {"default": "großes E mit Breve"}}}, {"key": "0116", "mappings": {"default": {"default": "großes E mit Punkt darüber"}}}, {"key": "0118", "mappings": {"default": {"default": "großes E mit Ogonek"}}}, {"key": "011A", "mappings": {"default": {"default": "großes E mit Caron"}}}, {"key": "011C", "mappings": {"default": {"default": "großes G mit Zirkumflex"}}}, {"key": "011E", "mappings": {"default": {"default": "großes G mit Breve"}}}, {"key": "0120", "mappings": {"default": {"default": "großes G mit Punkt darüber"}}}, {"key": "0122", "mappings": {"default": {"default": "großes G mit Cedille"}}}, {"key": "0124", "mappings": {"default": {"default": "großes H mit Zirkumflex"}}}, {"key": "0128", "mappings": {"default": {"default": "großes I mit Tilde"}}}, {"key": "012A", "mappings": {"default": {"default": "großes I mit Makron"}}}, {"key": "012C", "mappings": {"default": {"default": "großes I mit Breve"}}}, {"key": "012E", "mappings": {"default": {"default": "großes I mit Ogonek"}}}, {"key": "0130", "mappings": {"default": {"default": "großes I mit Punkt darüber"}}}, {"key": "0134", "mappings": {"default": {"default": "großes J mit Zirkumflex"}}}, {"key": "0136", "mappings": {"default": {"default": "großes K mit Cedille"}}}, {"key": "0139", "mappings": {"default": {"default": "großes L mit Aigu"}}}, {"key": "013B", "mappings": {"default": {"default": "großes L mit Cedille"}}}, {"key": "013D", "mappings": {"default": {"default": "großes L mit Caron"}}}, {"key": "013F", "mappings": {"default": {"default": "großes L mit Punkt in der Mitte"}}}, {"key": "0143", "mappings": {"default": {"default": "großes N mit Aigu"}}}, {"key": "0145", "mappings": {"default": {"default": "großes N mit Cedille"}}}, {"key": "0147", "mappings": {"default": {"default": "großes N mit Caron"}}}, {"key": "014C", "mappings": {"default": {"default": "großes O mit Makron"}}}, {"key": "014E", "mappings": {"default": {"default": "großes O mit Breve"}}}, {"key": "0150", "mappings": {"default": {"default": "großes O mit doppeltem Aigu"}}}, {"key": "0154", "mappings": {"default": {"default": "großes R mit Aigu"}}}, {"key": "0156", "mappings": {"default": {"default": "großes R mit Cedille"}}}, {"key": "0158", "mappings": {"default": {"default": "großes R mit Caron"}}}, {"key": "015A", "mappings": {"default": {"default": "großes S mit Aigu"}}}, {"key": "015C", "mappings": {"default": {"default": "großes S mit Zirkumflex"}}}, {"key": "015E", "mappings": {"default": {"default": "großes S mit Cedille"}}}, {"key": "0160", "mappings": {"default": {"default": "großes S mit Caron"}}}, {"key": "0162", "mappings": {"default": {"default": "großes T mit Cedille"}}}, {"key": "0164", "mappings": {"default": {"default": "großes T mit Caron"}}}, {"key": "0168", "mappings": {"default": {"default": "großes U mit Tilde"}}}, {"key": "016A", "mappings": {"default": {"default": "großes U mit Makron"}}}, {"key": "016C", "mappings": {"default": {"default": "großes U mit Breve"}}}, {"key": "016E", "mappings": {"default": {"default": "großes U mit Ring darüber"}}}, {"key": "0170", "mappings": {"default": {"default": "großes U mit dopppeltem Aigu"}}}, {"key": "0172", "mappings": {"default": {"default": "großes U mit Ogonek"}}}, {"key": "0174", "mappings": {"default": {"default": "großes W mit Zirkumflex"}}}, {"key": "0176", "mappings": {"default": {"default": "großes Y mit Zirkumflex"}}}, {"key": "0178", "mappings": {"default": {"default": "großes Y mit Diärese"}}}, {"key": "0179", "mappings": {"default": {"default": "großes Z mit Aigu"}}}, {"key": "017B", "mappings": {"default": {"default": "großes Z mit Punkt darüber"}}}, {"key": "017D", "mappings": {"default": {"default": "großes Z mit Caron"}}}, {"key": "01CD", "mappings": {"default": {"default": "großes A mit Caron"}}}, {"key": "01CF", "mappings": {"default": {"default": "großes I mit Caron"}}}, {"key": "01D1", "mappings": {"default": {"default": "großes O mit Caron"}}}, {"key": "01D3", "mappings": {"default": {"default": "großes U mit Caron"}}}, {"key": "01E6", "mappings": {"default": {"default": "großes G mit Caron"}}}, {"key": "01E8", "mappings": {"default": {"default": "großes K mit Caron"}}}, {"key": "01EA", "mappings": {"default": {"default": "großes O mit Ogonek"}}}, {"key": "01F4", "mappings": {"default": {"default": "großes G mit Akut"}}}, {"key": "01F8", "mappings": {"default": {"default": "großes N mit Gravis"}}}, {"key": "0200", "mappings": {"default": {"default": "großes A mit Doppelgravis"}}}, {"key": "0202", "mappings": {"default": {"default": "großes A mit umgekehrter Breve"}}}, {"key": "0204", "mappings": {"default": {"default": "großes E mit Doppelgravis"}}}, {"key": "0206", "mappings": {"default": {"default": "großes E mit umgekehrter Breve"}}}, {"key": "0208", "mappings": {"default": {"default": "großes I mit Doppelgravis"}}}, {"key": "020A", "mappings": {"default": {"default": "großes I mit umgekehrter Breve"}}}, {"key": "020C", "mappings": {"default": {"default": "großes O mit Doppelgravis"}}}, {"key": "020E", "mappings": {"default": {"default": "großes O mit umgekehrter Breve"}}}, {"key": "0210", "mappings": {"default": {"default": "großes R mit Doppelgravis"}}}, {"key": "0212", "mappings": {"default": {"default": "großes R mit umgekehrter Breve"}}}, {"key": "0214", "mappings": {"default": {"default": "großes U mit Doppelgravis"}}}, {"key": "0216", "mappings": {"default": {"default": "großes U mit umgekehrter Breve"}}}, {"key": "0218", "mappings": {"default": {"default": "großes S mit untenstehendem Komma"}}}, {"key": "021A", "mappings": {"default": {"default": "großes T mit untenstehendem Komma"}}}, {"key": "021E", "mappings": {"default": {"default": "großes H mit Caron"}}}, {"key": "0226", "mappings": {"default": {"default": "großes A mit Punkt oben"}}}, {"key": "0228", "mappings": {"default": {"default": "großes E mit Cedilla"}}}, {"key": "022E", "mappings": {"default": {"default": "großes O mit Punkt oben"}}}, {"key": "0232", "mappings": {"default": {"default": "großes Y mit Makron"}}}, {"key": "1E00", "mappings": {"default": {"default": "großes A mit Ring unten"}}}, {"key": "1E02", "mappings": {"default": {"default": "großes B mit Punkt oben"}}}, {"key": "1E04", "mappings": {"default": {"default": "großes B mit untenstehendem Punkt"}}}, {"key": "1E06", "mappings": {"default": {"default": "großes B mit Zeile darunter"}}}, {"key": "1E0A", "mappings": {"default": {"default": "großes D mit Punkt oben"}}}, {"key": "1E0C", "mappings": {"default": {"default": "großes D mit untenstehendem Punkt"}}}, {"key": "1E0E", "mappings": {"default": {"default": "großes D mit Zeile darunter"}}}, {"key": "1E10", "mappings": {"default": {"default": "großes D mit Cedilla"}}}, {"key": "1E12", "mappings": {"default": {"default": "großes D mit darunterem Zirkumflex"}}}, {"key": "1E18", "mappings": {"default": {"default": "großes E mit darunterem Zirkumflex"}}}, {"key": "1E1A", "mappings": {"default": {"default": "großes E mit darunterliegender Tilde"}}}, {"key": "1E1E", "mappings": {"default": {"default": "großes F mit Punkt oben"}}}, {"key": "1E20", "mappings": {"default": {"default": "großes G mit Makron"}}}, {"key": "1E22", "mappings": {"default": {"default": "großes H mit Punkt oben"}}}, {"key": "1E24", "mappings": {"default": {"default": "großes H mit untenstehendem Punkt"}}}, {"key": "1E26", "mappings": {"default": {"default": "großes H mit Diärese"}}}, {"key": "1E28", "mappings": {"default": {"default": "großes H mit Cedilla"}}}, {"key": "1E2A", "mappings": {"default": {"default": "großes H mit Breve unten"}}}, {"key": "1E2C", "mappings": {"default": {"default": "großes I mit unten stehender Tilde"}}}, {"key": "1E30", "mappings": {"default": {"default": "großes K mit Akut"}}}, {"key": "1E32", "mappings": {"default": {"default": "großes K mit untenstehendem Punkt"}}}, {"key": "1E34", "mappings": {"default": {"default": "großes K mit Zeile"}}}, {"key": "1E36", "mappings": {"default": {"default": "großes L mit Punkt unten"}}}, {"key": "1E3A", "mappings": {"default": {"default": "großes L mit Zeile darunter"}}}, {"key": "1E3C", "mappings": {"default": {"default": "großes L mit darunterem Zirkumflex"}}}, {"key": "1E3E", "mappings": {"default": {"default": "großes M mit Akut"}}}, {"key": "1E40", "mappings": {"default": {"default": "großes M mit Punkt oben"}}}, {"key": "1E42", "mappings": {"default": {"default": "großes M mit untenstehendem Punkt"}}}, {"key": "1E44", "mappings": {"default": {"default": "großes N mit Punkt oben"}}}, {"key": "1E46", "mappings": {"default": {"default": "großes N mit untenstehendem Punkt"}}}, {"key": "1E48", "mappings": {"default": {"default": "großes N mit Zeile darunter"}}}, {"key": "1E4A", "mappings": {"default": {"default": "großes N mit darunterem Zirkumflex"}}}, {"key": "1E54", "mappings": {"default": {"default": "großes P mit Akut"}}}, {"key": "1E56", "mappings": {"default": {"default": "großes P mit Punkt oben"}}}, {"key": "1E58", "mappings": {"default": {"default": "großes R mit Punkt oben"}}}, {"key": "1E5A", "mappings": {"default": {"default": "großes R mit untenstehendem Punkt"}}}, {"key": "1E5E", "mappings": {"default": {"default": "großes R mit Zeile darunter"}}}, {"key": "1E60", "mappings": {"default": {"default": "großes S mit Punkt oben"}}}, {"key": "1E62", "mappings": {"default": {"default": "großes S mit untenstehendem Punkt"}}}, {"key": "1E6A", "mappings": {"default": {"default": "großes T mit Punkt oben"}}}, {"key": "1E6C", "mappings": {"default": {"default": "großes T mit untenstehendem Punkt"}}}, {"key": "1E6E", "mappings": {"default": {"default": "großes T mit Zeile darunter"}}}, {"key": "1E70", "mappings": {"default": {"default": "großes T mit darunterem Zirkumflex"}}}, {"key": "1E72", "mappings": {"default": {"default": "großes U mit untenstehender Diärese"}}}, {"key": "1E74", "mappings": {"default": {"default": "großes U mit darunterliegender Tilde"}}}, {"key": "1E76", "mappings": {"default": {"default": "großes U mit darunterem Zirkumflex"}}}, {"key": "1E7C", "mappings": {"default": {"default": "großes V mit Tilde"}}}, {"key": "1E7E", "mappings": {"default": {"default": "großes V mit untenstehendem Punkt"}}}, {"key": "1E80", "mappings": {"default": {"default": "großes W mit Gravis"}}}, {"key": "1E82", "mappings": {"default": {"default": "großes W mit Akut"}}}, {"key": "1E84", "mappings": {"default": {"default": "großes W mit Diärese"}}}, {"key": "1E86", "mappings": {"default": {"default": "großes W mit Punkt oben"}}}, {"key": "1E88", "mappings": {"default": {"default": "großes W mit Punkt unten"}}}, {"key": "1E8A", "mappings": {"default": {"default": "großes X mit Punkt oben"}}}, {"key": "1E8C", "mappings": {"default": {"default": "großes X mit Diärese"}}}, {"key": "1E8E", "mappings": {"default": {"default": "großes Y mit Punkt oben"}}}, {"key": "1E90", "mappings": {"default": {"default": "großes Z mit Zirkumflex"}}}, {"key": "1E92", "mappings": {"default": {"default": "großes Z mit untenstehendem Punkt"}}}, {"key": "1E94", "mappings": {"default": {"default": "großes Z mit Zeile darunter"}}}, {"key": "1EA0", "mappings": {"default": {"default": "großes A mit Punkt unten"}}}, {"key": "1EA2", "mappings": {"default": {"default": "großes A mit Haken oben"}}}, {"key": "1EB8", "mappings": {"default": {"default": "großes E mit untenstehendem Punkt"}}}, {"key": "1EBA", "mappings": {"default": {"default": "großes E mit Haken oben"}}}, {"key": "1EBC", "mappings": {"default": {"default": "großes E mit Tilde"}}}, {"key": "1EC8", "mappings": {"default": {"default": "großes I mit Haken oben"}}}, {"key": "1ECA", "mappings": {"default": {"default": "großes I mit untenstehendem Punkt"}}}, {"key": "1ECC", "mappings": {"default": {"default": "großes O mit untenstehendem Punkt"}}}, {"key": "1ECE", "mappings": {"default": {"default": "großes O mit Haken oben"}}}, {"key": "1EE4", "mappings": {"default": {"default": "großes U mit untenstehendem Punkt"}}}, {"key": "1EE6", "mappings": {"default": {"default": "großes U mit Haken oben"}}}, {"key": "1EF2", "mappings": {"default": {"default": "großes Y mit Gravis"}}}, {"key": "1EF4", "mappings": {"default": {"default": "großes Y mit untenstehendem Punkt"}}}, {"key": "1EF6", "mappings": {"default": {"default": "großes Y mit Haken oben"}}}, {"key": "1EF8", "mappings": {"default": {"default": "großes Y mit Tilde"}}}], "de/symbols/math_angles.min": [{"locale": "de"}, {"key": "22BE", "mappings": {"default": {"default": "rechter Winkel mit Bogen"}}}, {"key": "237C", "mappings": {"default": {"default": "rechter Win<PERSON> mit nach unten gerichtetem Zickzackpfeil"}}}, {"key": "27C0", "mappings": {"default": {"default": "Dreidimen<PERSON><PERSON>"}}}, {"key": "299B", "mappings": {"default": {"default": "Gemessene <PERSON>ö<PERSON>nung links"}}}, {"key": "299C", "mappings": {"default": {"default": "Rechtwinklige Variante mit Quadrat"}}}, {"key": "299D", "mappings": {"default": {"default": "rechter Win<PERSON> mit Punkt gemessen"}}}, {"key": "299E", "mappings": {"default": {"default": "Winkel mit S nach innen"}}}, {"key": "299F", "mappings": {"default": {"default": "Spitzer Winkel"}}}, {"key": "29A0", "mappings": {"default": {"default": "Sphärischer Öffnungswinkel nach links"}}}, {"key": "29A1", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON>, der sich ö<PERSON>net"}}}, {"key": "29A2", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "29A3", "mappings": {"default": {"default": "Umgekehrter Winkel"}}}, {"key": "29A4", "mappings": {"default": {"default": "<PERSON><PERSON> mit Underbar"}}}, {"key": "29A5", "mappings": {"default": {"default": "Umgekehrter Winkel mit Unterleiste"}}}, {"key": "29A6", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>, der sich ö<PERSON>net"}}}, {"key": "29A7", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>, der sich nach unten öffnet"}}}, {"key": "29A8", "mappings": {"default": {"default": "Gemessener Winkel mit offenem Arm endet im Pfeil nach oben und rechts"}}}, {"key": "29A9", "mappings": {"default": {"default": "Gemessener Winkel mit offenem Arm und Pfeil nach oben und links"}}}, {"key": "29AA", "mappings": {"default": {"default": "Gemessener Winkel mit offenem Arm endet im nach unten zeigenden Pfeil und rechts"}}}, {"key": "29AB", "mappings": {"default": {"default": "Gemessener Winkel mit offenem Arm endet im nach unten zeigenden Pfeil"}}}, {"key": "29AC", "mappings": {"default": {"default": "Gemessener Winkel mit offenem Arm endet im Pfeil nach rechts und oben"}}}, {"key": "29AD", "mappings": {"default": {"default": "Gemessener Winkel mit offenem Arm endet im Pfeil nach links und oben"}}}, {"key": "29AE", "mappings": {"default": {"default": "Gemessener Winkel mit offenem Arm endet im nach rechts und unten zeigenden Pfeil"}}}, {"key": "29AF", "mappings": {"default": {"default": "Gemessener Winkel mit offenem Arm und Pfeil nach links und unten"}}}], "de/symbols/math_arrows.min": [{"locale": "de"}, {"key": "2190", "mappings": {"default": {"default": "Pfeil nach links"}}}, {"key": "2191", "mappings": {"default": {"default": "Aufwärtspfeil"}}}, {"key": "2192", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> nach rechts"}}}, {"key": "2193", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> nach unten"}}}, {"key": "2194", "mappings": {"default": {"default": "<PERSON><PERSON> <PERSON>chter Pfeil"}}}, {"key": "2195", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> nach oben"}}}, {"key": "2196", "mappings": {"default": {"default": "Nordwestpfeil"}}}, {"key": "2197", "mappings": {"default": {"default": "Nordostpfeil"}}}, {"key": "2198", "mappings": {"default": {"default": "Südostpfeil"}}}, {"key": "2199", "mappings": {"default": {"default": "Südwestpfeil"}}}, {"key": "219A", "mappings": {"default": {"default": "Pfeil nach links mit Strich"}}}, {"key": "219B", "mappings": {"default": {"default": "Pfeil nach rechts mit Strich"}}}, {"key": "219C", "mappings": {"default": {"default": "Wellenpfeil nach links"}}}, {"key": "219D", "mappings": {"default": {"default": "Wellenpfeil nach rechts"}}}, {"key": "219E", "mappings": {"default": {"default": "Pfeil nach links mit zwei Spitzen"}}}, {"key": "219F", "mappings": {"default": {"default": "Aufwärts zwei Pfeil nach unten"}}}, {"key": "21A0", "mappings": {"default": {"default": "Pfeil nach rechts mit zwei Köpfen"}}}, {"key": "21A1", "mappings": {"default": {"default": "Abwärts zwei Pfeil nach unten"}}}, {"key": "21A2", "mappings": {"default": {"default": "Pfeil nach links mit Schwanz"}}}, {"key": "21A3", "mappings": {"default": {"default": "P<PERSON>il nach rechts mit Schwanz"}}}, {"key": "21A4", "mappings": {"default": {"default": "Pfeil nach links von der Bar"}}}, {"key": "21A5", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> von der Bar nach oben"}}}, {"key": "21A6", "mappings": {"default": {"default": "Pfeil nach rechts von der Bar"}}}, {"key": "21A7", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> von der Bar nach unten"}}}, {"key": "21A8", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> nach unten mit Basis"}}}, {"key": "21A9", "mappings": {"default": {"default": "Pfeil nach links mit Haken"}}}, {"key": "21AA", "mappings": {"default": {"default": "P<PERSON>il nach rechts mit Haken"}}}, {"key": "21AB", "mappings": {"default": {"default": "Pfeil nach links mit Schleife"}}}, {"key": "21AC", "mappings": {"default": {"default": "Pfeil nach rechts mit Schleife"}}}, {"key": "21AD", "mappings": {"default": {"default": "<PERSON><PERSON> <PERSON>chter Wellenpfeil"}}}, {"key": "21AE", "mappings": {"default": {"default": "<PERSON><PERSON> rechter Pfeil mit Strich"}}}, {"key": "21AF", "mappings": {"default": {"default": "Zickzack-Pfeil nach unten"}}}, {"key": "21B0", "mappings": {"default": {"default": "Aufwärtspfeil mit Spitze nach links"}}}, {"key": "21B1", "mappings": {"default": {"default": "Aufwärtspfeil mit Spitze nach rechts"}}}, {"key": "21B2", "mappings": {"default": {"default": "Pfeil nach unten mit Spitze nach links"}}}, {"key": "21B3", "mappings": {"default": {"default": "Abwärtspfeil mit Spitze nach rechts"}}}, {"key": "21B4", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> nach rechts mit <PERSON>cke nach unten"}}}, {"key": "21B5", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> nach unten mit <PERSON> nach links"}}}, {"key": "21B6", "mappings": {"default": {"default": "Gegen den Uhrzeigersinn oberer halbkreisförmiger Pfeil"}}}, {"key": "21B7", "mappings": {"default": {"default": "Halbkreis im Uhrzeigersinn"}}}, {"key": "21B8", "mappings": {"default": {"default": "Nordwestpfeil zur langen Bar"}}}, {"key": "21B9", "mappings": {"default": {"default": "Pfeil nach links zum Überfahren Pfeil nach rechts zum Überfahren"}}}, {"key": "21BA", "mappings": {"default": {"default": "Offener Kreispfeil gegen den Uhrzeigersinn"}}}, {"key": "21BB", "mappings": {"default": {"default": "Kreispfeil im Uhrzeigersinn öffnen"}}}, {"key": "21C4", "mappings": {"default": {"default": "Pfeil nach rechts über Pfeil nach links"}}}, {"key": "21C5", "mappings": {"default": {"default": "Aufwärtspfeil nach links von Abwärtspfeil"}}}, {"key": "21C6", "mappings": {"default": {"default": "Pfeil nach links über Pfeil nach rechts"}}}, {"key": "21C7", "mappings": {"default": {"default": "Links gepaarte Pfeile"}}}, {"key": "21C8", "mappings": {"default": {"default": "Aufwärts gepaarte Pfeile"}}}, {"key": "21C9", "mappings": {"default": {"default": "Rechts gepaarte Pfeile"}}}, {"key": "21CA", "mappings": {"default": {"default": "Abwärts gepaarte Pfeile"}}}, {"key": "21CD", "mappings": {"default": {"default": "Doppelpfeil nach links mit Strich"}}}, {"key": "21CE", "mappings": {"default": {"default": "Links Rechts Doppelpfeil mit Strich"}}}, {"key": "21CF", "mappings": {"default": {"default": "Doppelpfeil nach rechts mit Strich"}}}, {"key": "21D0", "mappings": {"default": {"default": "Doppelpfeil nach links"}}}, {"key": "21D1", "mappings": {"default": {"default": "Doppelpfeil nach oben"}}}, {"key": "21D2", "mappings": {"default": {"default": "Doppelpfeil nach rechts"}}}, {"key": "21D3", "mappings": {"default": {"default": "Doppelpfeil nach unten"}}}, {"key": "21D4", "mappings": {"default": {"default": "<PERSON><PERSON> <PERSON>chter Doppelpfeil"}}}, {"key": "21D5", "mappings": {"default": {"default": "Doppelpfeil nach unten"}}}, {"key": "21D6", "mappings": {"default": {"default": "Nordwest-Doppelpfeil"}}}, {"key": "21D7", "mappings": {"default": {"default": "Nordost-Doppelpfeil"}}}, {"key": "21D8", "mappings": {"default": {"default": "Südost Doppelpfeil"}}}, {"key": "21D9", "mappings": {"default": {"default": "Südwest-Doppelpfeil"}}}, {"key": "21DA", "mappings": {"default": {"default": "Dreifacher Pfeil nach links"}}}, {"key": "21DB", "mappings": {"default": {"default": "Dreifacher Pfeil nach rechts"}}}, {"key": "21DC", "mappings": {"default": {"default": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>"}}}, {"key": "21DD", "mappings": {"default": {"default": "Nach rechts gekrümmter Pfeil"}}}, {"key": "21DE", "mappings": {"default": {"default": "Aufwärtspfeil mit Doppelstrich"}}}, {"key": "21DF", "mappings": {"default": {"default": "Abwärtspfeil mit Doppelstrich"}}}, {"key": "21E0", "mappings": {"default": {"default": "P<PERSON>il nach links nach links"}}}, {"key": "21E1", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> nach oben gestrichen"}}}, {"key": "21E2", "mappings": {"default": {"default": "Rechts gestrichener Pfeil"}}}, {"key": "21E3", "mappings": {"default": {"default": "Abwärts gestrichelter Pfeil"}}}, {"key": "21E4", "mappings": {"default": {"default": "Pfeil nach links zur Bar"}}}, {"key": "21E5", "mappings": {"default": {"default": "Pfeil nach rechts zur Bar"}}}, {"key": "21E6", "mappings": {"default": {"default": "Weißer Pfeil nach links"}}}, {"key": "21E7", "mappings": {"default": {"default": "Aufwärts weißer Pfeil"}}}, {"key": "21E8", "mappings": {"default": {"default": "Rechts weißer Pfeil"}}}, {"key": "21E9", "mappings": {"default": {"default": "Abwärts weißer Pfeil"}}}, {"key": "21EA", "mappings": {"default": {"default": "Aufwärts weißer P<PERSON>il von der <PERSON>"}}}, {"key": "21EB", "mappings": {"default": {"default": "Aufwärts weißer Pfeil auf So<PERSON>l"}}}, {"key": "21EC", "mappings": {"default": {"default": "Aufwärts weißer Pfeil auf Sockel mit horizontaler Stange"}}}, {"key": "21ED", "mappings": {"default": {"default": "Aufwärts weißer Pfeil auf Sockel mit vertikaler Stange"}}}, {"key": "21EE", "mappings": {"default": {"default": "Aufwärts weißer Doppelpfeil"}}}, {"key": "21EF", "mappings": {"default": {"default": "Aufwärts weißer doppelter Pfeil auf Sockel"}}}, {"key": "21F0", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> weiße<PERSON> <PERSON><PERSON><PERSON>"}}}, {"key": "21F1", "mappings": {"default": {"default": "Nordwestpfeil zur Ecke"}}}, {"key": "21F2", "mappings": {"default": {"default": "Südostpfeil zur Ecke"}}}, {"key": "21F3", "mappings": {"default": {"default": "<PERSON><PERSON>r Pfeil nach unten"}}}, {"key": "21F4", "mappings": {"default": {"default": "rechter Pfeil mit kleinem Kreis"}}}, {"key": "21F5", "mappings": {"default": {"default": "Abwärtspfeil nach links von Aufwärtspfeil"}}}, {"key": "21F6", "mappings": {"default": {"default": "Drei nach rechts gerichtete Pfeile"}}}, {"key": "21F7", "mappings": {"default": {"default": "Pfeil nach links mit vertikalem Strich"}}}, {"key": "21F8", "mappings": {"default": {"default": "Pfeil nach rechts mit vertikalem Strich"}}}, {"key": "21F9", "mappings": {"default": {"default": "<PERSON><PERSON> rechter Pfeil mit vertikalem Strich"}}}, {"key": "21FA", "mappings": {"default": {"default": "Pfeil nach links mit doppeltem vertikalem Strich"}}}, {"key": "21FB", "mappings": {"default": {"default": "Pfeil nach rechts mit doppeltem vertikalem Strich"}}}, {"key": "21FC", "mappings": {"default": {"default": "<PERSON><PERSON> rechter Pfeil mit doppeltem vertikalem Strich"}}}, {"key": "21FD", "mappings": {"default": {"default": "Pfeil nach links mit offenem Kopf"}}}, {"key": "21FE", "mappings": {"default": {"default": "P<PERSON>il nach rechts mit offenem Kopf"}}}, {"key": "21FF", "mappings": {"default": {"default": "<PERSON><PERSON> rechter Pfeil mit offenem Kopf"}}}, {"key": "2301", "mappings": {"default": {"default": "Elektrischer Pfeil"}}}, {"key": "2303", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> nach oben"}}}, {"key": "2304", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> nach unten"}}}, {"key": "2324", "mappings": {"default": {"default": "Pfeilspitze zwischen zwei horizontalen Balken"}}}, {"key": "238B", "mappings": {"default": {"default": "Gebrochener Kreis mit Nordwestpfeil"}}}, {"key": "2794", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>, nach rechts gerichteter Pfeil nach rechts"}}}, {"key": "2798", "mappings": {"default": {"default": "Schwerer Südostpfeil"}}}, {"key": "2799", "mappings": {"default": {"default": "Schwerer Pfeil nach rechts"}}}, {"key": "279A", "mappings": {"default": {"default": "Schwerer Nordostpfeil"}}}, {"key": "279B", "mappings": {"default": {"default": "Zeichnungspunkt Pfeil nach rechts"}}}, {"key": "279C", "mappings": {"default": {"default": "Schwerer runder Pfeil nach rechts"}}}, {"key": "279D", "mappings": {"default": {"default": "Dreieckköpfiger Pfeil nach rechts"}}}, {"key": "279E", "mappings": {"default": {"default": "Schwerer Dreieckspfeil nach rechts"}}}, {"key": "279F", "mappings": {"default": {"default": "Dreieckköpfiger Pfeil nach rechts"}}}, {"key": "27A0", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>, gest<PERSON><PERSON>ter Dreieckspfeil nach rechts"}}}, {"key": "27A1", "mappings": {"default": {"default": "Schwarzer Pfeil nach rechts"}}}, {"key": "27A2", "mappings": {"default": {"default": "Dreidimensionale, nach oben beleuchtete Pfeilspitze"}}}, {"key": "27A3", "mappings": {"default": {"default": "Dreidimen<PERSON><PERSON>, von unten beleuchtete Pfeilspitze nach rechts"}}}, {"key": "27A4", "mappings": {"default": {"default": "Schwarze Pfeilspitze nach rechts"}}}, {"key": "27A5", "mappings": {"default": {"default": "Schwerer schwarzer Pfeil nach unten und rechts gebogen"}}}, {"key": "27A6", "mappings": {"default": {"default": "Schwerer schwarzer Pfeil nach oben und rechts gebogen"}}}, {"key": "27A7", "mappings": {"default": {"default": "Schwarzer nach rechts gerichteter Pfeil"}}}, {"key": "27A8", "mappings": {"default": {"default": "Schwerer konkaver Spitze-Pfeil nach rechts"}}}, {"key": "27A9", "mappings": {"default": {"default": "Rechts geschatteter weißer Pfeil nach rechts"}}}, {"key": "27AA", "mappings": {"default": {"default": "<PERSON><PERSON> schattierter weißer Pfeil nach rechts"}}}, {"key": "27AB", "mappings": {"default": {"default": "Zurückgekippter, beschatteter weißer Pfeil nach rechts"}}}, {"key": "27AC", "mappings": {"default": {"default": "<PERSON><PERSON>e geneig<PERSON>, beschatteter weißer Pfeil nach rechts"}}}, {"key": "27AD", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>er unterer rechter Schatten mit weißem Pfeil nach rechts"}}}, {"key": "27AE", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>er oberer rechter Schatten mit weißem Pfeil nach rechts"}}}, {"key": "27AF", "mappings": {"default": {"default": "Gekerbter Pfeil nach rechts unten rechts"}}}, {"key": "27B1", "mappings": {"default": {"default": "<PERSON><PERSON>r Pfeil nach rechts oben gekerbt"}}}, {"key": "27B2", "mappings": {"default": {"default": "Eingekreister schwerer weißer Pfeil nach rechts"}}}, {"key": "27B3", "mappings": {"default": {"default": "<PERSON><PERSON>il mit weißer Feder nach rechts"}}}, {"key": "27B4", "mappings": {"default": {"default": "Schwarz-gefiederter Südostpfeil"}}}, {"key": "27B5", "mappings": {"default": {"default": "P<PERSON>il mit schwarzer Feder nach rechts"}}}, {"key": "27B6", "mappings": {"default": {"default": "Schwarzgefiederter Nordostpfeil"}}}, {"key": "27B7", "mappings": {"default": {"default": "Schwerer schwarzgefiederter Südostpfeil"}}}, {"key": "27B8", "mappings": {"default": {"default": "Schwerer schwarzgefiederter Pfeil nach rechts"}}}, {"key": "27B9", "mappings": {"default": {"default": "Schwerer schwarzgefiederter Nordostpfeil"}}}, {"key": "27BA", "mappings": {"default": {"default": "Teardrop-Barbed-Pfeil nach rechts"}}}, {"key": "27BB", "mappings": {"default": {"default": "Schwerer Tropfen-Pfeil nach rechts"}}}, {"key": "27BC", "mappings": {"default": {"default": "Keil-Schwanz nach rechts Pfeil"}}}, {"key": "27BD", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>, nach rechts gekeilter Pfeil"}}}, {"key": "27BE", "mappings": {"default": {"default": "Offener Pfeil nach rechts"}}}, {"key": "27F0", "mappings": {"default": {"default": "Vierfachpfeil nach oben"}}}, {"key": "27F1", "mappings": {"default": {"default": "Vierfacher Pfeil nach unten"}}}, {"key": "27F2", "mappings": {"default": {"default": "Kreispfeil gegen den Uhrzeigersinn"}}}, {"key": "27F3", "mappings": {"default": {"default": "Kreispfeil im Uhrzeigersinn"}}}, {"key": "27F4", "mappings": {"default": {"default": "rechter Pfeil mit Circled Plus"}}}, {"key": "27F5", "mappings": {"default": {"default": "Langer Pfeil nach links"}}}, {"key": "27F6", "mappings": {"default": {"default": "<PERSON><PERSON> P<PERSON>il nach rechts"}}}, {"key": "27F7", "mappings": {"default": {"default": "<PERSON><PERSON> linker rechter Pfeil"}}}, {"key": "27F8", "mappings": {"default": {"default": "Langer Doppelpfeil nach links"}}}, {"key": "27F9", "mappings": {"default": {"default": "Langer Doppelpfeil nach rechts"}}}, {"key": "27FA", "mappings": {"default": {"default": "<PERSON><PERSON> linker rechter Doppelpfeil"}}}, {"key": "27FB", "mappings": {"default": {"default": "Langer Pfeil nach links von der Bar"}}}, {"key": "27FC", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "27FD", "mappings": {"default": {"default": "Langer Doppelpfeil nach links von der Bar"}}}, {"key": "27FE", "mappings": {"default": {"default": "Langer Doppelpfeil nach rechts von der Bar"}}}, {"key": "27FF", "mappings": {"default": {"default": "Langer nach rechts gerichteter Squiggle-Pfeil"}}}, {"key": "2900", "mappings": {"default": {"default": "Pfeil nach rechts mit senkrechtem Strich"}}}, {"key": "2901", "mappings": {"default": {"default": "Zwei Pfeil nach rechts mit doppeltem vertikalem Strich"}}}, {"key": "2902", "mappings": {"default": {"default": "Doppelpfeil nach links mit vertikalem Strich"}}}, {"key": "2903", "mappings": {"default": {"default": "Doppelpfeil nach rechts mit vertikalem Strich"}}}, {"key": "2904", "mappings": {"default": {"default": "Linker rechter Doppelpfeil mit vertikalem Strich"}}}, {"key": "2905", "mappings": {"default": {"default": "Zwei Pfeil nach rechts von der Bar"}}}, {"key": "2906", "mappings": {"default": {"default": "Doppelpfeil nach links von der Bar"}}}, {"key": "2907", "mappings": {"default": {"default": "Doppelter Pfeil nach rechts von der Bar"}}}, {"key": "2908", "mappings": {"default": {"default": "Abwärtspfeil mit horizontalem Strich"}}}, {"key": "2909", "mappings": {"default": {"default": "Aufwärtspfeil mit horizontalem Anschlag"}}}, {"key": "290A", "mappings": {"default": {"default": "Dreifachpfeil nach oben"}}}, {"key": "290B", "mappings": {"default": {"default": "Dreifacher Pfeil nach unten"}}}, {"key": "290C", "mappings": {"default": {"default": "Doppelpfeil nach links"}}}, {"key": "290D", "mappings": {"default": {"default": "Doppelpfeil nach rechts"}}}, {"key": "290E", "mappings": {"default": {"default": "Dreifacher Strichpfeil nach links"}}}, {"key": "290F", "mappings": {"default": {"default": "Dreifacher Bindestrich nach rechts"}}}, {"key": "2910", "mappings": {"default": {"default": "Dreipoliger Pfeil nach rechts"}}}, {"key": "2911", "mappings": {"default": {"default": "Pfeil nach rechts mit gepunktetem Stamm"}}}, {"key": "2912", "mappings": {"default": {"default": "Aufwärtspfeil zur Bar"}}}, {"key": "2913", "mappings": {"default": {"default": "Abwärtspfeil zur Bar"}}}, {"key": "2914", "mappings": {"default": {"default": "Pfeil nach rechts mit Schwanz mit senkrechtem Anschlag"}}}, {"key": "2915", "mappings": {"default": {"default": "Pfeil nach rechts mit Schwanz mit doppeltem vertikalem Strich"}}}, {"key": "2916", "mappings": {"default": {"default": "Nach rechts gerichteter zweiköpfiger Pfeil mit Schwanz"}}}, {"key": "2917", "mappings": {"default": {"default": "Nach rechts gerichteter Doppelpfeil mit Schwanz mit senkrechtem Anschlag"}}}, {"key": "2918", "mappings": {"default": {"default": "Nach rechts gerichteter Doppelpfeil mit Schwanz mit doppeltem vertikalem Strich"}}}, {"key": "2919", "mappings": {"default": {"default": "P<PERSON>il nach links nach links"}}}, {"key": "291A", "mappings": {"default": {"default": "<PERSON><PERSON>il nach rechts nach rechts"}}}, {"key": "291B", "mappings": {"default": {"default": "Doppelter Pfeilschwanz nach links"}}}, {"key": "291C", "mappings": {"default": {"default": "Doppelter Pfeilschwanz nach rechts"}}}, {"key": "291D", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> nach links zu Black Diamond"}}}, {"key": "291E", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> nach rechts zu Black Diamond"}}}, {"key": "291F", "mappings": {"default": {"default": "<PERSON><PERSON>il nach links von Bar zu <PERSON> Diamond"}}}, {"key": "2920", "mappings": {"default": {"default": "<PERSON><PERSON>il nach rechts von Bar zu Black Diamond"}}}, {"key": "2921", "mappings": {"default": {"default": "Nordwest- und Südostpfeil"}}}, {"key": "2922", "mappings": {"default": {"default": "Nordost- und Südwestpfeil"}}}, {"key": "2923", "mappings": {"default": {"default": "Nordwestpfeil mit Haken"}}}, {"key": "2924", "mappings": {"default": {"default": "Nordostpfeil mit Haken"}}}, {"key": "2925", "mappings": {"default": {"default": "Südostpfeil mit Haken"}}}, {"key": "2926", "mappings": {"default": {"default": "Südwestpfeil mit Haken"}}}, {"key": "2927", "mappings": {"default": {"default": "Nordwestpfeil und Nordostpfeil"}}}, {"key": "2928", "mappings": {"default": {"default": "Nordostpfeil und Südostpfeil"}}}, {"key": "2929", "mappings": {"default": {"default": "Südostpfeil und Südwestpfeil"}}}, {"key": "292A", "mappings": {"default": {"default": "Südwestpfeil und Nordwestpfeil"}}}, {"key": "292D", "mappings": {"default": {"default": "Südostpfeil Kreuzung Nordostpfeil"}}}, {"key": "292E", "mappings": {"default": {"default": "Nordostpfeil Kreuzung Südostpfeil"}}}, {"key": "292F", "mappings": {"default": {"default": "Fallende Diagonale, die Nordostpfeil kreuzt"}}}, {"key": "2930", "mappings": {"default": {"default": "Steigende Diagonale, die Südostpfeil kreuzt"}}}, {"key": "2931", "mappings": {"default": {"default": "Nordostpfeilüberquerung Nordwestpfeil"}}}, {"key": "2932", "mappings": {"default": {"default": "Nordwestpfeilüberquerung Nordostpfeil"}}}, {"key": "2933", "mappings": {"default": {"default": "Wellenpfeil, der direkt nach rechts zeigt"}}}, {"key": "2934", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> nach rechts, dann nach oben gekrümmt"}}}, {"key": "2935", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> nach rechts, dann nach unten gekrümmt"}}}, {"key": "2936", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> nach unten, dann nach links gekr<PERSON><PERSON>t"}}}, {"key": "2937", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> nach unten, dann nach rechts gekrümmt"}}}, {"key": "2938", "mappings": {"default": {"default": "Rechtsseitiger Bogen im Uhrzeigersinn"}}}, {"key": "2939", "mappings": {"default": {"default": "Bogen der linken Seite gegen den Uhrzeigersinn"}}}, {"key": "293A", "mappings": {"default": {"default": "Pfeil oben gegen den Uhrzeigersinn"}}}, {"key": "293B", "mappings": {"default": {"default": "P<PERSON>il unten gegen den Uhrzeigersinn"}}}, {"key": "293C", "mappings": {"default": {"default": "Pfeil oben im Uhrzeigersinn mit Minus"}}}, {"key": "293D", "mappings": {"default": {"default": "Pfeil oben gegen den Uhrzeigersinn mit Plus"}}}, {"key": "293E", "mappings": {"default": {"default": "Rechts unten halbkreisförmig im Uhrzeigersinn"}}}, {"key": "293F", "mappings": {"default": {"default": "Linker halbkreisförmiger Pfeil gegen den Uhrzeigersinn"}}}, {"key": "2940", "mappings": {"default": {"default": "Geschlossener Kreispfeil gegen den Uhrzeigersinn"}}}, {"key": "2941", "mappings": {"default": {"default": "Geschlossener Kreispfeil im Uhrzeigersinn"}}}, {"key": "2942", "mappings": {"default": {"default": "Pfeil nach rechts über Kurzer Pfeil nach links"}}}, {"key": "2943", "mappings": {"default": {"default": "Pfeil nach links über Kurzer Pfeil nach rechts"}}}, {"key": "2944", "mappings": {"default": {"default": "Kurzer Pfeil nach rechts Über Pfeil nach links"}}}, {"key": "2945", "mappings": {"default": {"default": "Pfeil nach rechts mit Plus unten"}}}, {"key": "2946", "mappings": {"default": {"default": "Pfeil nach links mit Plus unten"}}}, {"key": "2947", "mappings": {"default": {"default": "Pfeil nach rechts durch X"}}}, {"key": "2948", "mappings": {"default": {"default": "Linker rechter Pfeil durch kleinen Kreis"}}}, {"key": "2949", "mappings": {"default": {"default": "Aufwärts zweiköpfiger Pfeil vom kleinen Kreis"}}}, {"key": "2970", "mappings": {"default": {"default": "round implies"}}}, {"key": "2971", "mappings": {"default": {"default": "Gleichheitszeichen über dem Pfeil nach rechts"}}}, {"key": "2972", "mappings": {"default": {"default": "Tilde-Operator über dem Pfeil nach rechts"}}}, {"key": "2973", "mappings": {"default": {"default": "Pfeil nach links über Tilde-Operator"}}}, {"key": "2974", "mappings": {"default": {"default": "Pfeil nach rechts über Tilde-Operator"}}}, {"key": "2975", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> nach rechts fast gleich"}}}, {"key": "2976", "mappings": {"default": {"default": "<PERSON><PERSON> als oben Pfeil nach links"}}}, {"key": "2977", "mappings": {"default": {"default": "Pfeil nach links durch weniger als"}}}, {"key": "2978", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>r als über dem Pfeil nach rechts"}}}, {"key": "2979", "mappings": {"default": {"default": "Untersatz über dem Pfeil nach rechts"}}}, {"key": "297A", "mappings": {"default": {"default": "Pfeil nach links durch Teilmenge"}}}, {"key": "297B", "mappings": {"default": {"default": "Superset über dem Pfeil nach links"}}}, {"key": "29B3", "mappings": {"default": {"default": "<PERSON><PERSON> Set mit Pfeil rechts oben"}}}, {"key": "29B4", "mappings": {"default": {"default": "<PERSON><PERSON> Set mit linkem Pfeil oben"}}}, {"key": "29BD", "mappings": {"default": {"default": "Pfeil nach oben durch Kreis"}}}, {"key": "29EA", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> Diamant mit Pfeil nach unten"}}}, {"key": "29EC", "mappings": {"default": {"default": "Weißer Kreis mit Pfeil nach unten"}}}, {"key": "29ED", "mappings": {"default": {"default": "Schwarzer Kreis mit Pfeil nach unten"}}}, {"key": "2A17", "mappings": {"default": {"default": "Integral mit Pfeil nach links mit Haken"}}}, {"key": "2B00", "mappings": {"default": {"default": "North East White Arrow"}}}, {"key": "2B01", "mappings": {"default": {"default": "Nordwestlicher weißer Pfeil"}}}, {"key": "2B02", "mappings": {"default": {"default": "Südostweißer Pfeil"}}}, {"key": "2B03", "mappings": {"default": {"default": "Südwestweißer Pfeil"}}}, {"key": "2B04", "mappings": {"default": {"default": "<PERSON><PERSON> rechter weißer Pfeil"}}}, {"key": "2B05", "mappings": {"default": {"default": "Schwarzer Pfeil nach links"}}}, {"key": "2B06", "mappings": {"default": {"default": "Aufwärts schwarzer Pfeil"}}}, {"key": "2B07", "mappings": {"default": {"default": "Abwärts schwarzer Pfeil"}}}, {"key": "2B08", "mappings": {"default": {"default": "Schwarzer Nordostpfeil"}}}, {"key": "2B09", "mappings": {"default": {"default": "Nordwestlicher schwarzer Pfeil"}}}, {"key": "2B0A", "mappings": {"default": {"default": "Schwarzer Südostpfeil"}}}, {"key": "2B0B", "mappings": {"default": {"default": "Südwestlicher schwarzer Pfeil"}}}, {"key": "2B0C", "mappings": {"default": {"default": "<PERSON><PERSON> rechter schwarzer Pfeil"}}}, {"key": "2B0D", "mappings": {"default": {"default": "Schwarzer Pfeil nach unten"}}}, {"key": "2B0E", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> nach rechts mit Spitze nach unten"}}}, {"key": "2B0F", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> nach rechts mit Spitze nach oben"}}}, {"key": "2B10", "mappings": {"default": {"default": "P<PERSON>il nach links mit Spitze nach unten"}}}, {"key": "2B11", "mappings": {"default": {"default": "Pfeil nach links mit Spitze nach oben"}}}, {"key": "2B30", "mappings": {"default": {"default": "Linker Pfeil mit kleinem Kreis"}}}, {"key": "2B31", "mappings": {"default": {"default": "Drei nach links gerichtete Pfeile"}}}, {"key": "2B32", "mappings": {"default": {"default": "Linker Pfeil mit Circled Plus"}}}, {"key": "2B33", "mappings": {"default": {"default": "Langer nach links gerichteter Squiggle-Pfeil"}}}, {"key": "2B34", "mappings": {"default": {"default": "Pfeil nach links mit senkrechtem Strich"}}}, {"key": "2B35", "mappings": {"default": {"default": "Doppelpfeil nach links mit doppeltem vertikalem Strich"}}}, {"key": "2B36", "mappings": {"default": {"default": "Doppelpfeil nach links von der Bar"}}}, {"key": "2B37", "mappings": {"default": {"default": "Pfeil nach links mit zwei Köpfen"}}}, {"key": "2B38", "mappings": {"default": {"default": "Pfeil nach links mit gepunktetem Stamm"}}}, {"key": "2B39", "mappings": {"default": {"default": "P<PERSON>il nach links mit Schwanz mit vertikalem Strich"}}}, {"key": "2B3A", "mappings": {"default": {"default": "Pfeil nach links mit Schwanz mit doppeltem vertikalem Strich"}}}, {"key": "2B3B", "mappings": {"default": {"default": "Nach links gerichteter zweiköpfiger Pfeil mit Schwanz"}}}, {"key": "2B3C", "mappings": {"default": {"default": "Nach links gerichteter zweiköpfiger Pfeil mit Schwanz mit vertikalem Strich"}}}, {"key": "2B3D", "mappings": {"default": {"default": "Nach links gerichteter zweiköpfiger Pfeil mit Schwanz mit doppeltem vertikalem Strich"}}}, {"key": "2B3E", "mappings": {"default": {"default": "Pfeil nach links durch X"}}}, {"key": "2B3F", "mappings": {"default": {"default": "Wellenpfeil, der direkt nach links zeigt"}}}, {"key": "2B40", "mappings": {"default": {"default": "Gleichheitszeichen über dem Pfeil nach links"}}}, {"key": "2B41", "mappings": {"default": {"default": "Reverse-Tilde-Operator Über Linkspfeil"}}}, {"key": "2B42", "mappings": {"default": {"default": "Pfeil nach links über der Rückseite fast gleich"}}}, {"key": "2B43", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> nach rechts durch mehr als"}}}, {"key": "2B44", "mappings": {"default": {"default": "Pfeil nach rechts durch Superset"}}}, {"key": "2B45", "mappings": {"default": {"default": "Vierfachpfeil nach links"}}}, {"key": "2B46", "mappings": {"default": {"default": "Vierfachpfeil nach rechts"}}}, {"key": "2B47", "mappings": {"default": {"default": "Reverse-Tilde-Operator über dem Pfeil nach rechts"}}}, {"key": "2B48", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> nach rechts oben fast gleich"}}}, {"key": "2B49", "mappings": {"default": {"default": "Tilde-Operator über dem Pfeil nach links"}}}, {"key": "2B4A", "mappings": {"default": {"default": "<PERSON><PERSON>il nach links oben fast gleich"}}}, {"key": "2B4B", "mappings": {"default": {"default": "Pfeil nach links über Reverse-Tilde-Operator"}}}, {"key": "2B4C", "mappings": {"default": {"default": "Pfeil nach rechts über Reverse-Tilde-Operator"}}}, {"key": "FFE9", "mappings": {"default": {"default": "Halbbreiter Pfeil nach links"}}}, {"key": "FFEA", "mappings": {"default": {"default": "Halbbreiter Pfeil nach oben"}}}, {"key": "FFEB", "mappings": {"default": {"default": "Halbbreiter Pfeil nach rechts"}}}, {"key": "FFEC", "mappings": {"default": {"default": "Halbbreiter Pfeil nach unten"}}}], "de/symbols/math_characters.min": [{"locale": "de"}, {"key": "2113", "mappings": {"default": {"default": "Schreibschrift l"}}}, {"key": "2118", "mappings": {"default": {"default": "großes Schreibschrift P"}}}, {"key": "213C", "mappings": {"default": {"default": "pi mit <PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "213D", "mappings": {"default": {"default": "gamma mit Doppelstrich"}}}, {"key": "213E", "mappings": {"default": {"default": "großes Gamma mit Doppelstrich"}}}, {"key": "213F", "mappings": {"default": {"default": "großes Pi mit Doppelstrich"}}}, {"key": "2140", "mappings": {"default": {"default": "Summenzeichen mit Doppelstrich"}}}, {"key": "2145", "mappings": {"default": {"default": "großes D kursiv mit Doppelstrich"}}}, {"key": "2146", "mappings": {"default": {"default": "d kursiv mit Doppelstrich"}}}, {"key": "2147", "mappings": {"default": {"default": "e kursiv mit Doppelstrich"}}}, {"key": "2148", "mappings": {"default": {"default": "i kursiv mit Doppelstrich"}}}, {"key": "2149", "mappings": {"default": {"default": "j kursiv mit Doppelstrich"}}}, {"key": "1D6A4", "mappings": {"default": {"default": "punktloses i kursiv"}}}, {"key": "1D6A5", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> j kursiv"}}}], "de/symbols/math_delimiters.min": [{"locale": "de"}, {"key": "0028", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> auf"}}}, {"key": "0029", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> zu"}}}, {"key": "005B", "mappings": {"default": {"default": "e<PERSON><PERSON> auf"}}}, {"key": "005D", "mappings": {"default": {"default": "e<PERSON><PERSON> zu"}}}, {"key": "007B", "mappings": {"default": {"default": "geschwungene Klammer auf"}}}, {"key": "007D", "mappings": {"default": {"default": "geschwunge<PERSON> Klammer zu"}}}, {"key": "2045", "mappings": {"default": {"default": "<PERSON><PERSON> eckige Klammer mit Stachel"}}}, {"key": "2046", "mappings": {"default": {"default": "<PERSON>chte eckige Klammer mit Stachel"}}}, {"key": "2308", "mappings": {"default": {"default": "linke Aufrundungsklammer"}}}, {"key": "2309", "mappings": {"default": {"default": "rechte Aufrundungsklammer"}}}, {"key": "230A", "mappings": {"default": {"default": "linke Abrundungsklammer"}}}, {"key": "230B", "mappings": {"default": {"default": "rechter Abrundungsklammer"}}}, {"key": "230C", "mappings": {"default": {"default": "unterer rechter Abschnitt"}}}, {"key": "230D", "mappings": {"default": {"default": "Unterer linker Abschnitt"}}}, {"key": "230E", "mappings": {"default": {"default": "<PERSON><PERSON>er rechter Abschnitt"}}}, {"key": "230F", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> linker Abschnitt"}}}, {"key": "231C", "mappings": {"default": {"default": "<PERSON>bere linke <PERSON>"}}}, {"key": "231D", "mappings": {"default": {"default": "<PERSON>bere rechte <PERSON>"}}}, {"key": "231E", "mappings": {"default": {"default": "Untere linke <PERSON>"}}}, {"key": "231F", "mappings": {"default": {"default": "<PERSON>chte untere Ecke"}}}, {"key": "2320", "mappings": {"default": {"default": "Obere Hälfte eines Integrals"}}}, {"key": "2321", "mappings": {"default": {"default": "Untere Hälfte eines Integrals"}}}, {"key": "2329", "mappings": {"default": {"default": "<PERSON><PERSON> spitze <PERSON>"}}}, {"key": "232A", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> spitze <PERSON>lam<PERSON>"}}}, {"key": "239B", "mappings": {"default": {"default": "Oberer Teil der linken Klammer"}}}, {"key": "239C", "mappings": {"default": {"default": "Linke Klammerverlängerung"}}}, {"key": "239D", "mappings": {"default": {"default": "Unterer Teil der linken Klammer"}}}, {"key": "239E", "mappings": {"default": {"default": "Oberer Teil der rechten Klammer"}}}, {"key": "239F", "mappings": {"default": {"default": "Rechte Klammerverlängerung"}}}, {"key": "23A0", "mappings": {"default": {"default": "Unterer Teil der rechten Klammer"}}}, {"key": "23A1", "mappings": {"default": {"default": "Oberer Teil der linken eckigen <PERSON>"}}}, {"key": "23A2", "mappings": {"default": {"default": "Linke eckige Klammerverlängerung"}}}, {"key": "23A3", "mappings": {"default": {"default": "unterer Teil der linken eckigen <PERSON>"}}}, {"key": "23A4", "mappings": {"default": {"default": "Oberer Teil der rechten eckigen <PERSON>"}}}, {"key": "23A5", "mappings": {"default": {"default": "Rechte eckige Klammerverlängerung"}}}, {"key": "23A6", "mappings": {"default": {"default": "unterer Teil der rechten eckigen <PERSON>"}}}, {"key": "23A7", "mappings": {"default": {"default": "oberer Teil der linken geschweiften Klammer"}}}, {"key": "23A8", "mappings": {"default": {"default": "Mittelstück der linken geschweiften Klammer"}}}, {"key": "23A9", "mappings": {"default": {"default": "unterer Teil der linken geschweiften Klammer"}}}, {"key": "23AA", "mappings": {"default": {"default": "Zwischenteil einer geschweiften Klammer"}}}, {"key": "23AB", "mappings": {"default": {"default": "oberer Teil der rechten geschweiften Klammer"}}}, {"key": "23AC", "mappings": {"default": {"default": "Mittelstück der rechten geschweiften Klammer"}}}, {"key": "23AD", "mappings": {"default": {"default": "unterer Teil der rechten geschweiften Klammer"}}}, {"key": "23AE", "mappings": {"default": {"default": "Mittelteil eines Integrals"}}}, {"key": "23AF", "mappings": {"default": {"default": "Erweiterung einer Querlinie"}}}, {"key": "23B0", "mappings": {"default": {"default": "Geschweiftes Klammerteil oben links oder rechts unten"}}}, {"key": "23B1", "mappings": {"default": {"default": "Geschweiftes Klammerteil oben rechts oder unten links"}}}, {"key": "23B2", "mappings": {"default": {"default": "<PERSON>berer Teil einer Summe"}}}, {"key": "23B3", "mappings": {"default": {"default": "Unterer Teil einer Summe"}}}, {"key": "23B4", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> e<PERSON>"}}}, {"key": "23B5", "mappings": {"default": {"default": "Untere ecki<PERSON>"}}}, {"key": "23B6", "mappings": {"default": {"default": "Untere eckige <PERSON> auf oberer eckiger <PERSON>"}}}, {"key": "23B7", "mappings": {"default": {"default": "Unterer Teil des Radikalsymbols"}}}, {"key": "23B8", "mappings": {"default": {"default": "<PERSON><PERSON> vertikale <PERSON>"}}}, {"key": "23B9", "mappings": {"default": {"default": "Rechte vertikale <PERSON>"}}}, {"key": "23DC", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "23DD", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "23DE", "mappings": {"default": {"default": "Obere geschweifte Klammer"}}}, {"key": "23DF", "mappings": {"default": {"default": "Untere geschweifte Klammer"}}}, {"key": "23E0", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> e<PERSON>"}}}, {"key": "23E1", "mappings": {"default": {"default": "Untere ecki<PERSON>"}}}, {"key": "2768", "mappings": {"default": {"default": "Mi<PERSON>re linke Klammerverzierung"}}}, {"key": "2769", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> rechte Klammerverzierung"}}}, {"key": "276A", "mappings": {"default": {"default": "Mi<PERSON>re abgeflachte linke Klammerverzierung"}}}, {"key": "276B", "mappings": {"default": {"default": "<PERSON><PERSON>re abgeflachte rechte Klammerverzierung"}}}, {"key": "276C", "mappings": {"default": {"default": "Mittlere nach links weisende Winkelhalterung"}}}, {"key": "276D", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>, rechtwinklige Winkelhalterung"}}}, {"key": "276E", "mappings": {"default": {"default": "Schwere nach links weisende Anführungszeichen-Verzierung"}}}, {"key": "276F", "mappings": {"default": {"default": "Schwere nach rechts zeigenden Winkel-Anführungszeichen-Verzierung"}}}, {"key": "2770", "mappings": {"default": {"default": "Schwere nach links weisende Winkelhalterung"}}}, {"key": "2771", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>, nach rechts weisende Winkelhalterung"}}}, {"key": "2772", "mappings": {"default": {"default": "Leichte linke Schildpatt Bracket Ornament"}}}, {"key": "2773", "mappings": {"default": {"default": "Leichte rechte Schildpatt-Klammerverzierung"}}}, {"key": "2774", "mappings": {"default": {"default": "Mittlere linke geschweifte Klammerverzierung"}}}, {"key": "2775", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> rechte geschweifte Klammerverzierung"}}}, {"key": "27C5", "mappings": {"default": {"default": "Linker S-f<PERSON><PERSON><PERSON>"}}}, {"key": "27C6", "mappings": {"default": {"default": "rechter S-förmiger <PERSON>schenbegrenzer"}}}, {"key": "27E6", "mappings": {"default": {"default": "Mathematische linke weiße eckige Klammer"}}}, {"key": "27E7", "mappings": {"default": {"default": "Mathematische rechte weiße eckige Klammer"}}}, {"key": "27E8", "mappings": {"default": {"default": "Mathematische linke Winkelklammer"}}}, {"key": "27E9", "mappings": {"default": {"default": "Mathematische rechtwinklige Klammer"}}}, {"key": "27EA", "mappings": {"default": {"default": "Mathematische linke doppelte Winkelklammer"}}}, {"key": "27EB", "mappings": {"default": {"default": "Mathematische rechtwinklige eckige <PERSON>"}}}, {"key": "27EC", "mappings": {"default": {"default": "Mathematische linke Schildkrötenpanzerhalterung"}}}, {"key": "27ED", "mappings": {"default": {"default": "Mathematische rechte weiße Schildpatt-Klammer"}}}, {"key": "27EE", "mappings": {"default": {"default": "Mathematische linke abgeflachte Klammer"}}}, {"key": "27EF", "mappings": {"default": {"default": "Mathematische rechte abgeflachte Klammer"}}}, {"key": "2983", "mappings": {"default": {"default": "Linke weiße geschweifte Klammer"}}}, {"key": "2984", "mappings": {"default": {"default": "Rechte weiße geschweifte Klammer"}}}, {"key": "2985", "mappings": {"default": {"default": "Linke weiße Klammer"}}}, {"key": "2986", "mappings": {"default": {"default": "Rechte weiße Klammer"}}}, {"key": "2987", "mappings": {"default": {"default": "Z-Notation Linke Bildklammer"}}}, {"key": "2988", "mappings": {"default": {"default": "Z-Notation rechte Bildklammer"}}}, {"key": "2989", "mappings": {"default": {"default": "Z-Notation <PERSON><PERSON>"}}}, {"key": "298A", "mappings": {"default": {"default": "Z Notation Right Binding Bracket"}}}, {"key": "298B", "mappings": {"default": {"default": "<PERSON>e eckige Klammer mit Unterlenker"}}}, {"key": "298C", "mappings": {"default": {"default": "<PERSON>chte eckige Klammer mit Unterlenker"}}}, {"key": "298D", "mappings": {"default": {"default": "<PERSON><PERSON> eckige Klammer mit Häkchen in der oberen Ecke"}}}, {"key": "298E", "mappings": {"default": {"default": "<PERSON>chte eckige Klammer mit Tick in der unteren Ecke"}}}, {"key": "298F", "mappings": {"default": {"default": "<PERSON><PERSON> eckige Klammer mit Tick in der unteren Ecke"}}}, {"key": "2990", "mappings": {"default": {"default": "<PERSON>chte eckige Klammer mit Tick in der oberen Ecke"}}}, {"key": "2991", "mappings": {"default": {"default": "Linke Winkelklammer mit Punkt"}}}, {"key": "2992", "mappings": {"default": {"default": "Rechtwinklige Klammer mit Punkt"}}}, {"key": "2993", "mappings": {"default": {"default": "Linker Bogen weniger als Halterung"}}}, {"key": "2994", "mappings": {"default": {"default": "rechter Bogen größer als Halterung"}}}, {"key": "2995", "mappings": {"default": {"default": "Doppelter linker Bogen größer als Halterung"}}}, {"key": "2996", "mappings": {"default": {"default": "Doppelter rechter Bogen weniger als Halterung"}}}, {"key": "2997", "mappings": {"default": {"default": "Linke schwarze Schildkrötenhalterung"}}}, {"key": "2998", "mappings": {"default": {"default": "Rechte schwarze Schildpatt-Klammer"}}}, {"key": "29D8", "mappings": {"default": {"default": "<PERSON><PERSON> wa<PERSON><PERSON>"}}}, {"key": "29D9", "mappings": {"default": {"default": "rechter wacke<PERSON>ger <PERSON>"}}}, {"key": "29DA", "mappings": {"default": {"default": "<PERSON><PERSON> doppel<PERSON> wackeliger <PERSON>"}}}, {"key": "29DB", "mappings": {"default": {"default": "rechter doppelter wackeliger <PERSON>"}}}, {"key": "29FC", "mappings": {"default": {"default": "Nach links zeigender gebogener Winkelhalter"}}}, {"key": "29FD", "mappings": {"default": {"default": "Nach rechts zeigender gebogener Winkelhalter"}}}, {"key": "2E22", "mappings": {"default": {"default": "Obere linke halbe Halterung"}}}, {"key": "2E23", "mappings": {"default": {"default": "Obere rechte halbe <PERSON>"}}}, {"key": "2E24", "mappings": {"default": {"default": "Linke halbe halterung unten"}}}, {"key": "2E25", "mappings": {"default": {"default": "Untere rechte halbe Halterung"}}}, {"key": "2E26", "mappings": {"default": {"default": "U-Halterung links seitlich"}}}, {"key": "2E27", "mappings": {"default": {"default": "U-Halterung rechts seitlich"}}}, {"key": "2E28", "mappings": {"default": {"default": "<PERSON><PERSON> do<PERSON>"}}}, {"key": "2E29", "mappings": {"default": {"default": "<PERSON><PERSON>e doppelte Klammer"}}}, {"key": "3008", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "3009", "mappings": {"default": {"default": "Rechtwinklige Halterung"}}}, {"key": "300A", "mappings": {"default": {"default": "Linke Doppelwinkelhalterung"}}}, {"key": "300B", "mappings": {"default": {"default": "Rechte Doppelwinkelhalterung"}}}, {"key": "300C", "mappings": {"default": {"default": "Linke Eckklammer"}}}, {"key": "300D", "mappings": {"default": {"default": "Rechte Eckhalterung"}}}, {"key": "300E", "mappings": {"default": {"default": "Linke weiße Eckhalterung"}}}, {"key": "300F", "mappings": {"default": {"default": "Rechte weiße Eckhalterung"}}}, {"key": "3010", "mappings": {"default": {"default": "Linke schwarze linsenförmige Halterung"}}}, {"key": "3011", "mappings": {"default": {"default": "Rechte schwarze linsenförmige Halterung"}}}, {"key": "3014", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "3015", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "3016", "mappings": {"default": {"default": "Linke weiße linsenförmige Halterung"}}}, {"key": "3017", "mappings": {"default": {"default": "Rechte weiße linsenförmige Halterung"}}}, {"key": "3018", "mappings": {"default": {"default": "Linke weiße Schildpatt-Klammer"}}}, {"key": "3019", "mappings": {"default": {"default": "Rechte weiße Schildpatt-Klammer"}}}, {"key": "301A", "mappings": {"default": {"default": "Linke weiße eckige Klammer"}}}, {"key": "301B", "mappings": {"default": {"default": "Rechte weiße eckige Klammer"}}}, {"key": "301D", "mappings": {"default": {"default": "Doppelte Anführungszeichen in umgekehrter Reihenfolge"}}}, {"key": "301E", "mappings": {"default": {"default": "Doppelte Anführungszeichen"}}}, {"key": "301F", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> doppel<PERSON> Prime-Anführungszeichen"}}}, {"key": "FD3E", "mappings": {"default": {"default": "Verzierte linke Klammer"}}}, {"key": "FD3F", "mappings": {"default": {"default": "Verzierte rechte Klammer"}}}, {"key": "FE17", "mappings": {"default": {"default": "Präsentationsformular für vertikale linke weiße linsenförmige Halterung"}}}, {"key": "FE18", "mappings": {"default": {"default": "Präsentationsformular für vertikale rechtwinklige linsenförmige Halterung"}}}, {"key": "FE35", "mappings": {"default": {"default": "Präsentationsformular für vertikale linke Klammer"}}}, {"key": "FE36", "mappings": {"default": {"default": "Präsentationsformular für vertikale rechte Klammer"}}}, {"key": "FE37", "mappings": {"default": {"default": "Präsentationsformular für vertikale linke geschweifte Klammer"}}}, {"key": "FE38", "mappings": {"default": {"default": "Präsentationsformular für vertikale, recht geschweifte Klammer"}}}, {"key": "FE39", "mappings": {"default": {"default": "Präsentationsformular für vertikale linke Schildpattklammer"}}}, {"key": "FE3A", "mappings": {"default": {"default": "Präsentationsformular für vertikale rechte Schildpatt-Klammer"}}}, {"key": "FE3B", "mappings": {"default": {"default": "Präsentationsformular für vertikale linke schwarze linsenförmige Halterung"}}}, {"key": "FE3C", "mappings": {"default": {"default": "Präsentationsformular für vertikale rechtwinklige schwarze linsenförmige Halterung"}}}, {"key": "FE3D", "mappings": {"default": {"default": "Präsentationsformular für vertikale linke Doppelwinkelhalterung"}}}, {"key": "FE3E", "mappings": {"default": {"default": "Präsentationsformular für vertikale rechtwinklige, rechtwinklige Halterung"}}}, {"key": "FE3F", "mappings": {"default": {"default": "Präsentationsformular für vertikale linke Winkelhalterung"}}}, {"key": "FE40", "mappings": {"default": {"default": "Präsentationsformular für vertikale rechtwinklige Halterung"}}}, {"key": "FE41", "mappings": {"default": {"default": "Präsentationsformular für vertikale linke Eckhalterung"}}}, {"key": "FE42", "mappings": {"default": {"default": "Präsentationsformular für vertikale rechte Eckhalterung"}}}, {"key": "FE43", "mappings": {"default": {"default": "Präsentationsformular für vertikale linke weiße Eckhalterung"}}}, {"key": "FE44", "mappings": {"default": {"default": "Präsentationsformular für vertikale rechte weiße Eckhalterung"}}}, {"key": "FE47", "mappings": {"default": {"default": "Präsentationsformular für vertikale linke eckige Klammer"}}}, {"key": "FE48", "mappings": {"default": {"default": "Präsentationsformular für vertikale eckige Klammer"}}}, {"key": "FE59", "mappings": {"default": {"default": "Kleine linke Klammer"}}}, {"key": "FE5A", "mappings": {"default": {"default": "<PERSON>e rechte Klammer"}}}, {"key": "FE5B", "mappings": {"default": {"default": "Kleine linke geschweifte Klammer"}}}, {"key": "FE5C", "mappings": {"default": {"default": "Kleine rechte geschweifte Klammer"}}}, {"key": "FE5D", "mappings": {"default": {"default": "Kleine linke Schildpatt-Klammer"}}}, {"key": "FE5E", "mappings": {"default": {"default": "Kleine rechte Schildpatt-Klammer"}}}, {"key": "FF08", "mappings": {"default": {"default": "<PERSON><PERSON> Klammer in voller Breite"}}}, {"key": "FF09", "mappings": {"default": {"default": "<PERSON>chte Klammer mit voller Breite"}}}, {"key": "FF3B", "mappings": {"default": {"default": "Linke eckige Klammer mit voller Breite"}}}, {"key": "FF3D", "mappings": {"default": {"default": "Rechteckige Klammer mit voller Breite"}}}, {"key": "FF5B", "mappings": {"default": {"default": "Linke geschweifte Klammer mit voller Breite"}}}, {"key": "FF5D", "mappings": {"default": {"default": "Rechtwinklige Klammer mit voller Breite"}}}, {"key": "FF5F", "mappings": {"default": {"default": "Linke weiße Klammer mit voller Breite"}}}, {"key": "FF60", "mappings": {"default": {"default": "Rechte breite weiße Klammer"}}}, {"key": "FF62", "mappings": {"default": {"default": "Halbe breite linke eckklammer"}}}, {"key": "FF63", "mappings": {"default": {"default": "Halbe breite rechte eckklammer"}}}], "de/symbols/math_geometry.min": [{"locale": "de"}, {"key": "2500", "mappings": {"default": {"default": "Box Drawings Light Horizontal"}}}, {"key": "2501", "mappings": {"default": {"default": "Box Drawings Schwere Horizontale"}}}, {"key": "2502", "mappings": {"default": {"default": "Box Drawings Light Vertical"}}}, {"key": "2503", "mappings": {"default": {"default": "Box Drawings Schwere Vertikale"}}}, {"key": "2504", "mappings": {"default": {"default": "Box-Zeichnungen Licht Triple Dash Horizontal"}}}, {"key": "2505", "mappings": {"default": {"default": "Box Drawings Heavy Triple Dash Horizontal"}}}, {"key": "2506", "mappings": {"default": {"default": "Box Drawings Light Triple Dash Vertical"}}}, {"key": "2507", "mappings": {"default": {"default": "Box Drawings Heavy Triple Dash Vertical"}}}, {"key": "2508", "mappings": {"default": {"default": "Box Drawings Light Quadruple Dash Horizontal"}}}, {"key": "2509", "mappings": {"default": {"default": "Box Drawings Heavy Quadruple Dash Horizontal"}}}, {"key": "250A", "mappings": {"default": {"default": "Box Drawings Light Quadruple Dash Vertical"}}}, {"key": "250B", "mappings": {"default": {"default": "Box Drawings Heavy Quadruple Dash Vertical"}}}, {"key": "250C", "mappings": {"default": {"default": "Box Drawings Light Down und Right"}}}, {"key": "250D", "mappings": {"default": {"default": "Box Drawings Down Light und Right Heavy"}}}, {"key": "250E", "mappings": {"default": {"default": "Box Drawings Down Heavy und Right Light"}}}, {"key": "250F", "mappings": {"default": {"default": "Box Drawings Heavy Down und Right"}}}, {"key": "2510", "mappings": {"default": {"default": "Box Drawings Light Down und Links"}}}, {"key": "2511", "mappings": {"default": {"default": "Box Drawings Down Light und Left Heavy"}}}, {"key": "2512", "mappings": {"default": {"default": "Box Drawings Down Heavy und Left Light"}}}, {"key": "2513", "mappings": {"default": {"default": "Box Drawings Heavy Down und Left"}}}, {"key": "2514", "mappings": {"default": {"default": "Box-<PERSON><PERSON>chnungen leuchten auf und richtig"}}}, {"key": "2515", "mappings": {"default": {"default": "Box Drawings Up Light und Right Heavy"}}}, {"key": "2516", "mappings": {"default": {"default": "Box Drawings Up Heavy und Right Light"}}}, {"key": "2517", "mappings": {"default": {"default": "Box Drawings Heavy Up und Right"}}}, {"key": "2518", "mappings": {"default": {"default": "Box-Zeichnungen leuchten und links"}}}, {"key": "2519", "mappings": {"default": {"default": "Box Drawings Up Light und Left Heavy"}}}, {"key": "251A", "mappings": {"default": {"default": "Box Drawings Up Heavy und Left Light"}}}, {"key": "251B", "mappings": {"default": {"default": "Box Drawings Heavy Up und Links"}}}, {"key": "251C", "mappings": {"default": {"default": "Box Drawings Light Vertical und Right"}}}, {"key": "251D", "mappings": {"default": {"default": "Box Drawings Vertical Light und Right Heavy"}}}, {"key": "251E", "mappings": {"default": {"default": "Box Drawings Up Heavy und Right Down Light"}}}, {"key": "251F", "mappings": {"default": {"default": "Box Drawings Down Heavy und Right Up Light"}}}, {"key": "2520", "mappings": {"default": {"default": "Box Drawings Vertical Heavy und Right Light"}}}, {"key": "2521", "mappings": {"default": {"default": "Box Drawings Down Light und Right Up Heavy"}}}, {"key": "2522", "mappings": {"default": {"default": "Box Drawings Up Light und Right Down Heavy"}}}, {"key": "2523", "mappings": {"default": {"default": "Box Drawings Heavy Vertical und Right"}}}, {"key": "2524", "mappings": {"default": {"default": "Box Drawings Light Vertical und Left"}}}, {"key": "2525", "mappings": {"default": {"default": "Box Drawings Vertical Light und Left Heavy"}}}, {"key": "2526", "mappings": {"default": {"default": "Box Drawings Up Heavy und Left Down Light"}}}, {"key": "2527", "mappings": {"default": {"default": "Box Drawings Down Heavy und Left Up Light"}}}, {"key": "2528", "mappings": {"default": {"default": "Box Drawings Vertical Heavy und Left Light"}}}, {"key": "2529", "mappings": {"default": {"default": "Box Drawings Down Light und Left Up Heavy"}}}, {"key": "252A", "mappings": {"default": {"default": "Box Drawings Up Light und Left Down Heavy"}}}, {"key": "252B", "mappings": {"default": {"default": "Box Drawings Heavy Vertical und Left"}}}, {"key": "252C", "mappings": {"default": {"default": "Box-Zeichnungen leicht und horizontal"}}}, {"key": "252D", "mappings": {"default": {"default": "Box Drawings Left Heavy und Right Down Light"}}}, {"key": "252E", "mappings": {"default": {"default": "Box Drawings Right Heavy und Left Down Light"}}}, {"key": "252F", "mappings": {"default": {"default": "Box Drawings Down Light und Horizontal Heavy"}}}, {"key": "2530", "mappings": {"default": {"default": "Box-Zeichnungen für schweres und horizontales Licht"}}}, {"key": "2531", "mappings": {"default": {"default": "Box Drawings Right Light und Left Down Heavy"}}}, {"key": "2532", "mappings": {"default": {"default": "Box Drawings Left Light und Right Down Heavy"}}}, {"key": "2533", "mappings": {"default": {"default": "Box-Z<PERSON>chnungen nach unten und horizontal"}}}, {"key": "2534", "mappings": {"default": {"default": "Box-Zeichnungen leuchten und horizontal"}}}, {"key": "2535", "mappings": {"default": {"default": "Box Drawings Left Heavy und Right Up Light"}}}, {"key": "2536", "mappings": {"default": {"default": "Box Drawings Right Heavy und Left Up Light"}}}, {"key": "2537", "mappings": {"default": {"default": "Box Drawings Up Light und Horizontal Heavy"}}}, {"key": "2538", "mappings": {"default": {"default": "Box-Zeichnungen für schweres und horizontales Licht"}}}, {"key": "2539", "mappings": {"default": {"default": "Box Drawings Right Light und Left Up Heavy"}}}, {"key": "253A", "mappings": {"default": {"default": "Box Drawings Left Light und Right Up Heavy"}}}, {"key": "253B", "mappings": {"default": {"default": "Box-Zeichnungen schwer und horizontal"}}}, {"key": "253C", "mappings": {"default": {"default": "Box Drawings Light vertikal und horizontal"}}}, {"key": "253D", "mappings": {"default": {"default": "Kastenzeichnungen links schweres und rechtes vertikales Licht"}}}, {"key": "253E", "mappings": {"default": {"default": "Box Drawings Right Heavy und Left Vertical Light"}}}, {"key": "253F", "mappings": {"default": {"default": "Box Drawings Vertical Light und Horizontal Heavy"}}}, {"key": "2540", "mappings": {"default": {"default": "Box Drawings Up Heavy und Down Horizontal Light"}}}, {"key": "2541", "mappings": {"default": {"default": "Box Drawings Down Heavy und Up Horizontal Light"}}}, {"key": "2542", "mappings": {"default": {"default": "Kastenzeichnungen Vertikales schweres und horizontales Licht"}}}, {"key": "2543", "mappings": {"default": {"default": "Box Drawings Left Up Heavy und Right Down Light"}}}, {"key": "2544", "mappings": {"default": {"default": "Box Drawings Right Up Heavy und Left Down Light"}}}, {"key": "2545", "mappings": {"default": {"default": "Box Drawings Left Down Heavy und Right Up Light"}}}, {"key": "2546", "mappings": {"default": {"default": "Box Drawings Right Down Heavy und Left Up Light"}}}, {"key": "2547", "mappings": {"default": {"default": "Box Drawings Down Light und Up Horizontal Heavy"}}}, {"key": "2548", "mappings": {"default": {"default": "Box Drawings Up Light und Down Horizontal Heavy"}}}, {"key": "2549", "mappings": {"default": {"default": "Box Drawings Right Light und Left Vertical Heavy"}}}, {"key": "254A", "mappings": {"default": {"default": "Box Drawings Left Light und Right Vertical Heavy"}}}, {"key": "254B", "mappings": {"default": {"default": "Box Drawings Heavy vertikal und horizontal"}}}, {"key": "254C", "mappings": {"default": {"default": "Box Drawings Light Double Dash Horizontal"}}}, {"key": "254D", "mappings": {"default": {"default": "Box Drawings Heavy Double Dash Horizontal"}}}, {"key": "254E", "mappings": {"default": {"default": "Box Drawings Light Double Dash Vertical"}}}, {"key": "254F", "mappings": {"default": {"default": "Box Drawings Heavy Double Dash Vertical"}}}, {"key": "2550", "mappings": {"default": {"default": "Box-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> doppelt horizontal"}}}, {"key": "2551", "mappings": {"default": {"default": "Box Drawings Double Vertical"}}}, {"key": "2552", "mappings": {"default": {"default": "Box Drawings Down Single und Right Double"}}}, {"key": "2553", "mappings": {"default": {"default": "Box Drawings Down Double und Right Single"}}}, {"key": "2554", "mappings": {"default": {"default": "Box Drawings Double Down und Right"}}}, {"key": "2555", "mappings": {"default": {"default": "Box Drawings Down Single und Left Double"}}}, {"key": "2556", "mappings": {"default": {"default": "Box Drawings Down Double und Left Single"}}}, {"key": "2557", "mappings": {"default": {"default": "Box Drawings Double Down und Links"}}}, {"key": "2558", "mappings": {"default": {"default": "Box Drawings Up Single und Right Double"}}}, {"key": "2559", "mappings": {"default": {"default": "Box Drawings Up Double und Right Single"}}}, {"key": "255A", "mappings": {"default": {"default": "Box-Z<PERSON><PERSON>nungen verdoppeln und rechts"}}}, {"key": "255B", "mappings": {"default": {"default": "Box Drawings Up Single und Left Double"}}}, {"key": "255C", "mappings": {"default": {"default": "Box Drawings Up Double und Left Single"}}}, {"key": "255D", "mappings": {"default": {"default": "Box-Zeichnungen verdoppeln und links"}}}, {"key": "255E", "mappings": {"default": {"default": "Box Drawings Vertical Single und Right Double"}}}, {"key": "255F", "mappings": {"default": {"default": "Box Drawings Vertical Double und Right Single"}}}, {"key": "2560", "mappings": {"default": {"default": "Box Drawings Double Vertical und Right"}}}, {"key": "2561", "mappings": {"default": {"default": "Box Drawings Vertical Single und Left Double"}}}, {"key": "2562", "mappings": {"default": {"default": "Box Drawings Vertical Double und Left Single"}}}, {"key": "2563", "mappings": {"default": {"default": "Box Drawings Double Vertical und Left"}}}, {"key": "2564", "mappings": {"default": {"default": "Box Drawings Down Single und Horizontal Double"}}}, {"key": "2565", "mappings": {"default": {"default": "Box Drawings Down Double und Horizontal Single"}}}, {"key": "2566", "mappings": {"default": {"default": "Box Drawings Double Down und Horizontal"}}}, {"key": "2567", "mappings": {"default": {"default": "Box Drawings Up Single und Horizontal Double"}}}, {"key": "2568", "mappings": {"default": {"default": "Box Drawings Up Double und Horizontal Single"}}}, {"key": "2569", "mappings": {"default": {"default": "Box-Zeichnungen verdoppeln und horizontal"}}}, {"key": "256A", "mappings": {"default": {"default": "Box Drawings Vertical Single und Horizontal Double"}}}, {"key": "256B", "mappings": {"default": {"default": "Box Drawings Vertical Double und Horizontal Single"}}}, {"key": "256C", "mappings": {"default": {"default": "Box-Zeichnungen doppelt vertikal und horizontal"}}}, {"key": "256D", "mappings": {"default": {"default": "Box Drawings Light Arc Down und Right"}}}, {"key": "256E", "mappings": {"default": {"default": "Box Drawings Light Arc Down und Links"}}}, {"key": "256F", "mappings": {"default": {"default": "Box Drawings Light Arc Up und links"}}}, {"key": "2570", "mappings": {"default": {"default": "Box Drawings Light Arc Up und rechts"}}}, {"key": "2571", "mappings": {"default": {"default": "Box Drawings Light Diagonal Oben Rechts Nach Unten Links"}}}, {"key": "2572", "mappings": {"default": {"default": "Box Drawings Light Diagonal von oben links nach rechts unten"}}}, {"key": "2573", "mappings": {"default": {"default": "Box Zeichnungen Light Diagonal Cross"}}}, {"key": "2574", "mappings": {"default": {"default": "Box-Zeichnungen Light Left"}}}, {"key": "2575", "mappings": {"default": {"default": "Box-<PERSON><PERSON>chnungen leuchten auf"}}}, {"key": "2576", "mappings": {"default": {"default": "Box-Zeichnungen Light Right"}}}, {"key": "2577", "mappings": {"default": {"default": "Box Drawings Light Down"}}}, {"key": "2578", "mappings": {"default": {"default": "Box Drawings Heavy Left"}}}, {"key": "2579", "mappings": {"default": {"default": "Box Drawings Heavy Up"}}}, {"key": "257A", "mappings": {"default": {"default": "Box Drawings Heavy Right"}}}, {"key": "257B", "mappings": {"default": {"default": "Box Drawings Heavy Down"}}}, {"key": "257C", "mappings": {"default": {"default": "Box Drawings Light Left und Heavy Right"}}}, {"key": "257D", "mappings": {"default": {"default": "Box Drawings Light Up und Heavy Down"}}}, {"key": "257E", "mappings": {"default": {"default": "Box Drawings Heavy Left und Light Right"}}}, {"key": "257F", "mappings": {"default": {"default": "Box Drawings Heavy Up und Light Down"}}}, {"key": "2580", "mappings": {"default": {"default": "Block der oberen Hälfte"}}}, {"key": "2581", "mappings": {"default": {"default": "Unterer achter Block"}}}, {"key": "2582", "mappings": {"default": {"default": "Unterer Viertelblock"}}}, {"key": "2583", "mappings": {"default": {"default": "<PERSON>ei Achtel-<PERSON> senken"}}}, {"key": "2584", "mappings": {"default": {"default": "Block der unteren Hälfte"}}}, {"key": "2585", "mappings": {"default": {"default": "Fünf Achtel Block"}}}, {"key": "2586", "mappings": {"default": {"default": "Unterer Dreiviertelblock"}}}, {"key": "2587", "mappings": {"default": {"default": "Unterer Sieben Achtel-Block"}}}, {"key": "2588", "mappings": {"default": {"default": "<PERSON><PERSON> Block"}}}, {"key": "2589", "mappings": {"default": {"default": "Linker Sieben Achtel Block"}}}, {"key": "258A", "mappings": {"default": {"default": "<PERSON><PERSON> Dreivier<PERSON>"}}}, {"key": "258B", "mappings": {"default": {"default": "Linker Block mit fünf Achteln"}}}, {"key": "258C", "mappings": {"default": {"default": "Linke halbe Block"}}}, {"key": "258D", "mappings": {"default": {"default": "Block drei <PERSON>"}}}, {"key": "258E", "mappings": {"default": {"default": "Linke einen Viertelblock"}}}, {"key": "258F", "mappings": {"default": {"default": "Linker achter Block"}}}, {"key": "2590", "mappings": {"default": {"default": "Rechte halbe Block"}}}, {"key": "2591", "mappings": {"default": {"default": "Lichtschatten"}}}, {"key": "2592", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "2593", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "2594", "mappings": {"default": {"default": "Oberer achter Block"}}}, {"key": "2595", "mappings": {"default": {"default": "rechter achter Block"}}}, {"key": "2596", "mappings": {"default": {"default": "Quadrant unten links"}}}, {"key": "2597", "mappings": {"default": {"default": "Quadrant rechts unten"}}}, {"key": "2598", "mappings": {"default": {"default": "Quadrant oben links"}}}, {"key": "2599", "mappings": {"default": {"default": "Quadrant oben links und unten links und rechts unten"}}}, {"key": "259A", "mappings": {"default": {"default": "Quadrant oben links und rechts unten"}}}, {"key": "259B", "mappings": {"default": {"default": "Quadrant oben links und oben rechts und unten links"}}}, {"key": "259C", "mappings": {"default": {"default": "Quadrant oben links und oben rechts und unten rechts"}}}, {"key": "259D", "mappings": {"default": {"default": "Quadrant oben rechts"}}}, {"key": "259E", "mappings": {"default": {"default": "Quadrant oben rechts und unten links"}}}, {"key": "259F", "mappings": {"default": {"default": "Quadrant oben rechts und unten links und unten rechts"}}}, {"key": "25A0", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "25A1", "mappings": {"default": {"default": "Weißes Quadrat"}}}, {"key": "25A2", "mappings": {"default": {"default": "Weißes Quadrat mit abgerundeten Ecken"}}}, {"key": "25A3", "mappings": {"default": {"default": "<PERSON>ßes Quadrat, das schwarzes kleines Quadrat enthält"}}}, {"key": "25A4", "mappings": {"default": {"default": "Quadrat mit horizontaler Füllung"}}}, {"key": "25A5", "mappings": {"default": {"default": "Quadrat mit vertikaler Füllung"}}}, {"key": "25A6", "mappings": {"default": {"default": "Quadrat mit orthogonaler Schraffurfüllung"}}}, {"key": "25A7", "mappings": {"default": {"default": "Quadrat mit Oben links nach rechts unten füllen"}}}, {"key": "25A8", "mappings": {"default": {"default": "Quadrat mit oberer rechter bis unterer linker Füllung"}}}, {"key": "25A9", "mappings": {"default": {"default": "Quadrat mit diagonaler Schraffurfüllung"}}}, {"key": "25AA", "mappings": {"default": {"default": "Schwarzes kleines Quadrat"}}}, {"key": "25AB", "mappings": {"default": {"default": "Weißes kleines Quadrat"}}}, {"key": "25AC", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "25AD", "mappings": {"default": {"default": "Weißes Rechteck"}}}, {"key": "25AE", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> vertika<PERSON>"}}}, {"key": "25AF", "mappings": {"default": {"default": "Weißes vertikales Rechteck"}}}, {"key": "25B0", "mappings": {"default": {"default": "Schwarzes Parallelogramm"}}}, {"key": "25B1", "mappings": {"default": {"default": "Weißes Parallelogramm"}}}, {"key": "25B2", "mappings": {"default": {"default": "Schwarzes nach oben zeigendes Dreieck"}}}, {"key": "25B3", "mappings": {"default": {"default": "Weißes nach oben zeigendes Dreieck"}}}, {"key": "25B4", "mappings": {"default": {"default": "Schwarzes nach oben zeigendes kleines Dreieck"}}}, {"key": "25B5", "mappings": {"default": {"default": "Weißes nach oben zeigendes kleines Dreieck"}}}, {"key": "25B6", "mappings": {"default": {"default": "Schwarzes rechtwinkliges Dreieck"}}}, {"key": "25B7", "mappings": {"default": {"default": "Weißes rechtwinkliges Dreieck"}}}, {"key": "25B8", "mappings": {"default": {"default": "Schwarzes nach rechts zeigendes kleines Dreieck"}}}, {"key": "25B9", "mappings": {"default": {"default": "Weißes nach rechts zeigendes kleines Dreieck"}}}, {"key": "25BA", "mappings": {"default": {"default": "Schwarzer nach rechts zeigender Zeiger"}}}, {"key": "25BB", "mappings": {"default": {"default": "<PERSON>ßer nach rechts zeigender Zeiger"}}}, {"key": "25BC", "mappings": {"default": {"default": "Schwarzes Abwärtsdreieck"}}}, {"key": "25BD", "mappings": {"default": {"default": "Weißes nach unten zeigendes Dreieck"}}}, {"key": "25BE", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>, nach unten zeigendes kleines Dreieck"}}}, {"key": "25BF", "mappings": {"default": {"default": "Weißes kleines Dreieck"}}}, {"key": "25C0", "mappings": {"default": {"default": "Schwarzes nach links zeigendes Dreieck"}}}, {"key": "25C1", "mappings": {"default": {"default": "Weißes nach links zeigendes Dreieck"}}}, {"key": "25C2", "mappings": {"default": {"default": "Schwarzes nach links zeigendes kleines Dreieck"}}}, {"key": "25C3", "mappings": {"default": {"default": "Weißes nach links zeigendes kleines Dreieck"}}}, {"key": "25C4", "mappings": {"default": {"default": "Schwarzer nach links zeigender Zeiger"}}}, {"key": "25C5", "mappings": {"default": {"default": "Weißer nach links zeigender Zeiger"}}}, {"key": "25C6", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "25C7", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "25C8", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> mit schwarzem kleinem Diamant"}}}, {"key": "25C9", "mappings": {"default": {"default": "Fischauge"}}}, {"key": "25CA", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "25CB", "mappings": {"default": {"default": "Weißer Kreis"}}}, {"key": "25CC", "mappings": {"default": {"default": "Gepunkteter Kreis"}}}, {"key": "25CD", "mappings": {"default": {"default": "Kreis mit vertikaler Füllung"}}}, {"key": "25CE", "mappings": {"default": {"default": "Bullseye"}}}, {"key": "25CF", "mappings": {"default": {"default": "Schwarzer Kreis"}}}, {"key": "25D0", "mappings": {"default": {"default": "Kreis mit linker Hälfte schwarz"}}}, {"key": "25D1", "mappings": {"default": {"default": "Kreis mit rechter Hälfte schwarz"}}}, {"key": "25D2", "mappings": {"default": {"default": "Kreis mit unterer Hälfte Schwarz"}}}, {"key": "25D3", "mappings": {"default": {"default": "Kreis mit oberer Hälfte schwarz"}}}, {"key": "25D4", "mappings": {"default": {"default": "Kreis mit oberem rechten Quadranten schwarz"}}}, {"key": "25D5", "mappings": {"default": {"default": "Kreis mit ganzem oberen linken Quadranten schwarz"}}}, {"key": "25D6", "mappings": {"default": {"default": "Linker halber schwarzer Kreis"}}}, {"key": "25D7", "mappings": {"default": {"default": "rechter halber schwarzer Kreis"}}}, {"key": "25D8", "mappings": {"default": {"default": "Inverse <PERSON>"}}}, {"key": "25D9", "mappings": {"default": {"default": "Inverse weißer Kreis"}}}, {"key": "25DA", "mappings": {"default": {"default": "Umgekehrter weißer Kreis der oberen Hälfte"}}}, {"key": "25DB", "mappings": {"default": {"default": "Inverser weißer Kreis der unteren Hälfte"}}}, {"key": "25DC", "mappings": {"default": {"default": "Kreisbogen des oberen linken Quadranten"}}}, {"key": "25DD", "mappings": {"default": {"default": "Kreisbogen im oberen rechten Quadranten"}}}, {"key": "25DE", "mappings": {"default": {"default": "Kreisbogen des rechten unteren Quadranten"}}}, {"key": "25DF", "mappings": {"default": {"default": "Kreisbogen des unteren linken Quadranten"}}}, {"key": "25E0", "mappings": {"default": {"default": "Oberer Halbkreis"}}}, {"key": "25E1", "mappings": {"default": {"default": "Unterer Halbkreis"}}}, {"key": "25E2", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> rechtes Dreieck"}}}, {"key": "25E3", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> unteres linkes Dreieck"}}}, {"key": "25E4", "mappings": {"default": {"default": "Schwarzes oberes linkes <PERSON>eck"}}}, {"key": "25E5", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> rechtes Dreieck"}}}, {"key": "25E6", "mappings": {"default": {"default": "Weiße Kugel"}}}, {"key": "25E7", "mappings": {"default": {"default": "Quadrat mit linker H<PERSON><PERSON><PERSON> schwarz"}}}, {"key": "25E8", "mappings": {"default": {"default": "Quadrat mit rechter Hälfte schwarz"}}}, {"key": "25E9", "mappings": {"default": {"default": "Quadrat mit der oberen linken Diagonalenhälfte schwarz"}}}, {"key": "25EA", "mappings": {"default": {"default": "Quadrat mit unterer rechter diagonaler Hälfte schwarz"}}}, {"key": "25EB", "mappings": {"default": {"default": "Weißes Quadrat mit senkrechter Linie"}}}, {"key": "25EC", "mappings": {"default": {"default": "Weißes nach oben zeigendes Dreieck mit Punkt"}}}, {"key": "25ED", "mappings": {"default": {"default": "Aufwärtszeigendes Dreieck mit linker Hälfte schwarz"}}}, {"key": "25EE", "mappings": {"default": {"default": "Aufwärtszeigendes Dreieck mit rechter Hälfte schwarz"}}}, {"key": "25EF", "mappings": {"default": {"default": "Großer Kreis"}}}, {"key": "25F0", "mappings": {"default": {"default": "Weißes Quadrat mit oberem linken Quadranten"}}}, {"key": "25F1", "mappings": {"default": {"default": "Weißes Quadrat mit unterem linken Quadranten"}}}, {"key": "25F2", "mappings": {"default": {"default": "Weißes Quadrat mit unterem rechten Quadranten"}}}, {"key": "25F3", "mappings": {"default": {"default": "Weißes Quadrat mit rechtem oberen Quadranten"}}}, {"key": "25F4", "mappings": {"default": {"default": "Weißer Kreis mit oberem linkem Quadranten"}}}, {"key": "25F5", "mappings": {"default": {"default": "Weißer Kreis mit unterem linken Quadranten"}}}, {"key": "25F6", "mappings": {"default": {"default": "Weißer Kreis mit unterem rechten Quadranten"}}}, {"key": "25F7", "mappings": {"default": {"default": "Weißer Kreis mit rechtem oberen Quadranten"}}}, {"key": "25F8", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> link<PERSON>"}}}, {"key": "25F9", "mappings": {"default": {"default": "<PERSON>beres rechtes Dreieck"}}}, {"key": "25FA", "mappings": {"default": {"default": "Unteres linkes <PERSON>"}}}, {"key": "25FB", "mappings": {"default": {"default": "Weißes mittleres Quadrat"}}}, {"key": "25FC", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> mittleres Quadrat"}}}, {"key": "25FD", "mappings": {"default": {"default": "Weißes mittleres kleines Quadrat"}}}, {"key": "25FE", "mappings": {"default": {"default": "Schwarzes mittleres kleines Quadrat"}}}, {"key": "25FF", "mappings": {"default": {"default": "Unteres rechtes Dreieck"}}}, {"key": "2B12", "mappings": {"default": {"default": "Quadrat mit oberer Hälfte schwarz"}}}, {"key": "2B13", "mappings": {"default": {"default": "Quadrat mit unterer Hälfte schwarz"}}}, {"key": "2B14", "mappings": {"default": {"default": "Quadrat mit oberer rechter diagonaler Hä<PERSON>te schwarz"}}}, {"key": "2B15", "mappings": {"default": {"default": "Quadrat mit unterer linker diagonaler <PERSON><PERSON><PERSON><PERSON> schwarz"}}}, {"key": "2B16", "mappings": {"default": {"default": "Diamant mit linker H<PERSON><PERSON>te schwarz"}}}, {"key": "2B17", "mappings": {"default": {"default": "Diamant mit rechter Hälfte schwarz"}}}, {"key": "2B18", "mappings": {"default": {"default": "Diamant mit oberer Hälfte schwarz"}}}, {"key": "2B19", "mappings": {"default": {"default": "Diamant mit unterer Hälfte schwarz"}}}, {"key": "2B1A", "mappings": {"default": {"default": "Gepunktetes Quadrat"}}}, {"key": "2B1B", "mappings": {"default": {"default": "Schwarzes großes Quadrat"}}}, {"key": "2B1C", "mappings": {"default": {"default": "Weißes großes Quadrat"}}}, {"key": "2B1D", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> sehr kleines Quadrat"}}}, {"key": "2B1E", "mappings": {"default": {"default": "Weißes sehr kleines Quadrat"}}}, {"key": "2B1F", "mappings": {"default": {"default": "Schwarzes Pentagon"}}}, {"key": "2B20", "mappings": {"default": {"default": "Weißes Pentagon"}}}, {"key": "2B21", "mappings": {"default": {"default": "Weißes Sechseck"}}}, {"key": "2B22", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "2B23", "mappings": {"default": {"default": "Horizontales schwarzes Sechseck"}}}, {"key": "2B24", "mappings": {"default": {"default": "Schwarzer großer Kreis"}}}, {"key": "2B25", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> mittlerer Diamant"}}}, {"key": "2B26", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> mittlerer Diamant"}}}, {"key": "2B27", "mappings": {"default": {"default": "Schwarze mittlere Raute"}}}, {"key": "2B28", "mappings": {"default": {"default": "Weiße mittlere Raute"}}}, {"key": "2B29", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>"}}}, {"key": "2B2A", "mappings": {"default": {"default": "Schwarze kleine <PERSON>"}}}, {"key": "2B2B", "mappings": {"default": {"default": "Weiße kleine Raute"}}}, {"key": "2B2C", "mappings": {"default": {"default": "Schwarze horizontale Ellipse"}}}, {"key": "2B2D", "mappings": {"default": {"default": "Weiße horizontale Ellipse"}}}, {"key": "2B2E", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> vertika<PERSON>"}}}, {"key": "2B2F", "mappings": {"default": {"default": "Weiße vertikale Ellipse"}}}, {"key": "2B50", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> mittler<PERSON>"}}}, {"key": "2B51", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>"}}}, {"key": "2B52", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> klein<PERSON>"}}}, {"key": "2B53", "mappings": {"default": {"default": "Schwarzes nach rechts zeigendes Pentagon"}}}, {"key": "2B54", "mappings": {"default": {"default": "Weißes nach rechts zeigendes Pentagon"}}}, {"key": "2B55", "mappings": {"default": {"default": "Schwerer großer Kreis"}}}, {"key": "2B56", "mappings": {"default": {"default": "Schweres Oval mit innenliegendem Oval"}}}, {"key": "2B57", "mappings": {"default": {"default": "Schwerer Kreis mit Kreis nach innen"}}}, {"key": "2B58", "mappings": {"default": {"default": "Schwerer Kreis"}}}, {"key": "2B59", "mappings": {"default": {"default": "Schwerer eingekreister Saltire"}}}], "de/symbols/math_harpoons.min": [{"locale": "de"}, {"key": "21BC", "mappings": {"default": {"default": "<PERSON><PERSON> Harpune mit Barb nach oben"}}}, {"key": "21BD", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> nach links mit Barb nach unten"}}}, {"key": "21BE", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> nach oben mit Barb nach rechts"}}}, {"key": "21BF", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> nach oben mit Barb nach links"}}}, {"key": "21C0", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> nach rechts mit Barb nach oben"}}}, {"key": "21C1", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> nach rechts mit Barb nach unten"}}}, {"key": "21C2", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> nach unten mit Barb nach rechts"}}}, {"key": "21C3", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> nach unten mit Barb nach links"}}}, {"key": "21CB", "mappings": {"default": {"default": "Harpune nach links über Harpune nach rechts"}}}, {"key": "21CC", "mappings": {"default": {"default": "Harpune nach rechts über Harpune nach links"}}}, {"key": "294A", "mappings": {"default": {"default": "Linke Barb Up Rechte Barb Down Harpoon"}}}, {"key": "294B", "mappings": {"default": {"default": "Linke Barb Down Rechte Barb Up Harpoon"}}}, {"key": "294C", "mappings": {"default": {"default": "Nach oben Barb Rechts Nach links Barb Harpoon"}}}, {"key": "294D", "mappings": {"default": {"default": "Up Barb Left Abwärts Barb Right Harpoon"}}}, {"key": "294E", "mappings": {"default": {"default": "Linke Barb Up Rechte Barb Up Harpoon"}}}, {"key": "294F", "mappings": {"default": {"default": "Barb nach rechts Barb nach rechts Harpoon"}}}, {"key": "2950", "mappings": {"default": {"default": "Linke Barb Down Rechte Barb Down Harpoon"}}}, {"key": "2951", "mappings": {"default": {"default": "Up Barb Left Abwärts Barb Left Harpoon"}}}, {"key": "2952", "mappings": {"default": {"default": "<PERSON><PERSON> Harp<PERSON> mit Barb Up To Bar"}}}, {"key": "2953", "mappings": {"default": {"default": "Ha<PERSON>une nach rechts mit Barb bis zur Bar"}}}, {"key": "2954", "mappings": {"default": {"default": "Ha<PERSON><PERSON> nach oben mit Widerhaken rechts"}}}, {"key": "2955", "mappings": {"default": {"default": "Ha<PERSON>une nach unten mit Barb Right to Bar"}}}, {"key": "2956", "mappings": {"default": {"default": "<PERSON><PERSON> Harpune mit Widerhaken nach unten"}}}, {"key": "2957", "mappings": {"default": {"default": "Ha<PERSON><PERSON> nach rechts mit Widerhaken nach unten"}}}, {"key": "2958", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> nach oben mit Barb nach links"}}}, {"key": "2959", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> nach unten mit Barb nach links"}}}, {"key": "295A", "mappings": {"default": {"default": "<PERSON><PERSON> Harpune mit Barb Up von <PERSON> Bar"}}}, {"key": "295B", "mappings": {"default": {"default": "Ha<PERSON>une nach rechts mit Barb Up von der Bar"}}}, {"key": "295C", "mappings": {"default": {"default": "Ha<PERSON>une nach oben mit <PERSON>b rechts von der Bar"}}}, {"key": "295D", "mappings": {"default": {"default": "Ha<PERSON>une nach unten mit Barb rechts von der Bar"}}}, {"key": "295E", "mappings": {"default": {"default": "<PERSON><PERSON> Harpune mit Barb Down von der Bar"}}}, {"key": "295F", "mappings": {"default": {"default": "Re<PERSON>s Harpoon mit Barb Down von der Bar"}}}, {"key": "2960", "mappings": {"default": {"default": "Ha<PERSON><PERSON> nach oben mit Barb links von der Bar"}}}, {"key": "2961", "mappings": {"default": {"default": "Ha<PERSON>une nach unten mit Barb links von der Bar"}}}, {"key": "2962", "mappings": {"default": {"default": "<PERSON>rpune nach links mit Barb nach oben Harpune nach links mit Barb nach unten"}}}, {"key": "2963", "mappings": {"default": {"default": "Harpune nach oben mit Barb links neben Harpune nach oben mit Barb rechts"}}}, {"key": "2964", "mappings": {"default": {"default": "Ha<PERSON><PERSON> nach rechts mit Barb nach oben Harpoon nach rechts mit Barb nach unten"}}}, {"key": "2965", "mappings": {"default": {"default": "Harpune nach unten mit Barb nach unten Harpune nach unten mit Barb nach rechts"}}}, {"key": "2966", "mappings": {"default": {"default": "Linke Harpune mit Barb Up Oben Rechts Harpoon mit Barb Up"}}}, {"key": "2967", "mappings": {"default": {"default": "Linke Harpune mit Widerhaken nach unten Rechts Harpune mit Widerhaken nach unten"}}}, {"key": "2968", "mappings": {"default": {"default": "Harpune nach rechts mit Barb nach oben Harpune nach links mit Barb nach oben"}}}, {"key": "2969", "mappings": {"default": {"default": "Harpune nach rechts mit Barb nach unten Harpune nach links mit Barb nach unten"}}}, {"key": "296A", "mappings": {"default": {"default": "<PERSON><PERSON> mit Barb Up Above Long Dash"}}}, {"key": "296B", "mappings": {"default": {"default": "<PERSON><PERSON> mit Barb Down Under Long Dash"}}}, {"key": "296C", "mappings": {"default": {"default": "Ha<PERSON>une nach rechts mit Barb Up Long Long Dash"}}}, {"key": "296D", "mappings": {"default": {"default": "Ha<PERSON><PERSON> nach rechts mit Widerhaken nach unten"}}}, {"key": "296E", "mappings": {"default": {"default": "Harpune nach oben mit Barb links neben Harpune nach unten mit Barb rechts"}}}, {"key": "296F", "mappings": {"default": {"default": "Harpune nach unten mit Barb links neben Harpune nach oben mit Barb rechts"}}}, {"key": "297C", "mappings": {"default": {"default": "linker <PERSON>"}}}, {"key": "297D", "mappings": {"default": {"default": "<PERSON>cht<PERSON>"}}}, {"key": "297E", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "297F", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}], "de/symbols/math_non_characters.min": [{"locale": "de"}, {"key": "210F", "mappings": {"default": {"default": "Planck-Konstante über zwei Pi"}}}, {"key": "2114", "mappings": {"default": {"default": "L B Balkensymbol"}}}, {"key": "2116", "mappings": {"default": {"default": "Numero-Zeichen"}}}, {"key": "2117", "mappings": {"default": {"default": "Tonaufnahme Copyright"}}}, {"key": "211E", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>en"}}}, {"key": "211F", "mappings": {"default": {"default": "Antwort"}}}, {"key": "2120", "mappings": {"default": {"default": "Dienstleistungsmarke"}}}, {"key": "2121", "mappings": {"default": {"default": "Telefon Zeichen"}}}, {"key": "2122", "mappings": {"default": {"default": "Markenzeichen"}}}, {"key": "2123", "mappings": {"default": {"default": "Versicle"}}}, {"key": "2125", "mappings": {"default": {"default": "Unze-Zeichen"}}}, {"key": "2126", "mappings": {"default": {"default": "Ohm-Zeichen"}}}, {"key": "2127", "mappings": {"default": {"default": "Umgekehrtes Ohm-Zeichen"}}}, {"key": "212A", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "212B", "mappings": {"default": {"default": "Angstrom-Zeichen"}}}, {"key": "212E", "mappings": {"default": {"default": "Geschätztes Symbol"}}}, {"key": "2132", "mappings": {"default": {"default": "Umgeschlagenes Kapital F"}}}, {"key": "2139", "mappings": {"default": {"default": "Informationsquelle"}}}, {"key": "213A", "mappings": {"default": {"default": "G<PERSON>rehtes Kapital Q"}}}, {"key": "213B", "mappings": {"default": {"default": "Faksimile-<PERSON><PERSON><PERSON>"}}}, {"key": "2141", "mappings": {"default": {"default": "Sans-Serif Capital G"}}}, {"key": "2142", "mappings": {"default": {"default": "Sans-Serif Capital L"}}}, {"key": "2143", "mappings": {"default": {"default": "Aufgehobenes Sans-Serif-Kapital L"}}}, {"key": "2144", "mappings": {"default": {"default": "Sans-Serif Capital Y"}}}], "de/symbols/math_symbols.min": [{"locale": "de"}, {"key": "0021", "mappings": {"default": {"default": "Fakultät"}}}, {"key": "0022", "mappings": {"default": {"default": "Anführungszeichen"}}}, {"key": "0023", "mappings": {"default": {"default": "Nummernzeichen"}}}, {"key": "0024", "mappings": {"default": {"default": "Dollar"}}}, {"key": "0025", "mappings": {"default": {"default": "Prozent"}}}, {"key": "0026", "mappings": {"default": {"default": "und"}}}, {"key": "0027", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "002A", "mappings": {"default": {"default": "times"}}}, {"key": "002B", "mappings": {"default": {"default": "plus"}}}, {"key": "002C", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "002D", "mappings": {"default": {"default": "minus"}}}, {"key": "002E", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "002F", "mappings": {"default": {"default": "Schrägstrich"}}}, {"key": "003A", "mappings": {"default": {"default": "Doppelpunkt"}}}, {"key": "003B", "mappings": {"default": {"default": "Strichpunkt"}}}, {"key": "003C", "mappings": {"default": {"default": "kleiner als"}}}, {"key": "003D", "mappings": {"default": {"default": "ist gleich"}}}, {"key": "003E", "mappings": {"default": {"default": "gr<PERSON><PERSON><PERSON> als"}}}, {"key": "003F", "mappings": {"default": {"default": "Fragezeichen"}}}, {"key": "0040", "mappings": {"default": {"default": "Klammeraffe"}}}, {"key": "005C", "mappings": {"default": {"default": "Umgekehrter Schrägstrich"}}}, {"key": "005E", "mappings": {"default": {"default": "circumflex"}}}, {"key": "005F", "mappings": {"default": {"default": "<PERSON><PERSON>tric<PERSON>"}}}, {"key": "0060", "mappings": {"default": {"default": "Gravis <PERSON>"}}}, {"key": "007C", "mappings": {"default": {"default": "senk<PERSON><PERSON> Strich"}}}, {"key": "007E", "mappings": {"default": {"default": "tilde"}}}, {"key": "00A1", "mappings": {"default": {"default": "Umgekehrtes Ausrufezeichen"}}}, {"key": "00A2", "mappings": {"default": {"default": "Cent"}}}, {"key": "00A3", "mappings": {"default": {"default": "Pfund"}}}, {"key": "00A4", "mappings": {"default": {"default": "währungszeichen"}}}, {"key": "00A5", "mappings": {"default": {"default": "yen"}}}, {"key": "00A6", "mappings": {"default": {"default": "geb<PERSON>chene Linie"}}}, {"key": "00A7", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "00A8", "mappings": {"default": {"default": "diaeresis"}}}, {"key": "00A9", "mappings": {"default": {"default": "Copyright"}}}, {"key": "00AA", "mappings": {"default": {"default": "weibliches Ordnungszeichen"}}}, {"key": "00AB", "mappings": {"default": {"default": "Nach links zeigendes doppeltes spitzes Anführungszeichen"}}}, {"key": "00AC", "mappings": {"default": {"default": "nicht"}}}, {"key": "00AE", "mappings": {"default": {"default": "registered sign"}}}, {"key": "00AF", "mappings": {"default": {"default": "Überstrich", "alternative": "<PERSON><PERSON><PERSON>"}}}, {"key": "00B0", "mappings": {"default": {"default": "Grad"}}}, {"key": "00B1", "mappings": {"default": {"default": "plus minus"}}}, {"key": "00B4", "mappings": {"default": {"default": "Acuteakzent"}}}, {"key": "00B5", "mappings": {"default": {"default": "Mikrozeichen"}}}, {"key": "00B6", "mappings": {"default": {"default": "Paragra<PERSON>"}}}, {"key": "00B7", "mappings": {"default": {"default": "mal", "alternative": "<PERSON><PERSON> mittig"}}}, {"key": "00B8", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "00BA", "mappings": {"default": {"default": "Männliches Ordnungszeichen"}}}, {"key": "00BB", "mappings": {"default": {"default": "Nach rechts zeigendes doppeltes spitzes Anführungszeichen"}}}, {"key": "00BF", "mappings": {"default": {"default": "umgekehrtes Fragezeichen"}}}, {"key": "00D7", "mappings": {"default": {"default": "mal"}}}, {"key": "00F7", "mappings": {"default": {"default": "get<PERSON>t durch"}}}, {"key": "02B9", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "02BA", "mappings": {"default": {"default": "zwei Strich"}}}, {"key": "02D8", "mappings": {"default": {"default": "Breve"}}}, {"key": "02D9", "mappings": {"default": {"default": "Überpunkt"}}}, {"key": "02DA", "mappings": {"default": {"default": "Überring"}}}, {"key": "02DB", "mappings": {"default": {"default": "<PERSON>gonek"}}}, {"key": "02DC", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "02DD", "mappings": {"default": {"default": "Doppleakutakzent"}}}, {"key": "2010", "mappings": {"default": {"default": "Bindestrich"}}}, {"key": "2011", "mappings": {"default": {"default": "Nicht brechender Bindestrich"}}}, {"key": "2012", "mappings": {"default": {"default": "Abb<PERSON><PERSON><PERSON> Bindestrich"}}}, {"key": "2013", "mappings": {"default": {"default": "En Dash"}}}, {"key": "2014", "mappings": {"default": {"default": "EM Dash"}}}, {"key": "2015", "mappings": {"default": {"default": "Horizontale <PERSON>"}}}, {"key": "2016", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> vertika<PERSON>"}}}, {"key": "2017", "mappings": {"default": {"default": "Do<PERSON><PERSON> Unterstrich"}}}, {"key": "2018", "mappings": {"default": {"default": "Linkes einfaches Anführungszeichen"}}}, {"key": "2019", "mappings": {"default": {"default": "Rechtes einfaches Anführungszeichen"}}}, {"key": "201A", "mappings": {"default": {"default": "Einzelnes rechtes Anführungszeichen"}}}, {"key": "201B", "mappings": {"default": {"default": "Einzelnes linkes Anführungszeichen"}}}, {"key": "201C", "mappings": {"default": {"default": "Linkes doppeltes Anführungszeichen"}}}, {"key": "201D", "mappings": {"default": {"default": "Rechtes doppeltes Anführungszeichen"}}}, {"key": "201E", "mappings": {"default": {"default": "Doppeltes rechtes Anführungszeichen"}}}, {"key": "201F", "mappings": {"default": {"default": "Doppeltes linkes Anführungszeichen"}}}, {"key": "2020", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "2021", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "2022", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "2023", "mappings": {"default": {"default": "Dreieckige Kugel"}}}, {"key": "2024", "mappings": {"default": {"default": "Einzelpunkt"}}}, {"key": "2025", "mappings": {"default": {"default": "zwei Punkte"}}}, {"key": "2026", "mappings": {"default": {"default": "horizontale Ellipsis"}}}, {"key": "2027", "mappings": {"default": {"default": "Trennungspunkt"}}}, {"key": "2030", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "2031", "mappings": {"default": {"default": "pro Zehntausend"}}}, {"key": "2032", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "2033", "mappings": {"default": {"default": "zwei Strich"}}}, {"key": "2034", "mappings": {"default": {"default": "d<PERSON><PERSON>"}}}, {"key": "2035", "mappings": {"default": {"default": "strich invertiert"}}}, {"key": "2036", "mappings": {"default": {"default": "zwei Strich invertiert"}}}, {"key": "2037", "mappings": {"default": {"default": "dre<PERSON> invertiert"}}}, {"key": "2038", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "2039", "mappings": {"default": {"default": "Einfacher nach links zeigender Winkel Anführungszeichen"}}}, {"key": "203A", "mappings": {"default": {"default": "Einzelne Nach-rechts-Winkel-Anführungszeichen"}}}, {"key": "203B", "mappings": {"default": {"default": "Referenzmarke"}}}, {"key": "203C", "mappings": {"default": {"default": "Doppelausrufezeichen"}}}, {"key": "203D", "mappings": {"default": {"default": "Interrobang"}}}, {"key": "203E", "mappings": {"default": {"default": "Überschrift"}}}, {"key": "203F", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "2040", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "2041", "mappings": {"default": {"default": "Caret-Einfügungspunkt"}}}, {"key": "2042", "mappings": {"default": {"default": "Asterism"}}}, {"key": "2043", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "2044", "mappings": {"default": {"default": "Fraktions-Schrägstrich"}}}, {"key": "2047", "mappings": {"default": {"default": "Doppel-Fragezeichen"}}}, {"key": "2048", "mappings": {"default": {"default": "Fragezeichen für Ausrufezeichen"}}}, {"key": "2049", "mappings": {"default": {"default": "Ausrufezeichen"}}}, {"key": "204B", "mappings": {"default": {"default": "Umgekehrtes Pilcrow-Zeichen"}}}, {"key": "204C", "mappings": {"default": {"default": "Schwarze Kugel nach links"}}}, {"key": "204D", "mappings": {"default": {"default": "Schwarze Kugel nach rechts"}}}, {"key": "204E", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "204F", "mappings": {"default": {"default": "Umgekehrtes Semikolon"}}}, {"key": "2050", "mappings": {"default": {"default": "Nahansicht"}}}, {"key": "2051", "mappings": {"default": {"default": "Zwei Sternchen vertikal ausgerichtet"}}}, {"key": "2052", "mappings": {"default": {"default": "Kommerzielles Minuszeichen"}}}, {"key": "2053", "mappings": {"default": {"default": "<PERSON>"}}}, {"key": "2054", "mappings": {"default": {"default": "Inverted <PERSON><PERSON>"}}}, {"key": "2055", "mappings": {"default": {"default": "Blume Interpunktionszeichen"}}}, {"key": "2056", "mappings": {"default": {"default": "<PERSON><PERSON>-Interpunktion"}}}, {"key": "2057", "mappings": {"default": {"default": "vier <PERSON>"}}}, {"key": "2058", "mappings": {"default": {"default": "Vierpunkt-Interpunktion"}}}, {"key": "2059", "mappings": {"default": {"default": "Fünf <PERSON>t-Interpunktion"}}}, {"key": "205A", "mappings": {"default": {"default": "Zwei Punkt-Interpunktion"}}}, {"key": "205B", "mappings": {"default": {"default": "Vier-Punkt-Markierung"}}}, {"key": "205C", "mappings": {"default": {"default": "Gepunktetes Kreuz"}}}, {"key": "205D", "mappings": {"default": {"default": "Tricolon"}}}, {"key": "205E", "mappings": {"default": {"default": "<PERSON>ert<PERSON><PERSON> vier Punkte"}}}, {"key": "207A", "mappings": {"default": {"default": "Hochgestelltes Pluszeichen"}}}, {"key": "207B", "mappings": {"default": {"default": "Hochgestelltes Minus"}}}, {"key": "207C", "mappings": {"default": {"default": "Hochgestelltes Gleichheitszeichen"}}}, {"key": "207D", "mappings": {"default": {"default": "Hochgestellte linke Klammer"}}}, {"key": "207E", "mappings": {"default": {"default": "Hochgestellte rechte Klammer"}}}, {"key": "208A", "mappings": {"default": {"default": "Index Pluszeichen"}}}, {"key": "208B", "mappings": {"default": {"default": "Tiefgestelltes <PERSON>"}}}, {"key": "208C", "mappings": {"default": {"default": "Tiefgestelltes Gleichheitszeichen"}}}, {"key": "208D", "mappings": {"default": {"default": "Tiefgestellte linke Klammer"}}}, {"key": "208E", "mappings": {"default": {"default": "Tiefgestellte rechte Klammer"}}}, {"key": "214A", "mappings": {"default": {"default": "Grundstücksgrenze"}}}, {"key": "214B", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> ged<PERSON><PERSON>"}}}, {"key": "214C", "mappings": {"default": {"default": "Pro Zeichen"}}}, {"key": "214D", "mappings": {"default": {"default": "Aktieselskab"}}}, {"key": "214E", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> klein F"}}}, {"key": "2200", "mappings": {"default": {"default": "für alle"}}}, {"key": "2201", "mappings": {"default": {"default": "Komplement"}}}, {"key": "2203", "mappings": {"default": {"default": "es gibt"}}}, {"key": "2204", "mappings": {"default": {"default": "es gibt nicht"}}}, {"key": "2205", "mappings": {"default": {"default": "leere Menge"}}}, {"key": "2206", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "2208", "mappings": {"default": {"default": "<PERSON><PERSON> von"}}}, {"key": "2209", "mappings": {"default": {"default": "nicht Element von"}}}, {"key": "220A", "mappings": {"default": {"default": "<PERSON><PERSON> von"}}}, {"key": "220B", "mappings": {"default": {"default": "hat als Element"}}}, {"key": "220C", "mappings": {"default": {"default": "hat nicht als Element"}}}, {"key": "220D", "mappings": {"default": {"default": "hat als Element"}}}, {"key": "220E", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "220F", "mappings": {"default": {"default": "Produkt"}}}, {"key": "2210", "mappings": {"default": {"default": "Ko<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "2211", "mappings": {"default": {"default": "Summe"}}}, {"key": "2212", "mappings": {"default": {"default": "minus"}}}, {"key": "2213", "mappings": {"default": {"default": "minus plus"}}}, {"key": "2214", "mappings": {"default": {"default": "Plus mit Punkt"}}}, {"key": "2215", "mappings": {"default": {"default": "Division"}}}, {"key": "2216", "mappings": {"default": {"default": "Mengendifferenz"}}}, {"key": "2217", "mappings": {"default": {"default": "Sternoperator"}}}, {"key": "2218", "mappings": {"default": {"default": "verknüpft mit"}}}, {"key": "2219", "mappings": {"default": {"default": "Kreisoperator"}}}, {"key": "221A", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "221B", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "221C", "mappings": {"default": {"default": "vier<PERSON>"}}}, {"key": "221D", "mappings": {"default": {"default": "proportional zu"}}}, {"key": "221E", "mappings": {"default": {"default": "unendlich"}}}, {"key": "221F", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "2220", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "2221", "mappings": {"default": {"default": "g<PERSON><PERSON><PERSON>"}}}, {"key": "2222", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "2223", "mappings": {"default": {"default": "teilt"}}}, {"key": "2224", "mappings": {"default": {"default": "teilt nicht"}}}, {"key": "2225", "mappings": {"default": {"default": "parallel zu"}}}, {"key": "2226", "mappings": {"default": {"default": "nicht parallel zu"}}}, {"key": "2227", "mappings": {"default": {"default": "und"}}}, {"key": "2228", "mappings": {"default": {"default": "oder"}}}, {"key": "2229", "mappings": {"default": {"default": "Durchschnitt"}}}, {"key": "222A", "mappings": {"default": {"default": "Vereinigung"}}}, {"key": "222B", "mappings": {"default": {"default": "Integral"}}}, {"key": "222C", "mappings": {"default": {"default": "Doppelintegral"}}}, {"key": "222D", "mappings": {"default": {"default": "Dreifachintegral"}}}, {"key": "222E", "mappings": {"default": {"default": "Randintegral"}}}, {"key": "222F", "mappings": {"default": {"default": "Oberflächenintegral"}}}, {"key": "2230", "mappings": {"default": {"default": "Volumenintegral"}}}, {"key": "2231", "mappings": {"default": {"default": "Integral im Uhrzeigersinn"}}}, {"key": "2232", "mappings": {"default": {"default": "Kurvenintegral im Uhrzeigersinn"}}}, {"key": "2233", "mappings": {"default": {"default": "Kurvenintegral im Gegenuhrzeigersinn"}}}, {"key": "2234", "mappings": {"default": {"default": "folglich"}}}, {"key": "2235", "mappings": {"default": {"default": "weil"}}}, {"key": "2236", "mappings": {"default": {"default": "Verhältnis"}}}, {"key": "2237", "mappings": {"default": {"default": "Proportion"}}}, {"key": "2238", "mappings": {"default": {"default": "<PERSON><PERSON> mit <PERSON>"}}}, {"key": "2239", "mappings": {"default": {"default": "Überschuss"}}}, {"key": "223A", "mappings": {"default": {"default": "geometrische Proportion"}}}, {"key": "223B", "mappings": {"default": {"default": "homothetisch"}}}, {"key": "223C", "mappings": {"default": {"default": "Tildeoperator"}}}, {"key": "223D", "mappings": {"default": {"default": "Umgekehrte Tilde"}}}, {"key": "223E", "mappings": {"default": {"default": "Umgekehrtes stummes S"}}}, {"key": "223F", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "2240", "mappings": {"default": {"default": "Kranzprodukt"}}}, {"key": "2241", "mappings": {"default": {"default": "durchgestrichene Tilde"}}}, {"key": "2242", "mappings": {"default": {"default": "<PERSON>us über Tilde"}}}, {"key": "2243", "mappings": {"default": {"default": "asymptotisch gleich"}}}, {"key": "2244", "mappings": {"default": {"default": "nicht asymptotisch gleich"}}}, {"key": "2245", "mappings": {"default": {"default": "ungefä<PERSON> gleich"}}}, {"key": "2246", "mappings": {"default": {"default": "unge<PERSON><PERSON><PERSON>, aber nicht ganz gleich"}}}, {"key": "2247", "mappings": {"default": {"default": "weder ungefähr noch ganz gleich"}}}, {"key": "2248", "mappings": {"default": {"default": "be<PERSON><PERSON> gleich"}}}, {"key": "2249", "mappings": {"default": {"default": "nicht beinahe gleich"}}}, {"key": "224A", "mappings": {"default": {"default": "be<PERSON><PERSON> gleich oder gleich"}}}, {"key": "224B", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "224C", "mappings": {"default": {"default": "alles gleich"}}}, {"key": "224D", "mappings": {"default": {"default": "äquivalent zu"}}}, {"key": "224E", "mappings": {"default": {"default": "geometrisch äquivalent zu"}}}, {"key": "224F", "mappings": {"default": {"default": "Differenz zwischen"}}}, {"key": "2250", "mappings": {"default": {"default": "nähert sich der Grenze"}}}, {"key": "2251", "mappings": {"default": {"default": "geomet<PERSON><PERSON> gleich"}}}, {"key": "2252", "mappings": {"default": {"default": "Ungefähr gleich oder das Bild von"}}}, {"key": "2253", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> von oder ungefähr gleich"}}}, {"key": "2254", "mappings": {"default": {"default": "definiert als"}}}, {"key": "2255", "mappings": {"default": {"default": "definiert als von rechts"}}}, {"key": "2256", "mappings": {"default": {"default": "ungefä<PERSON> gleich"}}}, {"key": "2257", "mappings": {"default": {"default": "ungefä<PERSON> gleich"}}}, {"key": "2258", "mappings": {"default": {"default": "entspricht"}}}, {"key": "2259", "mappings": {"default": {"default": "entspricht"}}}, {"key": "225A", "mappings": {"default": {"default": "gle<PERSON><PERSON><PERSON><PERSON><PERSON> zu"}}}, {"key": "225B", "mappings": {"default": {"default": "gleich mit <PERSON>"}}}, {"key": "225C", "mappings": {"default": {"default": "gleich mit <PERSON>"}}}, {"key": "225D", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON> gleich"}}}, {"key": "225E", "mappings": {"default": {"default": "gemessen mit"}}}, {"key": "225F", "mappings": {"default": {"default": "vielleicht gleich"}}}, {"key": "2260", "mappings": {"default": {"default": "ungle<PERSON>"}}}, {"key": "2261", "mappings": {"default": {"default": "kongruent mit"}}}, {"key": "2262", "mappings": {"default": {"default": "nicht kongruent mit"}}}, {"key": "2263", "mappings": {"default": {"default": "genau äquivalent mit"}}}, {"key": "2264", "mappings": {"default": {"default": "kleiner oder gleich"}}}, {"key": "2265", "mappings": {"default": {"default": "gr<PERSON><PERSON><PERSON> oder gleich"}}}, {"key": "2266", "mappings": {"default": {"default": "kleiner als über gleich"}}}, {"key": "2267", "mappings": {"default": {"default": "gr<PERSON>ßer als über gleich"}}}, {"key": "2268", "mappings": {"default": {"default": "kleiner als, aber nicht gleich"}}}, {"key": "2269", "mappings": {"default": {"default": "gr<PERSON><PERSON><PERSON> al<PERSON>, aber nicht gleich"}}}, {"key": "226A", "mappings": {"default": {"default": "viel kleiner als"}}}, {"key": "226B", "mappings": {"default": {"default": "viel größer als"}}}, {"key": "226C", "mappings": {"default": {"default": "zwischen"}}}, {"key": "226D", "mappings": {"default": {"default": "nicht äquivalent mit"}}}, {"key": "226E", "mappings": {"default": {"default": "nicht kleiner als"}}}, {"key": "226F", "mappings": {"default": {"default": "nicht größer als"}}}, {"key": "2270", "mappings": {"default": {"default": "weder kleiner als noch gleich"}}}, {"key": "2271", "mappings": {"default": {"default": "weder größer als noch gleich"}}}, {"key": "2272", "mappings": {"default": {"default": "kleiner als oder äquivalent"}}}, {"key": "2273", "mappings": {"default": {"default": "größer als oder äquivalent"}}}, {"key": "2274", "mappings": {"default": {"default": "weder kleiner als noch äquivalent"}}}, {"key": "2275", "mappings": {"default": {"default": "weder größer als noch äquivalent"}}}, {"key": "2276", "mappings": {"default": {"default": "kleiner oder größer als"}}}, {"key": "2277", "mappings": {"default": {"default": "gr<PERSON>ßer oder kleiner als"}}}, {"key": "2278", "mappings": {"default": {"default": "weder kleiner noch größer als"}}}, {"key": "2279", "mappings": {"default": {"default": "weder größer noch kleiner als"}}}, {"key": "227A", "mappings": {"default": {"default": "vorange<PERSON>"}}}, {"key": "227B", "mappings": {"default": {"default": "nachfolgend"}}}, {"key": "227C", "mappings": {"default": {"default": "vorangehend oder gleich"}}}, {"key": "227D", "mappings": {"default": {"default": "nachfolgend oder gleich"}}}, {"key": "227E", "mappings": {"default": {"default": "vorangehend oder äquivalent"}}}, {"key": "227F", "mappings": {"default": {"default": "nachfolgend oder äquivalent"}}}, {"key": "2280", "mappings": {"default": {"default": "nicht vorangehend"}}}, {"key": "2281", "mappings": {"default": {"default": "nicht nachfolgend"}}}, {"key": "2282", "mappings": {"default": {"default": "echte Teilmenge von"}}}, {"key": "2283", "mappings": {"default": {"default": "echte Obermenge von"}}}, {"key": "2284", "mappings": {"default": {"default": "keine echte Teilmenge von"}}}, {"key": "2285", "mappings": {"default": {"default": "kein echte Obermenge von"}}}, {"key": "2286", "mappings": {"default": {"default": "Teilmen<PERSON> oder gleich"}}}, {"key": "2287", "mappings": {"default": {"default": "Obermenge oder gleich"}}}, {"key": "2288", "mappings": {"default": {"default": "weder Teilmenge noch gleich"}}}, {"key": "2289", "mappings": {"default": {"default": "weder Obermenge noch gleich"}}}, {"key": "228A", "mappings": {"default": {"default": "Teilmenge aber nicht gleich"}}}, {"key": "228B", "mappings": {"default": {"default": "Obermenge aber nicht gleich"}}}, {"key": "228C", "mappings": {"default": {"default": "Multimenge"}}}, {"key": "228D", "mappings": {"default": {"default": "Multimengenmultiplikation"}}}, {"key": "228E", "mappings": {"default": {"default": "Multimengenvereinigung"}}}, {"key": "228F", "mappings": {"default": {"default": "quadratisches Bild von"}}}, {"key": "2290", "mappings": {"default": {"default": "quadratisches Original von"}}}, {"key": "2291", "mappings": {"default": {"default": "quadratisches Bild oder gleich"}}}, {"key": "2292", "mappings": {"default": {"default": "quadratisches Original oder gleich"}}}, {"key": "2293", "mappings": {"default": {"default": "quadratische Schnittmenge"}}}, {"key": "2294", "mappings": {"default": {"default": "quadratische VereinigungTasse"}}}, {"key": "2295", "mappings": {"default": {"default": "eingekreistes Plus"}}}, {"key": "2296", "mappings": {"default": {"default": "eingek<PERSON><PERSON> Minus"}}}, {"key": "2297", "mappings": {"default": {"default": "eingekreiste Multiplikation"}}}, {"key": "2298", "mappings": {"default": {"default": "eingekreiste Division"}}}, {"key": "2299", "mappings": {"default": {"default": "eingekreister Punktoperator"}}}, {"key": "229A", "mappings": {"default": {"default": "eingekreister Ringoperator"}}}, {"key": "229B", "mappings": {"default": {"default": "eingekreister Sternoperator"}}}, {"key": "229C", "mappings": {"default": {"default": "eingekreistes Gleich"}}}, {"key": "229D", "mappings": {"default": {"default": "eingekreister Gedankenstrich"}}}, {"key": "229E", "mappings": {"default": {"default": "eingerahmtes Plus"}}}, {"key": "229F", "mappings": {"default": {"default": "<PERSON>ingerahm<PERSON> Minus"}}}, {"key": "22A0", "mappings": {"default": {"default": "eingerahmte Multiplikation"}}}, {"key": "22A1", "mappings": {"default": {"default": "eingerahmter Punktoperator"}}}, {"key": "22A2", "mappings": {"default": {"default": "ergibt"}}}, {"key": "22A3", "mappings": {"default": {"default": "ergibt nicht"}}}, {"key": "22A4", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> von"}}}, {"key": "22A5", "mappings": {"default": {"default": "senkrecht auf"}}}, {"key": "22A6", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "22A7", "mappings": {"default": {"default": "Model"}}}, {"key": "22A8", "mappings": {"default": {"default": "wahr"}}}, {"key": "22A9", "mappings": {"default": {"default": "erzwingen"}}}, {"key": "22AA", "mappings": {"default": {"default": "dre<PERSON><PERSON> vertikale Leiste mit rechtem Drehkreuz"}}}, {"key": "22AB", "mappings": {"default": {"default": "doppelte vertikale Leiste mit doppeltem rechtem Drehkreuz"}}}, {"key": "22AC", "mappings": {"default": {"default": "beweist nicht"}}}, {"key": "22AD", "mappings": {"default": {"default": "nicht wahr"}}}, {"key": "22AE", "mappings": {"default": {"default": "nicht erzwingen"}}}, {"key": "22AF", "mappings": {"default": {"default": "negierte doppelte vertikale Leiste mit doppeltem rechten Drehkreuz"}}}, {"key": "22B0", "mappings": {"default": {"default": "vorangehend in Relation"}}}, {"key": "22B1", "mappings": {"default": {"default": "nachfolgend in Relation"}}}, {"key": "22B2", "mappings": {"default": {"default": "normale Untergruppe"}}}, {"key": "22B3", "mappings": {"default": {"default": "enthält normale Untergruppe"}}}, {"key": "22B4", "mappings": {"default": {"default": "normale Untergruppe von oder gleich"}}}, {"key": "22B5", "mappings": {"default": {"default": "enthält normale Untergruppe oder gleich"}}}, {"key": "22B6", "mappings": {"default": {"default": "Original von"}}}, {"key": "22B7", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> von"}}}, {"key": "22B8", "mappings": {"default": {"default": "Mehrfachzuordnung"}}}, {"key": "22B9", "mappings": {"default": {"default": "hermitesch konjugierte Matrix"}}}, {"key": "22BA", "mappings": {"default": {"default": "e<PERSON><PERSON><PERSON>"}}}, {"key": "22BB", "mappings": {"default": {"default": "Ausschließendes Oder"}}}, {"key": "22BC", "mappings": {"default": {"default": "Nand verknüpft mit"}}}, {"key": "22BD", "mappings": {"default": {"default": "Nor verknüpft mit"}}}, {"key": "22BF", "mappings": {"default": {"default": "rechtwinkliges Dreieck"}}}, {"key": "22C0", "mappings": {"default": {"default": "N-stufiges logisches Und"}}}, {"key": "22C1", "mappings": {"default": {"default": "N-stufiges logisches Oder"}}}, {"key": "22C2", "mappings": {"default": {"default": "N-stufiger Durchschnitt"}}}, {"key": "22C3", "mappings": {"default": {"default": "N-stufige Vereinigung"}}}, {"key": "22C4", "mappings": {"default": {"default": "Rautenoperator"}}}, {"key": "22C5", "mappings": {"default": {"default": "mal", "alternative": "Multiplikationspunkt"}}}, {"key": "22C6", "mappings": {"default": {"default": "Sternoperator"}}}, {"key": "22C7", "mappings": {"default": {"default": "Divisionsanzahl"}}}, {"key": "22C8", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "22C9", "mappings": {"default": {"default": "linkes halbdirektes Produkt"}}}, {"key": "22CA", "mappings": {"default": {"default": "rechtes halbdirektes Produkt"}}}, {"key": "22CB", "mappings": {"default": {"default": "linkes halbdirektes Produkt"}}}, {"key": "22CC", "mappings": {"default": {"default": "rechtes halbdirektes Produkt"}}}, {"key": "22CD", "mappings": {"default": {"default": "umgekehrte Tilde g<PERSON>"}}}, {"key": "22CE", "mappings": {"default": {"default": "geschweiftes logisches Oder"}}}, {"key": "22CF", "mappings": {"default": {"default": "geschweiftes logisches Und"}}}, {"key": "22D0", "mappings": {"default": {"default": "do<PERSON><PERSON> Teilmenge"}}}, {"key": "22D1", "mappings": {"default": {"default": "doppelte Obermenge"}}}, {"key": "22D2", "mappings": {"default": {"default": "do<PERSON><PERSON> Durchschnitt"}}}, {"key": "22D3", "mappings": {"default": {"default": "doppelte Vereinigung"}}}, {"key": "22D4", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>sch<PERSON>"}}}, {"key": "22D5", "mappings": {"default": {"default": "gleich und parallel"}}}, {"key": "22D6", "mappings": {"default": {"default": "kleiner als mit Punkt"}}}, {"key": "22D7", "mappings": {"default": {"default": "gr<PERSON>ßer als mit Punkt"}}}, {"key": "22D8", "mappings": {"default": {"default": "sehr viel kleiner als"}}}, {"key": "22D9", "mappings": {"default": {"default": "sehr viel größer als"}}}, {"key": "22DA", "mappings": {"default": {"default": "kleiner als, gleich oder größer als"}}}, {"key": "22DB", "mappings": {"default": {"default": "g<PERSON><PERSON><PERSON><PERSON> al<PERSON>, gleich oder kleiner als"}}}, {"key": "22DC", "mappings": {"default": {"default": "gleich oder kleiner als"}}}, {"key": "22DD", "mappings": {"default": {"default": "gleich oder größer als"}}}, {"key": "22DE", "mappings": {"default": {"default": "gleich oder vorangehend"}}}, {"key": "22DF", "mappings": {"default": {"default": "gleich oder nachfolgend"}}}, {"key": "22E0", "mappings": {"default": {"default": "weder vorangehend oder gleich"}}}, {"key": "22E1", "mappings": {"default": {"default": "weder nachfolgend oder gleich"}}}, {"key": "22E2", "mappings": {"default": {"default": "kein quadratisches Bild oder gleich"}}}, {"key": "22E3", "mappings": {"default": {"default": "kein quadratisches Original oder gleich"}}}, {"key": "22E4", "mappings": {"default": {"default": "quadratisches Bild oder nicht gleich"}}}, {"key": "22E5", "mappings": {"default": {"default": "quadratisches Original oder nicht gleich"}}}, {"key": "22E6", "mappings": {"default": {"default": "kleiner als, aber nicht äquivalent"}}}, {"key": "22E7", "mappings": {"default": {"default": "gr<PERSON><PERSON><PERSON> al<PERSON>, aber nicht äquivalent"}}}, {"key": "22E8", "mappings": {"default": {"default": "v<PERSON><PERSON><PERSON>, aber nicht äquivalent"}}}, {"key": "22E9", "mappings": {"default": {"default": "nachfolgend, aber nicht äquivalent"}}}, {"key": "22EA", "mappings": {"default": {"default": "nicht normale Untergruppe von"}}}, {"key": "22EB", "mappings": {"default": {"default": "enthält keine normale Untergruppe"}}}, {"key": "22EC", "mappings": {"default": {"default": "keine normale Untergruppe von oder gleich"}}}, {"key": "22ED", "mappings": {"default": {"default": "enthält nicht als normale Untergruppe oder gleich"}}}, {"key": "22EE", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "22EF", "mappings": {"default": {"default": "Zentrierte horizontale Ellipse"}}}, {"key": "22F0", "mappings": {"default": {"default": "Diagonale Ellipse unten links nach oben rechts"}}}, {"key": "22F1", "mappings": {"default": {"default": "Diagonale Ellipse, oben links nach unten rechts"}}}, {"key": "22F2", "mappings": {"default": {"default": "Element mit langem horizontalen Strich"}}}, {"key": "22F3", "mappings": {"default": {"default": "Element mit vertikalem Strich am Ende des horizontalen Strichs"}}}, {"key": "22F4", "mappings": {"default": {"default": "kleines Element mit vertikalem Strich am Ende des horizontalen Strichs"}}}, {"key": "22F5", "mappings": {"default": {"default": "Element mit Punkt"}}}, {"key": "22F6", "mappings": {"default": {"default": "Element mit Überstrich"}}}, {"key": "22F7", "mappings": {"default": {"default": "kleines Element mit Überstrich"}}}, {"key": "22F8", "mappings": {"default": {"default": "Element mit Unterstrich"}}}, {"key": "22F9", "mappings": {"default": {"default": "Element mit 2 horizontalen Strichen"}}}, {"key": "22FA", "mappings": {"default": {"default": "umgekehrtes Elementzeichen mit langem horizontalen Strich"}}}, {"key": "22FB", "mappings": {"default": {"default": "umgekehrtes Elementzeichen mit vertikalem Strich am Ende des horizontalen Strichs"}}}, {"key": "22FC", "mappings": {"default": {"default": "kleines umgekehrtes Elementzeichen mit vertikalem Strich am Ende des horizontalen Strichs"}}}, {"key": "22FD", "mappings": {"default": {"default": "umgekehrtes Elementzeichen mit Überstrich"}}}, {"key": "22FE", "mappings": {"default": {"default": "kleines umgekehrtes Elementzeichen mit Überstrich"}}}, {"key": "22FF", "mappings": {"default": {"default": "Z-Notation-Bag-Mitgliedschaft"}}}, {"key": "2300", "mappings": {"default": {"default": "Durchmesser-Zeichen"}}}, {"key": "2302", "mappings": {"default": {"default": "Haus"}}}, {"key": "2305", "mappings": {"default": {"default": "Projektiv"}}}, {"key": "2306", "mappings": {"default": {"default": "Perspektive"}}}, {"key": "2307", "mappings": {"default": {"default": "Schlangenlinie"}}}, {"key": "2310", "mappings": {"default": {"default": "Umgekehrtes Nichtzeichen"}}}, {"key": "2311", "mappings": {"default": {"default": "Quadratische Raute"}}}, {"key": "2312", "mappings": {"default": {"default": "Bogen"}}}, {"key": "2313", "mappings": {"default": {"default": "Segment"}}}, {"key": "2314", "mappings": {"default": {"default": "Se<PERSON><PERSON>"}}}, {"key": "2795", "mappings": {"default": {"default": "Schweres Pluszeichen"}}}, {"key": "2796", "mappings": {"default": {"default": "Schweres Minuszeichen"}}}, {"key": "2797", "mappings": {"default": {"default": "Heavy Division Sign"}}}, {"key": "27B0", "mappings": {"default": {"default": "Curly <PERSON>"}}}, {"key": "27BF", "mappings": {"default": {"default": "Doppelte geschweifte Schleife"}}}, {"key": "27C1", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> Dreieck, das kleines weißes Dreieck enthält"}}}, {"key": "27C2", "mappings": {"default": {"default": "Aufrecht"}}}, {"key": "27C3", "mappings": {"default": {"default": "<PERSON>set <PERSON>"}}}, {"key": "27C4", "mappings": {"default": {"default": "Superset öffnen"}}}, {"key": "27C7", "mappings": {"default": {"default": "Oder mit innerem Punkt"}}}, {"key": "27C8", "mappings": {"default": {"default": "Umgekehrter Schrägstrich Vorhergehender Teilsatz"}}}, {"key": "27C9", "mappings": {"default": {"default": "Superset Vorgänger Schrägstrich"}}}, {"key": "27CA", "mappings": {"default": {"default": "Vertikaler Balken mit horizontalem Hub"}}}, {"key": "27CB", "mappings": {"default": {"default": "Mathematische aufsteigende Diagonale"}}}, {"key": "27CC", "mappings": {"default": {"default": "Lange Abteilung"}}}, {"key": "27CD", "mappings": {"default": {"default": "Mathematische fallende Diagonale"}}}, {"key": "27CE", "mappings": {"default": {"default": "quadratisches und"}}}, {"key": "27CF", "mappings": {"default": {"default": "quadratisches oder"}}}, {"key": "27D0", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> Diamant mit zentriertem Punkt"}}}, {"key": "27D1", "mappings": {"default": {"default": "Und mit Dot"}}}, {"key": "27D2", "mappings": {"default": {"default": "Element der Öffnung nach oben"}}}, {"key": "27D3", "mappings": {"default": {"default": "Untere rechte Ecke mit Punkt"}}}, {"key": "27D4", "mappings": {"default": {"default": "Obere linke Ecke mit Punkt"}}}, {"key": "27D5", "mappings": {"default": {"default": "Linke äußere Verbindung"}}}, {"key": "27D6", "mappings": {"default": {"default": "rechter äußerer Join"}}}, {"key": "27D7", "mappings": {"default": {"default": "Volle äußere Verbindung"}}}, {"key": "27D8", "mappings": {"default": {"default": "Große Up Tack"}}}, {"key": "27D9", "mappings": {"default": {"default": "Großes Down Tack"}}}, {"key": "27DA", "mappings": {"default": {"default": "Linkes und rechtes doppeltes Drehkreuz"}}}, {"key": "27DB", "mappings": {"default": {"default": "<PERSON><PERSON> und rechter Ta<PERSON>"}}}, {"key": "27DC", "mappings": {"default": {"default": "Linke Multimap"}}}, {"key": "27DD", "mappings": {"default": {"default": "Long Right Tack"}}}, {"key": "27DE", "mappings": {"default": {"default": "<PERSON><PERSON> <PERSON><PERSON>"}}}, {"key": "27DF", "mappings": {"default": {"default": "Up Tack mit Kreis oben"}}}, {"key": "27E0", "mappings": {"default": {"default": "Raute durch horizontale Regel geteilt"}}}, {"key": "27E1", "mappings": {"default": {"default": "<PERSON>ßer konkavseitiger Diamant"}}}, {"key": "27E2", "mappings": {"default": {"default": "Weißer konkavseitiger Diamant mit Zecke nach links"}}}, {"key": "27E3", "mappings": {"default": {"default": "<PERSON>ßer konkavseitiger Diamant mit Zecke nach rechts"}}}, {"key": "27E4", "mappings": {"default": {"default": "Weißes Quadrat mit Zecke nach links"}}}, {"key": "27E5", "mappings": {"default": {"default": "Weißes Quadrat mit Tick nach rechts"}}}, {"key": "292B", "mappings": {"default": {"default": "Steigende diagonale Kreuzung fallende Diagonale"}}}, {"key": "292C", "mappings": {"default": {"default": "Fallende diagonale Kreuzung steigende Diagonale"}}}, {"key": "2980", "mappings": {"default": {"default": "Dreifacher vertikaler Balkenbegrenzer"}}}, {"key": "2981", "mappings": {"default": {"default": "Z Notationspunkt"}}}, {"key": "2982", "mappings": {"default": {"default": "Z Notationstyp Doppelpunkt"}}}, {"key": "2999", "mappings": {"default": {"default": "Gepunkteter Zaun"}}}, {"key": "299A", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "29B0", "mappings": {"default": {"default": "Umgekehrter leerer <PERSON>"}}}, {"key": "29B1", "mappings": {"default": {"default": "<PERSON><PERSON> mit Overbar"}}}, {"key": "29B2", "mappings": {"default": {"default": "<PERSON><PERSON> Set mit kleinem Kreis oben"}}}, {"key": "29B5", "mappings": {"default": {"default": "Kreis mit horizontaler Leiste"}}}, {"key": "29B6", "mappings": {"default": {"default": "circled vertical bar"}}}, {"key": "29B7", "mappings": {"default": {"default": "eingekreiste Parallele"}}}, {"key": "29B8", "mappings": {"default": {"default": "eingekreister umgekehrter Schrägstrich"}}}, {"key": "29B9", "mappings": {"default": {"default": "eingekreistes Senkrecht"}}}, {"key": "29BA", "mappings": {"default": {"default": "Kreis geteilt durch horizontale Leiste und obere Hälfte geteilt durch vertikale Leiste"}}}, {"key": "29BB", "mappings": {"default": {"default": "Kreis mit überlagertem X"}}}, {"key": "29BC", "mappings": {"default": {"default": "eingek<PERSON>tes, gegen den Uhrzeigersinn gedrehtes Divisionszeichen"}}}, {"key": "29BE", "mappings": {"default": {"default": "eingekreiste weiße Kugel"}}}, {"key": "29BF", "mappings": {"default": {"default": "eingekreiste Kugel"}}}, {"key": "29C0", "mappings": {"default": {"default": "eingekreist weniger als"}}}, {"key": "29C1", "mappings": {"default": {"default": "eingekreist größer als"}}}, {"key": "29C2", "mappings": {"default": {"default": "Kreis mit kleinem Kreis nach rechts"}}}, {"key": "29C3", "mappings": {"default": {"default": "Kreis mit zwei horizontalen Strichen nach rechts"}}}, {"key": "29C4", "mappings": {"default": {"default": "Quadrat mit Diagonale links unten nach rechts oben"}}}, {"key": "29C5", "mappings": {"default": {"default": "Quadrat mit Diagonale links oben nach rechts unten"}}}, {"key": "29C6", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "29C7", "mappings": {"default": {"default": "Quadratischer kleiner Kreis"}}}, {"key": "29C8", "mappings": {"default": {"default": "Doppel<PERSON>drat"}}}, {"key": "29C9", "mappings": {"default": {"default": "Zwei verbundene Quadrate"}}}, {"key": "29CA", "mappings": {"default": {"default": "Dreieck mit Punkt oben"}}}, {"key": "29CB", "mappings": {"default": {"default": "Dreieck mit Unterleiste"}}}, {"key": "29CC", "mappings": {"default": {"default": "S im Dreieck"}}}, {"key": "29CD", "mappings": {"default": {"default": "Dreieck mit Serifen unten"}}}, {"key": "29CE", "mappings": {"default": {"default": "Rechtes Dreieck über linkem Dreieck"}}}, {"key": "29CF", "mappings": {"default": {"default": "<PERSON>es Dreieck neben vertikaler Leiste"}}}, {"key": "29D0", "mappings": {"default": {"default": "Vertikaler Balken neben dem rechten Dreieck"}}}, {"key": "29D1", "mappings": {"default": {"default": "Fliege mit linker Hälfte schwarz"}}}, {"key": "29D2", "mappings": {"default": {"default": "Fliege mit rechter Hälfte schwarz"}}}, {"key": "29D3", "mappings": {"default": {"default": "Schwarze Fliege"}}}, {"key": "29D4", "mappings": {"default": {"default": "Zeiten mit linker Hälfte schwarz"}}}, {"key": "29D5", "mappings": {"default": {"default": "Mal mit rechter Hälfte Schwarz"}}}, {"key": "29D6", "mappings": {"default": {"default": "Weiße Sanduhr"}}}, {"key": "29D7", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "29DC", "mappings": {"default": {"default": "Unvollständige Unendlichkeit"}}}, {"key": "29DD", "mappings": {"default": {"default": "Krawatte über Unendlichkeit"}}}, {"key": "29DE", "mappings": {"default": {"default": "Unendlichkeit mit vertikaler Leiste negiert"}}}, {"key": "29DF", "mappings": {"default": {"default": "Doppelter Multimap"}}}, {"key": "29E0", "mappings": {"default": {"default": "Quadrat mit konturierter Kontur"}}}, {"key": "29E1", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON> als"}}}, {"key": "29E2", "mappings": {"default": {"default": "Produkt mischen"}}}, {"key": "29E3", "mappings": {"default": {"default": "Gleichheitszeichen und geneigte Parallele"}}}, {"key": "29E4", "mappings": {"default": {"default": "Gleichheitszeichen und schräge Parallele zu Tilde oben"}}}, {"key": "29E5", "mappings": {"default": {"default": "Identisch und schräg parallel"}}}, {"key": "29E6", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "29E7", "mappings": {"default": {"default": "Thermodynamisch"}}}, {"key": "29E8", "mappings": {"default": {"default": "Abwärtszeigendes Dreieck mit linker Hälfte schwarz"}}}, {"key": "29E9", "mappings": {"default": {"default": "Abwärtsdreieck mit rechter Hälfte schwarz"}}}, {"key": "29EB", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "29EE", "mappings": {"default": {"default": "Fehlergesperrtes weißes Quadrat"}}}, {"key": "29EF", "mappings": {"default": {"default": "Fehlerfreies schwarzes Quadrat"}}}, {"key": "29F0", "mappings": {"default": {"default": "Fehlergesperrter weißer Diamant"}}}, {"key": "29F1", "mappings": {"default": {"default": "Error-Barred <PERSON>"}}}, {"key": "29F2", "mappings": {"default": {"default": "Fehlergesperrter weißer Kreis"}}}, {"key": "29F3", "mappings": {"default": {"default": "Fehlergesperrter schwarzer Kreis"}}}, {"key": "29F4", "mappings": {"default": {"default": "rule delayed"}}}, {"key": "29F5", "mappings": {"default": {"default": "Umgekehrter Schrägstrich Operator"}}}, {"key": "29F6", "mappings": {"default": {"default": "Schrägstrich mit Overbar"}}}, {"key": "29F7", "mappings": {"default": {"default": "Umgekehrter Schrägstrich mit horizontalem Hub"}}}, {"key": "29F8", "mappings": {"default": {"default": "G<PERSON>ßer Schrägstrich"}}}, {"key": "29F9", "mappings": {"default": {"default": "Großer umgekehrter Schrägstrich"}}}, {"key": "29FA", "mappings": {"default": {"default": "Doppel plus"}}}, {"key": "29FB", "mappings": {"default": {"default": "Triple Plus"}}}, {"key": "29FE", "mappings": {"default": {"default": "Winzig"}}}, {"key": "29FF", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "2A00", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>-Punkt-Operator"}}}, {"key": "2A01", "mappings": {"default": {"default": "N-Ary Circled Plus Operator"}}}, {"key": "2A02", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>-Times-Operator"}}}, {"key": "2A03", "mappings": {"default": {"default": "N-Ary Union-Operator mit Punkt"}}}, {"key": "2A04", "mappings": {"default": {"default": "N-Ary Union-Operator mit Plus"}}}, {"key": "2A05", "mappings": {"default": {"default": "N-Ary Square-Kreuzungsoperator"}}}, {"key": "2A06", "mappings": {"default": {"default": "N-Ary Square Union-Betreiber"}}}, {"key": "2A07", "mappings": {"default": {"default": "Zwei logische und Operator"}}}, {"key": "2A08", "mappings": {"default": {"default": "Zwei logisch oder Operator"}}}, {"key": "2A09", "mappings": {"default": {"default": "N-Ary Times-Operator"}}}, {"key": "2A0A", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "2A0B", "mappings": {"default": {"default": "Summation mit Integral"}}}, {"key": "2A0C", "mappings": {"default": {"default": "Vierfach-Integral-Operator"}}}, {"key": "2A0D", "mappings": {"default": {"default": "Finite Part Integral"}}}, {"key": "2A0E", "mappings": {"default": {"default": "Integral mit Doppelhub"}}}, {"key": "2A0F", "mappings": {"default": {"default": "Integraler Durchschnitt mit Schrägstrich"}}}, {"key": "2A10", "mappings": {"default": {"default": "Zirkulationsfunktion"}}}, {"key": "2A11", "mappings": {"default": {"default": "Integration gegen den Uhrzeigersinn"}}}, {"key": "2A12", "mappings": {"default": {"default": "Linienintegration mit rechteckigem Pfad um die Pole"}}}, {"key": "2A13", "mappings": {"default": {"default": "Linienintegration mit halbkreisförmigem Pfad um den Pol"}}}, {"key": "2A14", "mappings": {"default": {"default": "Leitungsintegration ohne Pole"}}}, {"key": "2A15", "mappings": {"default": {"default": "Integral um einen Punktoperator"}}}, {"key": "2A16", "mappings": {"default": {"default": "Quaternion Integral Operator"}}}, {"key": "2A18", "mappings": {"default": {"default": "Integriert mit Times Sign"}}}, {"key": "2A19", "mappings": {"default": {"default": "Integral mit der Kreuzung"}}}, {"key": "2A1A", "mappings": {"default": {"default": "Integral mit Union"}}}, {"key": "2A1B", "mappings": {"default": {"default": "Integral mit Overbar"}}}, {"key": "2A1C", "mappings": {"default": {"default": "Integral mit Underbar"}}}, {"key": "2A1D", "mappings": {"default": {"default": "Beitreten"}}}, {"key": "2A1E", "mappings": {"default": {"default": "Großes linkes Dreieckbediener"}}}, {"key": "2A1F", "mappings": {"default": {"default": "Z-Notationsschema-Zusammensetzung"}}}, {"key": "2A20", "mappings": {"default": {"default": "Z-Notationsschema-Piping"}}}, {"key": "2A21", "mappings": {"default": {"default": "Z-Notationsschema-Projektion"}}}, {"key": "2A22", "mappings": {"default": {"default": "Pluszeichen mit kleinem Kreis oben"}}}, {"key": "2A23", "mappings": {"default": {"default": "Pluszeichen mit Zirkumflex-Akzent oben"}}}, {"key": "2A24", "mappings": {"default": {"default": "Pluszeichen mit Tilde oben"}}}, {"key": "2A25", "mappings": {"default": {"default": "Pluszeichen mit Punkt unten"}}}, {"key": "2A26", "mappings": {"default": {"default": "Pluszeichen mit Tilde unter"}}}, {"key": "2A27", "mappings": {"default": {"default": "Pluszeichen mit Index Zwei"}}}, {"key": "2A28", "mappings": {"default": {"default": "Pluszeichen mit schwarzem Dreieck"}}}, {"key": "2A29", "mappings": {"default": {"default": "Minuszeichen mit Komma oben"}}}, {"key": "2A2A", "mappings": {"default": {"default": "Minuszeichen mit <PERSON>t unten"}}}, {"key": "2A2B", "mappings": {"default": {"default": "Minuszeichen mit fallenden Punkten"}}}, {"key": "2A2C", "mappings": {"default": {"default": "Minuszeichen mit steigenden Punkten"}}}, {"key": "2A2D", "mappings": {"default": {"default": "Pluszeichen Im linken Halbkreis"}}}, {"key": "2A2E", "mappings": {"default": {"default": "Pluszeichen Im rechten Halbkreis"}}}, {"key": "2A2F", "mappings": {"default": {"default": "Vektor- oder Kreuzprodukt"}}}, {"key": "2A30", "mappings": {"default": {"default": "Multiplikationszeichen mit Punkt oben"}}}, {"key": "2A31", "mappings": {"default": {"default": "Multiplikationszeichen mit Unterleiste"}}}, {"key": "2A32", "mappings": {"default": {"default": "Semidirektprodukt mit geschlossenem Boden"}}}, {"key": "2A33", "mappings": {"default": {"default": "smash product"}}}, {"key": "2A34", "mappings": {"default": {"default": "Multiplikationszeichen im linken Halbkreis"}}}, {"key": "2A35", "mappings": {"default": {"default": "Multiplikationszeichen im rechten Halbkreis"}}}, {"key": "2A36", "mappings": {"default": {"default": "eingekreistes Multiplikationszeichen mit Circumflex-Akzent"}}}, {"key": "2A37", "mappings": {"default": {"default": "Multiplikationszeichen im doppelten Kreis"}}}, {"key": "2A38", "mappings": {"default": {"default": "circled division sign"}}}, {"key": "2A39", "mappings": {"default": {"default": "Pluszeichen im Dreieck"}}}, {"key": "2A3A", "mappings": {"default": {"default": "Minuszeichen im Dreieck"}}}, {"key": "2A3B", "mappings": {"default": {"default": "Multiplikationszeichen im Dreieck"}}}, {"key": "2A3C", "mappings": {"default": {"default": "Innenprodukt"}}}, {"key": "2A3D", "mappings": {"default": {"default": "Produkt für den rechten Innenraum"}}}, {"key": "2A3E", "mappings": {"default": {"default": "Z-Notation relationaler Auf<PERSON>u"}}}, {"key": "2A3F", "mappings": {"default": {"default": "Verschmelzung oder Koprodukt"}}}, {"key": "2A40", "mappings": {"default": {"default": "Schnittpunkt mit Punkt"}}}, {"key": "2A41", "mappings": {"default": {"default": "Union mit Minuszeichen"}}}, {"key": "2A42", "mappings": {"default": {"default": "Union mit Overbar"}}}, {"key": "2A43", "mappings": {"default": {"default": "Kreuzung mit Overbar"}}}, {"key": "2A44", "mappings": {"default": {"default": "Überschneidung mit logischem und"}}}, {"key": "2A45", "mappings": {"default": {"default": "Vereinigung mit logischem Or"}}}, {"key": "2A46", "mappings": {"default": {"default": "Vereinigung über dem Schnittpunkt"}}}, {"key": "2A47", "mappings": {"default": {"default": "Schnittpunkt über Union"}}}, {"key": "2A48", "mappings": {"default": {"default": "Vereinigung über Stab über Kreuzung"}}}, {"key": "2A49", "mappings": {"default": {"default": "Schnittpunkt oberhalb der Stange oberhalb der Union"}}}, {"key": "2A4A", "mappings": {"default": {"default": "Union daneben und mit Union verbunden"}}}, {"key": "2A4B", "mappings": {"default": {"default": "Kreuzung neben und mit Kreuzung verbunden"}}}, {"key": "2A4C", "mappings": {"default": {"default": "Geschlossene Vereinigung mit Serifen"}}}, {"key": "2A4D", "mappings": {"default": {"default": "Geschlossener Schnittpunkt mit Serifen"}}}, {"key": "2A4E", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "2A4F", "mappings": {"default": {"default": "Double Square Union"}}}, {"key": "2A50", "mappings": {"default": {"default": "Closed Union mit Serifen und Smash-Produkten"}}}, {"key": "2A51", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> und mit Punkt oben"}}}, {"key": "2A52", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> oder mit <PERSON> oben"}}}, {"key": "2A53", "mappings": {"default": {"default": "double logical and"}}}, {"key": "2A54", "mappings": {"default": {"default": "double logical or"}}}, {"key": "2A55", "mappings": {"default": {"default": "Zwei durchschneiden logisch und"}}}, {"key": "2A56", "mappings": {"default": {"default": "Zwei sich überschneidende logische Oder"}}}, {"key": "2A57", "mappings": {"default": {"default": "Abfallend großes oder"}}}, {"key": "2A58", "mappings": {"default": {"default": "Abfallend großes und"}}}, {"key": "2A59", "mappings": {"default": {"default": "Logisch oder Überlappend Logisch Und"}}}, {"key": "2A5A", "mappings": {"default": {"default": "Lo<PERSON><PERSON> und mit Middle Stem"}}}, {"key": "2A5B", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> oder mit Middle Stem"}}}, {"key": "2A5C", "mappings": {"default": {"default": "Logisch und mit Horizontalstrich"}}}, {"key": "2A5D", "mappings": {"default": {"default": "Lo<PERSON><PERSON> oder mit Horizontalstrich"}}}, {"key": "2A5E", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> und mit Double Overbar"}}}, {"key": "2A5F", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> und mit Underbar"}}}, {"key": "2A60", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> und mit Double Underbar"}}}, {"key": "2A61", "mappings": {"default": {"default": "kleines Vee mit Underbar"}}}, {"key": "2A62", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> oder mit Double Overbar"}}}, {"key": "2A63", "mappings": {"default": {"default": "<PERSON><PERSON>ch oder mit doppelter Unterleiste"}}}, {"key": "2A64", "mappings": {"default": {"default": "Z-Notation Domain Antirestriction"}}}, {"key": "2A65", "mappings": {"default": {"default": "Z Notationsbereich Antirestriction"}}}, {"key": "2A66", "mappings": {"default": {"default": "Gleichheitszeichen mit Punkt unten"}}}, {"key": "2A67", "mappings": {"default": {"default": "Identisch mit dem obigen Punkt"}}}, {"key": "2A68", "mappings": {"default": {"default": "Dreifacher Horizontalbalken mit doppeltem vertikalem Hub"}}}, {"key": "2A69", "mappings": {"default": {"default": "Dreifacher Horizontalbalken mit dreifachem vertikalem Hub"}}}, {"key": "2A6A", "mappings": {"default": {"default": "Tilde-Operator mit Punkt oben"}}}, {"key": "2A6B", "mappings": {"default": {"default": "Tilde-Operator mit steigenden Punkten"}}}, {"key": "2A6C", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> <PERSON>"}}}, {"key": "2A6D", "mappings": {"default": {"default": "Kongruent mit Punkt oben"}}}, {"key": "2A6E", "mappings": {"default": {"default": "Entspricht Asterisk"}}}, {"key": "2A6F", "mappings": {"default": {"default": "Fast gleichwertig mit dem Circumflex Accent"}}}, {"key": "2A70", "mappings": {"default": {"default": "Ungefä<PERSON> gleich oder gleich zu"}}}, {"key": "2A71", "mappings": {"default": {"default": "Gleichheitszeichen über Pluszeichen"}}}, {"key": "2A72", "mappings": {"default": {"default": "Pluszeichen über Gleichheitszeichen"}}}, {"key": "2A73", "mappings": {"default": {"default": "Gleichheitszeichen über Tilde-Operator"}}}, {"key": "2A74", "mappings": {"default": {"default": "Doppelpunkt gleich"}}}, {"key": "2A75", "mappings": {"default": {"default": "double equal"}}}, {"key": "2A76", "mappings": {"default": {"default": "Drei aufeinander folgende Gleichheitszeichen"}}}, {"key": "2A77", "mappings": {"default": {"default": "Gleichheitszeichen mit zwei Punkten oben und zwei Punkten unten"}}}, {"key": "2A78", "mappings": {"default": {"default": "Gleichwertig mit vier Punkten oben"}}}, {"key": "2A79", "mappings": {"default": {"default": "<PERSON><PERSON> als mit Circle Inside"}}}, {"key": "2A7A", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>r als mit Kreis nach innen"}}}, {"key": "2A7B", "mappings": {"default": {"default": "Wen<PERSON> als mit Fragezeichen oben"}}}, {"key": "2A7C", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>r als mit Fragezeichen oben"}}}, {"key": "2A7D", "mappings": {"default": {"default": "<PERSON><PERSON> als oder schräg gleich"}}}, {"key": "2A7E", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON> als oder schräg gleich"}}}, {"key": "2A7F", "mappings": {"default": {"default": "<PERSON><PERSON> als oder schräg gleich mit Punkt nach innen"}}}, {"key": "2A80", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON> als oder schräg gleich mit <PERSON>t nach innen"}}}, {"key": "2A81", "mappings": {"default": {"default": "<PERSON><PERSON> als oder schräg gleich mit <PERSON>t oben"}}}, {"key": "2A82", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON> als oder schräg gleich mit <PERSON>t oben"}}}, {"key": "2A83", "mappings": {"default": {"default": "<PERSON><PERSON> als oder schräg gleich mit <PERSON>t oben rechts"}}}, {"key": "2A84", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON> als oder schräg gleich mit Punkt oben links"}}}, {"key": "2A85", "mappings": {"default": {"default": "<PERSON><PERSON> als oder ungefähr"}}}, {"key": "2A86", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON> als oder ungefähr"}}}, {"key": "2A87", "mappings": {"default": {"default": "Kleiner als aber nicht gleich"}}}, {"key": "2A88", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON> als aber nicht gleich"}}}, {"key": "2A89", "mappings": {"default": {"default": "Kleiner als aber nicht ungefähr gleich"}}}, {"key": "2A8A", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>r als aber nicht ungefähr gleich"}}}, {"key": "2A8B", "mappings": {"default": {"default": "<PERSON><PERSON> als aber größer als oben"}}}, {"key": "2A8C", "mappings": {"default": {"default": "Größer als aber weniger als unten und größer als oben"}}}, {"key": "2A8D", "mappings": {"default": {"default": "<PERSON><PERSON> als oben ähnlich oder gleich"}}}, {"key": "2A8E", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON> als oben ähnlich oder gleich"}}}, {"key": "2A8F", "mappings": {"default": {"default": "<PERSON><PERSON> als oben ähnlich oben größer als"}}}, {"key": "2A90", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>r als oben Ähnlich oben"}}}, {"key": "2A91", "mappings": {"default": {"default": "less than greater than or equal to"}}}, {"key": "2A92", "mappings": {"default": {"default": "greater than less than or equal to"}}}, {"key": "2A93", "mappings": {"default": {"default": "<PERSON><PERSON> als oben schräg gleich oben Größer als oben schräg gleich"}}}, {"key": "2A94", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>r als oben schräg gleich oben kleiner als oben schräg gleich groß"}}}, {"key": "2A95", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> gleich oder kleiner als"}}}, {"key": "2A96", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> gleich oder größer als"}}}, {"key": "2A97", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> gleich oder kleiner als mit innenliegendem Punkt"}}}, {"key": "2A98", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> gleich oder größer als mit <PERSON>t nach innen"}}}, {"key": "2A99", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> oder kleiner als"}}}, {"key": "2A9A", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> oder größer als"}}}, {"key": "2A9B", "mappings": {"default": {"default": "Doppelte Linie schräg gleich oder kleiner als"}}}, {"key": "2A9C", "mappings": {"default": {"default": "Doppelte Linie schräg gleich oder größer als"}}}, {"key": "2A9D", "mappings": {"default": {"default": "Äquivalent oder kleiner als"}}}, {"key": "2A9E", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> oder größer als"}}}, {"key": "2A9F", "mappings": {"default": {"default": "Ähnlich oben Weniger als Gleiches Gleichheitszeichen"}}}, {"key": "2AA0", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> oben größer als oben Gleichheitszeichen"}}}, {"key": "2AA1", "mappings": {"default": {"default": "Verschachteltes kleiner als"}}}, {"key": "2AA2", "mappings": {"default": {"default": "Verschachteltes größer als"}}}, {"key": "2AA3", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> verschachtelt, weniger als mit Unterleiste"}}}, {"key": "2AA4", "mappings": {"default": {"default": "less than greater than overlay"}}}, {"key": "2AA5", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON> als weniger als"}}}, {"key": "2AA6", "mappings": {"default": {"default": "Wen<PERSON> als durch Kurve geschlossen"}}}, {"key": "2AA7", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>r als durch Kurve geschlossen"}}}, {"key": "2AA8", "mappings": {"default": {"default": "<PERSON><PERSON> als dann geschlossen durch Kurve oberhalb der gleichen Neigung"}}}, {"key": "2AA9", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>r als geschlossen durch Kurve oberhalb der gleichen Neigung"}}}, {"key": "2AAA", "mappings": {"default": {"default": "kleiner als"}}}, {"key": "2AAB", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON> als"}}}, {"key": "2AAC", "mappings": {"default": {"default": "kleiner als oder gleich"}}}, {"key": "2AAD", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON> als oder gleich"}}}, {"key": "2AAE", "mappings": {"default": {"default": "difference between (variant"}}}, {"key": "2AAF", "mappings": {"default": {"default": "Stellt über dem einzeiligen Gleichheitszeichen ein"}}}, {"key": "2AB0", "mappings": {"default": {"default": "Erfolg über dem einzeiligen Gleichheitszeichen"}}}, {"key": "2AB1", "mappings": {"default": {"default": "Vorangehende Single-Line ist nicht gleich"}}}, {"key": "2AB2", "mappings": {"default": {"default": "Erfolg über Single-Line nicht gleich"}}}, {"key": "2AB3", "mappings": {"default": {"default": "Vorangegangenes Gleichheitszeichen"}}}, {"key": "2AB4", "mappings": {"default": {"default": "Erfolgreich über Gleichheitszeichen"}}}, {"key": "2AB5", "mappings": {"default": {"default": "precedes but not equal to"}}}, {"key": "2AB6", "mappings": {"default": {"default": "succeeds but not equal to"}}}, {"key": "2AB7", "mappings": {"default": {"default": "Vorangegangenes fast gleich zu"}}}, {"key": "2AB8", "mappings": {"default": {"default": "Erreicht über fast gleich viel"}}}, {"key": "2AB9", "mappings": {"default": {"default": "Vorangegangene nicht annä<PERSON>nd gleich"}}}, {"key": "2ABA", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>t oben nicht ann<PERSON><PERSON>nd gleich"}}}, {"key": "2ABB", "mappings": {"default": {"default": "Doppelter Vorgänger"}}}, {"key": "2ABC", "mappings": {"default": {"default": "Doppelter Erfolg"}}}, {"key": "2ABD", "mappings": {"default": {"default": "Teilmenge mit Punkt"}}}, {"key": "2ABE", "mappings": {"default": {"default": "Superset mit Punkt"}}}, {"key": "2ABF", "mappings": {"default": {"default": "Teilmenge mit Pluszeichen unten"}}}, {"key": "2AC0", "mappings": {"default": {"default": "Superset mit Pluszeichen unten"}}}, {"key": "2AC1", "mappings": {"default": {"default": "Teilmenge mit Multiplikationszeichen unten"}}}, {"key": "2AC2", "mappings": {"default": {"default": "Superset mit Multiplikationszeichen unten"}}}, {"key": "2AC3", "mappings": {"default": {"default": "Teilmenge oder gleich mit dem obigen Punkt"}}}, {"key": "2AC4", "mappings": {"default": {"default": "Superset von oder gleich mit <PERSON>t oben"}}}, {"key": "2AC5", "mappings": {"default": {"default": "Teilmenge des obigen Gleichheitszeichens"}}}, {"key": "2AC6", "mappings": {"default": {"default": "Superset des obigen Gleichheitszeichens"}}}, {"key": "2AC7", "mappings": {"default": {"default": "approximate subset of"}}}, {"key": "2AC8", "mappings": {"default": {"default": "approximate superset of"}}}, {"key": "2AC9", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> von oben <PERSON> gleich"}}}, {"key": "2ACA", "mappings": {"default": {"default": "Superset von oben fast gleichwertig"}}}, {"key": "2ACB", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> von oben nicht gleich"}}}, {"key": "2ACC", "mappings": {"default": {"default": "Superset von oben nicht gleich"}}}, {"key": "2ACD", "mappings": {"default": {"default": "Platz links Feldbetreiber geöffnet"}}}, {"key": "2ACE", "mappings": {"default": {"default": "Rechteckige Box mit offener Box"}}}, {"key": "2ACF", "mappings": {"default": {"default": "Geschlossener Teilsatz"}}}, {"key": "2AD0", "mappings": {"default": {"default": "Superset geschlossen"}}}, {"key": "2AD1", "mappings": {"default": {"default": "Geschlossene Untermenge oder gleich"}}}, {"key": "2AD2", "mappings": {"default": {"default": "Superset geschlossen oder gleich"}}}, {"key": "2AD3", "mappings": {"default": {"default": "subset over superset"}}}, {"key": "2AD4", "mappings": {"default": {"default": "superset over subset"}}}, {"key": "2AD5", "mappings": {"default": {"default": "subset over subset"}}}, {"key": "2AD6", "mappings": {"default": {"default": "superset over superset"}}}, {"key": "2AD7", "mappings": {"default": {"default": "Superset neben dem Subset"}}}, {"key": "2AD8", "mappings": {"default": {"default": "Superset Neben und <PERSON><PERSON> von Dash mit Subset"}}}, {"key": "2AD9", "mappings": {"default": {"default": "Element der Öffnung nach unten"}}}, {"key": "2ADA", "mappings": {"default": {"default": "Heugabel mit T-Stück"}}}, {"key": "2ADB", "mappings": {"default": {"default": "Transversalschnitt"}}}, {"key": "2ADC", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "2ADD", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "2ADE", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "2ADF", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> Down Tack"}}}, {"key": "2AE0", "mappings": {"default": {"default": "Short Up Tack"}}}, {"key": "2AE1", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zu <PERSON>"}}}, {"key": "2AE2", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>Drehkreuz"}}}, {"key": "2AE3", "mappings": {"default": {"default": "Doppelter vertikaler Balken linkes Drehkreuz"}}}, {"key": "2AE4", "mappings": {"default": {"default": "double left turnstile vertical bar"}}}, {"key": "2AE5", "mappings": {"default": {"default": "Doppelter vertikaler Balken Doppelter linker Drehkreuz"}}}, {"key": "2AE6", "mappings": {"default": {"default": "<PERSON> <PERSON> von <PERSON> Member von <PERSON> Vertical"}}}, {"key": "2AE7", "mappings": {"default": {"default": "Short Down Tack mit Overbar"}}}, {"key": "2AE8", "mappings": {"default": {"default": "perpendicular over bar"}}}, {"key": "2AE9", "mappings": {"default": {"default": "Short Up Tack Über Short Down Tack"}}}, {"key": "2AEA", "mappings": {"default": {"default": "Double Down Tack"}}}, {"key": "2AEB", "mappings": {"default": {"default": "Verdoppeln Si<PERSON>"}}}, {"key": "2AEC", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> nicht unterschreiben"}}}, {"key": "2AED", "mappings": {"default": {"default": "Umgekehrter Doppelstrich nicht unterschrieben"}}}, {"key": "2AEE", "mappings": {"default": {"default": "Te<PERSON> sich nicht mit umgekehrten Negationsschrägstrich"}}}, {"key": "2AEF", "mappings": {"default": {"default": "Vertikale Linie mit Kreis oben"}}}, {"key": "2AF0", "mappings": {"default": {"default": "Vertikale Linie mit Kreis unten"}}}, {"key": "2AF1", "mappings": {"default": {"default": "Down Tack mit Kreis unten"}}}, {"key": "2AF2", "mappings": {"default": {"default": "Parallel zum horizontalen Anschlag"}}}, {"key": "2AF3", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> zu Tilde Operator"}}}, {"key": "2AF4", "mappings": {"default": {"default": "Dreifache vertikale Balken-Binärbeziehung"}}}, {"key": "2AF5", "mappings": {"default": {"default": "Dreifacher vertikaler Strich mit horizontalem Hub"}}}, {"key": "2AF6", "mappings": {"default": {"default": "Triple Colon Operator"}}}, {"key": "2AF7", "mappings": {"default": {"default": "Dreifach verschachtelt weniger als"}}}, {"key": "2AF8", "mappings": {"default": {"default": "Dreifach verschachteltes Größeres als"}}}, {"key": "2AF9", "mappings": {"default": {"default": "Doppellini<PERSON> s<PERSON>r<PERSON>, weniger als oder gleich"}}}, {"key": "2AFA", "mappings": {"default": {"default": "Doppelte Linie schräg größer als oder gleich"}}}, {"key": "2AFB", "mappings": {"default": {"default": "Dreifache Schrägstrich-Binärbeziehung"}}}, {"key": "2AFC", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> dre<PERSON>acher vertikaler Barbediener"}}}, {"key": "2AFD", "mappings": {"default": {"default": "Doppelter Schrägstrich-Operator"}}}, {"key": "2AFE", "mappings": {"default": {"default": "Weiße vertikale Leiste"}}}, {"key": "2AFF", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> White Vertical Bar"}}}, {"key": "301C", "mappings": {"default": {"default": "Wave Dash"}}}, {"key": "FE10", "mappings": {"default": {"default": "Präsentationsformular für vertikales Komma"}}}, {"key": "FE13", "mappings": {"default": {"default": "Präsentationsformular für vertikalen Doppelpunkt"}}}, {"key": "FE14", "mappings": {"default": {"default": "Präsentationsformular für vertikales Semikolon"}}}, {"key": "FE15", "mappings": {"default": {"default": "Präsentationsformular für vertikales Ausrufezeichen"}}}, {"key": "FE16", "mappings": {"default": {"default": "Präsentationsformular für vertikales Fragezeichen"}}}, {"key": "FE19", "mappings": {"default": {"default": "Präsentationsformular für vertikale horizontale Ellipsen"}}}, {"key": "FE30", "mappings": {"default": {"default": "Präsentationsformular für vertikalen Zwei-Punkt-Leader"}}}, {"key": "FE31", "mappings": {"default": {"default": "Präsentationsformular für Vertical Em Dash"}}}, {"key": "FE32", "mappings": {"default": {"default": "Präsentationsformular für Vertical En Dash"}}}, {"key": "FE33", "mappings": {"default": {"default": "Präsentationsformular für Vertical Low Line"}}}, {"key": "FE34", "mappings": {"default": {"default": "Präsentationsformular für Vertical Wavy Low Line"}}}, {"key": "FE45", "mappings": {"default": {"default": "Sesamp<PERSON><PERSON>"}}}, {"key": "FE46", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> Sesam <PERSON>"}}}, {"key": "FE49", "mappings": {"default": {"default": "Gestrichelte Überlänge"}}}, {"key": "FE4A", "mappings": {"default": {"default": "Centreline Overline"}}}, {"key": "FE4B", "mappings": {"default": {"default": "Wellenförmige Überlänge"}}}, {"key": "FE4C", "mappings": {"default": {"default": "Doppelte Wellenlinie"}}}, {"key": "FE4D", "mappings": {"default": {"default": "Gestrichelte Low Line"}}}, {"key": "FE4E", "mappings": {"default": {"default": "Mittellinie Low Line"}}}, {"key": "FE4F", "mappings": {"default": {"default": "Wellenförmige Low Line"}}}, {"key": "FE50", "mappings": {"default": {"default": "kleines <PERSON>mma"}}}, {"key": "FE52", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "FE54", "mappings": {"default": {"default": "kleines Semikolon"}}}, {"key": "FE55", "mappings": {"default": {"default": "k<PERSON><PERSON>"}}}, {"key": "FE56", "mappings": {"default": {"default": "kleines Fragezeichen"}}}, {"key": "FE57", "mappings": {"default": {"default": "kleines Ausrufezeichen"}}}, {"key": "FE58", "mappings": {"default": {"default": "kleiner <PERSON>"}}}, {"key": "FE5F", "mappings": {"default": {"default": "kleines Nummernzeichen"}}}, {"key": "FE60", "mappings": {"default": {"default": "kleines kaufmännisches"}}}, {"key": "FE61", "mappings": {"default": {"default": "kleines Sternchen"}}}, {"key": "FE62", "mappings": {"default": {"default": "kleines Pluszeichen"}}}, {"key": "FE63", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "FE64", "mappings": {"default": {"default": "kleines weniger als Zeichen"}}}, {"key": "FE65", "mappings": {"default": {"default": "kleines Größeres als Zeichen"}}}, {"key": "FE66", "mappings": {"default": {"default": "kleines Gleichheitszeichen"}}}, {"key": "FE68", "mappings": {"default": {"default": "kleiner umgekehrter Schrägstrich"}}}, {"key": "FE69", "mappings": {"default": {"default": "kleines Dollarzeichen"}}}, {"key": "FE6A", "mappings": {"default": {"default": "kleines Prozentzeichen"}}}, {"key": "FE6B", "mappings": {"default": {"default": "kleine kommerzielle bei"}}}, {"key": "FF01", "mappings": {"default": {"default": "vollbreites Ausrufezeichen"}}}, {"key": "FF02", "mappings": {"default": {"default": "vollbreites Anführungszeichen"}}}, {"key": "FF03", "mappings": {"default": {"default": "vollbreites Nummernzeichen"}}}, {"key": "FF04", "mappings": {"default": {"default": "vollbreites Dollarzeichen"}}}, {"key": "FF05", "mappings": {"default": {"default": "vollbreites Prozentzeichen"}}}, {"key": "FF06", "mappings": {"default": {"default": "vollbreites kaufmännisches und"}}}, {"key": "FF07", "mappings": {"default": {"default": "vollbreites Apostroph"}}}, {"key": "FF0A", "mappings": {"default": {"default": "vollbreiter Stern"}}}, {"key": "FF0B", "mappings": {"default": {"default": "vollbreites Plus"}}}, {"key": "FF0C", "mappings": {"default": {"default": "vollbreites Komma"}}}, {"key": "FF0D", "mappings": {"default": {"default": "vollbreites Minus"}}}, {"key": "FF0E", "mappings": {"default": {"default": "vollbreiter Punkt"}}}, {"key": "FF0F", "mappings": {"default": {"default": "Schrägstrich mit voller Breite"}}}, {"key": "FF1A", "mappings": {"default": {"default": "vollbreiter Doppelpunkt"}}}, {"key": "FF1B", "mappings": {"default": {"default": "vollbreites Semikolon"}}}, {"key": "FF1C", "mappings": {"default": {"default": "<PERSON><PERSON> als das gesamte Zeichen"}}}, {"key": "FF1D", "mappings": {"default": {"default": "vollbreites Gleichspannungszeichen"}}}, {"key": "FF1E", "mappings": {"default": {"default": "Fullwidth Mehr als Zeichen"}}}, {"key": "FF1F", "mappings": {"default": {"default": "vollbreites Fragezeichen"}}}, {"key": "FF20", "mappings": {"default": {"default": "Fullwidth Commercial at"}}}, {"key": "FF3C", "mappings": {"default": {"default": "Umgekehrter Schrägstrich mit voller Breite"}}}, {"key": "FF3E", "mappings": {"default": {"default": "Circumflex-Akzent mit voller Breite"}}}, {"key": "FF3F", "mappings": {"default": {"default": "vollbreites Low Line"}}}, {"key": "FF40", "mappings": {"default": {"default": "vollbreiter Gravis Akzent"}}}, {"key": "FF5C", "mappings": {"default": {"default": "vollbreites Vertikale Linie"}}}, {"key": "FF5E", "mappings": {"default": {"default": "vollbreite Tilde"}}}, {"key": "FFE0", "mappings": {"default": {"default": "vollbreites Cent-Zeichen"}}}, {"key": "FFE1", "mappings": {"default": {"default": "vollbreites Pfundzeichen"}}}, {"key": "FFE2", "mappings": {"default": {"default": "vollbreite nicht Symbol"}}}, {"key": "FFE3", "mappings": {"default": {"default": "vollbreiter Überstrich", "alternative": "vollbreites Makron"}}}, {"key": "FFE4", "mappings": {"default": {"default": "vollbreite gestrichelte Linie"}}}, {"key": "FFE5", "mappings": {"default": {"default": "vollbreites Yen-Zeichen"}}}, {"key": "FFE6", "mappings": {"default": {"default": "vollbreites Won-Zeichen"}}}, {"key": "FFE8", "mappings": {"default": {"default": "Halbbreite bildet leicht vertikal"}}}, {"key": "FFED", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> Quadrat mit halber Breite"}}}, {"key": "FFEE", "mappings": {"default": {"default": "Weißer Kreis mit halber Breite"}}}], "de/symbols/math_whitespace.min": [{"locale": "de"}, {"key": "0020", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "00A0", "mappings": {"default": {"default": " "}}}, {"key": "00AD", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> T<PERSON>"}}}, {"key": "2000", "mappings": {"default": {"default": "En Quad"}}}, {"key": "2001", "mappings": {"default": {"default": "Em Quad"}}}, {"key": "2002", "mappings": {"default": {"default": "En Space"}}}, {"key": "2003", "mappings": {"default": {"default": ""}}}, {"key": "2004", "mappings": {"default": {"default": "Raum für drei Per-Em"}}}, {"key": "2005", "mappings": {"default": {"default": "Raum für vier pro Em"}}}, {"key": "2006", "mappings": {"default": {"default": "Six-Per-Em Space"}}}, {"key": "2007", "mappings": {"default": {"default": "<PERSON><PERSON> da<PERSON>n"}}}, {"key": "2008", "mappings": {"default": {"default": "Interpunktionsraum"}}}, {"key": "2009", "mappings": {"default": {"default": ""}}}, {"key": "200A", "mappings": {"default": {"default": ""}}}, {"key": "200B", "mappings": {"default": {"default": ""}}}, {"key": "200C", "mappings": {"default": {"default": "Nullbreite ohne Joiner"}}}, {"key": "200D", "mappings": {"default": {"default": "<PERSON>idth Joiner"}}}, {"key": "200E", "mappings": {"default": {"default": "Markierung von links nach rechts"}}}, {"key": "200F", "mappings": {"default": {"default": "Markierung von rechts nach links"}}}, {"key": "2028", "mappings": {"default": {"default": "Trennzeichen"}}}, {"key": "2029", "mappings": {"default": {"default": "Absatztrennzeichen"}}}, {"key": "202A", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> von links nach rechts"}}}, {"key": "202B", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> von rechts nach links"}}}, {"key": "202C", "mappings": {"default": {"default": "Pop Richtungsformatierung"}}}, {"key": "202D", "mappings": {"default": {"default": "<PERSON> links nach rechts überschreiben"}}}, {"key": "202E", "mappings": {"default": {"default": "Von rechts nach links überschreiben"}}}, {"key": "202F", "mappings": {"default": {"default": "Schmaler No-Break-Bereich"}}}, {"key": "205F", "mappings": {"default": {"default": ""}}}, {"key": "2060", "mappings": {"default": {"default": "Wortverbinding"}}}, {"key": "2061", "mappings": {"default": {"default": "von"}}}, {"key": "2062", "mappings": {"default": {"default": "mal"}}}, {"key": "2063", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "2064", "mappings": {"default": {"default": "plus"}}}, {"key": "206A", "mappings": {"default": {"default": "Symmetrisches Austauschen verhindern"}}}, {"key": "206B", "mappings": {"default": {"default": "Aktivieren Sie das symmetrische Austauschen"}}}, {"key": "206E", "mappings": {"default": {"default": "Nationale Ziffernformen"}}}, {"key": "206F", "mappings": {"default": {"default": "Nominale Ziffernformen"}}}, {"key": "FEFF", "mappings": {"default": {"default": "Nullbreite Leerzeichen"}}}, {"key": "FFF9", "mappings": {"default": {"default": "Interlinearer <PERSON>"}}}, {"key": "FFFA", "mappings": {"default": {"default": "Separator für interlineare Anmerkungen"}}}, {"key": "FFFB", "mappings": {"default": {"default": "Interlinear Annotation Terminator"}}}], "de/symbols/other_stars.min": [{"locale": "de"}, {"key": "23E8", "mappings": {"default": {"default": "Dezimalexponentensymbol"}}}, {"key": "2605", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "2606", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "26AA", "mappings": {"default": {"default": "Mittlerer weißer Kreis"}}}, {"key": "26AB", "mappings": {"default": {"default": "Mittlerer schwarzer Kreis"}}}, {"key": "2705", "mappings": {"default": {"default": "Weißes schweres Häkchen"}}}, {"key": "2713", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "2714", "mappings": {"default": {"default": "Schweres Häkchen"}}}, {"key": "2715", "mappings": {"default": {"default": "Multiplikation X"}}}, {"key": "2716", "mappings": {"default": {"default": "Schwere Multiplikation X"}}}, {"key": "2717", "mappings": {"default": {"default": "Stimmzettel X"}}}, {"key": "2718", "mappings": {"default": {"default": "Schwerer Stimmzettel X"}}}, {"key": "271B", "mappings": {"default": {"default": "Center Cross öffnen"}}}, {"key": "271C", "mappings": {"default": {"default": "Schweres offenes Mittelkreuz"}}}, {"key": "2720", "mappings": {"default": {"default": "Malteserkreuz"}}}, {"key": "2721", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "2722", "mappings": {"default": {"default": "Vier Teardrop-Spoked Asterisk"}}}, {"key": "2723", "mappings": {"default": {"default": "Vier Ballon-Speichen-Sternchen"}}}, {"key": "2724", "mappings": {"default": {"default": "Schwere Vier-Ballon-Speichen-Sternchen"}}}, {"key": "2725", "mappings": {"default": {"default": "Vier Club-Spoked Asterisk"}}}, {"key": "2726", "mappings": {"default": {"default": "Schwarzer vierzackiger Stern"}}}, {"key": "2727", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> vier<PERSON>kiger Stern"}}}, {"key": "2728", "mappings": {"default": {"default": "Funkelt"}}}, {"key": "2729", "mappings": {"default": {"default": "<PERSON><PERSON> um<PERSON> weißer <PERSON>"}}}, {"key": "272A", "mappings": {"default": {"default": "Eingekreister weißer Stern"}}}, {"key": "272B", "mappings": {"default": {"default": "Open Center Black Star"}}}, {"key": "272C", "mappings": {"default": {"default": "Schwarzer Center White Star"}}}, {"key": "272D", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "272E", "mappings": {"default": {"default": "Schwerer umrissener schwarzer Stern"}}}, {"key": "272F", "mappings": {"default": {"default": "<PERSON><PERSON> Stern"}}}, {"key": "2730", "mappings": {"default": {"default": "Beschatteter weißer Stern"}}}, {"key": "2731", "mappings": {"default": {"default": "Schwer<PERSON> Stern"}}}, {"key": "2732", "mappings": {"default": {"default": "Öffnen Sie Center Asterisk"}}}, {"key": "2733", "mappings": {"default": {"default": "Acht Speichen-Sternchen"}}}, {"key": "2734", "mappings": {"default": {"default": "Achtzackiger schwarzer Stern"}}}, {"key": "2735", "mappings": {"default": {"default": "Achtzackiger Sternradstern"}}}, {"key": "2736", "mappings": {"default": {"default": "Sechszackiger schwarzer Stern"}}}, {"key": "2739", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "273A", "mappings": {"default": {"default": "Sechzehn Spitzen Sternchen"}}}, {"key": "273B", "mappings": {"default": {"default": "Teardrop<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "273C", "mappings": {"default": {"default": "Open Center Teardrop-<PERSON><PERSON><PERSON>"}}}, {"key": "273D", "mappings": {"default": {"default": "Schwerer Tropfen-Speichen-Sternchen"}}}, {"key": "273E", "mappings": {"default": {"default": "Sechs Petalled Schwarzweiss-Florette"}}}, {"key": "273F", "mappings": {"default": {"default": "Schwarz<PERSON> F<PERSON>tte"}}}, {"key": "2740", "mappings": {"default": {"default": "Weiße Florette"}}}, {"key": "2741", "mappings": {"default": {"default": "<PERSON>cht Petalled umrissene schwarze Florette"}}}, {"key": "2742", "mappings": {"default": {"default": "Eingekreistes offenes Zentrum Achtstern"}}}, {"key": "2743", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>, tropfenbesetztes Nadelrad-Sternchen"}}}, {"key": "2744", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "2745", "mappings": {"default": {"default": "Feste Trifoliate-Schneeflocke"}}}, {"key": "2746", "mappings": {"default": {"default": "<PERSON>hwer<PERSON> Chevron-<PERSON><PERSON><PERSON><PERSON>locke"}}}, {"key": "2747", "mappings": {"default": {"default": "Funkeln"}}}, {"key": "2748", "mappings": {"default": {"default": "Schweres Funkeln"}}}, {"key": "2749", "mappings": {"default": {"default": "Ballon-Speichen-Sternchen"}}}, {"key": "274A", "mappings": {"default": {"default": "Acht Teardrop-Spoked Propeller Sternchen"}}}, {"key": "274B", "mappings": {"default": {"default": "Schwere Acht Teardrop-Spoked Propeller Sternchen"}}}, {"key": "274C", "mappings": {"default": {"default": "Cross Mark"}}}, {"key": "274D", "mappings": {"default": {"default": "Beschatteter weißer Kreis"}}}], "de/units/area.min": [{"locale": "de"}, {"key": "sq", "mappings": {"default": {"default": "Quadrat"}}}, {"key": "sq inch", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "sq rd", "mappings": {"default": {"default": "Quadratrute", "plural": "Quadratruten"}}}, {"key": "sq ft", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "sq yd", "mappings": {"default": {"default": "Quadratyard", "plural": "Quadratyards"}}}, {"key": "sq mi", "mappings": {"default": {"default": "Quadratmeile", "plural": "Quadratmeilen"}}}, {"key": "acr", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "ha", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}], "de/units/currency.min": [{"locale": "de"}, {"key": "$", "mappings": {"default": {"default": "Dollar"}}}, {"key": "£", "mappings": {"default": {"default": "Pfund"}}}, {"key": "¥", "mappings": {"default": {"default": "Yen"}}}, {"key": "€", "mappings": {"default": {"default": "Euro"}}}, {"key": "₡", "mappings": {"default": {"default": "Colon"}}}, {"key": "₢", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "₣", "mappings": {"default": {"default": "Franc"}}}, {"key": "₤", "mappings": {"default": {"default": "<PERSON><PERSON>", "plural": "<PERSON><PERSON>"}}}, {"key": "₥", "mappings": {"default": {"default": "Mill"}}}, {"key": "₦", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "₧", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>", "plural": "<PERSON><PERSON><PERSON>"}}}, {"key": "₨", "mappings": {"default": {"plural": "Rupies", "default": "<PERSON><PERSON><PERSON>"}}}, {"key": "₩", "mappings": {"default": {"default": "Won"}}}, {"key": "₪", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "₫", "mappings": {"default": {"default": "<PERSON>"}}}, {"key": "₭", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "₮", "mappings": {"default": {"default": "<PERSON><PERSON>gr<PERSON><PERSON>"}}}, {"key": "₯", "mappings": {"default": {"plural": "Drachmen", "default": "Drachme"}}}, {"key": "₰", "mappings": {"default": {"default": "Pfennig"}}}, {"key": "₱", "mappings": {"default": {"default": "Peso"}}}, {"key": "₲", "mappings": {"default": {"default": "Guarani"}}}, {"key": "₳", "mappings": {"default": {"default": "Austral"}}}, {"key": "₴", "mappings": {"default": {"plural": "<PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "₵", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "₸", "mappings": {"default": {"default": "Tenge", "plural": "Tenge"}}}, {"key": "₺", "mappings": {"default": {"default": "türkische Lira", "plural": "türkische Lira"}}}, {"key": "元", "mappings": {"default": {"default": "Yuan"}}}, {"key": "¢", "mappings": {"default": {"default": "Cent"}}}], "de/units/energy.min": [{"locale": "de"}, {"key": "W", "mappings": {"default": {"default": "<PERSON>"}}}, {"key": "kwh", "mappings": {"default": {"default": "Kilowattstunde", "plural": "Kilowattstunden"}}}, {"key": "J", "mappings": {"default": {"default": "Joule"}}}, {"key": "N", "mappings": {"default": {"default": "<PERSON>"}}}, {"key": "A", "mappings": {"default": {"default": "Ampere"}}}, {"key": "V", "mappings": {"default": {"default": "Volt"}}}, {"key": "ohm", "mappings": {"default": {"default": "Ohm"}}}, {"key": "Ω", "mappings": {"default": {"default": "Ohm"}}}], "de/units/length.min": [{"locale": "de"}, {"key": "m", "mappings": {"default": {"default": "<PERSON>er"}}}, {"key": "ft", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "in", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "mi", "mappings": {"default": {"plural": "<PERSON><PERSON>", "default": "<PERSON><PERSON>"}}}, {"key": "yd", "mappings": {"default": {"plural": "Yards", "default": "Yard"}}}, {"key": "n.m.", "mappings": {"default": {"default": "nautische Meile", "plural": "nautische Meilen"}}}, {"key": "link", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>", "plural": "Kettenglieder"}}}, {"key": "rod", "mappings": {"default": {"default": "Rute", "plural": "<PERSON><PERSON><PERSON>"}}}, {"key": "chain", "mappings": {"default": {"default": "<PERSON><PERSON>", "plural": "<PERSON><PERSON>"}}}, {"key": "furlong", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "plural": "Furchenlängen"}}}], "de/units/memory.min": [{"locale": "de"}, {"key": "b", "mappings": {"default": {"plural": "Bits", "default": "Bit"}}}, {"key": "B", "mappings": {"default": {"plural": "Bytes", "default": "Byte"}}}, {"key": "KB", "mappings": {"default": {"plural": "Kilobytes", "default": "Kilobyte"}}}], "de/units/other.min": [{"locale": "de"}, {"key": "doz", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}], "de/units/speed.min": [{"locale": "de"}, {"key": "kt", "mappings": {"default": {"default": "Knoten"}}}, {"key": "mph", "mappings": {"default": {"plural": "Meilen pro Stunde", "default": "Meile pro Stunde"}}}, {"key": "kmh", "mappings": {"default": {"default": "Kilometer pro Stunde"}}}, {"key": "rpm", "mappings": {"default": {"plural": "Umdrehungen pro Minute", "default": "<PERSON><PERSON><PERSON>g pro Minute"}}}], "de/units/temperature.min": [{"locale": "de"}, {"key": "F", "mappings": {"default": {"default": "Grad Fahrenheit"}}}, {"key": "C", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "K", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}], "de/units/time.min": [{"locale": "de"}, {"key": "s", "mappings": {"default": {"default": "Sekunde", "plural": "Sekunden"}}}, {"key": "″", "mappings": {"default": {"default": "Sekunde", "plural": "Sekunden"}}}, {"key": "min", "mappings": {"default": {"default": "Minute", "plural": "Minuten"}}}, {"key": "°", "mappings": {"default": {"default": "Grad"}}}, {"key": "h", "mappings": {"default": {"default": "Stunde", "plural": "Stunden"}}}], "de/units/volume.min": [{"locale": "de"}, {"key": "cu", "mappings": {"default": {"default": "Kubik"}}}, {"key": "cu inch", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "cu ft", "mappings": {"default": {"default": "Kubikfuß"}}}, {"key": "cu yd", "mappings": {"default": {"default": "Kubikyard", "plural": "Kubikyards"}}}, {"key": "bbl", "mappings": {"default": {"default": "Fass"}}}, {"key": "fl. oz.", "mappings": {"default": {"default": "Fluid ounce", "plural": "Fluid ounces"}}}, {"key": "gal", "mappings": {"default": {"plural": "<PERSON><PERSON><PERSON>", "default": "Gallone"}}}, {"key": "pt", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "qt", "mappings": {"default": {"plural": "Quarts", "default": "Quart"}}}, {"key": "tbsp", "mappings": {"default": {"default": "Esslöffel"}}}, {"key": "tsp", "mappings": {"default": {"default": "Teelöffel"}}}, {"key": "fluid dram", "mappings": {"default": {"plural": "Flüssigdrachmen", "default": "Flüssigdrachme"}}}, {"key": "cup", "mappings": {"default": {"plural": "Tassen", "default": "Tasse"}}}, {"key": "cc", "mappings": {"default": {"default": "Kubikzentimeter"}}}, {"key": "l", "mappings": {"default": {"default": "Liter"}}}], "de/units/weight.min": [{"locale": "de"}, {"key": "lb", "mappings": {"default": {"default": "Pfund"}}}, {"key": "oz", "mappings": {"default": {"plural": "<PERSON><PERSON>", "default": "<PERSON><PERSON>"}}}, {"key": "LT", "mappings": {"default": {"default": "Long ton"}}}, {"key": "gr", "mappings": {"default": {"default": "Gramm"}}}, {"key": "g", "mappings": {"default": {"default": "Gramm"}}}, {"key": "mcg", "mappings": {"default": {"default": "Mikrogramm"}}}, {"key": "t", "mappings": {"default": {"plural": "<PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON>"}}}, {"key": "dram", "mappings": {"default": {"plural": "Drachmen", "default": "Drachme"}}}, {"key": "st", "mappings": {"default": {"default": "Stone"}}}, {"key": "qtr", "mappings": {"default": {"default": "Viertelzentner"}}}, {"key": "cwt", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}, "names": ["Ztr.", "Ctr."]}], "de/rules/clearspeak_german.min": {"locale": "de", "domain": "clearspeak", "modality": "speech", "inherits": "base", "rules": [["Precondition", "font-number", "default", "self::number[@font!=\"normal\"]", "not(contains(@grammar, \"ignoreFont\"))"], ["Precondition", "font-double-struck", "default", "self::*[@font=\"double-struck\" or @font=\"double-struck-italic\"]", "name(self::*)!=\"number\"", "string-length(text())=1", "not(contains(@grammar, \"ignoreFont\"))"], ["Specialized", "font-double-struck", "default", "Caps_SayCaps"], ["Precondition", "font-number-double-struck", "default", "self::number[@font=\"double-struck\" or @font=\"double-struck-italic\"]", "string-length(text())=1", "not(contains(@grammar, \"ignoreFont\"))"], ["Precondition", "function-article", "default", "self::function", "@role=\"prefix function\"", "contains(@grammar, \"addArticle\")"], ["Precondition", "function-article-fem", "default", "self::function", "@role=\"prefix function\"", "contains(@grammar, \"addArticle\")", "text()=\"det\" or text()=\"dim\" or text()=\"tr\""], ["Precondition", "superscript-ordinal-prefixop", "default", "self::superscript", "name(children/*[2])=\"prefixop\"", "children/*[2][@role=\"negative\"]", "name(children/*[2]/children/*[1])=\"number\"", "children/*[2]/children/*[1][@role=\"integer\"]"], ["Precondition", "superscript-non-ordinal", "Exponent_OrdinalPower", "self::superscript", "children/*[2][@role=\"negative\"]", "name(children/*[2]/children/*[1])=\"number\"", "children/*[2]/children/*[1][@role=\"integer\"]"], ["Precondition", "superscript-simple-power", "Exponent_OrdinalPower", "self::superscript", "not(descendant::superscript)"], ["Precondition", "superscript-simple-power-end", "Exponent_OrdinalPower", "self::superscript", "not(descendant::superscript)", "not(following-sibling::*)"], ["<PERSON><PERSON>", "superscript-simple-power", "self::superscript", "children/superscript/children/*[2][text()=\"2\"] or children/superscript/children/*[2][text()=\"3\"]", "name(children/superscript/children/*[1])=\"number\"", "contains(children/superscript/children/*[1]/@annotation, \"clearspeak:simple\")"], ["<PERSON><PERSON>", "superscript-simple-power", "self::superscript", "children/superscript/children/*[2][text()=\"2\"] or children/superscript/children/*[2][text()=\"3\"]", "name(children/superscript/children/*[1])=\"fraction\"", "contains(children/superscript/children/*[1]/@annotation, \"clearspeak:simple\")"], ["<PERSON><PERSON>", "superscript-simple-power", "self::superscript", "children/superscript/children/*[2][text()=\"2\"] or children/superscript/children/*[2][text()=\"3\"]", "name(children/superscript/children/*[1])=\"identifier\""], ["<PERSON><PERSON>", "superscript-simple-power", "self::superscript", "children/*[2][@role=\"implicit\"]", "count(children/*[2]/children/*)=2", "contains(children/*[2]/children/*[1]/@annotation, \"simple\")", "name(children/*[2]/children/*[2])=\"superscript\"", "(name(children/*[2]/children/*[2]/children/*[1])=\"number\" and contains(children/*[2]/children/*[2]/children/*[1]/@annotation, \"clearspeak:simple\")) or name(children/*[2]/children/*[2]/children/*[1])=\"identifier\"", "children/*[2]/children/*[2]/children/*[2][text()=\"2\"] or children/*[2]/children/*[2]/children/*[2][text()=\"3\"]"], ["Precondition", "superscript-ordinal-power", "Exponent_OrdinalPower", "self::superscript", "name(children/*[2])=\"number\"", "children/*[2][@role=\"integer\"]"], ["<PERSON><PERSON>", "superscript-ordinal-power", "self::superscript", "name(children/*[2])=\"identifier\"", "children/*[2][@role=\"latinletter\" or @role=\"greekletter\" or @role=\"otherletter\"]"], ["Precondition", "superscript-simple-function-ordinal", "Exponent_OrdinalPower", "self::superscript", "name(children/*[1])=\"identifier\"", "children/*[1][@role=\"simple function\"]", "children/*[2][@role!=\"prime\"]", "not(contains(@grammar, \"functions_none\"))"], ["Precondition", "exponent-zero", "default", "self::number", "@role=\"integer\"", "contains(@grammar, \"ordinal\")", "text()=\"0\""], ["Precondition", "superscript-simple-exp", "Exponent_Exponent", "self::superscript", "not(descendant::superscript)"], ["Precondition", "superscript-simple-exp-end", "Exponent_Exponent", "self::superscript", "not(descendant::superscript)", "not(following-sibling::*)"], ["<PERSON><PERSON>", "superscript-simple-exp", "self::superscript", "children/superscript/children/*[2][text()=\"2\"] or children/superscript/children/*[2][text()=\"3\"]", "name(children/superscript/children/*[1])=\"number\"", "contains(children/superscript/children/*[1]/@annotation, \"clearspeak:simple\")"], ["<PERSON><PERSON>", "superscript-simple-exp", "self::superscript", "children/superscript/children/*[2][text()=\"2\"] or children/superscript/children/*[2][text()=\"3\"]", "name(children/superscript/children/*[1])=\"fraction\"", "contains(children/superscript/children/*[1]/@annotation, \"clearspeak:simple\")"], ["<PERSON><PERSON>", "superscript-simple-exp", "self::superscript", "children/superscript/children/*[2][text()=\"2\"] or children/superscript/children/*[2][text()=\"3\"]", "name(children/superscript/children/*[1])=\"identifier\""], ["<PERSON><PERSON>", "superscript-simple-exp", "self::superscript", "children/*[2][@role=\"implicit\"]", "count(children/*[2]/children/*)=2", "contains(children/*[2]/children/*[1]/@annotation, \"simple\")", "name(children/*[2]/children/*[2])=\"superscript\"", "(name(children/*[2]/children/*[2]/children/*[1])=\"number\" and contains(children/*[2]/children/*[2]/children/*[1]/@annotation, \"clearspeak:simple\")) or name(children/*[2]/children/*[2]/children/*[1])=\"identifier\"", "children/*[2]/children/*[2]/children/*[2][text()=\"2\"] or children/*[2]/children/*[2]/children/*[2][text()=\"3\"]"], ["Precondition", "element", "default", "self::infixop[contains(@role, \"element\")]"], ["Precondition", "set-prefix-operators-masculine", "default", "self::*[@grammar]", "contains(@grammar,\"prefix\")", "descendant-or-self::*/text()=\"∩\""], ["Precondition", "set-prefix-operators-feminine", "default", "self::*[@grammar]", "contains(@grammar,\"prefix\")", "descendant-or-self::*/text()=\"∪\""]]}, "de/rules/clearspeak_german_actions.min": {"locale": "de", "domain": "clearspeak", "modality": "speech", "kind": "actions", "rules": [["Action", "collapsed", "[n] . (engine:modality=summary, grammar:?collapsed); [t] \"kollabiert\""], ["Action", "font", "[t] @font (grammar:localFont); [n] . (pause:short, grammar:ignoreFont=@font)"], ["Action", "font-number", "[t] @font (grammar:localFontNumber); [n] . (grammar:ignoreFont=@font)"], ["Action", "font-double-struck", "[n] . (grammar:ignoreFont=@font); [t] @font (grammar:localFont)"], ["Action", "font-number-double-struck", "[n] . (grammar:ignoreFont=@font); [t] @font (grammar:localFontNumber)"], ["Action", "ellipsis", "[t] \"und so weiter\""], ["Action", "ellipsis-andsoon", "[t] \"und so weiter bis\""], ["Action", "vbar-evaluated", "[n] children/*[1] (pause:short); [t] \"ausgewertet für\"; [n] content/*[1]/children/*[2] (pause:short)"], ["Action", "vbar-evaluated-both", "[n] children/*[1] (pause:short); [t] \"ausgewertet für\"; [n] content/*[1]/children/*[2] (pause:short); [t] \"minus des gleichen Ausdrucks ausgewertet für\"; [n] content/*[1]/children/*[1]/children/*[2] (pause:short)"], ["Action", "vbar-such-that", "[t] \"so dass\""], ["Action", "vbar-divides", "[t] \"teilt\""], ["Action", "vbar-always-divides", "[t] \"teilt\""], ["Action", "vbar-given", "[t] \"für die gilt\""], ["Action", "element", "[n] children/*[1]; [n] content/*[1]; [n] children/*[2] (grammar:case=\"dative\")"], ["Action", "member", "[t] \"Element von\""], ["Action", "member-element", "[t] \"Element von\""], ["Action", "member-in", "[t] \"aus\""], ["Action", "member-belongs", "[t] \"gehört zu\""], ["Action", "not-member", "[t] \"ist kein Element von\""], ["Action", "not-member-element", "[t] \"ist kein Element von\""], ["Action", "not-member-in", "[t] \"nicht in\""], ["Action", "not-member-belongs", "[t] \"gehört nicht zu\""], ["Action", "set-member", "[t] \"aus\""], ["Action", "set-member-element", "[t] \"Element von\""], ["Action", "set-member-belongs", "[t] \"gehört zu\""], ["Action", "set-not-member", "[t] \"nicht in\""], ["Action", "set-not-member-element", "[t] \"kein Element von\""], ["Action", "set-not-member-belongs", "[t] \"gehört nicht zu\""], ["Action", "function-article", "[t] \"der\" (grammar:article); [n] text()"], ["Action", "function-article-fem", "[t] \"die\" (grammar:article); [n] text()"], ["Action", "appl", "[n] children/*[1]; [t] \"von\"; [n] children/*[2] (pause:short, grammar:case=\"dative\")"], ["Action", "appl-simple", "[n] children/*[1]; [t] \"von\" (pause:short); [n] children/*[2] (pause:short, grammar:case=\"dative\")"], ["Action", "appl-simple-fenced", "[n] children/*[1]; [t] \"von\" (pause:short); [n] children/*[2] (pause:short, grammar:case=\"dative\")"], ["Action", "appl-times", "[p] (pause:short); [n] children/*[1]; [t] \"mal\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix", "[n] children/*[1]; [n] children/*[2]"], ["Action", "function-prefix-simple-arg", "[n] children/*[1]; [n] children/*[2]"], ["Action", "function-prefix-embell", "[p] (pause:short); [n] children/*[1]; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-fenced-or-frac-arg", "[p] (pause:short); [n] children/*[1] (grammar:addArticle); [t] \"von\"; [n] children/*[2] (pause:short, grammar:case=\"dative\")"], ["Action", "function-prefix-subscript", "[p] (pause:short); [n] children/*[1] (grammar:addArticle); [t] \"von\" (pause:short); [n] children/*[2] (pause:short, grammar:case=\"dative\")"], ["Action", "function-ln", "[n] children/*[1]; [n] children/*[2]"], ["Action", "function-ln-pause", "[n] children/*[1]; [n] children/*[2] (pause:short)"], ["Action", "function-ln-of", "[n] children/*[1]; [t] \"von\"; [n] children/*[2] (pause:short, grammar:case=\"dative\")"], ["Action", "function-prefix-as-exp", "[n] children/*[1]; [t] \"von\" (pause:short); [n] children/*[2] (pause:short, grammar:case=\"dative\")"], ["Action", "function-prefix-subscript-as-exp", "[n] children/*[1]; [t] \"von\" (pause:short); [n] children/*[2] (pause:short, grammar:case=\"dative\")"], ["Action", "function-prefix-hyper", "[p] (pause:short); [n] children/*[1]; [t] \"von\"; [n] children/*[2] (pause:short, grammar:case=\"dative\")"], ["Action", "function-prefix-inverse", "[p] (pause:short); [t] \"der\" (grammar:article); [t] \"inverse\" (grammar:masculine); [n] children/*[1]/children/*[1]; [t] \"von\"; [n] children/*[2] (pause:short, grammar:case=\"dative\")"], ["Action", "appl-triginverse", "[p] (pause:short); [n] children/*[1]; [t] \"von\"; [n] children/*[2] (pause:short, grammar:case=\"dative\")"], ["Action", "function-prefix-arc-simple", "[p] (pause:short); [t] \"Arkus\" (join:\"\"); [n] children/*[1]/children/*[1] (grammar:lowercase); [n] children/*[2] (pause:short)"], ["Action", "function-prefix-arc-simple-fenced", "[p] (pause:short); [t] \"Arkus\" (join:\"\"); [n] children/*[1]/children/*[1] (pause:short, grammar:lowercase); [n] children/*[2] (pause:short)"], ["Action", "function-prefix-arc", "[p] (pause:short); [t] \"Arkus\" (join:\"\"); [n] children/*[1]/children/*[1] (grammar:lowercase); [t] \"von\"; [n] children/*[2] (pause:short, grammar:case=\"dative\")"], ["Action", "function-inverse", "[n] children/*[1]; [t] \"invers\""], ["Action", "superscript-prefix-function", "[t] \"die\" (grammar:article); [n] children/*[2] (grammar:ordinal); [t] \"Potenz von\"; [n] children/*[1]"], ["Action", "superscript", "[n] children/*[1]; [t] \"mit Exponent\" (pause:short); [n] children/*[2] (pause:short); [t] \"Ende Exponent\" (pause:short)"], ["Action", "superscript-ordinal", "[n] children/*[1]; [t] \"hoch\"; [n] children/*[2] (pause:short)"], ["Action", "superscript-ordinal-prefixop", "[n] children/*[1]; [t] \"hoch\"; [n] children/*[2] (pause:short)"], ["Action", "superscript-ordinal-default", "[n] children/*[1]; [t] \"mit Exponent\" (pause:short); [n] children/*[2] (pause:short); [t] \"Ende Exponent\" (pause:short)"], ["Action", "superscript-simple-exponent", "[n] children/*[1]; [t] \"hoch\"; [n] children/*[2] (pause:short)"], ["Action", "superscript-simple-exponent-end", "[n] children/*[1]; [t] \"hoch\"; [n] children/*[2]"], ["Action", "superscript-simple-power", "[n] children/*[1]; [t] \"potenziert mit\"; [n] children/*[2] (pause:short)"], ["Action", "superscript-simple-power-end", "[n] children/*[1]; [t] \"potenziert mit\"; [n] children/*[2]"], ["Action", "superscript-ordinal-power", "[n] children/*[1]; [t] \"zur\"; [n] children/*[2] (join:\"\", grammar:ordinal); [t] \"n Potenz\" (pause:short)"], ["Action", "superscript-non-ordinal", "[n] children/*[1]; [t] \"zur negativ\"; [n] children/*[2]/children/*[1] (join:\"\", grammar:ordinal); [t] \"n Potenz\" (pause:short)"], ["Action", "superscript-simple-function-ordinal", "[t] \"die\" (grammar:article); [n] children/*[2] (grammar:ordinal); [t] \"Potenz von\" (pause:short); [n] children/*[1]"], ["Action", "exponent", "[n] text() (join:\"\"); [t] \"te\""], ["Action", "exponent-number", "[t] CSFwordOrdinal"], ["Action", "exponent-zero", "[t] \"nullte\""], ["Action", "superscript-simple-exp", "[n] children/*[1]; [t] \"mit Exponent\"; [n] children/*[2] (pause:short)"], ["Action", "superscript-simple-exp-end", "[n] children/*[1]; [t] \"mit Exponent\"; [n] children/*[2]"], ["Action", "superscript-simple-function-none", "[n] . (grammar:functions_none)"], ["Action", "square", "[n] children/*[1]; [t] \"Quadrat\""], ["Action", "cube", "[n] children/*[1]; [t] \"Kubik\""], ["Action", "fences-points", "[t] \"der Punkt mit Koordinaten\"; [n] children/*[1]"], ["Action", "fences-auto-interval", "[t] \"das Interval von\"; [n] children/*[1]/children/*[1]; [t] \"bis\"; [n] children/*[1]/children/*[3] (pause:short); [n] . (grammar:interval)"], ["Action", "fences-interval", "[t] \"das Interval von\"; [n] children/*[1]/children/*[1]; [t] \"bis\"; [n] children/*[1]/children/*[3] (pause:short); [n] . (grammar:interval)"], ["Action", "interval-open", "[t] \"ohne\"; [n] children/*[1]/children/*[1]; [t] \"und\"; [n] children/*[1]/children/*[3]"], ["Action", "interval-closed-open", "[t] \"einsch<PERSON><PERSON><PERSON>\"; [n] children/*[1]/children/*[1] (pause:short); [t] \"aber ohne\"; [n] children/*[1]/children/*[3]"], ["Action", "interval-open-closed", "[t] \"ohne\"; [n] children/*[1]/children/*[1] (pause:short); [t] \"aber einschließlich\"; [n] children/*[1]/children/*[3]"], ["Action", "interval-closed", "[t] \"einsch<PERSON>ß<PERSON>\"; [n] children/*[1]/children/*[1]; [t] \"und\"; [n] children/*[1]/children/*[3]"], ["Action", "interval-open-inf-r", "[t] \"ohne\"; [n] children/*[1]/children/*[1]"], ["Action", "interval-open-inf-l", "[t] \"ohne\"; [n] children/*[1]/children/*[3]"], ["Action", "interval-closed-open-inf", "[t] \"einsch<PERSON><PERSON><PERSON>\"; [n] children/*[1]/children/*[1]"], ["Action", "interval-open-closed-inf", "[t] \"einsch<PERSON><PERSON><PERSON>\"; [n] children/*[1]/children/*[3]"], ["Action", "set-empty", "[t] \"die\" (grammar:article); [t] \"leere Menge\""], ["Action", "set-extended", "[t] \"die Menge aller\"; [n] children/*[1]/children/*[1]; [t] \"mit\"; [n] children/*[1]/children/*[3]"], ["Action", "set-collection", "[t] \"die Menge\"; [n] children/*[1]"], ["Action", "set-extended-woall", "[t] \"die Menge von\"; [n] children/*[1]/children/*[1]; [t] \"mit\"; [n] children/*[1]/children/*[3]"], ["Action", "subscript", "[p] (pause:short); [n] children/*[1]; [t] \"Index\"; [n] children/*[2] (pause:short)"], ["Action", "logarithm-base", "[n] children/*[1]; [t] \"Basis\"; [n] children/*[2]"], ["Action", "subscript-index", "[n] children/*[1]; [t] \"Index\"; [n] children/*[2]"], ["Action", "fraction", "[p] (pause:short); [t] \"<PERSON><PERSON><PERSON> mit Zähler\"; [n] children/*[1] (pause:short); [t] \"und N<PERSON>r\"; [n] children/*[2] (pause:short)"], ["Action", "fraction-none", "[p] (pause:short); [t] \"<PERSON><PERSON><PERSON> mit Zähler\"; [n] children/*[1] (pause:short); [t] \"und N<PERSON>r\"; [n] children/*[2] (pause:short)"], ["Action", "simple-fraction", "[p] (pause:short); [n] children/*[1]; [t] \"geteilt durch\"; [n] children/*[2] (pause:short)"], ["Action", "simple-vulgar-fraction", "[p] (pause:short); [n] children/*[1]; [t] \"geteilt durch\"; [n] children/*[2] (pause:short)"], ["Action", "simple-text-fraction", "[p] (pause:short); [n] children/*[1]; [t] \"geteilt durch\"; [n] children/*[2] (pause:short)"], ["Action", "vulgar-fraction", "[t] CSFvulgarFraction (grammar:correctOne)"], ["Action", "fraction-over", "[p] (pause:short); [n] children/*[1]; [t] \"geteilt durch\"; [n] children/*[2] (pause:short)"], ["Action", "fraction-overendfrac", "[p] (pause:short); [n] children/*[1]; [t] \"geteilt durch\"; [n] children/*[2] (pause:short); [t] \"End<PERSON> Bruch\" (pause:short)"], ["Action", "fraction-fracover", "[p] (pause:short); [t] \"<PERSON>ru<PERSON>\"; [n] children/*[1]; [t] \"geteilt durch\"; [n] children/*[2] (pause:short)"], ["Action", "fraction-per", "[p] (pause:short); [n] children/*[1]; [t] \"per\"; [n] children/*[2] (pause:short)"], ["Action", "fraction-general<PERSON><PERSON><PERSON>", "[p] (pause:short); [t] \"<PERSON><PERSON><PERSON> mit Zähler\"; [n] children/*[1] (pause:short); [t] \"und Nenner\"; [n] children/*[2] (pause:short); [t] \"Ende Bruch\" (pause:short)"], ["Action", "fraction-general", "[p] (pause:short); [t] \"<PERSON><PERSON><PERSON> mit Zähler\"; [n] children/*[1] (pause:short); [t] \"und N<PERSON>r\"; [n] children/*[2] (pause:short)"], ["Action", "simple-vulgar-fraction-ordinal", "[t] CSFvulgarFraction (grammar:correctOne)"], ["Action", "fraction-endfrac", "[p] (pause:short); [n] . (grammar:endfrac); [t] \"<PERSON><PERSON> Bruch\" (pause:short)"], ["Action", "vulgar-fraction-endfrac", "[p] (pause:short); [n] children/*[1]; [t] \"geteilt durch\"; [n] children/*[2] (pause:short)"], ["Action", "simple-vulgar-fraction-endfrac", "[t] CSFvulgarFraction (grammar:correctOne)"], ["Action", "sqrt", "[t] \"Quadratwu<PERSON>el aus\"; [n] children/*[1] (pause:short)"], ["Action", "sqrt-nested", "[p] (pause:\"short\"); [t] \"Quadratwurzel aus\"; [n] children/*[1] (pause:short)"], ["Action", "negative-sqrt", "[t] \"negative Quadratwu<PERSON>el aus\"; [n] children/*[1]/children/*[1] (pause:short)"], ["Action", "negative-sqrt-default", "[p] (pause:\"short\"); [t] \"negative Quadratwu<PERSON>el aus\"; [n] children/*[1]/children/*[1] (pause:short)"], ["Action", "sqrt-plus-minus", "[t] \"positive Quadratwu<PERSON>el aus\"; [n] children/*[1] (pause:short)"], ["Action", "sqrt-nested-plus-minus", "[p] (pause:\"short\"); [t] \"positive Quadratwu<PERSON>el aus\"; [n] children/*[1] (pause:short)"], ["Action", "sqrt-plus-minus-posnegsqrootend", "[t] \"positive Quadratwu<PERSON>el aus\"; [n] children/*[1] (pause:short)"], ["Action", "sqrt-nested-plus-minus-posnegsqrootend", "[p] (pause:\"short\"); [t] \"positive Quadratwu<PERSON>el aus\"; [n] children/*[1] (pause:short)"], ["Action", "sqrt-endroot", "[n] . (grammar:?EndRoot); [t] \"<PERSON><PERSON><PERSON>\" (pause:short)"], ["Action", "negative-sqrt-endroot", "[n] . (grammar:?EndRoot); [t] \"<PERSON><PERSON><PERSON>\" (pause:short)"], ["Action", "sqrt-posnegsqrootend", "[n] . (grammar:?EndRoot); [t] \"<PERSON><PERSON><PERSON>\" (pause:short)"], ["Action", "negative-sqrt-posnegsqrootend", "[n] . (grammar:?EndRoot); [t] \"<PERSON><PERSON><PERSON>\" (pause:short)"], ["Action", "cubic", "[t] \"Ku<PERSON>k<PERSON><PERSON><PERSON> aus\"; [n] children/*[2] (pause:short)"], ["Action", "cubic-nested", "[p] (pause:short); [t] \"Ku<PERSON><PERSON><PERSON><PERSON><PERSON> aus\"; [n] children/*[2] (pause:short)"], ["Action", "root", "[t] \"die\" (grammar:article); [n] children/*[1] (grammar:ordinal); [t] \"<PERSON><PERSON>el aus\"; [n] children/*[2] (pause:short)"], ["Action", "root-nested", "[p] (pause:short); [t] \"die\" (grammar:article); [n] children/*[1] (grammar:ordinal); [t] \"<PERSON><PERSON><PERSON> aus\"; [n] children/*[2] (pause:short)"], ["Action", "root-endroot", "[n] . (grammar:?EndRoot); [t] \"<PERSON><PERSON><PERSON>\" (pause:short)"], ["Action", "root-posnegsqrootend", "[n] . (grammar:?EndRoot); [t] \"<PERSON><PERSON><PERSON>\" (pause:short)"], ["Action", "negative", "[t] \"minus\"; [n] children/*[1]"], ["Action", "positive", "[t] \"plus\"; [n] children/*[1]"], ["Action", "angle-measure", "[t] \"das Maß des Winkels\"; [n] children/*[2] (grammar:angle)"], ["Action", "set-prefix-operators-masculine", "[t] \"der\" (grammar:article); [n] .; [t] \"von\""], ["Action", "set-prefix-operators-feminine", "[t] \"die\" (grammar:article); [n] .; [t] \"von\""], ["Action", "division", "[n] children/*[1]; [t] \"geteilt durch\"; [n] children/*[2]"], ["Action", "natural-numbers", "[t] \"die\" (grammar:article:plural); [t] \"natürlichen Zahlen\""], ["Action", "integers", "[t] \"die\" (grammar:article:plural); [t] \"ganzen Zahlen\""], ["Action", "rational-numbers", "[t] \"die\" (grammar:article:plural); [t] \"rationalen Zahlen\""], ["Action", "real-numbers", "[t] \"die\" (grammar:article:plural); [t] \"reellen Zahlen\""], ["Action", "complex-numbers", "[t] \"die\" (grammar:article:plural); [t] \"komplexen Zahlen\""], ["Action", "natural-numbers-with-zero", "[t] \"die\" (grammar:article:plural); [t] \"natürlichen Zahlen mit Null\""], ["Action", "positive-integers", "[t] \"die\" (grammar:article:plural); [t] \"positiven ganzen Zahlen\""], ["Action", "negative-integers", "[t] \"die\" (grammar:article:plural); [t] \"negativen ganzen Zahlen\""], ["Action", "positive-rational-numbers", "[t] \"die\" (grammar:article:plural); [t] \"positiven rationalen Zahlen\""], ["Action", "negative-rational-numbers", "[t] \"die\" (grammar:article:plural); [t] \"negativen rationalen Zahlen\""], ["Action", "fences-neutral", "[p] (pause:short); [t] \"der Betrag von\"; [n] children/*[1] (pause:short)"], ["Action", "fences-neutral-absend", "[p] (pause:short); [t] \"der Betrag von\"; [n] children/*[1] (pause:short); [t] \"Ende Betrag\" (pause:short)"], ["Action", "fences-neutral-cardinality", "[p] (pause:short); [t] \"die Mächtigkeit der Menge\"; [n] children/*[1] (pause:short)"], ["Action", "fences-neutral-determinant", "[p] (pause:short); [t] \"die Determinante von\"; [n] children/*[1] (pause:short)"], ["Action", "fences-metric", "[p] (pause:short); [t] \"die Metrik von\" (span:.); [n] children/*[1] (pause:short)"], ["Action", "fences-metric-absend", "[p] (pause:short); [t] \"die Metrik von\" (span:content/*[1]); [n] children/*[1] (pause:short); [t] \"Ende Metrik\" (span:content/*[1], pause:short)"], ["Action", "matrix", "[t] \"die\" (grammar:article); [t] count(children/*); [t] \"mal\"; [t] count(children/*[1]/children/*); [t] \"Matrize\" (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Zeile-:\", pause:long)"], ["Action", "matrix-simple", "[t] \"die\" (grammar:article); [t] count(children/*); [t] \"mal\"; [t] count(children/*[1]/children/*); [t] \"Matrize\" (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Zeile-:\", pause:long, grammar:simpleDet)"], ["Action", "matrix-trivial", "[t] \"die 1 mal 1 Matrize mit Element\"; [n] children/*[1] (pause:long)"], ["Action", "determinant", "[t] \"die\" (grammar:article); [t] \"Determinante der\"; [t] count(children/*); [t] \"mal\"; [t] count(children/*[1]/children/*); [t] \"Matrize\" (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Zeile-:\", pause:long, grammar:simpleDet)"], ["Action", "determinant-simple", "[t] \"die\" (grammar:article); [t] \"Determinante der\"; [t] count(children/*); [t] \"mal\"; [t] count(children/*[1]/children/*); [t] \"Matrize\" (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Zeile-:\", pause:long)"], ["Action", "matrix-vector", "[t] \"die\" (grammar:article); [t] count(children/*); [t] \"mal\"; [t] count(children/*[1]/children/*); [t] \"Spaltenmatrize\" (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Zeile-:\", pause:long, grammar:simpleDet)"], ["Action", "matrix-vector-simple", "[t] \"die\" (grammar:article); [t] count(children/*); [t] \"mal\"; [t] count(children/*[1]/children/*); [t] \"Spaltenmatrize\" (pause:long); [m] children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "matrix-vector-simple-silentcolnum", "[t] \"die\" (grammar:article); [t] count(children/*); [t] \"mal\"; [t] count(children/*[1]/children/*); [t] \"Spaltenmatrize\" (pause:long); [m] children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "matrix-row-vector", "[t] \"die\" (grammar:article); [t] count(children/*); [t] \"mal\"; [t] count(children/*[1]/children/*); [t] \"Zeilenmatrize\" (pause:long); [m] children/*[1]/children/* (ctxtFunc:CTFnodeCounter, context:\"Spalte-:\", pause:long, grammar:simpleDet)"], ["Action", "matrix-row-vector-simple", "[t] \"die\" (grammar:article); [t] count(children/*); [t] \"mal\"; [t] count(children/*[1]/children/*); [t] \"Zeilenmatrize\" (pause:long); [m] children/*[1]/children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "matrix-row-vector-simple-silentcolnum", "[t] \"die\" (grammar:article); [t] count(children/*); [t] \"mal\"; [t] count(children/*[1]/children/*); [t] \"Zeilenmatrize\" (pause:long); [m] children/*[1]/children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "matrix-row", "[m] children/* (ctxtFunc:CTFnodeCounter, context:\"Spalte-,- \", sepFunc:CTFpauseSeparator, separator:\"medium\", pause:long)"], ["Action", "matrix-end-matrix", "[n] . (grammar:?EndMatrix); [t] \"Ende Matrize\""], ["Action", "matrix-end-vector", "[n] . (grammar:?EndMatrix); [t] \"Ende Matrize\""], ["Action", "matrix-end-determinant", "[n] . (grammar:?EndMatrix); [t] \"Ende Determinante\""], ["Action", "vector", "[t] \"der\" (grammar:article); [t] count(children/*); [t] \"mal\"; [t] count(children/*[1]/children/*); [t] \"Spaltenvektor\" (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Zeile-:\", pause:long, grammar:simpleDet)"], ["Action", "vector-simple", "[t] \"der\" (grammar:article); [t] count(children/*); [t] \"mal\"; [t] count(children/*[1]/children/*); [t] \"Spaltenvektor\" (pause:long); [m] children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "row-vector", "[t] \"der\" (grammar:article); [t] count(children/*); [t] \"mal\"; [t] count(children/*[1]/children/*); [t] \"Zeilenvektor\" (pause:long); [m] children/*[1]/children/* (ctxtFunc:CTFnodeCounter, context:\"Spalte-:\", pause:long, grammar:simpleDet)"], ["Action", "row-vector-simple", "[t] \"der\" (grammar:article); [t] count(children/*); [t] \"mal\"; [t] count(children/*[1]/children/*); [t] \"Zeilenvektor\" (pause:long); [m] children/*[1]/children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "vector-end-matrix", "[n] . (grammar:?EndMatrix); [t] \"Ende Matrize\""], ["Action", "vector-end-vector", "[n] . (grammar:?EndMatrix); [t] \"Ende Vektor\""], ["Action", "vector-end-vector-endvector", "[n] . (grammar:?EndMatrix); [t] \"Ende Vektor\""], ["Action", "vector-end-determinant", "[n] . (grammar:?EndMatrix); [t] \"Ende Determinante\""], ["Action", "binomial", "[n] children/*[1]/children/*[1]; [t] \"über\"; [n] children/*[2]/children/*[1]"], ["Action", "lines-summary", "[p] (pause:short); [t] count(children/*); [t] \"<PERSON>eil<PERSON>\"; [n] . (grammar:?layoutSummary)"], ["Action", "cases-summary", "[p] (pause:short); [t] count(children/*); [t] \"<PERSON><PERSON><PERSON>\"; [n] . (grammar:?layoutSummary)"], ["Action", "lines", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Zeile-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "blank-cell", "[t] \"leer\""], ["Action", "blank-line", "[t] \"leer\""], ["Action", "blank-cell-empty", "[t] \"leer\""], ["Action", "blank-line-empty", "[t] \"leer\""], ["Action", "cases", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Fall-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-cases-summary", "[p] (pause:short); [t] count(children/*); [t] \"<PERSON><PERSON><PERSON>\"; [n] . (grammar:?layoutSummary)"], ["Action", "lines-cases", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Fall-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-equations-summary", "[p] (pause:short); [t] count(children/*); [t] \"Gleichungen\"; [n] . (grammar:?layoutSummary)"], ["Action", "lines-equations", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Gleichung-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-steps-summary", "[p] (pause:short); [t] count(children/*); [t] \"<PERSON>chenschrit<PERSON>\"; [n] . (grammar:?layoutSummary)"], ["Action", "lines-steps", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Schritt-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-rows-summary", "[p] (pause:short); [t] count(children/*); [t] \"<PERSON>eil<PERSON>\"; [n] . (grammar:?layoutSummary)"], ["Action", "lines-rows", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Zeile-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-constraints-summary", "[p] (pause:short); [t] count(children/*); [t] \"Bedingungen\"; [n] . (grammar:?layoutSummary)"], ["Action", "lines-constraints", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Bedingung-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "bigop", "[n] children/*[1]; [t] \"über\"; [n] children/*[2] (pause:short)"], ["Action", "limboth", "[n] children/*[1]; [t] \"von\"; [n] children/*[2]; [t] \"bis\"; [n] children/*[3]"], ["Action", "limlower", "[n] children/*[1]; [t] \"über\"; [n] children/*[2] (pause:short)"], ["Action", "limupper", "[n] children/*[1]; [t] \"unter\"; [n] children/*[2] (pause:short)"], ["Action", "integral", "[t] \"das\"; [n] children/*[1]; [t] \"über\"; [n] children/*[2] (pause:short); [n] children/*[3] (pause:short)"], ["Action", "integral-novar", "[t] \"das\"; [n] children/*[1]; [t] \"über\"; [n] children/*[2] (pause:short)"], ["Action", "overscript", "[n] children/*[1]; [t] \"unter\"; [n] children/*[2] (pause:short)"], ["Action", "overscript-limits", "[n] children/*[1]; [t] \"bis\"; [n] children/*[2]"], ["Action", "underscript", "[n] children/*[1]; [t] \"über\"; [n] children/*[2] (pause:short)"], ["Action", "underscript-limits", "[n] children/*[1]; [t] \"von\"; [n] children/*[2]"], ["Action", "mixed-number", "[n] children/*[1]; [n] children/*[2]"], ["Action", "number-with-chars", "[t] \"Zahl\"; [m] CQFspaceoutNumber (grammar:protected)"], ["Action", "decimal-period", "[t] \"Dezi<PERSON><PERSON><PERSON>ch\"; [n] children/*[1] (grammar:spaceout); [t] \"Komma mit Periode\"; [n] children/*[3]/children/*[1] (grammar:spaceout)"], ["Action", "decimal-period-float", "[t] \"Dezimalbruch\"; [n] children/*[1] (grammar:spaceout); [t] \"mit Periode\"; [n] children/*[2]/children/*[1] (grammar:spaceout)"], ["Action", "decimal-period-singular", "[t] \"Dezi<PERSON><PERSON><PERSON>ch\"; [n] children/*[1] (grammar:spaceout); [t] \"Komma mit Periode\"; [n] children/*[3]/children/*[1] (grammar:spaceout)"], ["Action", "decimal-period-singular-float", "[t] \"Dezimalbruch\"; [n] children/*[1] (grammar:spaceout); [t] \"mit Periode\"; [n] children/*[2]/children/*[1] (grammar:spaceout)"], ["Action", "decimal-point", "[t] \"Komma\""], ["Action", "line-segment", "[t] \"die Strecke\"; [n] children/*[1]/children/*[1]; [n] children/*[1]/children/*[2] (pause:short)"], ["Action", "conjugate", "[t] \"die komplexe Konjugation von\"; [n] children/*[1]"], ["Action", "defined-by", "[t] \"ist definiert als\" (pause:short)"], ["Action", "adorned-sign", "[n] children/*[1]; [t] \"<PERSON><PERSON><PERSON> mit darüberstehendem\"; [n] children/*[2]"], ["Action", "factorial", "[t] \"Fakultät\""], ["Action", "left-super", "[t] \"linker oberer Index\"; [n] text()"], ["Action", "left-super-list", "[t] \"linker oberer Index\"; [m] children/*"], ["Action", "left-sub", "[t] \"linker unterer Index\"; [n] text()"], ["Action", "left-sub-list", "[t] \"linker unterer Index\"; [m] children/*"], ["Action", "right-super", "[t] \"rechter oberer Index\"; [n] text()"], ["Action", "right-super-list", "[t] \"rechter oberer Index\"; [m] children/*"], ["Action", "right-sub", "[t] \"rechter unterer Index\"; [n] text()"], ["Action", "right-sub-list", "[t] \"rechter unterer Index\"; [m] children/*"], ["Action", "choose", "[n] children/*[4] (grammar:combinatorics); [t] \"aus\"; [n] children/*[2] (grammar:combinatorics)"], ["Action", "permute", "[n] children/*[4] (grammar:combinatorics); [t] \"Permutionen von\"; [n] children/*[2] (grammar:combinatorics)"], ["Action", "unit-square", "[t] \"Quadrat\" (join:\"\"); [n] children/*[1] (grammar:lowercase)"], ["Action", "unit-cubic", "[t] \"Kubik\" (join:\"\"); [n] children/*[1] (grammar:lowercase)"], ["Action", "unit-reciprocal", "[n] children/*[1]; [t] \"invers\""], ["Action", "unit-reciprocal-singular", "[t] \"pro\"; [n] children/*[1]"], ["Action", "unit-divide", "[n] children/*[1]; [t] \"pro\"; [n] children/*[2]"], ["Action", "multi-inference", "[t] \"<PERSON><PERSON><PERSON>regel\"; [m] content/*; [t] \"mit Folgerung\"; [n] children/*[1]; [t] \"aus\"; [t] count(children/*[2]/children/*); [t] \"<PERSON>r<PERSON><PERSON><PERSON>\""], ["Action", "inference", "[t] \"<PERSON><PERSON><PERSON>re<PERSON>\"; [m] content/*; [t] \"mit Folgerung\"; [n] children/*[1]; [t] \"aus\"; [t] count(children/*[2]/children/*); [t] \"Prämisse\""], ["Action", "premise", "[m] children/* (ctxtFunc:CTFordinalCounter, context:\"Prämis<PERSON> \")"], ["Action", "conclusion", "[n] children/*[1]"], ["Action", "label", "[t] \"<PERSON><PERSON>\"; [n] children/*[1]"], ["Action", "axiom", "[t] \"Axiom\"; [m] children/*[1]"], ["Action", "empty-axiom", "[t] \"leeres Axiom\""]]}, "de/rules/mathspeak_german.min": {"locale": "de", "domain": "mathspeak", "modality": "speech", "inherits": "base", "rules": [["Ignore", "vulgar-fraction"], ["Precondition", "font-number", "default", "self::number[@font!=\"normal\"]", "not(contains(@grammar, \"ignoreFont\"))"], ["Precondition", "font-double-struck", "default", "self::*[@font=\"double-struck\" or @font=\"double-struck-italic\"]", "name(self::*)!=\"number\"", "string-length(text())=1", "not(contains(@grammar, \"ignoreFont\"))"], ["Precondition", "font-number-double-struck", "default", "self::number[@font=\"double-struck\" or @font=\"double-struck-italic\"]", "string-length(text())=1", "not(contains(@grammar, \"ignoreFont\"))"], ["Precondition", "double-overscore", "default", "self::overscore", "children/*[2][@role=\"overaccent\"]", "name(children/*[1])=\"overscore\"", "children/*[1]/children/*[2][@role=\"overaccent\"]"], ["Precondition", "double-overscore-brief", "brief", "self::overscore", "children/*[2][@role=\"overaccent\"]", "name(children/*[1])=\"overscore\"", "children/*[1]/children/*[2][@role=\"overaccent\"]"], ["Specialized", "double-overscore-brief", "brief", "sbrief"], ["Precondition", "double-underscore", "default", "self::underscore", "children/*[2][@role=\"underaccent\"]", "name(children/*[1])=\"underscore\"", "children/*[1]/children/*[2][@role=\"underaccent\"]"], ["Precondition", "double-underscore-brief", "brief", "self::underscore", "children/*[2][@role=\"underaccent\"]", "name(children/*[1])=\"underscore\"", "children/*[1]/children/*[2][@role=\"underaccent\"]"], ["Specialized", "double-underscore-brief", "brief", "sbrief"], ["Precondition", "overbar", "default", "self::overscore", "contains(@role,\"letter\")", "children/*[2][@role=\"overaccent\"]", "children/*[2][contains(@annotation, \"accent:bar\")]"], ["Precondition", "underbar", "default", "self::underscore", "contains(@role,\"letter\")", "children/*[2][@role=\"underaccent\"]", "children/*[2][contains(@annotation, \"accent:bar\")]"], ["Precondition", "overtilde", "default", "self::overscore", "children/*[2][@role=\"overaccent\"]", "contains(@role,\"letter\")", "children/*[2][contains(@annotation, \"accent:tilde\")]"], ["Precondition", "undertilde", "default", "self::underscore", "contains(@role,\"letter\")", "children/*[2][@role=\"underaccent\"]", "children/*[2][contains(@annotation, \"accent:tilde\")]"], ["<PERSON><PERSON>", "overbar", "self::enclose", "@role=\"top\""], ["<PERSON><PERSON>", "underbar", "self::enclose", "@role=\"bottom\""], ["Precondition", "binomial-brief", "brief", "self::vector", "@role=\"binomial\""], ["Precondition", "cases-brief", "brief", "self::cases"]]}, "de/rules/mathspeak_german_actions.min": {"locale": "de", "domain": "mathspeak", "modality": "speech", "kind": "actions", "rules": [["Action", "collapsed", "[n] . (engine:modality=summary, grammar:?collapsed); [t] \"kollabiert\""], ["Action", "blank-cell-empty", "[t] \"leer\""], ["Action", "blank-line-empty", "[t] \"leer\""], ["Action", "font", "[t] @font (grammar:localFont); [n] . (grammar:ignoreFont=@font)"], ["Action", "font-number", "[t] @font (grammar:localFontNumber); [n] . (grammar:ignoreFont=@font)"], ["Action", "font-double-struck", "[n] . (grammar:ignoreFont=@font); [t] @font (grammar:localFont)"], ["Action", "font-number-double-struck", "[n] . (grammar:ignoreFont=@font); [t] @font (grammar:localFontNumber)"], ["Action", "mixed-number", "[n] children/*[1]; [n] children/*[2]"], ["Action", "number-with-chars", "[t] \"Zahl\"; [m] CQFspaceoutNumber (grammar:protected)"], ["Action", "number-as-upper-word", "[t] \"Wort groß\"; [t] CSFspaceoutText"], ["Action", "number-baseline", "[t] \"Grun<PERSON><PERSON><PERSON>\"; [n] . (grammar:baseline)"], ["Action", "number-baseline-brief", "[t] \"Grund\"; [n] . (grammar:baseline)"], ["Action", "number-baseline-font", "[t] \"Grundlinie\"; [t] @font (grammar:localFont); [n] . (grammar:ignoreFont=@font)"], ["Action", "number-baseline-font-brief", "[t] \"Grund\"; [t] @font (grammar:localFont); [n] . (grammar:ignoreFont=@font)"], ["Action", "negative-number", "[t] \"minus\"; [n] children/*[1]"], ["Action", "negative", "[t] \"minus\"; [n] children/*[1]"], ["Action", "division", "[n] children/*[1]; [t] \"geteilt durch\"; [n] children/*[2]"], ["Action", "subtraction", "[m] children/* (separator:\"minus\")"], ["Action", "fences-neutral", "[t] \"Anfang Betrag\"; [n] children/*[1]; [t] \"Ende Betrag\""], ["Action", "fences-neutral-sbrief", "[t] \"Betrag\"; [n] children/*[1]; [t] \"Ende Betrag\""], ["Action", "fences-metric", "[t] \"Anfang Metrik\"; [n] children/*[1]; [t] \"Ende Metrik\""], ["Action", "fences-metric-sbrief", "[t] \"Metrik\"; [n] children/*[1]; [t] \"Ende Metrik\""], ["Action", "empty-set", "[t] \"leere Menge\""], ["Action", "fences-set", "[t] \"Anfang Men<PERSON>\"; [n] children/*[1]; [t] \"<PERSON><PERSON> Menge\""], ["Action", "fences-set-sbrief", "[t] \"<PERSON><PERSON>\"; [n] children/*[1]; [t] \"<PERSON>e Menge\""], ["Action", "factorial", "[t] \"Fakultät\""], ["Action", "minus", "[t] \"minus\""], ["Action", "vulgar-fraction", "[t] CSFvulgarFraction (grammar:correctOne)"], ["Action", "continued-fraction-outer", "[t] \"Kettenbruch\"; [n] children/*[1]; [t] \"durch\"; [n] children/*[2]"], ["Action", "continued-fraction-inner", "[t] \"Anfang Bruch\"; [n] children/*[1]; [t] \"durch\"; [n] children/*[2]"], ["Action", "continued-fraction-inner-sbrief", "[t] \"Bruch\"; [n] children/*[1]; [t] \"durch\"; [n] children/*[2]"], ["Action", "limboth-end", "[n] children/*[1]; [t] CSFunderscript; [n] children/*[2]; [t] CSFoverscript; [n] children/*[3]; [t] \"Ende Überschrift\""], ["Action", "limlower-end", "[n] children/*[1]; [t] CSFunderscript; [n] children/*[2]; [t] \"Ende Unterschrift\""], ["Action", "limupper-end", "[n] children/*[1]; [t] CSFoverscript; [n] children/*[2]; [t] \"Ende Überschrift\""], ["Action", "integral", "[n] children/*[1]; [t] \"Index\"; [n] children/*[2]; [t] \"Hoch\"; [n] children/*[3]; [t] \"Grundlinie\""], ["Action", "integral-brief", "[n] children/*[1]; [t] \"Index\"; [n] children/*[2]; [t] \"Hoch\"; [n] children/*[3]; [t] \"Base\""], ["Action", "square", "[n] children/*[1]; [t] \"Quadrat\""], ["Action", "cube", "[n] children/*[1]; [t] \"Kubik\""], ["Action", "prime", "[n] children/*[1]; [n] children/*[2]"], ["Action", "counted-prime", "[t] count(children/*) (grammar:numbers2alpha); [t] \"<PERSON>rich\""], ["Action", "counted-prime-multichar", "[t] string-length(text()) (grammar:numbers2alpha); [t] \"<PERSON>rich\""], ["Action", "overscore", "[t] \"modifiziert oben\"; [n] children/*[1]; [t] \"mit\"; [n] children/*[2]"], ["Action", "overscore-brief", "[t] \"mod oben\"; [n] children/*[1]; [t] \"mit\"; [n] children/*[2]"], ["Action", "double-overscore", "[t] \"modifiziert oben oben\"; [n] children/*[1]; [t] \"mit\"; [n] children/*[2]"], ["Action", "double-overscore-brief", "[t] \"mod oben oben\"; [n] children/*[1]; [t] \"mit\"; [n] children/*[2]"], ["Action", "underscore", "[t] \"modifiziert unten\"; [n] children/*[1]; [t] \"mit\"; [n] children/*[2]"], ["Action", "underscore-brief", "[t] \"mod unten\"; [n] children/*[1]; [t] \"mit\"; [n] children/*[2]"], ["Action", "double-underscore", "[t] \"modifiziert unten unten\"; [n] children/*[1]; [t] \"mit\"; [n] children/*[2]"], ["Action", "double-underscore-brief", "[t] \"mod unten unten\"; [n] children/*[1]; [t] \"mit\"; [n] children/*[2]"], ["Action", "overbar", "[n] children/*[1]; [t] \"Überstrich\""], ["Action", "underbar", "[n] children/*[1]; [t] \"Unterstrich\""], ["Action", "overtilde", "[n] children/*[1]; [t] \"Tilde oben\""], ["Action", "undertilde", "[n] children/*[1]; [t] \"Tilde unten\""], ["Action", "matrix", "[t] \"Anfang\"; [t] count(children/*); [t] \"mal\"; [t] count(children/*[1]/children/*); [t] \"Matrize\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"Zeile \"); [t] \"Ende Matrize\""], ["Action", "matrix-sbrief", "[t] count(children/*); [t] \"mal\"; [t] count(children/*[1]/children/*); [t] \"Matrize\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"Zeile \"); [t] \"Ende Matrize\""], ["Action", "matrix-row", "[m] children/* (ctxtFunc:CTFordinalCounter, context:\"Spalte\", pause:200)"], ["Action", "row-with-label", "[t] \"mit Bezeichner\"; [n] content/*[1]; [t] \"Ende Bezeichner\" (pause:200); [m] children/* (ctxtFunc:CTFordinalCounter, context:\"Spalte\")"], ["Action", "row-with-label-brief", "[t] \"Be<PERSON>ichner\"; [n] content/*[1]; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"Spalte\")"], ["Action", "row-with-text-label", "[t] \"Bezeichner\"; [t] CSFRemoveParens; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"Spalte\")"], ["Action", "empty-row", "[t] \"leer\""], ["Action", "empty-cell", "[t] \"leer\" (pause:300)"], ["Action", "determinant", "[t] \"Anfang\"; [t] count(children/*); [t] \"mal\"; [t] count(children/*[1]/children/*); [t] \"Determinante\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"Zeile \"); [t] \"Ende Determinante\""], ["Action", "determinant-sbrief", "[t] count(children/*); [t] \"mal\"; [t] count(children/*[1]/children/*); [t] \"Determinante\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"Zeile \"); [t] \"Ende Determinante\""], ["Action", "determinant-simple", "[t] \"Anfang\"; [t] count(children/*); [t] \"mal\"; [t] count(children/*[1]/children/*); [t] \"Determinante\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"Zeile\", grammar:simpleDet); [t] \"Ende Determinante\""], ["Action", "determinant-simple-sbrief", "[t] count(children/*); [t] \"mal\"; [t] count(children/*[1]/children/*); [t] \"Determinante\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"Zeile\", grammar:simpleDet); [t] \"Ende Determinante\""], ["Action", "layout", "[t] \"Anfang Anordnung\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"<PERSON><PERSON><PERSON> \"); [t] \"Ende Anordnung\""], ["Action", "layout-sbrief", "[t] \"Anordnung\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"<PERSON>eile \"); [t] \"Ende Anordnung\""], ["Action", "binomial", "[t] \"Anfang Binomialkoeffizient\"; [n] children/*[2]/children/*[1]; [t] \"aus\"; [n] children/*[1]/children/*[1]; [t] \"Ende Binomialkoeffizient\""], ["Action", "binomial-brief", "[t] \"Anfang Binomial\"; [n] children/*[2]/children/*[1]; [t] \"aus\"; [n] children/*[1]/children/*[1]; [t] \"Ende Binomial\""], ["Action", "binomial-sbrief", "[t] \"Binomial\"; [n] children/*[2]/children/*[1]; [t] \"aus\"; [n] children/*[1]/children/*[1]; [t] \"Ende Binomial\""], ["Action", "cases", "[t] \"Anfang Fallunterscheidung\"; [t] \"große\"; [n] content/*[1]; [m] children/* (ctxtFunc:CTFordinal<PERSON>ounter, context:\"<PERSON>eil<PERSON> \"); [t] \"Ende Fallunterscheidung\""], ["Action", "cases-brief", "[t] \"Anfang Fälle\"; [t] \"große\"; [n] content/*[1]; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"<PERSON><PERSON><PERSON> \"); [t] \"Ende Fälle\""], ["Action", "cases-sbrief", "[t] \"Fälle\"; [t] \"große\"; [n] content/*[1]; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"<PERSON>eile \"); [t] \"Ende Fälle\""], ["Action", "line-with-label", "[t] \"mit Bezeichner\"; [n] content/*[1]; [t] \"Ende Bezeichner\" (pause:200); [m] children/*"], ["Action", "line-with-label-brief", "[t] \"<PERSON><PERSON><PERSON><PERSON>\"; [n] content/*[1] (pause:200); [m] children/*"], ["Action", "line-with-text-label", "[t] \"Bezeichner\"; [t] CSFRemoveParens; [m] children/*"], ["Action", "empty-line", "[t] \"leer\""], ["Action", "empty-line-with-label", "[t] \"mit Bezeichner\"; [n] content/*[1]; [t] \"Ende Bezeichner\" (pause:200); [t] \"leer\""], ["Action", "empty-line-with-label-brief", "[t] \"<PERSON><PERSON><PERSON><PERSON>\"; [n] content/*[1] (pause:200); [t] \"leer\""], ["Action", "enclose", "[t] \"Anfang Umschließung\"; [t] @role (grammar:localEnclose); [n] children/*[1]; [t] \"Ende Umschließung\""], ["Action", "leftbar", "[t] \"senkrechter Strich\"; [n] children/*[1]"], ["Action", "rightbar", "[n] children/*[1]; [t] \"senkrechter Strich\""], ["Action", "crossout", "[t] \"durchgestrichen\"; [n] children/*[1]; [t] \"Ende duchgestrichen\""], ["Action", "cancel", "[t] \"durchgestrichen\"; [n] children/*[1]/children/*[1]; [t] \"mit\"; [n] children/*[2]; [t] \"Ende duchgestrichen\""], ["Action", "cancel-reverse", "[t] \"durchgestrichen\"; [n] children/*[2]/children/*[1]; [t] \"mit\"; [n] children/*[1]; [t] \"Ende duchgestrichen\""], ["Action", "multi-inference", "[t] \"<PERSON><PERSON><PERSON>regel\"; [m] content/*; [t] \"mit Folgerung\"; [n] children/*[1]; [t] \"aus\"; [t] count(children/*[2]/children/*); [t] \"<PERSON>r<PERSON><PERSON><PERSON>\""], ["Action", "inference", "[t] \"<PERSON><PERSON><PERSON>re<PERSON>\"; [m] content/*; [t] \"mit Folgerung\"; [n] children/*[1]; [t] \"aus\"; [t] count(children/*[2]/children/*); [t] \"Prämisse\""], ["Action", "premise", "[m] children/* (ctxtFunc:CTFordinalCounter, context:\"Prämis<PERSON> \")"], ["Action", "conclusion", "[n] children/*[1]"], ["Action", "label", "[t] \"<PERSON><PERSON>\"; [n] children/*[1]"], ["Action", "axiom", "[t] \"Axiom\"; [m] children/*[1]"], ["Action", "empty-axiom", "[t] \"leeres Axiom\""]]}, "de/rules/prefix_german.min": {"modality": "prefix", "locale": "de", "inherits": "base", "domain": "default", "rules": []}, "de/rules/prefix_german_actions.min": {"modality": "prefix", "locale": "de", "domain": "default", "kind": "actions", "rules": [["Action", "numerator", "[t] \"<PERSON><PERSON><PERSON>\" (pause:200)"], ["Action", "denominator", "[t] \"<PERSON><PERSON><PERSON>\" (pause:200)"], ["Action", "base", "[t] \"Ba<PERSON>\" (pause:200)"], ["Action", "exponent", "[t] \"Exponent\" (pause:200)"], ["Action", "subscript", "[t] \"Index\" (pause:200)"], ["Action", "overscript", "[t] \"Oberer Grenzwert\" (pause:200)"], ["Action", "underscript", "[t] \"Unterer Grenzwert\" (pause:200)"], ["Action", "radicand", "[t] \"Ra<PERSON>kan<PERSON>\" (pause:200)"], ["Action", "index", "[t] \"Wurzelexponent\" (pause:200)"], ["Action", "leftsub", "[t] \"linker unterer Index\" (pause:200)"], ["Action", "leftsub-counted", "[t] CSFordinalPosition; [t] \"linker unterer Index\" (pause:200)"], ["Action", "leftsuper", "[t] \"linker oberer Index\" (pause:200)"], ["Action", "leftsuper-counted", "[t] CSFordinalPosition; [t] \"linker oberer Index\" (pause:200)"], ["Action", "rightsub", "[t] \"rechter unterer Index\" (pause:200)"], ["Action", "rightsub-counted", "[t] CSFordinalPosition; [t] \"rechter unterer Index\" (pause:200)"], ["Action", "<PERSON><PERSON>r", "[t] \"rechter oberer Index\" (pause:200)"], ["Action", "rightsuper-counted", "[t] CSFordinalPosition; [t] \"rechter oberer Index\" (pause:200)"], ["Action", "choice", "[t] \"<PERSON>run<PERSON>amtheit\" (pause:200)"], ["Action", "select", "[t] \"Stichprobengröße\" (pause:200)"], ["Action", "row", "[t] CSFordinalPosition; [t] \"<PERSON><PERSON><PERSON>\" (pause:200)"], ["Action", "cell", "[n] ../..; [t] CSFordinalPosition; [t] \"Spalte\" (pause:200)"], ["Action", "cell-simple", "[t] CSFordinalPosition; [t] \"Spalte\" (pause:200)"]]}, "de/rules/summary_german.min": {"modality": "summary", "locale": "de", "inherits": "base", "rules": []}, "de/rules/summary_german_actions.min": {"modality": "summary", "locale": "de", "kind": "actions", "rules": [["Action", "abstr-identifier-long", "[t] \"langer Bezeichner\""], ["Action", "abstr-identifier", "[t] \"Bezeichner\""], ["Action", "abstr-number-long", "[t] \"lange Zahl\""], ["Action", "abstr-number", "[t] \"<PERSON><PERSON>\""], ["Action", "abstr-mixed-number-long", "[t] \"langer gemischter Bruch\""], ["Action", "abstr-mixed-number", "[t] \"gemischter Bruch\""], ["Action", "abstr-text", "[t] \"Text\""], ["Action", "abstr-function", "[t] \"Funktionsausdruck\""], ["Action", "abstr-function-brief", "[t] \"Funk<PERSON>\""], ["Action", "abstr-lim", "[t] \"Grenzwertfunktion\""], ["Action", "abstr-lim-brief", "[t] \"Grenzwert\""], ["Action", "abstr-fraction", "[t] \"<PERSON>ruch\""], ["Action", "abstr-fraction-brief", "[t] \"<PERSON>ruch\""], ["Action", "abstr-continued-fraction", "[t] \"Kettenbruch\""], ["Action", "abstr-continued-fraction-brief", "[t] \"Kettenbruch\""], ["Action", "abstr-sqrt", "[t] \"Quadrat<PERSON>rzel\""], ["Action", "abstr-sqrt-nested", "[t] \"verschachtelte Quadratwurzel\""], ["Action", "abstr-root-end", "[t] \"Wurzel mit Exponent\"; [n] children/*[1] (engine:modality=speech); [t] \"Ende Exponent\""], ["Action", "abstr-root", "[t] \"<PERSON><PERSON>el mit Exponent\"; [n] children/*[1] (engine:modality=speech)"], ["Action", "abstr-root-brief", "[t] \"<PERSON><PERSON><PERSON>\""], ["Action", "abstr-root-nested-end", "[t] \"verschachtelte Wurzel mit Exponent\"; [n] children/*[1] (engine:modality=speech); [t] \"Ende Exponent\""], ["Action", "abstr-root-nested", "[t] \"verschachtelte Wurzel mit Exponent\"; [n] children/*[1] (engine:modality=speech)"], ["Action", "abstr-root-nested-brief", "[t] \"verschach<PERSON><PERSON> Wurzel\""], ["Action", "abstr-superscript", "[t] \"Potenz\""], ["Action", "abstr-subscript", "[t] \"Index\""], ["Action", "abstr-subsup", "[t] \"Potenz mit Index\""], ["Action", "abstr-infixop", "[t] @role (grammar:localRole); [t] \"mit\"; [t] count(./children/*); [t] \"Elementen\""], ["Action", "abstr-infixop-var", "[t] @role (grammar:localRole); [t] \"mit veränderlicher Anzahl an Elementen\""], ["Action", "abstr-infixop-brief", "[t] @role (grammar:localRole)"], ["Action", "abstr-addition", "[t] \"Summe mit\"; [t] count(./children/*); [t] \"Summanden\""], ["Action", "abstr-addition-brief", "[t] \"Summe\""], ["Action", "abstr-addition-var", "[t] \"Summe mit veränderlicher Anzahl an Summanden\""], ["Action", "abstr-multiplication", "[t] \"Produkt mit\"; [t] count(./children/*); [t] \"Faktoren\""], ["Action", "abstr-multiplication-brief", "[t] \"Produkt\""], ["Action", "abstr-multiplication-var", "[t] \"Produkt mit veränderlicher Anzahl an Faktoren\""], ["Action", "abstr-vector", "[t] count(./children/*); [t] \"dimensionaler Vektor\""], ["Action", "abstr-vector-brief", "[t] \"Vektor\""], ["Action", "abstr-vector-var", "[t] \"n dimensionaler Vektor\""], ["Action", "abstr-binomial", "[t] \"Binomialkoeffizient\""], ["Action", "abstr-determinant", "[t] count(./children/*); [t] \"dimensionale Determinante\""], ["Action", "abstr-determinant-brief", "[t] \"Determinante\""], ["Action", "abstr-determinant-var", "[t] \"n dimensionale Determinante\""], ["Action", "abstr-squarematrix", "[t] count(./children/*); [t] \"dimensionale quadratische Matrize\""], ["Action", "abstr-squarematrix-brief", "[t] \"quadratische Matrize\""], ["Action", "abstr-rowvector", "[t] count(./children/row/children/*); [t] \"dimensionaler Zeilenvektor\""], ["Action", "abstr-rowvector-brief", "[t] \"Zeilenvektor\""], ["Action", "abstr-rowvector-var", "[t] \"n dimensionaler Zeilenvektor\""], ["Action", "abstr-matrix", "[t] count(children/*); [t] \"mal\"; [t] count(children/*[1]/children/*); [t] \"Matrize\""], ["Action", "abstr-matrix-brief", "[t] \"Matrize\""], ["Action", "abstr-matrix-var", "[t] \"n mal m dimensionale Matrize\""], ["Action", "abstr-cases", "[t] \"Fallunterscheidung\"; [t] \"mit\"; [t] count(children/*); [t] \"Fällen\""], ["Action", "abstr-cases-brief", "[t] \"Fallunterscheidung\""], ["Action", "abstr-cases-var", "[t] \"Fallunterscheidung mit veränderlicher Anzahl an Fällen\""], ["Action", "abstr-punctuated", "[t] \"mit\"; [n] content/*[1]; [t] \"getrennte Liste der Länge\"; [t] count(children/*) - count(content/*)"], ["Action", "abstr-punctuated-brief", "[t] \"mit\"; [n] content/*[1]; [t] \"getrennte Liste\""], ["Action", "abstr-punctuated-var", "[t] \"mit\"; [n] content/*[1]; [t] \"getrennte Liste\"; [t] \"veränderlicher Länge\""], ["Action", "abstr-bigop", "[n] content/*[1]"], ["Action", "abstr-integral", "[t] \"Integral\""], ["Action", "abstr-relation", "[t] @role (grammar:localRole)"], ["Action", "abstr-relation-seq", "[t] @role (join:\"\", grammar:localRole); [t] \"ssequenz\"; [t] \"mit\"; [t] count(./children/*); [t] \"Elementen\""], ["Action", "abstr-relation-seq-brief", "[t] @role (join:\"\", grammar:localRole); [t] \"ssequenz\""], ["Action", "abstr-relation-var", "[t] @role (join:\"\", grammar:localRole); [t] \"ssequenz\"; [t] \"mit veränderlicher Anzahl an Elementen\""], ["Action", "abstr-multirel", "[t] \"Relationsequenz\"; [t] \"mit\"; [t] count(./children/*); [t] \"Elementen\""], ["Action", "abstr-multirel-brief", "[t] \"Relationsequenz\""], ["Action", "abstr-multirel-var", "[t] \"Relationsequenz mit veränderlicher Anzahl an Elementen\""], ["Action", "abstr-table", "[t] \"Tabelle mit\"; [t] count(children/*); [t] \"Zeilen und\"; [t] count(children/*[1]/children/*); [t] \"Spalten\""], ["Action", "abstr-line", "[t] \"in\"; [t] @role (grammar:localRole)"], ["Action", "abstr-row", "[t] \"in\"; [t] @role (grammar:localRole); [t] count(preceding-sibling::..); [t] \"mit\"; [t] count(children/*); [t] \"Spalten\""], ["Action", "abstr-cell", "[t] \"in\"; [t] @role (grammar:localRole)"]]}}