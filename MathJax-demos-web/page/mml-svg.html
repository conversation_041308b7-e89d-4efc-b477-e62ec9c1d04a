<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width">
  <link rel="stylesheet" href="../styles/globals.css">
  <title>MathJax v4: MathML input and HTML output</title>
  <script class="show" defer src="https://cdn.jsdelivr.net/npm/mathjax@4/mml-svg.js"></script>
</head>
<body>

<div id="frame">

<h1>MathJax v4: MathML input, HTML output</h1>

<div class="show display">
  
  <p>
    When <math xmlns="http://www.w3.org/1998/Math/MathML">
    <mi>a</mi>
    <mo>&#x2260;</mo>
    <mn>0</mn>
  </math>, there are two solutions to <math xmlns="http://www.w3.org/1998/Math/MathML">
    <mi>a</mi>
    <msup>
      <mi>x</mi>
      <mn>2</mn>
    </msup>
    <mo>+</mo>
    <mi>b</mi>
    <mi>x</mi>
    <mo>+</mo>
    <mi>c</mi>
    <mo>=</mo>
    <mn>0</mn>
  </math> and they are
    <math xmlns="http://www.w3.org/1998/Math/MathML" display="block">
    <mi>x</mi>
    <mo>=</mo>
    <mrow data-mjx-texclass="ORD">
      <mfrac>
        <mrow>
          <mo>&#x2212;</mo>
          <mi>b</mi>
          <mo>&#xB1;</mo>
          <msqrt>
            <msup>
              <mi>b</mi>
              <mn>2</mn>
            </msup>
            <mo>&#x2212;</mo>
            <mn>4</mn>
            <mi>a</mi>
            <mi>c</mi>
          </msqrt>
        </mrow>
        <mrow>
          <mn>2</mn>
          <mi>a</mi>
        </mrow>
      </mfrac>
    </mrow>
    <mo>.</mo>
  </math>
  </p>

  <h2>The Lorenz Equations</h2>

  <p>
    <math xmlns="http://www.w3.org/1998/Math/MathML" display="block">
    <mtable displaystyle="true" columnalign="right left" columnspacing="0em" rowspacing="3pt" data-break-align="bottom top">
      <mtr>
        <mtd>
          <mrow data-mjx-texclass="ORD">
            <mover>
              <mi>x</mi>
              <mo>&#x2D9;</mo>
            </mover>
          </mrow>
        </mtd>
        <mtd>
          <mstyle indentshift="2em">
            <mi></mi>
            <mo>=</mo>
            <mi>&#x3C3;</mi>
            <mo stretchy="false">(</mo>
            <mi>y</mi>
            <mo>&#x2212;</mo>
            <mi>x</mi>
            <mo stretchy="false">)</mo>
          </mstyle>
        </mtd>
      </mtr>
      <mtr>
        <mtd>
          <mrow data-mjx-texclass="ORD">
            <mover>
              <mi>y</mi>
              <mo>&#x2D9;</mo>
            </mover>
          </mrow>
        </mtd>
        <mtd>
          <mstyle indentshift="2em">
            <mi></mi>
            <mo>=</mo>
            <mi>&#x3C1;</mi>
            <mi>x</mi>
            <mo>&#x2212;</mo>
            <mi>y</mi>
            <mo>&#x2212;</mo>
            <mi>x</mi>
            <mi>z</mi>
          </mstyle>
        </mtd>
      </mtr>
      <mtr>
        <mtd>
          <mrow data-mjx-texclass="ORD">
            <mover>
              <mi>z</mi>
              <mo>&#x2D9;</mo>
            </mover>
          </mrow>
        </mtd>
        <mtd>
          <mstyle indentshift="2em">
            <mi></mi>
            <mo>=</mo>
            <mo>&#x2212;</mo>
            <mi>&#x3B2;</mi>
            <mi>z</mi>
            <mo>+</mo>
            <mi>x</mi>
            <mi>y</mi>
          </mstyle>
        </mtd>
      </mtr>
    </mtable>
  </math>
  </p>

  <h2>The Cauchy-Schwarz Inequality</h2>

  <p><math xmlns="http://www.w3.org/1998/Math/MathML" display="block">
    <msup>
      <mrow data-mjx-texclass="INNER">
        <mo data-mjx-texclass="OPEN">(</mo>
        <munderover>
          <mo>&#x2211;</mo>
          <mrow data-mjx-texclass="ORD">
            <mi>k</mi>
            <mo>=</mo>
            <mn>1</mn>
          </mrow>
          <mi>n</mi>
        </munderover>
        <msub>
          <mi>a</mi>
          <mi>k</mi>
        </msub>
        <msub>
          <mi>b</mi>
          <mi>k</mi>
        </msub>
        <mo data-mjx-texclass="CLOSE">)</mo>
      </mrow>
      <mrow data-mjx-texclass="ORD">
        <mstyle scriptlevel="0">
          <mspace width="-0.167em"></mspace>
        </mstyle>
        <mstyle scriptlevel="0">
          <mspace width="-0.167em"></mspace>
        </mstyle>
        <mn>2</mn>
      </mrow>
    </msup>
    <mo>&#x2264;</mo>
    <mrow data-mjx-texclass="INNER">
      <mo data-mjx-texclass="OPEN">(</mo>
      <munderover>
        <mo>&#x2211;</mo>
        <mrow data-mjx-texclass="ORD">
          <mi>k</mi>
          <mo>=</mo>
          <mn>1</mn>
        </mrow>
        <mi>n</mi>
      </munderover>
      <msubsup>
        <mi>a</mi>
        <mi>k</mi>
        <mn>2</mn>
      </msubsup>
      <mo data-mjx-texclass="CLOSE">)</mo>
    </mrow>
    <mrow data-mjx-texclass="INNER">
      <mo data-mjx-texclass="OPEN">(</mo>
      <munderover>
        <mo>&#x2211;</mo>
        <mrow data-mjx-texclass="ORD">
          <mi>k</mi>
          <mo>=</mo>
          <mn>1</mn>
        </mrow>
        <mi>n</mi>
      </munderover>
      <msubsup>
        <mi>b</mi>
        <mi>k</mi>
        <mn>2</mn>
      </msubsup>
      <mo data-mjx-texclass="CLOSE">)</mo>
    </mrow>
  </math></p>

   <h2>A Cross Product Formula</h2>

   <p><math xmlns="http://www.w3.org/1998/Math/MathML" display="block">
    <msub>
      <mrow data-mjx-texclass="ORD">
        <mi mathvariant="bold">V</mi>
      </mrow>
      <mn>1</mn>
    </msub>
    <mo>&#xD7;</mo>
    <msub>
      <mrow data-mjx-texclass="ORD">
        <mi mathvariant="bold">V</mi>
      </mrow>
      <mn>2</mn>
    </msub>
    <mo>=</mo>
    <mrow data-mjx-texclass="INNER">
      <mo data-mjx-texclass="OPEN">|</mo>
      <mtable columnspacing="1em" rowspacing="4pt">
        <mtr>
          <mtd>
            <mrow data-mjx-texclass="ORD">
              <mi mathvariant="bold">i</mi>
            </mrow>
          </mtd>
          <mtd>
            <mrow data-mjx-texclass="ORD">
              <mi mathvariant="bold">j</mi>
            </mrow>
          </mtd>
          <mtd>
            <mrow data-mjx-texclass="ORD">
              <mi mathvariant="bold">k</mi>
            </mrow>
          </mtd>
        </mtr>
        <mtr>
          <mtd>
            <mfrac>
              <mrow>
                <mi>&#x2202;</mi>
                <mi>X</mi>
              </mrow>
              <mrow>
                <mi>&#x2202;</mi>
                <mi>u</mi>
              </mrow>
            </mfrac>
          </mtd>
          <mtd>
            <mfrac>
              <mrow>
                <mi>&#x2202;</mi>
                <mi>Y</mi>
              </mrow>
              <mrow>
                <mi>&#x2202;</mi>
                <mi>u</mi>
              </mrow>
            </mfrac>
          </mtd>
          <mtd>
            <mn>0</mn>
          </mtd>
        </mtr>
        <mtr>
          <mtd>
            <mfrac>
              <mrow>
                <mi>&#x2202;</mi>
                <mi>X</mi>
              </mrow>
              <mrow>
                <mi>&#x2202;</mi>
                <mi>v</mi>
              </mrow>
            </mfrac>
          </mtd>
          <mtd>
            <mfrac>
              <mrow>
                <mi>&#x2202;</mi>
                <mi>Y</mi>
              </mrow>
              <mrow>
                <mi>&#x2202;</mi>
                <mi>v</mi>
              </mrow>
            </mfrac>
          </mtd>
          <mtd>
            <mn>0</mn>
          </mtd>
        </mtr>
      </mtable>
      <mo data-mjx-texclass="CLOSE">|</mo>
    </mrow>
  </math></p>

  <h2>The probability of getting <math xmlns="http://www.w3.org/1998/Math/MathML">
    <mi>k</mi>
  </math> heads when flipping <math xmlns="http://www.w3.org/1998/Math/MathML">
    <mi>n</mi>
  </math> coins is:</h2>

  <p><math xmlns="http://www.w3.org/1998/Math/MathML" display="block">
    <mi>P</mi>
    <mo stretchy="false">(</mo>
    <mi>E</mi>
    <mo stretchy="false">)</mo>
    <mo>=</mo>
    <mrow data-mjx-texclass="ORD">
      <mrow data-mjx-texclass="ORD">
        <mrow data-mjx-texclass="OPEN">
          <mo minsize="2.047em" maxsize="2.047em">(</mo>
        </mrow>
        <mfrac linethickness="0">
          <mi>n</mi>
          <mi>k</mi>
        </mfrac>
        <mrow data-mjx-texclass="CLOSE">
          <mo minsize="2.047em" maxsize="2.047em">)</mo>
        </mrow>
      </mrow>
    </mrow>
    <msup>
      <mi>p</mi>
      <mi>k</mi>
    </msup>
    <mo stretchy="false">(</mo>
    <mn>1</mn>
    <mo>&#x2212;</mo>
    <mi>p</mi>
    <msup>
      <mo stretchy="false">)</mo>
      <mrow data-mjx-texclass="ORD">
        <mi>n</mi>
        <mo>&#x2212;</mo>
        <mi>k</mi>
      </mrow>
    </msup>
  </math></p>

  <h2>An Identity of Ramanujan</h2>

  <p><math xmlns="http://www.w3.org/1998/Math/MathML" display="block">
    <mfrac>
      <mn>1</mn>
      <mrow>
        <mrow data-mjx-texclass="INNER">
          <mo data-mjx-texclass="OPEN">(</mo>
          <msqrt>
            <mi>&#x3D5;</mi>
            <msqrt>
              <mn>5</mn>
            </msqrt>
          </msqrt>
          <mo>&#x2212;</mo>
          <mi>&#x3D5;</mi>
          <mo data-mjx-texclass="CLOSE">)</mo>
        </mrow>
        <msup>
          <mi>e</mi>
          <mrow data-mjx-texclass="ORD">
            <mfrac>
              <mn>2</mn>
              <mn>5</mn>
            </mfrac>
            <mi>&#x3C0;</mi>
          </mrow>
        </msup>
      </mrow>
    </mfrac>
    <mo>=</mo>
    <mn>1</mn>
    <mo>+</mo>
    <mfrac>
      <msup>
        <mi>e</mi>
        <mrow data-mjx-texclass="ORD">
          <mo>&#x2212;</mo>
          <mn>2</mn>
          <mi>&#x3C0;</mi>
        </mrow>
      </msup>
      <mrow>
        <mn>1</mn>
        <mo>+</mo>
        <mstyle displaystyle="true">
          <mfrac>
            <msup>
              <mi>e</mi>
              <mrow data-mjx-texclass="ORD">
                <mo>&#x2212;</mo>
                <mn>4</mn>
                <mi>&#x3C0;</mi>
              </mrow>
            </msup>
            <mrow>
              <mn>1</mn>
              <mo>+</mo>
              <mstyle displaystyle="true">
                <mfrac>
                  <msup>
                    <mi>e</mi>
                    <mrow data-mjx-texclass="ORD">
                      <mo>&#x2212;</mo>
                      <mn>6</mn>
                      <mi>&#x3C0;</mi>
                    </mrow>
                  </msup>
                  <mrow>
                    <mn>1</mn>
                    <mo>+</mo>
                    <mstyle displaystyle="true">
                      <mfrac>
                        <msup>
                          <mi>e</mi>
                          <mrow data-mjx-texclass="ORD">
                            <mo>&#x2212;</mo>
                            <mn>8</mn>
                            <mi>&#x3C0;</mi>
                          </mrow>
                        </msup>
                        <mrow>
                          <mn>1</mn>
                          <mo>+</mo>
                          <mo>&#x2026;</mo>
                        </mrow>
                      </mfrac>
                    </mstyle>
                  </mrow>
                </mfrac>
              </mstyle>
            </mrow>
          </mfrac>
        </mstyle>
      </mrow>
    </mfrac>
  </math></p>

  <h2>A Rogers-Ramanujan Identity</h2>

  <p><math xmlns="http://www.w3.org/1998/Math/MathML" display="block">
    <mn>1</mn>
    <mo>+</mo>
    <mfrac>
      <msup>
        <mi>q</mi>
        <mn>2</mn>
      </msup>
      <mrow>
        <mo stretchy="false">(</mo>
        <mn>1</mn>
        <mo>&#x2212;</mo>
        <mi>q</mi>
        <mo stretchy="false">)</mo>
      </mrow>
    </mfrac>
    <mo>+</mo>
    <mfrac>
      <msup>
        <mi>q</mi>
        <mn>6</mn>
      </msup>
      <mrow>
        <mo stretchy="false">(</mo>
        <mn>1</mn>
        <mo>&#x2212;</mo>
        <mi>q</mi>
        <mo stretchy="false">)</mo>
        <mo stretchy="false">(</mo>
        <mn>1</mn>
        <mo>&#x2212;</mo>
        <msup>
          <mi>q</mi>
          <mn>2</mn>
        </msup>
        <mo stretchy="false">)</mo>
      </mrow>
    </mfrac>
    <mo>+</mo>
    <mo>&#x22EF;</mo>
    <mo>=</mo>
    <munderover>
      <mo>&#x220F;</mo>
      <mrow data-mjx-texclass="ORD">
        <mi>j</mi>
        <mo>=</mo>
        <mn>0</mn>
      </mrow>
      <mrow data-mjx-texclass="ORD">
        <mi mathvariant="normal">&#x221E;</mi>
      </mrow>
    </munderover>
    <mfrac>
      <mn>1</mn>
      <mrow>
        <mo stretchy="false">(</mo>
        <mn>1</mn>
        <mo>&#x2212;</mo>
        <msup>
          <mi>q</mi>
          <mrow data-mjx-texclass="ORD">
            <mn>5</mn>
            <mi>j</mi>
            <mo>+</mo>
            <mn>2</mn>
          </mrow>
        </msup>
        <mo stretchy="false">)</mo>
        <mo stretchy="false">(</mo>
        <mn>1</mn>
        <mo>&#x2212;</mo>
        <msup>
          <mi>q</mi>
          <mrow data-mjx-texclass="ORD">
            <mn>5</mn>
            <mi>j</mi>
            <mo>+</mo>
            <mn>3</mn>
          </mrow>
        </msup>
        <mo stretchy="false">)</mo>
      </mrow>
    </mfrac>
    <mo>,</mo>
    <mspace width="1em"></mspace>
    <mspace width="1em"></mspace>
    <mrow>
      <mtext>for&#xA0;</mtext>
      <mrow data-mjx-texclass="ORD">
        <mrow>
          <mo data-mjx-texclass="ORD" stretchy="false">|</mo>
          <mi>q</mi>
          <mo data-mjx-texclass="ORD" stretchy="false">|</mo>
          <mo>&lt;</mo>
          <mn>1</mn>
        </mrow>
      </mrow>
    </mrow>
    <mo>.</mo>
  </math></p>

  <h2>Maxwell's Equations</h2>

  <p>
    <math xmlns="http://www.w3.org/1998/Math/MathML" display="block">
    <mtable displaystyle="true" columnalign="right left" columnspacing="0em" rowspacing="0.6em 0.6em 0.6em 0.30000000000000004em" data-break-align="bottom top">
      <mtr>
        <mtd>
          <mi mathvariant="normal">&#x2207;</mi>
          <mo>&#xD7;</mo>
          <mrow data-mjx-texclass="ORD">
            <mover>
              <mrow data-mjx-texclass="ORD">
                <mi mathvariant="bold">B</mi>
              </mrow>
              <mo stretchy="false">&#x2192;</mo>
            </mover>
          </mrow>
          <mspace width="0.167em"></mspace>
          <mo>&#x2212;</mo>
          <mfrac>
            <mn>1</mn>
            <mi>c</mi>
          </mfrac>
          <mspace width="0.167em"></mspace>
          <mfrac>
            <mrow>
              <mi>&#x2202;</mi>
              <mrow data-mjx-texclass="ORD">
                <mover>
                  <mrow data-mjx-texclass="ORD">
                    <mi mathvariant="bold">E</mi>
                  </mrow>
                  <mo stretchy="false">&#x2192;</mo>
                </mover>
              </mrow>
            </mrow>
            <mrow>
              <mi>&#x2202;</mi>
              <mi>t</mi>
            </mrow>
          </mfrac>
        </mtd>
        <mtd>
          <mstyle indentshift="2em">
            <mi></mi>
            <mo>=</mo>
            <mfrac>
              <mrow>
                <mn>4</mn>
                <mi>&#x3C0;</mi>
              </mrow>
              <mi>c</mi>
            </mfrac>
            <mrow data-mjx-texclass="ORD">
              <mover>
                <mrow data-mjx-texclass="ORD">
                  <mi mathvariant="bold">j</mi>
                </mrow>
                <mo stretchy="false">&#x2192;</mo>
              </mover>
            </mrow>
          </mstyle>
        </mtd>
      </mtr>
      <mtr>
        <mtd>
          <mi mathvariant="normal">&#x2207;</mi>
          <mo>&#x22C5;</mo>
          <mrow data-mjx-texclass="ORD">
            <mover>
              <mrow data-mjx-texclass="ORD">
                <mi mathvariant="bold">E</mi>
              </mrow>
              <mo stretchy="false">&#x2192;</mo>
            </mover>
          </mrow>
        </mtd>
        <mtd>
          <mstyle indentshift="2em">
            <mi></mi>
            <mo>=</mo>
            <mn>4</mn>
            <mi>&#x3C0;</mi>
            <mi>&#x3C1;</mi>
          </mstyle>
        </mtd>
      </mtr>
      <mtr>
        <mtd>
          <mi mathvariant="normal">&#x2207;</mi>
          <mo>&#xD7;</mo>
          <mrow data-mjx-texclass="ORD">
            <mover>
              <mrow data-mjx-texclass="ORD">
                <mi mathvariant="bold">E</mi>
              </mrow>
              <mo stretchy="false">&#x2192;</mo>
            </mover>
          </mrow>
          <mspace width="0.167em"></mspace>
          <mo>+</mo>
          <mfrac>
            <mn>1</mn>
            <mi>c</mi>
          </mfrac>
          <mspace width="0.167em"></mspace>
          <mfrac>
            <mrow>
              <mi>&#x2202;</mi>
              <mrow data-mjx-texclass="ORD">
                <mover>
                  <mrow data-mjx-texclass="ORD">
                    <mi mathvariant="bold">B</mi>
                  </mrow>
                  <mo stretchy="false">&#x2192;</mo>
                </mover>
              </mrow>
            </mrow>
            <mrow>
              <mi>&#x2202;</mi>
              <mi>t</mi>
            </mrow>
          </mfrac>
        </mtd>
        <mtd>
          <mstyle indentshift="2em">
            <mi></mi>
            <mo>=</mo>
            <mrow data-mjx-texclass="ORD">
              <mover>
                <mrow data-mjx-texclass="ORD">
                  <mn mathvariant="bold">0</mn>
                </mrow>
                <mo stretchy="false">&#x2192;</mo>
              </mover>
            </mrow>
          </mstyle>
        </mtd>
      </mtr>
      <mtr>
        <mtd>
          <mi mathvariant="normal">&#x2207;</mi>
          <mo>&#x22C5;</mo>
          <mrow data-mjx-texclass="ORD">
            <mover>
              <mrow data-mjx-texclass="ORD">
                <mi mathvariant="bold">B</mi>
              </mrow>
              <mo stretchy="false">&#x2192;</mo>
            </mover>
          </mrow>
        </mtd>
        <mtd>
          <mstyle indentshift="2em">
            <mi></mi>
            <mo>=</mo>
            <mn>0</mn>
          </mstyle>
        </mtd>
      </mtr>
    </mtable>
  </math>
  </p>

  <h2>In-line Mathematics</h2>

  <p>Finally, while display equations look good for a page of samples, the
  ability to mix math and text in a paragraph is also important.  This
  expression <math xmlns="http://www.w3.org/1998/Math/MathML">
    <msqrt>
      <mn>3</mn>
      <mi>x</mi>
      <mo>&#x2212;</mo>
      <mn>1</mn>
    </msqrt>
    <mo>+</mo>
    <mo stretchy="false">(</mo>
    <mn>1</mn>
    <mo>+</mo>
    <mi>x</mi>
    <msup>
      <mo stretchy="false">)</mo>
      <mn>2</mn>
    </msup>
  </math> is an example of an inline equation.  As
  you see, MathJax equations can be used this way as well, without unduly
  disturbing the spacing between lines.</p>
 
</div>
</div>

<script src="../scripts/source.js"></script>

</body>
</html>
