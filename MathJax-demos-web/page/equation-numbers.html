<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width">
  <link rel="stylesheet" href="../styles/globals.css">
  <title>MathJax v4: Automatic Equation Numbering</title>
  <script class="show">
  MathJax = {
    tex: {
      tags: 'ams'  // should be 'ams', 'none', or 'all'
    }
  };
  </script>
  <script class="show" defer src="https://cdn.jsdelivr.net/npm/mathjax@4/tex-chtml.js"></script>
</head>
<body>

<div id="frame">

<h1>Equations with automatic AMS numbering</h1>

<div class="inset">

<p>This example show automatic equation numbering using the AMS
numbering style (where certain environments get numbers, sometimes one
per environment, other times, one for every line, and starred
environments don't get numbers.  Use the "Show Source" button at the
bottom to see the TeX that produced it.</p>

</div>


<div class="show display">
 
  <h2>Equation:</h2>

  \begin{equation}
  E = mc^2
  \end{equation}

  <h2>Equation*:</h2>

  \begin{equation*}
  E = mc^2
  \end{equation*}

  <hr>

  <h2>Brackets:</h2>

  \[E = mc^2\]

  <h2>Brackets tagged:</h2>

  \[E = mc^2\tag{x}\]

  <hr>

  <h2>Split:</h2>

  \begin{equation}
  \begin{split} 
  a& =b+c-d\\ 
  & \quad +e-f\\ 
  & =g+h\\ 
  & =i 
  \end{split} 
  \end{equation} 

  <hr>

  <h2>Multline:</h2>

  \begin{multline}
    a+b+c+d+e+f+g\\
    M+N+O+P+Q\\
    R+S+T\\
    u+v+w+x+y+z
  \end{multline}

  <h2>Multline*:</h2>

  \begin{multline*}
    a+b+c+d+e+f+g\\
    M+N+O+P+Q\\
    R+S+T\\
    u+v+w+x+y+z
  \end{multline*}

  <hr>

  <h2>Gather:</h2>

  \begin{gather} 
  a_1=b_1+c_1\\ 
  a_2=b_2+c_2-d_2+e_2 
  \end{gather} 

  <h2>Gather*:</h2>

  \begin{gather*} 
  a_1=b_1+c_1\\ 
  a_2=b_2+c_2-d_2+e_2 
  \end{gather*} 

  <hr>

  <h2>Align:</h2>

  \begin{align} 
  a_1& =b_1+c_1\\ 
  a_2& =b_2+c_2-d_2+e_2 
  \end{align}

  <h2>Align*:</h2>

  \begin{align*} 
  a_1& =b_1+c_1\\ 
  a_2& =b_2+c_2-d_2+e_2 
  \end{align*}

  <h2>Align:</h2>

  \begin{align} 
  a_{11}& =b_{11}& a_{12}& =b_{12}\\ 
  a_{21}& =b_{21}& a_{22}& =b_{22}+c_{22} 
  \end{align}

  <h2>Align with <code>\notag</code> and <code>\tag</code>:</h2>

  \begin{align} 
  a_{11}& =b_{11}& a_{12}& =b_{12}\notag\\ 
  a_{21}& =b_{21}& a_{22}& =b_{22}+c_{22} \tag{y}
  \end{align}

  <h2>Align* with <code>\tag</code>:</h2>

  \begin{align*} 
  a_1& =b_1+c_1\tag{z}\\ 
  a_2& =b_2+c_2-d_2+e_2 
  \end{align*}
 
</div>
</div>

<script src="../scripts/source.js"></script>

</body>
</html>
