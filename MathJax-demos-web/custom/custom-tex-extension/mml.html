<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width">
  <link rel="stylesheet" href="../../styles/globals.css">
  <title>MathJax v4 custom TeX extension</title>
  <script class="show">
  MathJax = {
    loader: {
      load: ['[custom]/mml.min.js'],
      paths: {custom: '.'}
    },
    tex: {
      packages: {'[+]': ['mml']}
    }
  };
  </script>
  <script class="show" defer src="https://cdn.jsdelivr.net/npm/mathjax@4/tex-chtml.js"></script>
</head>
<body>

<div id="frame">

<h1>MathJax v4 Custom TeX Extension</h1>

<div style="inset">

<p>This file uses a custom TeX extension to allow a TeX expression to
specify MathML nodes directly via custom macros like <code>\mi</code>
and <code>\mo</code>. The expressions below use these macros to insert
MathML nodes into the parsed expression.  Use the "Show Source" button
at the bottom to see more details.</p>

</div>

<div class="show display">
 
  $$abc + \mi{abc} \mo{++} x ++ y$$

  $$\ms{Hello world!}$$

  $$\mn{\U{2460}\U{2461}\U{2462}}$$

  $$
    \mi[mathcolor=red]{x}
    \mi[mathvariant="bold"]{A}
    \ms[lquote="\U{AB}", rquote="\U{BB}"]{x}
    \mi[data-my-attribute="anything"]{x}
  $$

  $$\mi[bad="bad"]{x}$$
 
</div>
</div>

<div class="explain inset">

<p>This example shows how to create a custom TeX extension that defines
new TeX commands implemented by javascript functions.</p>

<p>The commands implemented by this example provide the ability to
generate MathML token elements from within TeX by hand. This allows
more control over the content and attributes of the elements
produced. The macros are <code>\mi</code>, <code>\mo</code>,
<code>\mn</code>, <code>\ms</code>, and <code>\mtext</code>, and they
each take an argument that is the text to be used as the content of
the corresponding MathML element. The text is not further processed by
TeX, but the extension does convert sequences of the form
<code>\U{NNNN}</code> (where the N’s are hexadecimal digits) into the
corresponding unicode character; e.g., <code>\mi{\U{2460}}</code> would
produce U+2460, a circled digit 1.</p>

<p>The main code for the extension is</p>

<div class="indent">
<details>
<summary><code>mml.js</code></summary>
<script type="text/x-load-code" src="mml.js"></script>
</details>
</div>

<p>which contains comments describing it in detail. In order to use
the extension in your web pages, you must turn this into a MathJax
component file, which you do by first defining the component using the
file</p>

<div class="indent">
<details>
<summary><code>config.json</code></summary>
<script type="text/x-load-code" src="config.json"></script>
</details>
</div>

<p>which gives the name of the component and a list of the other
components that this extension will draw on (so that only the needed
files will be included in this component), along with some other data
about directories. The <code>dest</code> property being set to
<code>'.'</code> means that the component will be placed in the
directory with the source file, but with <code>.min.js</code> as the
extension rather than <code>.js</code>.</p>

<p>To make the final component, use the commands</p>

<script type="text/x-colorize-code" class="html">
npm install
npm run make-custom-tex-extension
</script>

<p>from the main directory of this repository. That will create the
<code>mml.min.js</code> file in the <code>custom-tex-extension</code>
directory.</p>

<p>To use this in your own web page, the key lines are</p>

<p>This asks the loader to load the custom extension file, and defines
the <code>[custom]</code> path to be the directory relative to the
<code>mml.html</code> example file (<code>mml.min.js</code> is in the
same directory). The tex component is configured to add the
<code>mml</code> package to the other packages already there by default.
Finally, the <code>tex-chtml</code> component is loaded, and MathJax
does the rest.</p>

<p>The expressions in the example file illustrate the use of the custom
macros. For example</p>

<script type="text/x-colorize-code" class="html">
$$abc + \mi{abc} \mo{++} x ++ y$$
</script>

<p>shows the difference between <code>abc</code>, which produces three
separate <code>mi</code> elements internally, each containing one
letter, and <code>\mi{abc}</code>, which produces one <code>mi</code>
element containing three letters. The latter will be in upwrite
letters (as the default variant for <code>mi</code> elements depends
on the number of characters in the content). View the MathML source to
see the difference.</p>

<p>Similarly the difference between <code>\mo{++}</code> and
<code>++</code> is the number of <code>mo</code> elements and the
length of their content. <code>\mo{++}</code> produces
<code>&lt;mo&gt;++&lt;/mo&gt;</code> while <code>++</code> produces
<code>&lt;mo&gt;+&lt;/mo&gt;&lt;mo&gt;+&lt;/mo&gt;</code>. Note the
difference in output for these two: the TeX spacing rules for <code>x
++ y</code> means that it will be spaced as <code>x + +y</code>, while
<code>x \mo{++} y</code> will be <code>x ++ y</code>.</p>

<p>The expression</p>

<script type="text/x-colorize-code" class="html">
\mi[mathvariant="bold"]{A}
</script>

<p>produces a bold A via
<code>&lt;mi mathvariant="bold"&gt;A&lt;/mi&gt;</code>.</p>

<p>There are additional example equations in the source for the
example file, as listed below.</p>

</div>

<script src="../../scripts/source.js"></script>

</body>
</html>