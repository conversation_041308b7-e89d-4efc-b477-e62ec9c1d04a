(()=>{"use strict";const t=MathJax._.input.tex.HandlerTypes,e=t.ConfigurationType,a=t.HandlerType,n=MathJax._.input.tex.Configuration,r=n.Configuration,s=(n.Configu<PERSON>,n.<PERSON>onfiguration,MathJax._.input.tex.TokenMap),i=(s.<PERSON><PERSON>,s.AbstractTokenMap,s.RegExpMap,s.AbstractParseMap,s.CharacterMap,s.DelimiterMap,s.MacroMap,s.CommandMap),o=(s.EnvironmentMap,MathJax._.input.tex.TexError.default),c=MathJax._.util.string,u=(c.sort<PERSON>th,c.quotePattern,c.unicodeChars,c.unicodeString,c.isPercent,c.split,c.replaceUnicode);c.toEntity;function m(t){return this.getPrevClass(t),this}MathJax.loader&&MathJax.loader.checkVersion("[custom]/mml.min.js","4.0.0","tex extension");const l={style:!0,href:!0,id:!0,class:!0};function p(t,e,a){const n=t.configuration.nodeFactory.mmlFactory.getNodeClass(a),r=function(t,e){const a={};if(t){let n;for(;n=t.match(/^\s*((?:data-)?[a-z][-a-z]*)\s*=\s*(?:"([^"]*)"|(.*?))(?:\s+|,\s*|$)/i);){const r=n[1],s=n[2]||n[3];if(!Object.hasOwn(e.defaults,r)&&!Object.hasOwn(l,r)&&"data-"!==r.substr(0,5))throw new o("BadAttribute",'Unknown attribute "%1"',r);a[r]=u(s),t=t.substring(n[0].length)}if(t.length)throw new o("BadAttributeList","Can't parse as attributes: %1",t)}return a}(t.GetBrackets(e),n),s=u(t.GetArgument(e)),i=t.create("node",a,[t.create("text",s)],r);"mi"===a&&(i.setTeXclass=m),t.Push(i)}new i("mmlMap",{mi:[p,"mi"],mo:[p,"mo"],mn:[p,"mn"],ms:[p,"ms"],mtext:[p,"mtext"]}),r.create("mml",{[e.HANDLER]:{[a.MACRO]:["mmlMap"]}})})();