<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width">
  <link rel="stylesheet" href="../../styles/globals.css">
  <title>MathJax v4 loaded only if there is math</title>
  <script class="show" src="load-mathjax.js" defer></script>
</head>
<body>

<div id="frame">

<h1>Loading MathJax Only When Needed</h1>

<div class="inset">

<p>This page demonstrates a technique for loading MathJax in pages
only when they contain mathematical notation, and not otherwise.  Use
the "Show Source' button below to see the details.</p>

</div>

<div class="show display">
 
  <p>This is math in TeX: $$x+1\over x-1$$</p>

  <p>This is MathML:
  <math xmlns="http://www.w3.org/1998/Math/MathML">
    <msqrt>
      <mn>1</mn>
      <mo>-</mo>
      <msup>
        <mi>x</mi>
        <mn>2</mn>
      </msup>
    </msqrt>
  </math>
  </p>
 
</div>
</div>

<div class="explain inset">

<p>This example shows how to load MathJax only when there is actual
math in the page, and not load MathJax otherwise. This could be used,
for example, in the common header for a collection of pages, say ones
that are generated by a wiki or blog system. That way, pages without
math will not require the download of MathJax’s code and font
information.</p>

<p>This uses a script file <code>load-mathjax.js</code> containing the
following:</p>

<script type="text/x-load-code" src="load-mathjax.js"></script>

<p>This checks the page for either the presence of
<code>&lt;math&gt;</code> elements, or for the various standard TeX
math delimiters (<code>$</code>, <code>$$</code>, <code>\(</code>,
<code>\[</code>, or <code>\begin{...}</code>), and only loads MathJax
if one is present. If so, it sets up the MathJax global variable (if
one isn’t present) in order to include the single-dollar delimiters,
and then loads the <code>tex-mml-chtml</code> combined component file
to process both MathML and TeX.</p>

<p>If you want only MathML or only TeX, you can modify the script
accordingly. If you use different delimiters for TeX, modify the
regular expression to use your delimiters.</p>

<p>In your web page, you need only include</p>

<script type="text/x-colorize-code" class="html">
<script src="load-mathjax.js" defer>&lt;/script>
</script>

<p>to load and run this small script file. Adjust the <code>src</code>
attribute to include the path to <code>load-mathjax.js</code> if it is
not in the same directory as the HTML file itself.</p>

<p>The key sections of the HTML for this page are shown below:</p>

</div>

<script src="../../scripts/source.js"></script>

</body>
</html>
