<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width">
  <link rel="stylesheet" href="../styles/interactive.css">
  <title>MathJax v4: interactive MathML input and HTML output</title>
  <script class="show">
  MathJax = {
    //
    // Use the startup ready() function to set the options as they
    // are in the browser (in case of a reload that retains input states),
    // but reset the font menu, since loading the font can take time.
    //
    startup: {
      ready() {
        //
        // Do the usual startup actions (create document, input/output jax, etc.)
        //
        MathJax.startup.defaultReady();
        //
        // Set the options based on the checkbox states, but
        // reset the font menu, since getting one can be time consuming.
        //
        const doc = MathJax.startup.document;
        doc.inputJax.mml.mathml.options.allowHtmlInTokenNodes = document.querySelector('#allowhtml').checked;
        doc.outputJax.options.mathmlSpacing = document.querySelector('#mathmlspacing').checked;
        document.querySelector('#font').value = 'mathjax-newcm';
        //
        // Disable the some menu items.
        //
        doc.menu.menu.findID('Settings', 'Renderer').disable();
        //
        // Typeset the initial math, when ready.
        //
        MathJax.whenReady(() => Interactive.convert());
      }
    },
    //
    // A function to get the URL for the fonts.  We use jsdelivr.net.
    //
    fontUrl: (font) => `https://cdn.jsdelivr.net/npm/@mathjax/${font}-font@4`,
  }
  </script>
  <script class="show" defer src="https://cdn.jsdelivr.net/npm/mathjax@4/mml-chtml.js"></script>
  <script class="show">
  const Interactive = {
    async convert() {
      //
      // Disable the input controls until MathJax is done.
      //
      this.enableControls(false);
      //
      // Get the MathML input and the node to use for output.
      //
      const input = document.querySelector('#input').value.replace(/<!--.*?-->/g, '').trim();
      const output = document.querySelector('#output');
      //
      // Get the conversion options (metrics and display settings).
      // Convert the input to CHTML output and use a promise to wait for it to be ready
      //   (in case an extension needs to be loaded dynamically).
      //
      const options = MathJax.getMetricsFor(output);
      options.display = document.querySelector('#display').checked;
      await MathJax.mathml2chtmlPromise(input, options).then((node) => {
        //
        // The promise returns the typeset node, which we set as the output.
        // Then update the document to include the adjusted CSS for the
        //   content of the new equation.
        //
        output.innerHTML = '';
        output.appendChild(node);
        MathJax.startup.document.clear();
        MathJax.startup.document.updateDocument();
      }).catch((err) => {
        //
        // If there was an error, put the message into the output,
        // and report it to the console.
        //
        output.innerHTML = `<pre>Error: ${err.message}</pre>`;
        console.error(err);
      })
      //
      // Error or not, re-enable the display and render buttons
      //
      this.enableControls(true);
    },

    //
    // Enable/disable the input controls
    //
    enableControls(enable = true) {
      for (const name of ['display', 'allowhtml', 'mathmlspacing', 'font', 'render']) {
        document.querySelector(`#${name}`).disabled = !enable;
      }
    },

    //
    // Set the allowHtmlInTokenElements option and rerender.
    //
    allowHTML(allow) {
      MathJax.startup.document.inputJax.mml.mathml.options.allowHtmlInTokenNodes = allow;
      this.convert();
    },

    //
    // Set the mathmlSpacing option and rerender.
    //
    mathmlSpacing(spacing) {
      const jax = MathJax.startup.document.outputJax;
      jax.options.mathmlSpacing = spacing;
      jax.font.setOptions({mathmlSpacing: spacing});
      this.convert();
    },

    //
    // Load and initialize the new font.
    //
    async setFont(font) {
      this.enableControls(false);
      //
      // If the font isn't already loaded, put up a message that we are loading
      // the font (since it can take a few seconds), set up the font path and
      // clear any font configuraiton options that were set by the previous font.
      // Finally, load the new font and wait for it to arrive.
      //
      if (!MathJax._.output.fonts[font]) {
        document.querySelector('#output').innerHTML = `<div id="message">Loading ${font} font...</div>`;
        MathJax.config.loader.paths[font] = MathJax.config.fontUrl(font);
        for (const key of Object.keys(MathJax.config.chtml.fontData.OPTIONS)) {
          delete MathJax.config.chtml[key];
        }
        await MathJax.loader.load(`[${font}]/chtml.js`);
      }
      //
      // Look up the FontData object for the font (its only export).
      //
      const fontData = Object.values(MathJax._.output.fonts[font].chtml_ts)[0];
      //
      // Ask the output jax to remove its stylesheet and reset its caches,
      // then give it a new font and set its mathml spacing option.
      //
      const {selectOptionsFromKeys} = MathJax._.util.Options;
      const jax = MathJax.startup.document.outputJax;
      document.head.removeChild(jax.styleSheet());
      jax.chtmlStyles = null;
      jax.reset();
      jax.font = new fontData(selectOptionsFromKeys(MathJax.config.chtml, fontData.OPTIONS));
      jax.font.setOptions({mathmlSpacing: jax.options.mathmlSpacing});
      jax.font.adaptiveCSS(jax.options.adaptiveCSS);
      //
      // Re-render the math using the new font.
      //
      this.convert();
    }
  };
  </script>
</head>
<body>

<div id="frame">
<div class="show display">
 
  <h1>MathJax v4: MathML to CHTML</h1>

  <textarea id="input" rows="15" cols="10">
<!-- Enter MathML below -->

<math xmlns="http://www.w3.org/1998/Math/MathML" display="block">
  <mi>x</mi> <mo>=</mo>
  <mrow>
    <mfrac>
      <mrow>
        <mo>&#x2212;</mo>
        <mi>b</mi>
        <mo>&#x00B1;</mo>
        <msqrt>
          <msup><mi>b</mi><mn>2</mn></msup>
          <mo>&#x2212;</mo>
          <mn>4</mn><mi>a</mi><mi>c</mi>
        </msqrt>
      </mrow>
      <mrow> <mn>2</mn><mi>a</mi> </mrow>
    </mfrac>
  </mrow>
  <mtext>.</mtext>
</math>
  </textarea>

  <br />

  <div class="left">
    <div id="controls">

      <input type="checkbox" id="display" checked onchange="Interactiveconvert()">
         <label for="display">Display style</label><br>

      <input type="checkbox" id="allowhtml" onchange="Interactive.allowHTML(this.checked)">
         <label for="allowHTML">Allow HTML in Token Nodes</label><br>

      <input type="checkbox" id="mathmlspacing" onchange="Interactive.mathmlSpacing(this.checked)">
         <label for="allowHTML">Use MathML spacing rules</label><br>

      <hr>

      <label for="font">Font:</label>
        <select id="font" onchange="Interactive.setFont(this.value)">
          <option>mathjax-asana</option>
          <option>mathjax-bonum</option>
          <option>mathjax-dejavu</option>
          <option>mathjax-fira</option>
          <option>mathjax-modern</option>
          <option selected>mathjax-newcm</option>
          <option>mathjax-pagella</option>
          <option>mathjax-schola</option>
          <option>mathjax-stix2</option>
          <option>mathjax-termes</option>
          <option>mathjax-tex</option>
        </select>

    </div>
  </div>

  <div class="right">
    <input type="button" value="Render MathML" id="render" onclick="Interactive.convert()" />
  </div>

  <br clear="all" />

  <div id="output"></div>
 
  <script>Interactive.enableControls(false)</script>
 
</div>

<div class="explain inset">

<p>This example shows how to use the
<code>MathJax.mathml2chtmlPromise()</code> function to process user input,
allowing for the possibility that they use <code>\require</code> to
load extensions dynamically, or one is loaded automatically by the
<code>autoload</code> extension, or the font needs to load additional
data for the characters used in the expression.</p>

<p>When the user presses the <code>Render HTML</code> button or
switches the <code>display</code> checkbox or one of the other
controls, the <code>Interactive.convert()</code> function runs. The
comments in the code explain how the conversion process is
handled. Note that the user interface is disabled during the
typesetting process, since the conversion is done asynchronously in
this example. This prevents the user from starting a new typeset
operation while one is already in progress.</p>

</div>
</div>

<script src="../scripts/source.js"></script>

</body>
</html>
