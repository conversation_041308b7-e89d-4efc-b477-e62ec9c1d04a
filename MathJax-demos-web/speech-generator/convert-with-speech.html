<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width">
  <link rel="stylesheet" href="../styles/convert.css">
  <title>Convert LaTeX or MathML to Speech</title>
  <script>
  MathJax = {
    loader: {load: ['input/asciimath', 'input/mml', 'input/tex', 'output/svg', 'a11y/speech']},
    tex: {inlineMath: {'[+]': [['$', '$']]}},
    startup: {
      ready: function() {
        MathJax.startup.defaultReady();
        // Initialize convert when MathJax/SRE is fully loaded.
        MathJax.startup.promise.then(() => Convert.init());
      }
    }
  };
  </script>
  <script defer src="https://cdn.jsdelivr.net/npm/mathjax@4/startup.js"></script>
  <script src="./convert-with-speech.js"></script>
</head>
<body>

<h1>MathJax Speech Converter</h1>

<div id="explanation">
<p>

This tool converts math input expressions given in LaTeX, MathML or AsciiMath to
text output.

</p>
</div>

<div class="converter" id="inputpanel">
  <h2>Input:</h2>
  <textarea rows="15" cols="80" id="input" aria-label="Text input area for LaTeX, MathML and AsciiMath expression."></textarea>

  <div class="controls">
    <div class="radio-group">
      <label><input type="radio" checked name="format" value="latex"> LaTeX</label>
      <label><input type="radio" name="format" value="asciimath"> AsciiMath</label>
      <label><input type="radio" name="format" value="mathml"> MathML</label>
    </div>

    <div class="select-group">
      <label for="locale">Locale</label>
      <select id="locale" onchange="Convert.updateLocale(value)">
        <!-- options go here -->
      </select>
    </div>

    <div class="select-group">
      <label for="markup">Markup</label>
      <select id="markup" onchange="Convert.convertExpression()">
        <option value="none">None</option>
        <option value="punctuation">Punctuation</option>
        <option value="ssml">SSML</option>
        <option value="sable">Sable</option>
      </select>
    </div>

    <div class="convert-button">
      <input type="button" id="convert" value="Convert" onclick="Convert.convertExpression()" />
    </div>
  </div>

  <h2>Expression:</h2>
  <div id="rendered"></div>
</div><!--

--><div class="output-panel converter" id="outputpanel">

  <h2>Mathspeak Output:</h2>
  <div class="style-selector">
    <label for="style">Style</label>
    <select id="style" onchange="Convert.computeMathspeak()">
      <option value="default">Verbose</option>
      <option value="brief">Brief</option>
      <option value="sbrief">Superbrief</option>
    </select>
  </div>

  <pre class="speech" id="mathspeak"></pre>

  <h2>Clearspeak Output:</h2>
  <pre class="speech" id="clearspeak"></pre>

  <h2>Preferences:</h2>
  <div id="preferences"></div>

</div>

</body>
</html>
