(()=>{"use strict";const t=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-newcm"].chtml_ts.MathJaxNewcmFont.dynamicSetup("","marrows",t({MAR:{normal:{129024:[.356,-.144,1],129025:[.692,.192,.5],129026:[.356,-.144,1],129027:[.692,.192,.5],129028:[.397,-.103,1],129029:[.692,.192,.5],129030:[.397,-.103,1],129031:[.692,.192,.5],129032:[.437,-.063,1],129033:[.692,.192,.5],129034:[.437,-.063,1],129035:[.692,.192,.5],129040:[.357,-.143,1],129041:[.692,.192,.5],129042:[.357,-.143,1],129043:[.692,.192,.5],129044:[.377,-.123,1],129045:[.692,.192,.5],129046:[.377,-.123,1],129047:[.692,.192,.5],129048:[.417,-.083,1],129049:[.692,.192,.5],129050:[.417,-.083,1],129051:[.692,.192,.5],129052:[.45,-.05,1],129053:[.691,.191,.5],129054:[.45,-.05,1],129055:[.691,.191,.5],129056:[.356,-.144,1],129057:[.692,.192,.5],129058:[.356,-.144,1],129059:[.692,.192,.5],129060:[.356,-.144,1],129061:[.692,.192,.5],129062:[.356,-.144,1],129063:[.692,.192,.5],129064:[.356,-.144,1],129065:[.692,.192,.5],129066:[.356,-.144,1],129067:[.692,.192,.5],129068:[.356,-.144,1],129069:[.692,.192,.5],129070:[.356,-.144,1],129071:[.692,.192,.5],129072:[.356,-.144,1],129073:[.692,.192,.5],129074:[.356,-.144,1],129075:[.692,.192,.5],129076:[.394,-.106,.702],129077:[.533,.033,.702],129078:[.394,-.106,.702],129079:[.533,.033,.702],129080:[.356,-.144,.52],129081:[.452,-.048,.52],129082:[.356,-.144,.52],129083:[.452,-.048,.52],129084:[.356,-.144,.44],129085:[.412,-.088,.44],129086:[.356,-.144,.44],129087:[.412,-.088,.44],129088:[.417,-.083,.452],129089:[.421,-.079,.452],129090:[.417,-.083,.452],129091:[.421,-.079,.452],129092:[.417,-.083,.532],129093:[.461,-.039,.532],129094:[.417,-.083,.532],129095:[.461,-.039,.532],129104:[.442,-.058,1],129105:[.692,.192,.528],129106:[.442,-.058,1],129107:[.692,.192,.528],129108:[.567,.069,1],129109:[.567,.069,.528,{ic:.053}],129110:[.569,.067,1],129111:[.569,.067,.528,{ic:.055}],129112:[.442,-.058,1.068],129113:[.726,.226,.61],129120:[.504,.005,1],129121:[.687,.188,.646],129122:[.504,.005,1],129123:[.687,.188,.646],129124:[.563,.064,1],129125:[.563,.064,.646],129126:[.564,.063,1],129127:[.563,.064,.646],129128:[.507,.007,1],129129:[.692,.192,.646],129130:[.507,.007,1],129131:[.692,.192,.646],129132:[.567,.069,1],129133:[.567,.068,.646],129134:[.569,.067,1],129135:[.568,.067,.646],129136:[.506,.006,1],129137:[.692,.192,.666],129138:[.506,.006,1],129139:[.692,.192,.666],129140:[.567,.073,1],129141:[.567,.073,.666],129142:[.573,.067,1],129143:[.573,.067,.666],129144:[.506,.006,1],129145:[.692,.192,.684],129146:[.506,.006,1],129147:[.692,.192,.684],129148:[.567,.079,1],129149:[.567,.078,.684],129150:[.579,.067,1],129151:[.578,.067,.684],129152:[.506,.006,1],129153:[.692,.192,.676],129154:[.506,.006,1],129155:[.692,.192,.676],129156:[.567,.085,1],129157:[.567,.085,.676],129158:[.585,.067,1],129159:[.585,.067,.676],129168:[.356,-.143,.324],129169:[.35,-.15,.324],129170:[.356,-.144,.324],129171:[.35,-.15,.324],129172:[.451,-.049,.438],129173:[.406,-.095,.438],129174:[.451,-.049,.438],129175:[.405,-.094,.438],129176:[.356,-.144,.971],129177:[.67,.17,.375],129178:[.356,-.144,.971],129179:[.67,.17,.375],129180:[.291,-.211,.72],129181:[.291,-.211,.48],129182:[.291,-.211,.36],129183:[.291,-.211,.24],129184:[.492,.078,1.026],129185:[.492,.078,1.026],129186:[.492,.078,1.026],129187:[.492,.078,1.026],129188:[.491,.048,1.053],129189:[.491,.049,1.053],129190:[.492,.048,1.061],129191:[.492,.048,1.061],129192:[.492,.078,1.026],129193:[.492,.078,1.026],129194:[.492,.078,1.026],129195:[.492,.078,1.026],129196:[.341,-.105,.654],129197:[.341,-.105,.474],129200:[.714,.204,1],129201:[.707,.144,.618]}}},"NCM"),{},["MJX-NCM-MAR"]);MathJax.loader&&MathJax.loader.checkVersion("[mathjax-newcm]/chtml/dynamic/marrows","4.0.0-beta.7","dynamic-font")})();