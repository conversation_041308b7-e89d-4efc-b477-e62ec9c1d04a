(()=>{"use strict";const c=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-newcm"].chtml_ts.MathJaxNewcmFont.dynamicSetup("","PUA",c({PU:{normal:{57344:[.694,0,.667],57345:[.694,0,.667,{sk:-.019}],57346:[.691,0,.542,{sk:.033}],57347:[.694,0,.833,{sk:-.011}],57348:[.691,0,.597,{sk:.028}],57349:[.694,0,.611,{sk:.011}],57350:[.694,0,.708],57351:[.716,.022,.778],57352:[.694,0,.278],57353:[.694,0,.694],57354:[.694,0,.611],57355:[.694,0,.875],57356:[.694,0,.708],57357:[.689,0,.667],57358:[.716,.022,.736],57359:[.691,0,.708],57360:[.694,0,.639],57361:[.694,0,.722,{sk:.027}],57362:[.689,0,.68],57363:[.716,0,.778],57364:[.694,0,.722],57365:[.694,0,.667],57366:[.694,0,.778],57367:[.716,0,.722],57368:[.455,.011,.633,{sk:-.022}],57369:[.733,.139,.5,{sk:-.027}],57370:[.455,.171,.555,{sk:.011}],57371:[.721,.011,.472],57372:[.455,.011,.43],57373:[.738,.153,.472],57374:[.467,.195,.514,{sk:.017}],57375:[.733,.011,.5],57376:[.445,.011,.278,{sk:-.059}],57377:[.445,0,.483],57378:[.722,0,.5,{sk:-.136}],57379:[.445,.195,.555,{sk:-.045}],57380:[.445,0,.472,{sk:.01}],57381:[.744,.156,.472],57382:[.455,.011,.528],57383:[.445,.011,.583],57384:[.455,.195,.472],57385:[.445,.13,.444,{sk:.022}],57386:[.445,.011,.569,{sk:-.022}],57387:[.445,.011,.458],57388:[.445,.011,.5],57389:[.455,.195,.583],57390:[.458,.208,.555],57391:[.722,.195,.583],57392:[.445,.011,.667],57393:[.455,.011,.43],57394:[.75,.25,.997],57395:[.75,.25,.997],57409:[.694,0,.667,{sk:.154}],57410:[.694,0,.667,{ic:.028,sk:.16}],57411:[.691,0,.542,{ic:.105,sk:.116}],57412:[.694,0,.833,{sk:.149}],57413:[.691,0,.597,{ic:.092,sk:.115}],57414:[.694,0,.611,{ic:.092,sk:.087}],57415:[.694,0,.708,{ic:.054,sk:.112}],57416:[.716,.022,.778,{ic:.026,sk:.155}],57417:[.694,0,.278,{ic:.054,sk:.106}],57418:[.694,0,.694,{ic:.098,sk:.096}],57419:[.694,0,.611,{sk:.144}],57420:[.694,0,.875,{ic:.048,sk:.115}],57421:[.694,0,.708,{ic:.051,sk:.13}],57422:[.689,0,.667,{ic:.098,sk:.073}],57423:[.716,.022,.736,{ic:.027,sk:.152}],57424:[.691,0,.708,{ic:.053,sk:.116}],57425:[.694,0,.639,{ic:.052,sk:.128}],57426:[.694,0,.722,{ic:.092,sk:.12}],57427:[.689,0,.68,{ic:.111,sk:.081}],57428:[.716,0,.778,{ic:.065,sk:.104}],57429:[.694,0,.722,{ic:.022,sk:.131}],57430:[.694,0,.667,{ic:.096,sk:.072}],57431:[.694,0,.778,{ic:.075,sk:.093}],57432:[.716,0,.722,{ic:.047,sk:.13}],57433:[.455,.011,.633,{ic:.004,sk:.064}],57434:[.733,.139,.5,{ic:.032,sk:.136}],57435:[.455,.171,.555,{ic:.056,sk:.079}],57436:[.721,.011,.472,{ic:.092,sk:.047}],57437:[.455,.011,.43,{ic:.027,sk:.081}],57438:[.738,.153,.472,{ic:.097,sk:.097}],57439:[.467,.195,.514,{ic:.02,sk:.116}],57440:[.733,.011,.5,{ic:.053,sk:.152}],57441:[.445,.011,.278,{sk:.04}],57442:[.445,0,.483,{ic:.034,sk:.084}],57443:[.722,0,.5,{sk:.024}],57444:[.445,.195,.655,{sk:.09}],57445:[.445,0,.472,{ic:.053,sk:.063}],57446:[.744,.156,.472,{ic:.056,sk:.128}],57447:[.455,.011,.472,{ic:.011,sk:.123}],57448:[.445,.011,.583,{ic:.053,sk:.073}],57449:[.455,.195,.472,{ic:.011,sk:.13}],57450:[.445,.13,.444,{ic:.054,sk:.066}],57451:[.445,.011,.569,{ic:.066,sk:.046}],57452:[.445,.011,.458,{ic:.053,sk:.059}],57453:[.445,.011,.5,{ic:.016,sk:.074}],57454:[.456,.195,.583,{ic:.015,sk:.099}],57455:[.458,.208,.555,{ic:.06,sk:.066}],57456:[.722,.195,.583,{ic:.027,sk:.136}],57457:[.445,.011,.667,{ic:.009,sk:.09}],57458:[.455,.011,.43,{ic:.042,sk:.052}],59264:[.705,.29,.785],59265:[.738,.206,.5],59266:[.683,.29,.778],59267:[.694,.29,.528],59268:[.683,.29,.625],59269:[.694,.29,.278],59270:[.683,.29,.75],59271:[.442,.29,.556],59272:[.683,.29,.736],59273:[.442,.29,.392],59274:[.623,.18,.555],59395:[.705,0,.816],59908:[.873,-.518,.5],59909:[-.061,.233,.5],59910:[-.061,.233,.5],59913:[.873,-.518,.5],59915:[.899,-.518,.5],59917:[.69,-.518,.5],59920:[.823,-.518,.5],59927:[.832,-.516,.5],59930:[.832,-.516,.5],59932:[.858,-.516,.5],59934:[.782,-.516,.5],59935:[-.066,.29,.5],59942:[.697,-.51,.5,{sk:-.024}],59946:[.87,-.557,.5],59948:[.83,-.557,.5],59951:[.87,-.557,.5],59957:[.709,-.5,.5],59962:[-.131,.162,.5],59966:[-.131,.162,.5],59970:[.888,-.529,.75],59973:[-.1,.193,.5],60163:[.909,.211,.75],60164:[.698,.211,.5],60168:[.683,0,.683],60175:[.683,0,.683],60176:[0,0,0],60177:[0,0,0],60178:[0,0,0],60182:[.603,.025,.5],60185:[-.094,.2,.278],60190:[.909,.211,.681],60191:[.698,.211,.444],60200:[.705,.022,1.111],60201:[.5,.256,.472],60203:[.728,.045,.785],60209:[.341,-.088,.333],60213:[.909,.211,.361],60214:[.698,.211,.278],60218:[.909,.022,.514],60219:[.698,.205,.306],60224:[.746,.05,1,{ic:.011}],60232:[.909,.211,.778],60233:[.698,.211,.5],60237:[.347,.056,.392],60257:[.392,-.28,.278],60259:[.639,-.491,.333,{ic:.26}],60261:[.666,-.539,.278,{ic:.233}],60270:[.683,.233,.75],60271:[.442,.233,.556],60424:[.829,0,.75],60425:[.84,0,.556],60426:[.829,0,.625],60427:[.84,0,.278,{ic:.028}],60428:[.829,0,.722],60429:[.76,.011,.389],60430:[.831,0,.722],60432:[.878,.194,.778],60433:[.625,.194,.5],61699:[.705,.022,1.111],61700:[.277,-.255,.667],61705:[.123,0,.611],61719:[.348,-.145,.278],61720:[.712,.026,.555],61721:[.712,.012,.444],61722:[.683,.205,.778],61723:[.43,.205,.555],61724:[.683,.195,.778],61725:[.683,.195,.916],61726:[.683,.195,.68],61729:[.43,.162,.555],61730:[.43,.162,.667],61731:[.441,.311,.555],61734:[.683,.195,.625],61735:[.705,.022,.722],61736:[.43,.162,.43],61737:[.447,.011,.444],61738:[.683,.205,.916],61739:[.683,.205,.68],61740:[.43,.205,.667],61741:[.441,.322,.555],61743:[.62,.179,.555],61747:[.716,0,.75],61748:[.832,0,.639],61749:[.441,.011,.5],61750:[.625,.011,.417],61751:[0,.252,.139,{ic:0}],61752:[0,.251,.194,{ic:.001}],61757:[.694,-.521,.667],61761:[.668,-.48,.5],61762:[.668,-.48,.5],61763:[.243,-.002,.444],61764:[.243,-.002,.444],61765:[.696,-.38,.333],61766:[.647,-.374,.222,{ic:.069}],61771:[.02,.02,.333,{ic:.018}],61772:[.192,.019,.333,{ic:.018}],61773:[.366,.019,.333,{ic:.018}],61774:[.54,.019,.333,{ic:.018}],61775:[.713,.019,.333,{ic:.018}],61776:[.192,.019,.333,{ic:.018}],61777:[.366,.019,.333,{ic:.018}],61778:[.54,.019,.333,{ic:.018}],61779:[.713,.019,.333,{ic:.019}],61780:[.02,.02,.222,{ic:.018}],61781:[.192,.019,.222,{ic:.018}],61782:[.366,.019,.222,{ic:.019}],61783:[.539,.019,.222,{ic:.019}],61784:[.713,.019,.222,{ic:.019}],61785:[.192,.019,.222,{ic:.018}],61786:[.366,.019,.222,{ic:.019}],61787:[.54,.019,.222,{ic:.019}],61788:[.713,.019,.222,{ic:.019}],61791:[.695,-.516,.5],61792:[.695,-.516,.5],61793:[.695,-.503,.5],61794:[.695,-.503,.5],61795:[.698,-.51,.5],61796:[.698,-.51,.5],61797:[.441,.195,.333],61800:[.447,.016,.722],61804:[.43,.205,.306],61805:[.009,.205,.333],61806:[.107,.205,.222,{ic:.069}],61807:[.107,.205,.222],61808:[.107,.205,.278],61809:[.684,-.43,.3],61810:[.72,-.47,.444],61813:[.441,.011,.556],61817:[.711,.212,.5],61818:[.447,.205,.444],61819:[.695,.011,.736],61822:[.441,.205,.556],61823:[.441,.205,.556],61824:[.705,.205,.556,{ic:.059}],61826:[.668,.205,.35],61828:[.695,0,.833],61829:[.441,0,.667],61832:[.441,.008,.611],61833:[.441,.205,.583],61834:[.615,.011,.556],61835:[.441,.219,.389],61836:[.43,.236,.5],61837:[.453,.236,.5],61838:[.695,.063,.5],61839:[.695,.011,.5],61842:[.695,0,.392],61850:[.43,.02,.547],61854:[.43,0,.566],61855:[.43,0,.455],61857:[.445,0,.547],61859:[.755,-.519,.5],61860:[.755,-.519,.5],61863:[.022,.205,.222],61864:[.022,.205,.222,{ic:.069}],61865:[-.111,.274,.5],61866:[-.112,.283,.5],61867:[.248,-.226,.278],61868:[.348,-.145,.278],61869:[-.113,.276,.5],61870:[-.111,.287,.5],61871:[-.101,.243,.5],61872:[-.025,.275,.556],61873:[.661,-.453,.555],61874:[.716,-.5,.5],61875:[.716,-.5,.5],61876:[.802,.302,1.111],61877:[.853,.353,1.111],62082:[.09,0,.278],62083:[.705,-.498,.5],62110:[.694,-.5,.5],62113:[.694,.022,.68],62116:[.683,0,.778],62119:[.683,0,.778],62120:[.683,.193,1.122],62121:[.441,.011,.5],62124:[.43,0,.555],62126:[.43,0,.555],62127:[.43,.16,.778],62560:[.901,0,.75],62561:[.875,0,.68],62562:[.875,0,.778],62563:[.862,.022,.777],62564:[.875,.022,.75],62565:[.875,0,.972],62566:[.875,.022,.722],62567:[.875,.022,1.125],62568:[.875,0,.778],62570:[.642,.011,.5],62571:[.642,.011,.444],62572:[.649,0,.555],62573:[.642,.011,.5],62574:[.649,.205,.528],62575:[.649,0,.722],62576:[.649,.011,.444],62577:[.649,.011,.75],62578:[.649,0,.542],63166:[.442,.205,.306,{sk:.024}],63187:[.862,-.742,.5],63188:[.613,-.475,.5],63189:[.649,-.5,.5],63190:[.642,-.49,.5],63198:[.277,-.255,.75]}},PUB:{bold:{59395:[.7,0,.945],59908:[.907,-.514,.575],59909:[-.028,.215,.575],59910:[-.028,.215,.575],59913:[.907,-.514,.575],59915:[.909,-.514,.575],59917:[.7,-.513,.575],59920:[.864,-.514,.575],59927:[.874,-.503,.575],59930:[.874,-.503,.575],59932:[.876,-.503,.575],59934:[.831,-.503,.575],59935:[-.033,.295,.575],59942:[.704,-.509,.575],59946:[.9,-.537,.575],59948:[.884,-.537,.575],59951:[.9,-.537,.575],59957:[.703,-.51,.575],59962:[-.096,.148,.575],59966:[-.096,.148,.575],59970:[.895,-.521,.869],59973:[-.068,.176,.575],60163:[.909,.206,.869],60164:[.704,.206,.559],60168:[.686,0,.686],60175:[.686,0,.686],60176:[0,0,0],60177:[0,0,0],60178:[0,0,0],60182:[.608,.024,.575],60185:[-.044,.2,.319],60190:[.909,.206,.756],60191:[.704,.206,.527],60200:[.697,.011,1.278],60201:[.5,.269,.543],60203:[.758,.072,.904],60209:[.406,-.085,.383],60213:[.909,.206,.436],60214:[.704,.206,.319],60218:[.909,.011,.594],60219:[.704,.2,.351],60224:[.775,.079,1.15,{ic:.017}],60232:[.909,.206,.864],60233:[.704,.206,.575],60237:[.347,.056,.45],60257:[.402,-.278,.319,{ic:.003}],60259:[.654,-.499,.397,{ic:.309}],60261:[.668,-.542,.319,{ic:.277}],60270:[.686,.215,.885],60271:[.45,.215,.639],60424:[.836,0,.9],60425:[.844,0,.639],60426:[.836,0,.692],60427:[.844,0,.319,{ic:.032}],60428:[.836,0,.8],60429:[.794,.006,.447],60430:[.868,0,.8],60432:[.881,.194,.864],60433:[.639,.194,.575],61699:[.697,.011,1.277],61700:[.293,-.256,.766],61705:[.127,0,.703],61719:[.355,-.127,.319],61720:[.728,.018,.608],61721:[.718,.01,.511],61722:[.686,.2,.901],61723:[.445,.2,.639],61724:[.686,.195,.901],61725:[.686,.195,1.091,{ic:.001}],61726:[.686,.195,.786],61729:[.445,.162,.639],61730:[.445,.162,.766],61731:[.45,.311,.639],61734:[.686,.195,.691],61735:[.697,.011,.83],61736:[.445,.162,.48],61737:[.453,.005,.511],61738:[.686,.2,1.091],61739:[.686,.2,.786],61740:[.445,.2,.766],61741:[.45,.316,.639],61743:[.752,.285,.694],61747:[.698,0,.933],61748:[.886,0,.794],61749:[.45,.005,.655,{ic:.011}],61750:[.694,.005,.528],61751:[0,.256,.194],61752:[0,.255,.261],61757:[.694,-.507,.828],61761:[.656,-.461,.575],61762:[.656,-.461,.575],61763:[.291,-.001,.511],61764:[.291,-.001,.511],61765:[.7,-.384,.383],61766:[.648,-.364,.256,{ic:.078}],61771:[.03,.03,.383,{ic:.029}],61772:[.203,.029,.383,{ic:.029}],61773:[.377,.029,.383,{ic:.029}],61774:[.55,.029,.383,{ic:.029}],61775:[.724,.029,.383,{ic:.029}],61776:[.203,.029,.383,{ic:.029}],61777:[.377,.029,.383,{ic:.029}],61778:[.55,.029,.383,{ic:.029}],61779:[.724,.029,.383,{ic:.029}],61780:[.03,.03,.256,{ic:.028}],61781:[.203,.029,.256,{ic:.028}],61782:[.377,.029,.256,{ic:.028}],61783:[.55,.029,.256,{ic:.028}],61784:[.724,.029,.256,{ic:.028}],61785:[.203,.029,.256,{ic:.028}],61786:[.377,.029,.256,{ic:.028}],61787:[.55,.029,.256,{ic:.028}],61788:[.724,.029,.256,{ic:.028}],61791:[.696,-.487,.575],61792:[.696,-.487,.575],61793:[.697,-.5,.575],61794:[.697,-.5,.575],61795:[.705,-.498,.575],61796:[.705,-.498,.575],61797:[.45,.195,.383],61800:[.453,.008,.831],61804:[.445,.2,.351,{ic:.013}],61805:[.016,.2,.383],61806:[.113,.2,.256,{ic:.078}],61807:[.113,.2,.256],61808:[.113,.2,.319],61809:[.689,-.445,.356],61810:[.732,-.482,.511],61813:[.45,.005,.639],61817:[.703,.213,.575],61818:[.453,.202,.511],61819:[.695,.005,.847],61822:[.45,.2,.639],61823:[.45,.2,.639],61824:[.7,.2,.639,{ic:.073}],61826:[.695,.2,.402],61828:[.695,0,.958],61829:[.45,0,.767],61832:[.45,.012,.703],61833:[.45,.2,.671],61834:[.635,.005,.639],61835:[.45,.21,.447],61836:[.445,.211,.575],61837:[.455,.211,.575],61838:[.695,.066,.575],61839:[.695,.005,.575],61842:[.695,0,.474],61850:[.445,.008,.702],61854:[.445,0,.727],61855:[.445,0,.552],61857:[.452,0,.718],61859:[.774,-.528,.575],61860:[.774,-.528,.575],61863:[.023,.2,.256],61864:[.023,.2,.256,{ic:.078}],61865:[-.111,.281,.575],61866:[-.108,.29,.575],61867:[.261,-.228,.319],61868:[.355,-.127,.319],61869:[-.112,.282,.575],61870:[-.111,.301,.575],61871:[-.106,.251,.575],61872:[-.035,.286,.639],61873:[.664,-.456,.639],61874:[.705,-.514,.575],61875:[.705,-.514,.575],61876:[.806,.306,1.277],61877:[.881,.381,1.277],62082:[.124,.001,.319],62083:[.706,-.513,.575],62110:[.714,-.514,.575],62113:[.694,.011,.786],62116:[.686,0,.901],62119:[.686,0,.901],62120:[.686,.194,1.328],62121:[.45,.005,.575],62124:[.445,0,.639],62126:[.445,0,.639],62127:[.445,.162,.91],62560:[.875,0,.869],62561:[.889,0,.755],62562:[.889,0,.901],62563:[.875,.011,.863],62564:[.889,.011,.869],62565:[.889,0,1.125],62566:[.889,.011,.83],62567:[.889,.011,1.273],62568:[.889,0,.901],62570:[.662,.006,.559],62571:[.662,.006,.527],62572:[.671,0,.639],62573:[.662,.006,.575],62574:[.671,.2,.607],62575:[.671,0,.83],62576:[.671,.005,.511],62577:[.671,.005,.862],62578:[.671,0,.607],63166:[.45,.2,.351,{sk:.041}],63187:[.875,-.733,.575],63188:[.688,-.491,.575],63189:[.658,-.514,.575],63190:[.662,-.494,.575],63198:[.292,-.256,.863]}},PUI:{italic:{59395:[.705,.205,.792,{ic:.041}],59908:[.865,-.516,.511,{ic:.056}],59909:[-.062,.232,.511],59910:[-.062,.232,.511],59913:[.865,-.516,.511,{ic:.052}],59915:[.895,-.516,.511,{ic:.052}],59917:[.686,-.516,.511,{ic:.021}],59920:[.827,-.516,.511,{ic:.101}],59927:[.816,-.518,.511,{ic:.043}],59930:[.816,-.518,.511,{ic:.018}],59932:[.846,-.518,.511,{ic:.121}],59934:[.778,-.518,.511,{ic:.088}],59935:[-.066,.29,.511],59942:[.696,-.506,.511],59946:[.867,-.553,.511,{ic:.09}],59948:[.816,-.553,.511,{ic:.086}],59951:[.867,-.553,.511,{ic:.032}],59957:[.705,-.496,.511],59962:[-.132,.162,.511],59966:[-.132,.162,.511],59970:[.884,-.529,.831],59973:[-.096,.197,.511],60163:[.906,.211,.743],60164:[.696,.211,.511,{ic:.028}],60168:[.683,0,.683],60175:[.683,0,.683],60176:[0,0,0],60177:[0,0,0],60178:[0,0,0],60182:[.603,.025,.511,{ic:.053}],60185:[-.094,.2,.307],60190:[.906,.211,.678,{ic:.065}],60191:[.696,.211,.46,{ic:.053}],60200:[.705,.022,1.124,{ic:.07}],60201:[.5,.256,.511],60203:[.728,.045,.774,{ic:.038}],60209:[.327,-.077,.179,{ic:.132}],60213:[.906,.211,.386,{ic:.162}],60214:[.696,.211,.307,{ic:.13}],60218:[.906,.022,.525,{ic:.149}],60219:[.696,.205,.307,{ic:.13}],60224:[.736,.039,.971,{ic:.159}],60232:[.906,.211,.767,{ic:.021}],60233:[.696,.211,.511,{ic:.028}],60237:[.347,.056,.408],60257:[.392,-.28,.256,{ic:.087}],60259:[.639,-.49,.358,{ic:.398}],60261:[.666,-.538,.307,{ic:.385}],60270:[.683,.232,.743,{ic:.109}],60271:[.442,.232,.537,{ic:.024}],60424:[.837,0,.743,{ic:.109}],60425:[.848,.011,.511,{ic:.106}],60426:[.837,0,.627],60427:[.848,.011,.256,{ic:.233}],60428:[.837,0,.716,{ic:.09}],60429:[.798,.011,.332,{ic:.183}],60430:[.832,0,.716,{ic:.09}],60432:[.874,.19,.767,{ic:.021}],60433:[.621,.19,.511],61699:[.705,.022,1.073,{ic:.069}],61700:[.277,-.255,.715,{ic:.012}],61705:[.123,0,.613],61719:[.356,-.138,.307],61720:[.711,.026,.562,{ic:.039}],61721:[.711,.013,.46,{ic:.039}],61722:[.683,.205,.769,{ic:.108}],61723:[.442,.205,.562,{ic:.024}],61724:[.683,.194,.769,{ic:.107}],61725:[.683,.194,.896,{ic:.104}],61726:[.683,.194,.678,{ic:.052}],61729:[.442,.203,.542,{ic:.006}],61730:[.43,.203,.746,{ic:.007}],61731:[.442,.31,.506,{ic:.017}],61734:[.683,.194,.627,{ic:.077}],61735:[.705,.022,.715,{ic:.095}],61736:[.442,.203,.422,{ic:.013}],61737:[.442,.011,.442,{ic:.009}],61738:[.683,.206,.896,{ic:.104}],61739:[.683,.206,.678,{ic:.052}],61740:[.432,.206,.69,{ic:.035}],61741:[.442,.322,.506,{ic:.017}],61743:[.619,.178,.555,{ic:.095}],61747:[.716,0,.75],61748:[.832,0,.555,{ic:.175}],61749:[.442,.011,.555],61750:[.625,.011,.417,{ic:.045}],61751:[.008,.251,.139],61752:[.009,.252,.194],61757:[.694,-.52,.667,{ic:.18}],61867:[.248,-.226,.307],61868:[.356,-.138,.307],61873:[.661,-.453,.562],61874:[.716,-.491,.511],61875:[.716,-.491,.511],61876:[.802,.302,1.073],61877:[.854,.354,1.073],62082:[.083,0,.307],62083:[.706,-.498,.511],62110:[.688,-.491,.511],62113:[.694,.022,.678,{ic:.016}],62116:[.683,0,.769,{ic:.107}],62119:[.683,0,.769,{ic:.107}],62120:[.683,.194,1.086,{ic:.107}],62560:[.87,0,.743],62561:[.875,0,.678,{ic:.065}],62562:[.875,0,.769,{ic:.107}],62563:[.87,.022,.766,{ic:.022}],62564:[.875,.022,.743,{ic:.123}],62565:[.875,0,.948,{ic:.098}],62566:[.875,.022,.715,{ic:.02}],62567:[.875,.022,1.087,{ic:.021}],62568:[.875,0,.769,{ic:.106}],62570:[.651,.011,.511,{ic:.034}],62571:[.651,.011,.46,{ic:.06}],62572:[.644,.011,.562,{ic:.024}],62573:[.651,.011,.511,{ic:.034}],62574:[.644,.206,.511,{ic:.029}],62575:[.644,.011,.664,{ic:.024}],62576:[.644,.011,.442,{ic:.02}],62577:[.644,.011,.723,{ic:.016}],62578:[.644,.011,.537,{ic:.049}],63166:[.442,.205,.307,{ic:.014}],63172:[.563,.011,.422,{ic:.09}],63174:[.442,.205,.46,{ic:.027}],63175:[.563,.011,.562,{ic:.024}],63176:[.572,.011,.818,{ic:.023}],63187:[.871,-.751,.511,{ic:.151}],63188:[.638,-.475,.511,{ic:.026}],63189:[.65,-.491,.511,{ic:.012}],63190:[.651,-.499,.511],63198:[.277,-.255,.767,{ic:.027}]}},PUBI:{"bold-italic":{59395:[.702,.202,.935,{ic:.039}],59908:[.893,-.512,.591,{ic:.038}],59909:[-.036,.215,.591],59910:[-.036,.215,.591],59913:[.893,-.512,.591,{ic:.038}],59915:[.908,-.512,.591,{ic:.038}],59917:[.69,-.511,.591,{ic:.006}],59920:[.865,-.512,.591,{ic:.094}],59927:[.863,-.497,.591,{ic:.025}],59930:[.863,-.497,.591,{ic:.01}],59932:[.878,-.497,.591,{ic:.116}],59934:[.835,-.497,.591,{ic:.086}],59935:[-.051,.307,.591],59942:[.699,-.503,.591],59946:[.899,-.535,.591,{ic:.073}],59948:[.886,-.535,.591,{ic:.095}],59951:[.899,-.535,.591,{ic:.045}],59957:[.702,-.5,.591],59962:[-.101,.151,.591],59966:[-.101,.151,.591],59970:[.904,-.534,.949],59973:[-.066,.185,.591],60163:[.918,.204,.866],60164:[.699,.204,.591,{ic:.024}],60168:[.686,0,.686],60175:[.686,0,.686],60176:[0,0,0],60177:[0,0,0],60178:[0,0,0],60182:[.608,.024,.591,{ic:.056}],60185:[-.053,.2,.356],60190:[.918,.204,.757,{ic:.048}],60191:[.699,.204,.532,{ic:.035}],60200:[.703,.017,1.3,{ic:.053}],60201:[.5,.275,.591],60203:[.758,.072,.895,{ic:.013}],60209:[.384,-.067,.414,{ic:.001}],60213:[.918,.204,.472,{ic:.137}],60214:[.699,.204,.356,{ic:.122}],60218:[.918,.017,.611,{ic:.111}],60219:[.699,.202,.356,{ic:.122}],60224:[.762,.065,1.121,{ic:.16}],60232:[.918,.204,.855,{ic:.01}],60233:[.699,.204,.591,{ic:.005}],60237:[.347,.056,.475],60257:[.402,-.278,.297,{ic:.111}],60259:[.654,-.508,.433,{ic:.457}],60261:[.668,-.542,.356,{ic:.432}],60270:[.686,.215,.881,{ic:.107}],60271:[.452,.215,.621,{ic:.034}],60424:[.86,0,.896,{ic:.107}],60425:[.868,.008,.591,{ic:.094}],60426:[.86,0,.698],60427:[.868,.008,.297,{ic:.24}],60428:[.86,0,.796,{ic:.076}],60429:[.818,.008,.385,{ic:.184}],60430:[.873,0,.796,{ic:.076}],60432:[.881,.194,.855,{ic:.01}],60433:[.639,.194,.591,{ic:.004}],61699:[.708,.022,1.239,{ic:.052}],61700:[.293,-.256,.829,{ic:.005}],61705:[.127,0,.709],61719:[.353,-.13,.355],61720:[.729,.029,.619,{ic:.016}],61721:[.718,.016,.532,{ic:.017}],61722:[.686,.206,.895,{ic:.105}],61723:[.456,.205,.65,{ic:.033}],61724:[.686,.194,.895,{ic:.105}],61725:[.686,.194,1.073,{ic:.104}],61726:[.686,.194,.787,{ic:.037}],61729:[.456,.204,.626,{ic:.044}],61730:[.444,.204,.862,{ic:.041}],61731:[.456,.311,.585,{ic:.025}],61734:[.686,.194,.698,{ic:.062}],61735:[.708,.022,.826,{ic:.082}],61736:[.456,.204,.488,{ic:.012}],61737:[.456,.011,.511,{ic:.002}],61738:[.686,.206,1.073,{ic:.104}],61739:[.686,.206,.787,{ic:.037}],61740:[.449,.206,.797,{ic:.043}],61741:[.456,.322,.585,{ic:.025}],61743:[.752,.284,.694,{ic:.1}],61747:[.699,0,.933],61748:[.886,0,.694,{ic:.158}],61749:[.45,.006,.722],61750:[.694,.006,.528,{ic:.05}],61751:[0,.256,.194],61752:[0,.255,.261],61757:[.694,-.508,.828,{ic:.158}],61867:[.261,-.228,.355],61868:[.353,-.13,.355],61873:[.675,-.467,.65],61874:[.716,-.506,.591],61875:[.716,-.506,.591],61876:[.817,.318,1.239],61877:[.895,.395,1.239],62082:[.117,.001,.355],62083:[.715,-.513,.591],62110:[.708,-.506,.591],62113:[.694,.022,.787],62116:[.686,0,.895,{ic:.105}],62119:[.686,0,.895,{ic:.104}],62120:[.686,.194,1.29,{ic:.105}],62560:[.892,0,.865],62561:[.894,0,.756,{ic:.049}],62562:[.894,0,.895,{ic:.105}],62563:[.891,.022,.855,{ic:.01}],62564:[.894,.022,.865,{ic:.112}],62565:[.894,0,1.101,{ic:.099}],62566:[.894,.022,.826,{ic:.009}],62567:[.894,.022,1.237,{ic:.009}],62568:[.894,0,.895,{ic:.105}],62570:[.675,.011,.591,{ic:.032}],62571:[.675,.011,.532,{ic:.044}],62572:[.666,.011,.65,{ic:.032}],62573:[.675,.011,.591,{ic:.015}],62574:[.666,.206,.591,{ic:.032}],62575:[.666,.011,.768,{ic:.031}],62576:[.666,.011,.511,{ic:.025}],62577:[.666,.011,.835,{ic:.024}],62578:[.666,.011,.62,{ic:.041}],63166:[.452,.202,.356,{ic:.019}],63172:[.596,.011,.488,{ic:.09}],63174:[.456,.206,.532,{ic:.03}],63175:[.596,.011,.65,{ic:.032}],63176:[.606,.011,.944,{ic:.032}],63187:[.892,-.756,.591,{ic:.138}],63188:[.689,-.503,.591,{ic:.024}],63189:[.664,-.506,.591,{ic:.003}],63190:[.675,-.515,.591],63198:[.292,-.256,.887,{ic:.021}]}}},"NCM"),{},["MJX-NCM-PU","MJX-NCM-PUB","MJX-NCM-PUI","MJX-NCM-PUBI"]);MathJax.loader&&MathJax.loader.checkVersion("[mathjax-newcm]/chtml/dynamic/PUA","4.0.0-beta.7","dynamic-font")})();