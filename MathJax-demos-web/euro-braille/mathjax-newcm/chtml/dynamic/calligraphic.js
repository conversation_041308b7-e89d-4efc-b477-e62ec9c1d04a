(()=>{"use strict";const s=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-newcm"].chtml_ts.MathJaxNewcmFont.dynamicSetup("","calligraphic",s({C:{"-tex-calligraphic":{65:[.694,.015,.857,{sk:.274}],66:[.713,.001,.778,{sk:.174}],67:[.697,.015,.654,{sk:.135}],68:[.717,-.004,.871,{sk:.06}],69:[.702,.012,.613,{sk:.143}],70:[.699,.015,.904,{sk:.212}],71:[.697,.13,.685,{sk:.164}],72:[.69,.008,1.065,{sk:.163}],73:[.685,.014,.62,{sk:.159}],74:[.692,.129,.698,{sk:.219}],75:[.69,.012,.989,{sk:.173}],76:[.685,.009,.77,{sk:.208}],77:[.699,.013,1.149,{sk:.128}],78:[.706,.009,1.007,{sk:.121}],79:[.686,.018,.699,{sk:.114}],80:[.71,.011,.763,{sk:.163}],81:[.694,.025,.716,{sk:.124}],82:[.712,.006,.818,{sk:.191}],83:[.702,.012,.625,{sk:.139}],84:[.694,.006,.776,{sk:.112}],85:[.7,.016,.744,{sk:.06}],86:[.709,.01,.71,{sk:.042}],87:[.703,.006,1.028,{sk:.087}],88:[.706,.009,.87,{sk:.121}],89:[.703,.136,.628,{sk:.083}],90:[.696,.011,.726,{sk:.089}]}},CB:{"-tex-bold-calligraphic":{65:[.711,.017,.969,{sk:.283}],66:[.727,.001,.916,{sk:.101}],67:[.709,.015,.745,{sk:.15}],68:[.727,.001,1.007,{sk:.045}],69:[.708,.012,.705,{sk:.151}],70:[.731,.014,1.005,{sk:.151}],71:[.705,.138,.79,{sk:.177}],72:[.699,.012,1.191,{sk:.178}],73:[.703,.018,.715,{sk:.199}],74:[.701,.137,.771,{sk:.26}],75:[.709,.009,1.099,{sk:.196}],76:[.71,.012,.861,{sk:.259}],77:[.71,.017,1.284,{sk:.115}],78:[.712,.013,1.095,{sk:.139}],79:[.707,.02,.822,{sk:.183}],80:[.726,.013,.88,{sk:.126}],81:[.705,.042,.839,{sk:.138}],82:[.732,.012,.923,{sk:.133}],83:[.715,.018,.722,{sk:.141}],84:[.697,.011,.91,{sk:.144}],85:[.709,.013,.853,{sk:.07}],86:[.702,.016,.784,{sk:.035}],87:[.71,.008,1.15,{sk:.057}],88:[.712,.011,.97,{sk:.125}],89:[.709,.135,.738,{sk:.079}],90:[.705,.014,.8,{sk:.133}]}}},"NCM"));MathJax.loader&&MathJax.loader.checkVersion("[mathjax-newcm]/chtml/dynamic/calligraphic","4.0.0-beta.7","dynamic-font")})();