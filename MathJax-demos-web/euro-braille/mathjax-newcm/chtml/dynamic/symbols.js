(()=>{"use strict";const t=MathJax._.output.chtml.DynamicFonts.AddFontIds,a=MathJax._.output.fonts["mathjax-newcm"].chtml_ts.MathJaxNewcmFont,c=MathJax._.output.common.Direction,o=(c.DIRECTION,c.V,c.H);a.dynamicSetup("","symbols",t({SY:{normal:{161:[.5,.216,.278],162:[.476,.045,.444],164:[.491,-.01,.778],166:[.75,.25,.278],169:[.683,0,.683],170:[.705,-.333,.449],171:[.483,0,.556],173:[.245,-.187,.333],174:[.683,0,.683],178:[.705,-.306,.359],179:[.705,-.293,.359],185:[.705,-.306,.366],186:[.705,-.333,.419],187:[.483,0,.556],188:[.705,0,.825],189:[.705,0,.825],190:[.705,0,.825],191:[.5,.205,.472],3647:[.728,.045,.708],8204:[0,0,0],8205:[0,0,0],8215:[-.103,.293,.504],8218:[.104,.195,.278],8219:[.693,-.394,.262],8222:[.104,.195,.472],8223:[.693,-.394,.472],8226:[.445,-.055,.5],8227:[.45,-.052,.5],8228:[.106,0,.277],8229:[.106,0,.557],8233:[.694,.195,.472],8240:[.75,.056,1.14],8241:[.75,.056,1.457],8248:[.116,.063,.523],8249:[.483,0,.389],8250:[.483,0,.389],8251:[.492,-.009,.778],8252:[.716,0,.478],8253:[.756,0,.472],8254:[.802,-.762,.68],8255:[-.079,.272,.6],8256:[.74,-.547,.6],8257:[.398,.186,.562],8258:[.549,.028,.665],8259:[.266,-.16,.5],8261:[.75,.25,.278],8262:[.75,.25,.278],8263:[.705,0,.891],8264:[.716,0,.648],8265:[.716,0,.648],8266:[.525,.152,.442],8267:[.694,.194,.611],8268:[.428,-.066,.572],8269:[.428,-.066,.572],8270:[.269,.028,.335],8271:[.431,.193,.278],8272:[.725,-.023,.717],8273:[.589,.028,.335],8274:[.751,-.001,.5],8275:[.309,-.191,.556,{ic:.001}],8276:[-.08,.273,.6],8277:[.394,-.096,.335],8278:[.474,-.012,.345],8280:[.474,-.012,.516],8281:[.474,-.012,.516],8282:[.554,-.012,.175],8283:[.594,.028,.476],8284:[.483,-.017,.588],8285:[.554,-.012,.175],8286:[.554,-.012,.175],8319:[.553,-.287,.333],8320:[.347,.055,.389],8353:[.728,.045,.722],8358:[.683,0,.75],8361:[.683,.022,1.028],8362:[.682,.005,1.042],8363:[.694,.03,.556],8369:[.683,0,.681],8370:[.75,.055,.785],8448:[.683,.082,1.141],8449:[.683,.098,1.109],8451:[.705,.022,1.03],8452:[.683,0,.711],8453:[.683,.05,1.141],8454:[.683,.049,1.227],8456:[.706,.022,.72],8457:[.683,0,.98],8468:[.705,.012,.978],8470:[.695,.01,.916],8471:[.683,0,.683],8478:[.683,.022,.736],8479:[.83,.15,.736],8480:[.683,-.247,.883],8481:[.677,0,1.582],8482:[.687,-.277,.983],8483:[.83,.15,.75],8485:[.671,.365,.444],8489:[.441,.013,.25],8494:[.701,.01,.676],8505:[.685,0,.319],8506:[.671,0,.985],8507:[.68,0,1.399],8522:[.694,0,.632],8523:[.716,.022,.778],8524:[.7,.006,.806],8525:[.774,.12,1.019],8526:[.519,0,.46],8527:[.512,.01,1.258],8960:[.597,.097,.778],8961:[.389,0,.902],8962:[.725,-.001,.652],8963:[.802,-.525,.778],8964:[.192,.085,.778],8965:[.27,.155,.778],8966:[.367,.252,.778],8977:[.429,-.072,.572],8978:[.619,-.448,.778],8979:[.604,-.36,.667],8980:[.452,.057,.778],8981:[.599,-.098,.587],8982:[.703,.203,.986],8983:[.58,.086,.778],8984:[.575,.061,.738],8986:[.693,.191,.778],8987:[.698,0,.658],8996:[.266,.022,.778],8997:[.267,0,.759],8998:[.505,.005,.984],8999:[.483,-.017,.726],9e3:[.483,-.007,1.046],9003:[.505,.005,.984],9004:[.755,.131,.93],9005:[.594,.094,.81],9006:[.599,-.055,.655],9007:[.384,-.116,.68],9008:[.758,.011,1.137],9009:[.731,.231,1.15],9010:[.547,.047,.738],9011:[.547,-.23,.738],9012:[.128,0,.5],9013:[.188,.086,.529],9014:[.664,.024,.578],9015:[.666,0,.386],9016:[.773,.083,.618],9017:[.773,.083,.618],9018:[.773,.083,.618],9019:[.773,.083,.618],9020:[.773,.083,.618],9021:[.731,.231,.778],9022:[.592,.092,.796],9023:[.603,.103,.568],9024:[.603,.103,.568],9025:[.773,.083,.618],9026:[.773,.083,.618],9027:[.773,.083,.618],9028:[.773,.083,.618],9029:[.75,.25,.72],9030:[.75,.25,.72],9031:[.773,.083,.618],9032:[.773,.083,.618],9033:[.669,.169,.778],9034:[.684,0,.778],9035:[.81,.19,.778,{ic:.007}],9036:[.773,.083,.618],9037:[.773,.083,.618],9038:[.684,0,.778],9039:[.552,.052,.72],9040:[.773,.083,.618],9041:[.684,0,.778],9042:[.81,.19,.778,{ic:.007}],9043:[.773,.083,.618],9044:[.773,.083,.618],9045:[.684,0,.778],9046:[.552,.052,.72],9047:[.773,.083,.618],9048:[.705,0,.278],9049:[.615,0,.618],9050:[.668,0,.568],9051:[.575,0,.588],9052:[.63,0,.551],9053:[.604,.02,.667],9054:[.773,.083,.618],9055:[.445,-.055,.5],9056:[.773,.083,.618],9057:[.802,.02,.778],9058:[.802,.033,1],9059:[.572,.069,.827],9060:[.552,-.108,.618],9061:[.592,-.055,.5],9062:[.75,.25,.667],9063:[.75,.25,.778],9064:[.592,-.191,.556,{ic:.001}],9065:[.592,.047,.778],9066:[.46,-.007,.278],9067:[.683,.033,1],9068:[.666,.022,.6],9069:[.75,.25,.6],9070:[.431,.193,.33],9071:[.773,.083,.618],9072:[.773,.083,.618],9073:[.568,.017,.642],9074:[.568,.017,.642],9075:[.442,.011,.308],9076:[.442,.216,.488],9077:[.442,.011,.641],9078:[.442,.19,.641],9079:[.453,.19,.471],9080:[.442,.19,.331],9081:[.442,.19,.641],9082:[.442,.011,.641],9083:[.699,.027,.833],9084:[.679,.254,.957],9085:[.174,0,.52],9086:[.454,0,.667],9087:[.75,.25,.278],9088:[.632,.014,.623],9089:[.462,0,.965],9090:[.462,0,1.076],9091:[.528,-.019,1.31],9092:[.483,-.017,.786],9093:[.75,.25,.566],9094:[.571,.071,.756],9095:[.343,0,.767],9096:[.59,.09,.788],9097:[.445,-.055,.5],9098:[.445,-.055,.5],9099:[.464,-.055,.5],9100:[.637,-.108,1.028],9101:[.703,0,.613],9102:[.703,-.001,.589],9103:[.605,.071,.756],9104:[.605,.071,.756],9105:[.605,.071,.756],9106:[.605,.071,.756],9107:[.367,-.133,.778],9108:[.755,.131,.93],9109:[.773,.083,.618],9110:[.452,.004,.738],9111:[.626,0,.748],9112:[.626,0,.748],9113:[.723,.011,.777],9114:[.494,.011,.777],9142:[.469,-.031,1.066],9146:[.472,-.432,.396],9147:[.328,-.288,.396],9148:[.184,-.144,.396],9149:[.04,0,.396],9150:[.75,.05,.444],9151:[.75,.05,.444],9152:[.65,.1,.551],9153:[.65,.1,.551],9154:[.65,.1,.551],9155:[.75,.19,.734],9156:[.75,.19,.734],9157:[.75,.19,.734],9158:[.75,.19,.6],9159:[.75,.19,.65],9160:[.75,.19,.65],9161:[.75,.19,.734],9162:[.75,.19,.734],9163:[.75,.05,.444],9164:[.75,.05,.444],9165:[.612,-.017,.566],9167:[.442,0,.668],9169:[.38,-.208,.5],9170:[.473,-.208,.5],9171:[.47,-.209,.5],9172:[.473,-.208,.848],9173:[.47,-.209,.848],9174:[.38,-.208,.848],9175:[.471,-.229,.636],9176:[.471,-.229,.636],9177:[.471,-.229,.636],9178:[.75,.03,.734],9179:[.388,-.022,.967],9186:[.583,.083,1.184],9187:[.755,.131,.93],9188:[.27,-.23,.856],9189:[.423,.083,1.07],9190:[.442,.011,1],9191:[.859,.083,.948],9192:[.209,.204,.746],9193:[.417,-.083,.591],9194:[.417,-.083,.591],9195:[.52,0,.591],9196:[.52,0,.591],9197:[.417,-.083,.499],9198:[.417,-.083,.645],9199:[.417,-.083,.645],9200:[.693,.146,.778],9201:[.693,.092,.778],9202:[.592,.092,.778],9203:[.698,0,.658],9208:[.415,-.085,.239],9209:[.415,-.085,.428],9210:[.413,-.087,.428],9211:[.481,-.055,.5],9212:[.445,-.055,.5],9213:[.367,-.135,.5],9214:[.582,-.001,.667],9215:[.52,.02,.778],9250:[.694,.011,.556],9251:[.25,.105,.5],11159:[.562,.064,.766],11193:[.508,-.002,.978],11209:[.679,.162,.778],11216:[.583,.083,.778],11217:[.571,.071,.778],11218:[.673,.173,.744],11219:[.463,.313,.778],11220:[.713,0,.674],11221:[.582,0,.712],11222:[.563,.063,.712],11223:[.706,-.023,.485],11224:[.75,0,.614],11225:[.799,.049,.558],11226:[.742,.144,.346],11227:[.778,0,.626],11228:[.752,0,.57],11229:[.662,.25,.712],11230:[.631,.25,.6],11231:[.376,.25,.712],11232:[.77,.135,.801],11233:[.684,.097,.553],11234:[.737,-.013,.841],11235:[.725,-.081,.717],11236:[.77,.082,.801],11237:[.683,.084,.485],11238:[.706,0,.893],11239:[.617,-.031,.717],11240:[.568,.069,.826],11241:[.568,.069,.826],11248:[.7,.02,.585],11249:[.706,-.023,.485],11250:[.712,-.112,.713],11251:[.497,0,.778],11252:[.557,0,.496],11253:[.565,.007,.504],11254:[.497,0,.778],11255:[.564,.007,.325],11256:[.377,.007,.456],11257:[.747,.011,1],11258:[.445,-.055,.952],11259:[.445,-.055,1.212],11260:[.67,.17,.486],11261:[.706,-.023,.485],11262:[.679,-.013,.778],11263:[.683,-.001,.377],11800:[.5,.256,.472],12306:[.622,.018,.793],12310:[.77,.27,.458],12311:[.77,.27,.458],12336:[.319,-.151,1.216],64256:[.705,0,.583,{ic:.045}],64257:[.705,0,.556],64258:[.705,0,.556],64259:[.705,0,.833],64260:[.705,0,.833],64261:[.705,.011,.669],64262:[.708,.011,.783],65126:[.34,-.086,.333],65279:[0,0,0],127:[.66,0,1.418]}}},"NCM"),{8215:{dir:o,stretch:[0,8215],HDW:[-.103,.293,.504],hd:[-.103,.293]}},["MJX-NCM-SY"]);MathJax.loader&&MathJax.loader.checkVersion("[mathjax-newcm]/chtml/dynamic/symbols","4.0.0-beta.7","dynamic-font")})();