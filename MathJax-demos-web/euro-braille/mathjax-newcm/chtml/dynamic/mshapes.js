(()=>{"use strict";const c=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-newcm"].chtml_ts.MathJaxNewcmFont.dynamicSetup("","mshapes",c({MSH:{normal:{128896:[.497,-.003,.356],128897:[.379,-.121,.356,{ic:.071}],128898:[.497,-.003,.356],128899:[.379,-.121,.356,{ic:.071}],128900:[.308,-.192,.278],128901:[.592,.092,.796],128902:[.592,.092,.796],128903:[.592,.092,.796],128904:[.592,.092,.796],128905:[.592,.092,.796],128906:[.592,.092,.778],128907:[.592,.092,.778],128908:[.337,-.161,.316],128909:[.367,-.131,.316],128910:[.686,.18,1.024],128911:[.586,.08,1.024],128912:[.586,.08,1.024],128913:[.586,.08,1.024],128914:[.586,.08,1.024],128915:[.586,.08,1.024],128916:[.586,.08,1.024],128917:[.586,.08,1.024],128918:[.586,.08,1.024],128919:[.342,-.156,.316],128920:[.37,-.127,.316],128921:[.413,-.085,.316,{ic:.006}],128922:[.711,.205,1.024],128923:[.711,.205,1.024],128924:[.711,.205,1.024],128925:[.342,-.156,.316],128926:[.37,-.127,.316],128927:[.413,-.085,.316],128928:[.71,.205,1.024],128929:[.583,.083,.778],128930:[.583,.083,.778],128931:[.583,.083,.778],128932:[.583,.083,.778],128933:[.583,.083,.778],128934:[.583,.083,.778],128935:[.583,.083,.778],128936:[.491,-.009,.778],128937:[.496,-.004,.778],128938:[.501,.001,.778],128939:[.508,.008,.778],128940:[.514,.014,.778],128941:[.521,.021,.778],128942:[.528,.028,.778],128943:[.48,-.015,.5],128944:[.48,-.013,.5],128945:[.48,-.011,.5],128946:[.48,-.01,.5],128947:[.48,-.009,.5],128948:[.48,-.008,.5],128949:[.462,-.039,.5],128950:[.462,-.039,.5],128951:[.462,-.039,.5],128952:[.462,-.039,.5],128953:[.462,-.039,.5],128954:[.462,-.039,.5],128955:[.462,-.039,.5],128956:[.462,-.039,.5],128957:[.462,-.039,.5],128958:[.462,-.039,.5],128959:[.462,-.039,.5],128960:[.509,-.13,.5],128961:[.547,-.115,.5,{ic:.001}],128962:[.566,-.107,.5,{ic:.017}],128963:[.566,-.107,.5,{ic:.017}],128964:[.509,.029,.5,{ic:.019}],128965:[.547,.067,.5,{ic:.057}],128966:[.566,.086,.5,{ic:.076}],128967:[.566,.086,.5,{ic:.076}],128968:[.566,.086,.5,{ic:.076}],128969:[.527,.016,.62],128970:[.524,.013,.625],128971:[.586,.042,.64],128972:[.628,.084,.64],128973:[.628,.084,.64],128974:[.632,.047,.792],128975:[.674,.089,.792],128976:[.694,.109,.792],128977:[.694,.109,.792],128978:[.586,.042,.64],128979:[.628,.083,.64,{ic:.037}],128980:[.628,.083,.754],128981:[.445,-.055,.5],128982:[.445,-.055,.5],128983:[.445,-.055,.5],128984:[.445,-.055,.5],128992:[.592,.092,.796],128993:[.585,.099,.796],128994:[.592,.092,.796],128995:[.592,.092,.796],128996:[.592,.092,.796],128997:[.583,.083,.778],128998:[.583,.083,.778],128999:[.583,.083,.778],129e3:[.583,.083,.778],129001:[.583,.083,.778],129002:[.583,.083,.778],129003:[.583,.083,.778]}}},"NCM"),{},["MJX-NCM-MSH"]);MathJax.loader&&MathJax.loader.checkVersion("[mathjax-newcm]/chtml/dynamic/mshapes","4.0.0-beta.7","dynamic-font")})();