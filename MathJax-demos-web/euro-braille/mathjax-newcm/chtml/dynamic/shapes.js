(()=>{"use strict";const c=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-newcm"].chtml_ts.MathJaxNewcmFont.dynamicSetup("","shapes",c({SH:{normal:{8962:[.725,-.001,.652],8998:[.505,.005,.984],8999:[.483,-.017,.726],9e3:[.483,-.007,1.046],9003:[.505,.005,.984],9211:[.481,-.055,.5],9212:[.445,-.055,.5],9213:[.367,-.135,.5],9214:[.582,-.001,.667],9472:[.274,-.226,.666,{ic:.024}],9473:[.284,-.216,.666,{ic:.023}],9474:[.774,.274,.666],9475:[.773,.273,.666],9476:[.274,-.226,.666,{ic:.024}],9477:[.284,-.216,.666,{ic:.023}],9478:[.774,.274,.666],9479:[.773,.273,.666],9480:[.274,-.226,.666,{ic:.024}],9481:[.284,-.216,.666,{ic:.023}],9482:[.774,.274,.666],9483:[.773,.273,.666],9484:[.274,.274,.666,{ic:.023}],9485:[.284,.274,.666,{ic:.023}],9486:[.274,.273,.666,{ic:.024}],9487:[.284,.273,.666,{ic:.023}],9488:[.274,.274,.666],9489:[.284,.274,.666],9490:[.274,.273,.666],9491:[.284,.273,.666],9492:[.774,-.226,.666,{ic:.024}],9493:[.774,-.216,.666,{ic:.023}],9494:[.773,-.226,.666,{ic:.024}],9495:[.773,-.216,.666,{ic:.023}],9496:[.774,-.226,.666],9497:[.774,-.216,.666],9498:[.773,-.226,.666],9499:[.773,-.216,.666],9500:[.774,.274,.502,{ic:.025}],9501:[.774,.274,.502,{ic:.024}],9502:[.773,.274,.502,{ic:.025}],9503:[.774,.273,.502,{ic:.025}],9504:[.773,.273,.502,{ic:.025}],9505:[.773,.274,.502,{ic:.024}],9506:[.774,.273,.502,{ic:.024}],9507:[.773,.273,.502,{ic:.024}],9508:[.774,.274,.502],9509:[.774,.274,.502],9510:[.773,.274,.502],9511:[.774,.273,.502],9512:[.773,.273,.502],9513:[.773,.274,.502],9514:[.774,.273,.502],9515:[.773,.273,.502],9516:[.274,.274,.666,{ic:.024}],9517:[.274,.274,.666,{ic:.024}],9518:[.274,.274,.666,{ic:.023}],9519:[.284,.274,.666,{ic:.023}],9520:[.274,.273,.666,{ic:.024}],9521:[.274,.273,.666,{ic:.024}],9522:[.274,.273,.666,{ic:.023}],9523:[.274,.273,.666,{ic:.023}],9524:[.774,-.226,.666,{ic:.024}],9525:[.774,-.226,.666,{ic:.024}],9526:[.774,-.226,.666,{ic:.023}],9527:[.774,-.226,.666,{ic:.023}],9528:[.773,-.226,.666,{ic:.024}],9529:[.773,-.226,.666,{ic:.024}],9530:[.773,-.226,.666,{ic:.023}],9531:[.773,-.226,.666,{ic:.023}],9532:[.774,.274,.666,{ic:.024}],9533:[.774,.274,.666,{ic:.024}],9534:[.774,.274,.666,{ic:.023}],9535:[.774,.274,.666,{ic:.023}],9536:[.773,.274,.666,{ic:.024}],9537:[.774,.273,.666,{ic:.024}],9538:[.773,.273,.666,{ic:.024}],9539:[.773,.274,.666,{ic:.024}],9540:[.773,.274,.666,{ic:.023}],9541:[.774,.273,.666,{ic:.024}],9542:[.774,.273,.666,{ic:.023}],9543:[.773,.274,.666,{ic:.023}],9544:[.774,.273,.666,{ic:.023}],9545:[.773,.273,.666,{ic:.024}],9546:[.773,.273,.666,{ic:.023}],9547:[.773,.273,.666,{ic:.023}],9548:[.274,-.226,.666,{ic:.024}],9549:[.284,-.216,.666,{ic:.023}],9550:[.774,.274,.666],9551:[.773,.273,.666],9552:[.324,-.176,.666,{ic:.024}],9553:[.774,.274,.666],9554:[.324,.274,.666,{ic:.024}],9555:[.274,.274,.666,{ic:.024}],9556:[.324,.274,.666,{ic:.024}],9557:[.324,.274,.666],9558:[.274,.274,.666],9559:[.324,.274,.666],9560:[.784,-.226,.666,{ic:.024}],9561:[.774,-.226,.666,{ic:.024}],9562:[.774,-.176,.666,{ic:.024}],9563:[.784,-.226,.666],9564:[.774,-.226,.666],9565:[.774,-.176,.666],9566:[.774,.274,.502,{ic:.025}],9567:[.774,.274,.502,{ic:.025}],9568:[.774,.274,.502,{ic:.025}],9569:[.774,.274,.502],9570:[.774,.274,.502],9571:[.774,.274,.502],9572:[.324,.274,.666,{ic:.024}],9573:[.274,.274,.666,{ic:.024}],9574:[.324,.274,.666,{ic:.024}],9575:[.774,-.176,.666,{ic:.024}],9576:[.774,-.226,.666,{ic:.024}],9577:[.774,-.176,.666,{ic:.024}],9578:[.774,.274,.502,{ic:.025}],9579:[.774,.274,.666,{ic:.024}],9580:[.774,.274,.666,{ic:.024}],9581:[.274,.275,.666,{ic:.024}],9582:[.274,.275,.666],9583:[.775,-.226,.666],9584:[.775,-.226,.666,{ic:.024}],9585:[.754,.254,.5],9586:[.754,.254,.5],9587:[.754,.254,.5],9588:[.274,-.226,.666],9589:[.774,-.346,.666],9590:[.274,-.226,.666,{ic:.024}],9591:[.154,.274,.666],9592:[.284,-.216,.666],9593:[.773,-.347,.666],9594:[.284,-.216,.666,{ic:.023}],9595:[.153,.273,.666],9596:[.284,-.216,.666,{ic:.023}],9597:[.774,.273,.666],9598:[.284,-.216,.666,{ic:.024}],9599:[.773,.274,.666],9600:[.583,-.237,.778],9601:[.083,0,.664],9602:[.166,0,.664],9603:[.249,0,.664],9604:[.332,0,.664],9605:[.415,0,.664],9606:[.498,0,.664],9607:[.581,0,.664],9608:[.664,0,.664],9609:[.664,0,.664],9610:[.664,0,.664],9611:[.664,0,.664],9612:[.666,0,.778],9613:[.664,0,.664],9614:[.664,0,.664],9615:[.664,0,.664],9616:[.666,0,.778],9617:[.664,0,.664],9618:[.664,0,.664],9619:[.664,0,.664],9620:[.664,-.581,.664],9621:[.664,0,.664],9622:[.332,0,.664],9623:[.332,0,.664],9624:[.664,-.332,.664],9625:[.664,0,.664],9626:[.664,0,.664],9627:[.664,0,.664],9628:[.664,0,.664],9629:[.664,-.332,.664],9630:[.664,0,.664],9631:[.664,0,.664],9634:[.583,.083,.778],9635:[.583,.083,.778],9636:[.583,.083,.778],9637:[.583,.083,.778],9638:[.583,.083,.778],9639:[.583,.083,.778],9640:[.583,.083,.778],9641:[.583,.083,.778],9644:[.417,-.084,.778],9645:[.417,-.084,.778],9646:[.666,0,.466],9647:[.666,0,.466],9648:[.423,0,1.17],9649:[.423,0,1.17],9672:[.713,.213,.778,{ic:.075}],9673:[.701,.201,1.013],9676:[.622,.122,.796],9677:[.592,.092,.796],9678:[.592,.092,.796],9680:[.592,.092,.796],9681:[.592,.092,.796],9682:[.592,.092,.796],9683:[.592,.092,.796],9684:[.592,.092,.796],9685:[.592,.092,.796],9686:[.592,.092,.442],9687:[.592,.092,.442],9688:[.595,.097,.792],9689:[.686,.18,1.024],9690:[.686,-.252,1.024],9691:[.252,.182,1.024],9692:[.89,-.5,.6],9693:[.89,-.5,.6],9694:[.5,-.11,.6],9695:[.5,-.11,.6],9696:[.89,-.5,1],9697:[.5,-.11,1],9698:[.583,.083,.778],9699:[.583,.083,.778],9700:[.583,.083,.778],9701:[.583,.083,.778],9703:[.583,.083,.778],9704:[.583,.083,.778],9705:[.583,.083,.778],9706:[.583,.083,.778],9707:[.583,.083,.778],9708:[.688,0,.613],9709:[.748,0,.973],9710:[.748,0,.973],9712:[.583,.083,.778],9713:[.583,.083,.778],9714:[.583,.083,.778],9715:[.583,.083,.778],9716:[.594,.09,.848],9717:[.594,.09,.848],9718:[.594,.09,.848],9719:[.594,.09,.848],9728:[.603,.103,.846],9733:[.568,.069,.827],9734:[.568,.069,.827],9737:[.592,.092,.778],9761:[.543,.065,.778],9773:[.593,.001,.7],9785:[.701,.201,1.013],9786:[.701,.201,1.013],9787:[.701,.201,1.013],9788:[.603,.103,.846],9789:[.701,.201,.667],9790:[.701,.201,.667],9792:[.445,.238,.5],9794:[.599,-.055,.655],9824:[.727,.13,.778],9825:[.716,.033,.778],9826:[.727,.163,.778],9827:[.727,.13,.778],9828:[.727,.13,.778],9829:[.716,.033,.778],9830:[.727,.163,.778],9831:[.727,.13,.778],9833:[.695,.029,.442],9834:[.695,.029,.611],9835:[.695,.199,.898],9837:[.75,.022,.388],9838:[.728,.217,.388],9839:[.716,.216,.388],9854:[.701,.201,1.013],9856:[.533,.033,.706],9857:[.533,.033,.706],9858:[.533,.033,.706],9859:[.533,.033,.706],9860:[.533,.033,.706],9861:[.533,.033,.706],9862:[.592,.092,.796],9863:[.592,.092,.796],9864:[.592,.092,.796],9865:[.592,.092,.796],9893:[.599,.238,.654],9898:[.592,.092,.796],9899:[.592,.092,.796],9900:[.445,-.055,.5],9901:[.467,-.036,.5,{ic:.077}],9902:[.606,.104,.5,{ic:.188}],9906:[.716,.19,.664],10003:[.699,.027,.833],10013:[.602,.026,.5],10016:[.684,0,.796],10026:[.696,.116,1.013],10038:[.638,.099,.8],10045:[.634,.03,.575],10098:[.748,.248,.278],10099:[.748,.248,.278],10139:[.51,.01,1],10145:[.469,-.031,.977],11026:[.583,.083,.778],11027:[.583,.083,.778],11028:[.583,.083,.778],11029:[.583,.083,.778],11030:[.727,.163,.778],11031:[.727,.163,.778],11032:[.727,.163,.778],11033:[.727,.163,.778],11034:[.64,.24,.96],11035:[.686,.18,1.024],11036:[.686,.18,1.024],11037:[.217,-.001,.316],11038:[.218,-.001,.316],11039:[.636,0,.827],11040:[.634,0,.794],11041:[.639,.1,.8],11042:[.639,.1,.8],11043:[.639,0,.857],11044:[.701,.201,1.013],11045:[.621,.121,1.025],11046:[.621,.121,1.025],11047:[.538,.038,.572],11048:[.538,.038,.572],11049:[.546,.046,1.025],11050:[.48,-.02,.572],11051:[.48,-.02,.572],11052:[.592,.092,1.186],11053:[.592,.092,1.186],11054:[.763,.263,.764],11055:[.763,.263,.764],11088:[.504,.006,.827],11089:[.453,-.046,.486],11090:[.453,-.046,.486],11091:[.62,.049,.827],11092:[.619,.049,.794],11093:[.701,.201,1.013],11094:[.506,.006,.778],11095:[.592,.092,.778],11096:[.592,.092,.778],11097:[.592,.092,.778],11194:[.56,.06,.712],11195:[.56,.06,.712],11196:[.56,.06,.712],11197:[.483,-.017,.566],11198:[.592,.092,.778],11199:[.592,.092,.778],11200:[.4,-.1,.41],11201:[.454,-.046,.51],11202:[.417,-.081,.414],11203:[.439,-.061,.51],11204:[.451,-.048,.51],11205:[.637,-.099,.626],11206:[.637,-.099,.626],11207:[.676,-.06,.626],11208:[.676,-.06,.626],11210:[.529,-.255,.656],11211:[.255,.019,.656],11212:[.454,-.045,.51],11213:[.402,-.098,.51],11214:[.454,-.045,.51],11215:[.402,-.098,.51],11242:[.568,.069,.827],11243:[.568,.069,.827]}},SHB:{bold:{9474:[.75,.25,.319],9553:[.75,.25,.575],9773:[.593,.001,.7],9792:[.455,.205,.575],9834:[.695,.036,.703],9901:[.474,-.028,.575,{ic:.1}],9902:[.615,.113,.575,{ic:.228}],9906:[.455,.205,.575],10013:[.607,.025,.575]}},SHI:{italic:{9773:[.593,.001,.7],9834:[.695,.03,.562],9901:[.466,-.036,.511,{ic:.117}],9902:[.607,.105,.511,{ic:.22}],10013:[.602,.027,.511,{ic:.051}]}},SHBI:{"bold-italic":{9773:[.593,.001,.7],9834:[.695,.04,.648],9901:[.474,-.029,.591,{ic:.133}],9902:[.616,.113,.591,{ic:.252}],10013:[.607,.025,.591,{ic:.054}]}}},"NCM"),{},["MJX-NCM-SH","MJX-NCM-SHB","MJX-NCM-SHI","MJX-NCM-SHBI"]);MathJax.loader&&MathJax.loader.checkVersion("[mathjax-newcm]/chtml/dynamic/shapes","4.0.0-beta.7","dynamic-font")})();