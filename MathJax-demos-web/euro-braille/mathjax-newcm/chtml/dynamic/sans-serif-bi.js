(()=>{"use strict";const c=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-newcm"].chtml_ts.MathJaxNewcmFont.dynamicSetup("","sans-serif-bi",c({SSLIB:{"sans-serif-bold-italic":{192:[.924,0,.733],193:[.912,0,.733,{ic:.001}],194:[.934,0,.733],195:[.884,0,.733],196:[.899,0,.733,{ic:.006}],197:[.953,0,.733],198:[.694,0,.947,{ic:.085}],199:[.706,.202,.703,{ic:.073}],200:[.924,0,.642,{ic:.084}],201:[.912,0,.642,{ic:.084}],202:[.934,0,.642,{ic:.084}],203:[.899,0,.642,{ic:.084}],204:[.924,0,.331,{ic:.055}],205:[.912,0,.331,{ic:.211}],206:[.934,0,.331,{ic:.184}],207:[.899,0,.331,{ic:.216}],208:[.694,0,.794,{ic:.023}],209:[.884,0,.794,{ic:.055}],210:[.924,.022,.794,{ic:.026}],211:[.912,.022,.794,{ic:.026}],212:[.934,.022,.794,{ic:.026}],213:[.884,.022,.794,{ic:.026}],214:[.899,.022,.794,{ic:.026}],216:[.756,.061,.856,{ic:.024}],217:[.924,.022,.764,{ic:.055}],218:[.912,.022,.764,{ic:.055}],219:[.934,.022,.764,{ic:.055}],220:[.899,.022,.764,{ic:.055}],221:[.912,0,.733,{ic:.143}],222:[.694,0,.642,{ic:.027}],223:[.706,.011,.565,{ic:.061}],224:[.667,.01,.493,{ic:.014}],225:[.668,.01,.493,{ic:.061}],226:[.69,.01,.493,{ic:.034}],227:[.64,.01,.493,{ic:.037}],228:[.655,.01,.493,{ic:.066}],229:[.709,.01,.493,{ic:.014}],230:[.46,.01,.794],231:[.475,.214,.489,{ic:.052}],232:[.667,.01,.48,{ic:.032}],233:[.668,.01,.48,{ic:.066}],234:[.69,.01,.48,{ic:.039}],235:[.655,.01,.48,{ic:.071}],236:[.673,0,.235,{ic:.113}],237:[.668,0,.235,{ic:.215}],238:[.69,0,.235,{ic:.188}],239:[.655,0,.235,{ic:.22}],241:[.64,0,.527,{ic:.033}],242:[.667,.01,.517,{ic:.025}],243:[.668,.01,.517,{ic:.04}],244:[.69,.01,.517,{ic:.025}],245:[.666,.01,.517,{ic:.067}],246:[.655,.01,.517,{ic:.045}],248:[.54,.095,.55,{ic:.017}],249:[.667,.01,.527,{ic:.037}],250:[.668,.01,.527,{ic:.046}],251:[.69,.01,.527,{ic:.037}],252:[.655,.01,.527,{ic:.051}],253:[.661,.167,.47,{ic:.081}],254:[.666,.186,.541,{ic:.019}],255:[.648,.167,.47,{ic:.086}],256:[.891,0,.733,{ic:.033}],257:[.647,.01,.493,{ic:.093}],258:[.951,0,.733,{ic:.031}],259:[.707,.01,.493,{ic:.091}],260:[.694,.213,.733],261:[.46,.219,.493,{ic:.014}],262:[.912,.011,.703,{ic:.073}],263:[.668,.01,.459,{ic:.103}],264:[.934,.011,.703,{ic:.073}],265:[.69,.01,.459,{ic:.076}],266:[.881,.011,.703,{ic:.073}],267:[.637,.01,.459,{ic:.053}],268:[.941,.011,.703,{ic:.073}],269:[.697,.01,.459,{ic:.107}],270:[.941,0,.794,{ic:.023}],271:[.754,.01,.527,{ic:.288}],272:[.694,0,.794,{ic:.023}],273:[.654,.01,.561,{ic:.092}],274:[.891,0,.642,{ic:.088}],275:[.647,.01,.48,{ic:.098}],276:[.951,0,.642,{ic:.086}],277:[.707,.01,.48,{ic:.096}],278:[.881,0,.642,{ic:.084}],279:[.637,.01,.48,{ic:.032}],280:[.692,.218,.642,{ic:.084}],281:[.46,.226,.48,{ic:.032}],282:[.941,0,.642,{ic:.084}],283:[.697,.01,.48,{ic:.07}],284:[.934,.011,.733,{ic:.055}],285:[.69,.18,.527,{ic:.04}],286:[.951,.011,.733,{ic:.062}],287:[.707,.18,.527,{ic:.068}],288:[.881,.011,.733,{ic:.055}],289:[.637,.18,.527,{ic:.04}],290:[.706,.331,.733,{ic:.055}],291:[.808,.18,.527,{ic:.04}],292:[.934,0,.794,{ic:.055}],293:[.93,0,.527,{ic:.015}],294:[.694,0,.853,{ic:.091}],295:[.673,0,.525,{ic:.033}],296:[.884,0,.331,{ic:.187}],297:[.639,0,.235,{ic:.172}],298:[.891,0,.331,{ic:.243}],299:[.646,0,.235,{ic:.227}],300:[.951,0,.331,{ic:.241}],301:[.706,0,.235,{ic:.226}],302:[.694,.215,.331,{ic:.055}],303:[.653,.216,.24,{ic:.08}],304:[.881,0,.331,{ic:.086}],306:[.694,.022,.798,{ic:.073}],307:[.653,.167,.507,{ic:.08}],308:[.934,.022,.519,{ic:.157}],309:[.696,.178,.286,{ic:.145}],310:[.694,.301,.764,{ic:.085}],311:[.653,.302,.499,{ic:.052}],312:[.444,0,.499,{ic:.052}],313:[.912,0,.581],314:[.908,0,.24,{ic:.265}],315:[.694,.303,.581],316:[.653,.306,.24,{ic:.08}],317:[.754,0,.581,{ic:.144}],318:[.754,0,.24,{ic:.295}],319:[.694,0,.581,{ic:.017}],320:[.653,0,.475,{ic:.046}],321:[.694,0,.675],322:[.653,0,.584,{ic:.055}],323:[.912,0,.794,{ic:.055}],324:[.668,0,.527,{ic:.037}],325:[.694,.299,.794,{ic:.055}],326:[.455,.299,.527,{ic:.015}],327:[.941,0,.794,{ic:.055}],328:[.697,0,.527,{ic:.041}],329:[.722,0,.682,{ic:.015}],330:[.716,.021,.794],331:[.455,.186,.543,{ic:.015}],332:[.891,.022,.794,{ic:.026}],333:[.647,.01,.517,{ic:.072}],334:[.951,.022,.794,{ic:.026}],335:[.707,.01,.517,{ic:.07}],336:[.937,.022,.794,{ic:.026}],337:[.693,.01,.517,{ic:.076}],338:[.717,.022,1.069,{ic:.085}],339:[.46,.01,.802,{ic:.033}],340:[.912,0,.703,{ic:.047}],341:[.668,0,.349,{ic:.164}],342:[.694,.301,.703,{ic:.047}],343:[.454,.313,.349,{ic:.081}],344:[.941,0,.703,{ic:.047}],345:[.697,0,.349,{ic:.168}],346:[.912,.022,.611,{ic:.045}],347:[.668,.01,.396,{ic:.118}],348:[.934,.022,.611,{ic:.045}],349:[.69,.01,.396,{ic:.091}],350:[.717,.207,.611,{ic:.045}],351:[.46,.193,.396,{ic:.052}],352:[.941,.022,.611,{ic:.045}],353:[.697,.01,.396,{ic:.122}],354:[.689,.187,.733,{ic:.105}],355:[.571,.193,.38,{ic:.047}],356:[.941,0,.733,{ic:.105}],357:[.794,.01,.38,{ic:.224}],358:[.689,0,.733,{ic:.105}],359:[.571,.01,.38,{ic:.047}],360:[.884,.022,.764,{ic:.055}],361:[.64,.01,.527,{ic:.037}],362:[.891,.022,.764,{ic:.055}],363:[.647,.01,.527,{ic:.078}],364:[.951,.022,.764,{ic:.055}],365:[.707,.01,.527,{ic:.076}],366:[.953,.022,.764,{ic:.055}],367:[.709,.01,.527,{ic:.037}],368:[.95,.022,.764,{ic:.055}],369:[.706,.01,.527,{ic:.096}],370:[.694,.234,.764,{ic:.055}],371:[.444,.218,.527,{ic:.037}],372:[.934,0,1.039,{ic:.131}],373:[.69,0,.699,{ic:.079}],374:[.934,0,.733,{ic:.143}],375:[.683,.167,.47,{ic:.078}],376:[.899,0,.733,{ic:.143}],377:[.912,0,.672,{ic:.085}],378:[.668,0,.447,{ic:.096}],379:[.881,0,.672,{ic:.085}],380:[.637,0,.447,{ic:.059}],381:[.941,0,.672,{ic:.085}],382:[.697,0,.447,{ic:.1}],383:[.664,0,.316,{ic:.18}],384:[.674,.011,.543,{ic:.023}],385:[.694,0,.903,{ic:.021}],386:[.694,0,.733,{ic:.006}],387:[.653,.01,.527,{ic:.029}],388:[.694,0,.733],389:[.695,.01,.527,{ic:.024}],390:[.717,.022,.703,{ic:.029}],391:[.917,.011,.703,{ic:.291}],392:[.671,.01,.562,{ic:.169}],393:[.694,0,.904,{ic:.023}],394:[.694,0,.964,{ic:.023}],395:[.694,0,.733,{ic:.085}],396:[.653,.01,.527,{ic:.104}],397:[.454,.278,.472,{ic:.024}],398:[.692,0,.642,{ic:.099}],399:[.717,.022,.758,{ic:.04}],400:[.717,.022,.611,{ic:.071}],401:[.692,.177,.611,{ic:.099}],402:[.664,.177,.316,{ic:.18}],403:[.917,.011,.703,{ic:.311}],404:[.681,.206,.685,{ic:.145}],405:[.653,.011,.805,{ic:.033}],406:[.694,.011,.278,{ic:.01}],407:[.694,0,.495,{ic:.05}],408:[.699,0,.795,{ic:.12}],409:[.717,0,.499,{ic:.084}],410:[.695,0,.428,{ic:.062}],411:[.722,0,.5,{ic:.052}],412:[.694,.005,.815,{ic:.09}],413:[.694,.175,.794,{ic:.055}],414:[.455,.194,.527,{ic:.015}],415:[.717,.022,.794,{ic:.025}],416:[.824,.022,.794,{ic:.142}],417:[.563,.01,.517,{ic:.182}],418:[.717,.194,1.026,{ic:.083}],419:[.46,.194,.741,{ic:.05}],420:[.694,0,.883,{ic:.045}],421:[.717,.157,.527,{ic:.13}],422:[.694,.192,.714,{ic:.028}],423:[.717,.022,.611,{ic:.039}],424:[.46,.01,.396,{ic:.043}],425:[.693,0,.794,{ic:.085}],426:[.717,.217,.419,{ic:.007}],427:[.571,.217,.347,{ic:.08}],428:[.689,0,.903,{ic:.105}],429:[.633,.01,.38,{ic:.134}],430:[.689,.177,.611,{ic:.227}],431:[.786,.022,.764,{ic:.194}],432:[.563,.011,.561,{ic:.176}],433:[.716,0,.794,{ic:.102}],434:[.694,.022,.718,{ic:.047}],435:[.699,0,.833,{ic:.123}],436:[.444,.167,.47,{ic:.078}],437:[.694,0,.672,{ic:.085}],438:[.444,0,.447,{ic:.059}],439:[.694,.022,.598,{ic:.111}],440:[.694,.022,.598,{ic:.114}],441:[.458,.205,.476,{ic:.062}],442:[.458,.206,.476,{ic:.06}],443:[.717,0,.55,{ic:.055}],444:[.694,.022,.62,{ic:.055}],445:[.459,.257,.62,{ic:.005}],446:[.571,.01,.47,{ic:.023}],447:[.487,.209,.5,{ic:.042}],448:[.694,.195,.306,{ic:.063}],449:[.694,.195,.5,{ic:.069}],450:[.694,.195,.614,{ic:.015}],451:[.694,0,.367,{ic:.029}],452:[.941,0,1.465,{ic:.085}],453:[.697,0,1.244,{ic:.1}],454:[.697,.011,1.037,{ic:.08}],455:[.694,.022,1.1,{ic:.055}],456:[.694,.178,.867,{ic:.086}],457:[.694,.178,.542,{ic:.086}],458:[.694,.022,1.313,{ic:.055}],459:[.694,.178,1.08,{ic:.086}],460:[.694,.178,.847,{ic:.086}],461:[.904,0,.733],462:[.697,.01,.493,{ic:.065}],463:[.904,0,.331,{ic:.2}],464:[.697,0,.256,{ic:.184}],465:[.904,.022,.794,{ic:.025}],466:[.697,.011,.55,{ic:.038}],467:[.904,.022,.764,{ic:.055}],468:[.697,.01,.527,{ic:.05}],469:[1.053,.022,.764,{ic:.076}],470:[.807,.01,.527,{ic:.112}],471:[1.074,.022,.764,{ic:.055}],472:[.828,.01,.527,{ic:.08}],473:[1.103,.022,.764,{ic:.055}],474:[.857,.01,.527,{ic:.084}],475:[1.097,.022,.764,{ic:.055}],476:[.872,.011,.561,{ic:.039}],477:[.46,.01,.48,{ic:.027}],478:[1.05,0,.733,{ic:.061}],479:[.798,.01,.493,{ic:.121}],480:[1.058,0,.733,{ic:.059}],481:[.787,.01,.493,{ic:.115}],482:[.888,0,.947,{ic:.085}],483:[.618,.01,.794],484:[.706,.011,.733,{ic:.055}],485:[.455,.18,.527,{ic:.04}],486:[.931,.011,.733,{ic:.055}],487:[.676,.18,.527,{ic:.048}],488:[.931,0,.764,{ic:.085}],489:[.916,0,.499,{ic:.052}],490:[.717,.227,.794,{ic:.026}],491:[.46,.22,.517,{ic:.025}],492:[.888,.227,.794,{ic:.026}],493:[.648,.22,.517,{ic:.071}],494:[.931,.022,.598,{ic:.111}],495:[.674,.205,.476,{ic:.072}],496:[.682,.178,.286,{ic:.171}],497:[.694,0,1.466,{ic:.085}],498:[.694,0,1.243,{ic:.059}],499:[.653,.01,.979,{ic:.059}],500:[.92,.011,.733,{ic:.055}],501:[.668,.18,.527,{ic:.046}],502:[.694,.011,1.045,{ic:.043}],503:[.697,.198,.71,{ic:.037}],504:[.9,0,.794,{ic:.055}],505:[.667,0,.527,{ic:.015}],506:[1.188,0,.733,{ic:.064}],507:[.928,.01,.493,{ic:.115}],508:[.92,0,.947,{ic:.085}],509:[.638,.01,.794],510:[.92,.061,.856,{ic:.024}],511:[.682,.095,.55,{ic:.017}],512:[.92,0,.733],513:[.709,.01,.493,{ic:.014}],514:[.925,0,.733],515:[.683,.01,.493,{ic:.055}],516:[.92,0,.642,{ic:.084}],517:[.709,.01,.48,{ic:.032}],518:[.925,0,.642,{ic:.084}],519:[.683,.01,.48,{ic:.06}],520:[.92,0,.331,{ic:.197}],521:[.708,0,.235,{ic:.134}],522:[.925,0,.331,{ic:.207}],523:[.682,0,.235,{ic:.189}],524:[.92,.022,.794,{ic:.026}],525:[.709,.01,.517,{ic:.025}],526:[.925,.022,.794,{ic:.026}],527:[.683,.01,.517,{ic:.034}],528:[.92,0,.703,{ic:.047}],529:[.709,0,.349,{ic:.102}],530:[.925,0,.703,{ic:.047}],531:[.683,0,.349,{ic:.158}],532:[.92,.022,.764,{ic:.055}],533:[.709,.01,.527,{ic:.037}],534:[.925,.022,.764,{ic:.055}],535:[.683,.01,.527,{ic:.04}],536:[.717,.334,.611,{ic:.045}],537:[.46,.305,.396,{ic:.052}],538:[.689,.305,.733,{ic:.105}],539:[.571,.305,.38,{ic:.047}],540:[.69,.054,.579,{ic:.023}],541:[.457,.197,.48,{ic:.01}],542:[.931,0,.794,{ic:.055}],543:[.916,0,.527,{ic:.015}],544:[.685,.196,.718,{ic:.054}],545:[.653,.157,.66],546:[.708,.011,.5,{ic:.091}],547:[.656,.011,.5,{ic:.108}],548:[.694,.192,.67,{ic:.087}],549:[.444,.192,.546],550:[.882,0,.733],551:[.625,.01,.493,{ic:.014}],552:[.692,.196,.642,{ic:.084}],553:[.46,.204,.48,{ic:.032}],554:[1.05,.022,.794,{ic:.026}],555:[.808,.01,.517,{ic:.096}],556:[1.102,.022,.794,{ic:.026}],557:[.868,.01,.517,{ic:.112}],558:[.882,.022,.794,{ic:.026}],559:[.625,.01,.517,{ic:.025}],560:[1.058,.022,.794,{ic:.026}],561:[.823,.01,.517,{ic:.113}],562:[.888,0,.733,{ic:.143}],563:[.641,.167,.47,{ic:.103}],564:[.653,.157,.24,{ic:.121}],565:[.455,.157,.661],566:[.571,.157,.38,{ic:.047}],568:[.674,.011,.888,{ic:.021}],569:[.454,.231,.888,{ic:.021}],570:[.802,.122,.733,{ic:.156}],571:[.802,.122,.733,{ic:.076}],572:[.592,.122,.459,{ic:.13}],573:[.694,0,.661],574:[.802,.122,.733,{ic:.126}],575:[.46,.206,.432,{ic:.016}],576:[.444,.19,.447,{ic:.059}],577:[.705,0,.55,{ic:.052}],578:[.465,0,.55,{ic:.001}],579:[.694,0,.763,{ic:.021}],580:[.694,.022,.824,{ic:.042}],581:[.694,0,.733],582:[.802,.122,.642,{ic:.157}],583:[.592,.122,.48,{ic:.109}],584:[.694,.022,.595,{ic:.05}],585:[.653,.167,.269,{ic:.094}],586:[.712,.215,.687,{ic:.032}],587:[.454,.218,.527,{ic:.051}],588:[.694,0,.753,{ic:.047}],589:[.454,0,.349,{ic:.081}],590:[.694,0,.733,{ic:.143}],591:[.444,.167,.47,{ic:.078}],11377:[.469,0,.562,{ic:.078}],7680:[.694,.3,.733],7681:[.46,.252,.493,{ic:.014}],7682:[.882,0,.733,{ic:.021}],7683:[.818,.01,.527,{ic:.024}],7684:[.694,.159,.733,{ic:.021}],7685:[.653,.176,.527,{ic:.024}],7686:[.694,.18,.733,{ic:.021}],7687:[.653,.178,.527,{ic:.024}],7688:[.92,.202,.703,{ic:.073}],7689:[.668,.214,.489,{ic:.079}],7690:[.882,0,.794,{ic:.023}],7691:[.818,.01,.527,{ic:.082}],7692:[.694,.154,.794,{ic:.023}],7693:[.653,.182,.527,{ic:.082}],7694:[.694,.175,.794,{ic:.023}],7695:[.653,.184,.527,{ic:.082}],7696:[.694,.197,.794,{ic:.023}],7697:[.653,.202,.527,{ic:.082}],7698:[.694,.216,.794,{ic:.023}],7699:[.653,.217,.527,{ic:.082}],7704:[.692,.179,.642,{ic:.084}],7705:[.46,.231,.48,{ic:.032}],7706:[.692,.227,.642,{ic:.084}],7707:[.46,.181,.48,{ic:.032}],7710:[.882,0,.611,{ic:.099}],7711:[.829,0,.316,{ic:.183}],7712:[.888,.011,.733,{ic:.055}],7713:[.648,.18,.527,{ic:.07}],7714:[.882,0,.794,{ic:.055}],7715:[.818,0,.527,{ic:.015}],7716:[.694,.156,.794,{ic:.055}],7717:[.653,.155,.527,{ic:.015}],7718:[.894,0,.794,{ic:.055}],7719:[.889,0,.527,{ic:.015}],7722:[.694,.226,.794,{ic:.055}],7723:[.653,.202,.527,{ic:.015}],7724:[.694,.227,.331,{ic:.055}],7725:[.653,.169,.24,{ic:.08}],7726:[1.082,0,.331,{ic:.262}],7727:[.828,0,.235,{ic:.251}],7728:[.92,0,.764,{ic:.085}],7729:[.909,0,.499,{ic:.052}],7730:[.694,.155,.764,{ic:.085}],7731:[.653,.156,.499,{ic:.052}],7732:[.694,.176,.764,{ic:.085}],7733:[.653,.158,.499,{ic:.052}],7734:[.694,.157,.581],7735:[.653,.16,.24,{ic:.08}],7736:[.867,.157,.581],7737:[.888,.16,.24,{ic:.296}],7738:[.694,.178,.581],7739:[.653,.162,.24,{ic:.08}],7740:[.694,.219,.581],7741:[.653,.222,.24,{ic:.08}],7742:[.92,0,.978,{ic:.054}],7743:[.668,0,.815,{ic:.014}],7744:[.882,0,.978,{ic:.054}],7745:[.625,0,.815,{ic:.014}],7746:[.694,.165,.978,{ic:.054}],7747:[.455,.159,.815,{ic:.014}],7748:[.882,0,.794,{ic:.055}],7749:[.625,0,.527,{ic:.015}],7750:[.694,.153,.794,{ic:.055}],7751:[.455,.153,.527,{ic:.015}],7752:[.694,.174,.794,{ic:.055}],7753:[.455,.155,.527,{ic:.015}],7754:[.694,.179,.794,{ic:.055}],7755:[.455,.215,.527,{ic:.015}],7764:[.92,0,.703,{ic:.045}],7765:[.668,.157,.527,{ic:.094}],7766:[.882,0,.703,{ic:.045}],7767:[.625,.157,.527,{ic:.024}],7768:[.882,0,.703,{ic:.047}],7769:[.625,0,.349,{ic:.081}],7770:[.694,.155,.703,{ic:.047}],7771:[.454,.167,.349,{ic:.081}],7772:[.888,.155,.703,{ic:.047}],7773:[.648,.167,.349,{ic:.193}],7774:[.694,.176,.703,{ic:.047}],7775:[.454,.169,.349,{ic:.081}],7776:[.882,.022,.611,{ic:.045}],7777:[.625,.01,.396,{ic:.052}],7778:[.717,.188,.611,{ic:.045}],7779:[.46,.159,.396,{ic:.052}],7784:[.882,.188,.611,{ic:.045}],7785:[.625,.159,.396,{ic:.052}],7786:[.882,0,.733,{ic:.105}],7787:[.736,.01,.38,{ic:.047}],7788:[.689,.159,.733,{ic:.105}],7789:[.571,.159,.38,{ic:.047}],7790:[.689,.18,.733,{ic:.105}],7791:[.571,.161,.38,{ic:.047}],7792:[.689,.179,.733,{ic:.105}],7793:[.571,.221,.38,{ic:.047}],7794:[.694,.225,.764,{ic:.055}],7795:[.444,.217,.527,{ic:.037}],7796:[.694,.249,.764,{ic:.055}],7797:[.444,.173,.527,{ic:.037}],7798:[.694,.201,.764,{ic:.055}],7799:[.444,.223,.527,{ic:.037}],7804:[.955,0,.733,{ic:.131}],7805:[.664,0,.47,{ic:.098}],7806:[.694,.199,.733,{ic:.131}],7807:[.444,.176,.47,{ic:.078}],7808:[.9,0,1.039,{ic:.131}],7809:[.667,0,.699,{ic:.079}],7810:[.92,0,1.039,{ic:.131}],7811:[.668,0,.699,{ic:.079}],7812:[.894,0,1.039,{ic:.131}],7813:[.65,0,.699,{ic:.079}],7814:[.882,0,1.039,{ic:.131}],7815:[.625,0,.699,{ic:.079}],7816:[.694,.153,1.039,{ic:.131}],7817:[.444,.158,.699,{ic:.079}],7818:[.882,0,.733,{ic:.085}],7819:[.625,0,.47,{ic:.076}],7820:[.894,0,.733,{ic:.085}],7821:[.652,0,.47,{ic:.076}],7822:[.882,0,.733,{ic:.143}],7823:[.625,.167,.47,{ic:.078}],7824:[.923,0,.672,{ic:.085}],7825:[.668,0,.447,{ic:.059}],7826:[.694,.161,.672,{ic:.085}],7827:[.444,.159,.447,{ic:.059}],7828:[.694,.182,.672,{ic:.085}],7829:[.444,.161,.447,{ic:.059}],7830:[.653,.157,.527,{ic:.015}],7831:[.82,.01,.38,{ic:.1}],7832:[.703,0,.699,{ic:.079}],7833:[.703,.167,.47,{ic:.078}],7835:[.829,0,.316,{ic:.183}],7838:[.706,.011,.647,{ic:.039}],7840:[.694,.155,.733],7841:[.46,.155,.493,{ic:.014}],7842:[1.003,0,.733],7843:[.785,.01,.493,{ic:.041}],7844:[1.122,0,.733,{ic:.049}],7845:[.858,.01,.493,{ic:.07}],7846:[1.102,0,.733],7847:[.897,.01,.493,{ic:.034}],7848:[.965,0,.733],7849:[1.015,.01,.493,{ic:.09}],7850:[1.157,0,.733,{ic:.069}],7851:[.856,.01,.493,{ic:.088}],7852:[.928,.155,.733,{ic:.003}],7853:[.668,.155,.493,{ic:.029}],7854:[1.12,0,.733,{ic:.048}],7855:[.868,.01,.493,{ic:.091}],7856:[1.1,0,.733,{ic:.031}],7857:[.914,.01,.493,{ic:.091}],7858:[.982,0,.733,{ic:.031}],7859:[1.032,.01,.493,{ic:.094}],7860:[1.155,0,.733,{ic:.068}],7861:[.866,.01,.493,{ic:.091}],7862:[.978,.155,.733,{ic:.051}],7863:[.692,.155,.493,{ic:.089}],7864:[.692,.157,.642,{ic:.084}],7865:[.46,.169,.48,{ic:.032}],7866:[1.003,0,.642,{ic:.084}],7867:[.785,.01,.48,{ic:.062}],7868:[.955,0,.642,{ic:.084}],7869:[.666,.01,.48,{ic:.083}],7870:[1.119,0,.642,{ic:.104}],7871:[.868,.01,.48,{ic:.107}],7872:[1.099,0,.642,{ic:.084}],7873:[.897,.01,.48,{ic:.039}],7874:[.965,0,.642,{ic:.084}],7875:[1.015,.01,.48,{ic:.095}],7876:[1.154,0,.642,{ic:.124}],7877:[.866,.01,.48,{ic:.126}],7878:[.923,.157,.642,{ic:.084}],7879:[.668,.169,.48,{ic:.05}],7880:[1.003,0,.331,{ic:.152}],7881:[.785,0,.235,{ic:.171}],7882:[.694,.159,.331,{ic:.055}],7883:[.653,.157,.24,{ic:.08}],7884:[.717,.198,.794,{ic:.026}],7885:[.46,.167,.517,{ic:.025}],7886:[1.003,.022,.794,{ic:.026}],7887:[.785,.01,.517,{ic:.029}],7888:[1.12,.022,.794,{ic:.026}],7889:[.87,.01,.517,{ic:.081}],7890:[1.1,.022,.794,{ic:.026}],7891:[.897,.01,.517,{ic:.025}],7892:[1.203,.022,.794,{ic:.026}],7893:[1.015,.01,.517,{ic:.069}],7894:[1.155,.022,.794,{ic:.028}],7895:[.868,.01,.517,{ic:.1}],7896:[.919,.198,.794,{ic:.026}],7897:[.668,.167,.517,{ic:.025}],7898:[.945,.022,.794,{ic:.142}],7899:[.668,.01,.517,{ic:.182}],7900:[.925,.022,.794,{ic:.142}],7901:[.71,.01,.517,{ic:.182}],7902:[1.028,.022,.794,{ic:.142}],7903:[.8,.01,.517,{ic:.182}],7904:[.98,.022,.794,{ic:.142}],7905:[.666,.01,.517,{ic:.182}],7906:[.824,.187,.794,{ic:.142}],7907:[.563,.167,.517,{ic:.182}],7908:[.694,.179,.764,{ic:.055}],7909:[.444,.161,.527,{ic:.037}],7910:[.973,.022,.764,{ic:.055}],7911:[.785,.01,.527,{ic:.037}],7912:[.923,.022,.764,{ic:.194}],7913:[.668,.011,.561,{ic:.176}],7914:[.903,.022,.764,{ic:.194}],7915:[.7,.011,.561,{ic:.176}],7916:[1.006,.022,.764,{ic:.194}],7917:[.812,.011,.561,{ic:.176}],7918:[.958,.022,.764,{ic:.194}],7919:[.666,.011,.561,{ic:.176}],7920:[.786,.185,.764,{ic:.194}],7921:[.563,.16,.561,{ic:.176}],7922:[.85,0,.733,{ic:.143}],7923:[.667,.167,.47,{ic:.078}],7924:[.694,.155,.733,{ic:.143}],7925:[.444,.287,.47,{ic:.078}],7926:[.953,0,.733,{ic:.143}],7927:[.785,.167,.47,{ic:.078}],7928:[.905,0,.733,{ic:.143}],7929:[.659,.167,.47,{ic:.098}]}}},"NCM"),{},["MJX-NCM-SSLIB"]);MathJax.loader&&MathJax.loader.checkVersion("[mathjax-newcm]/chtml/dynamic/sans-serif-bi","4.0.0-beta.7","dynamic-font")})();