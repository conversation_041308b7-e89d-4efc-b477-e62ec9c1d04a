(()=>{"use strict";const c=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-newcm"].chtml_ts.MathJaxNewcmFont.dynamicSetup("","greek-ss",c({GKSS:{"sans-serif":{880:[.694,0,.611],881:[.529,0,.414],882:[.689,0,.68],883:[.523,0,.5],884:[.694,-.495,.194],885:[0,.2,.194,{ic:.002}],886:[.694,0,.708],887:[.529,0,.555],890:[-.067,.238,.111],891:[.461,.011,.444],892:[.461,.011,.444],893:[.461,.011,.444],894:[.444,.127,.278],895:[.694,.022,.472],900:[.694,-.495,.167],901:[.694,-.489,.264],902:[.694,0,.667],903:[.448,-.357,.278],904:[.694,0,.764],905:[.694,0,.875],906:[.694,0,.445],908:[.716,.022,.819],910:[.716,0,.945],911:[.716,0,.805],912:[.694,.011,.278],938:[.822,0,.278],939:[.822,0,.778],940:[.694,.011,.633],941:[.694,.011,.43],942:[.694,.195,.514],943:[.694,.011,.264],944:[.694,.011,.5],970:[.625,.011,.264],971:[.625,.011,.5],972:[.694,.011,.528],973:[.694,.011,.5],974:[.694,.011,.667],975:[.694,.261,.694],976:[.733,.011,.51],979:[.716,0,.945],980:[.822,0,.778],983:[.458,.268,.555],984:[.705,0,.5],985:[.613,.064,.5],986:[.689,0,.548],987:[.468,0,.5],988:[.691,0,.569],989:[.445,.195,.5],990:[.693,.034,.389],991:[.583,.145,.389],992:[.702,0,.639],993:[.722,0,.722],994:[.716,.23,.916],995:[.467,.227,.819],996:[.694,.189,.643],997:[.436,.278,.553],998:[.683,.206,.703],999:[.459,.193,.553],1e3:[.668,.162,.51],1001:[.468,.162,.51],1002:[.695,0,.787],1003:[.434,0,.567],1004:[.691,.011,.539],1005:[.471,.011,.539],1006:[.694,0,.753],1007:[.654,0,.514],1010:[.461,.011,.444],1011:[.655,.205,.267],1015:[.684,0,.639],1016:[.716,.195,.472],1017:[.706,.011,.639],1018:[.694,0,.875],1019:[.445,.205,.757],1020:[.455,.195,.472],1021:[.706,.011,.639],1022:[.706,.011,.639],1023:[.706,.011,.639],7936:[.694,.011,.633],7937:[.694,.011,.633],7938:[.694,.011,.633],7939:[.694,.011,.633],7940:[.694,.011,.633],7941:[.694,.011,.633],7942:[.694,.011,.567,{ic:.044}],7943:[.694,.011,.633],7944:[.694,0,.75],7945:[.694,0,.722],7946:[.694,0,.862],7947:[.694,0,.862],7948:[.694,0,.862],7949:[.694,0,.862],7950:[.694,0,.889],7951:[.694,0,.889],7952:[.694,.011,.43],7953:[.694,.011,.43],7954:[.694,.011,.43],7955:[.694,.011,.43],7956:[.694,.011,.43],7957:[.694,.011,.43],7960:[.694,0,.819],7961:[.694,0,.819],7962:[.694,0,.875],7963:[.694,0,.875],7964:[.694,0,.875],7965:[.694,0,.875],7968:[.694,.195,.514],7969:[.694,.195,.514],7970:[.694,.195,.514],7971:[.694,.195,.514],7972:[.694,.195,.514],7973:[.694,.195,.514],7974:[.694,.195,.514],7975:[.694,.195,.514],7976:[.694,0,.93],7977:[.694,0,.93],7978:[.694,0,.986],7979:[.694,0,.986],7980:[.694,0,.986],7981:[.694,0,.986],7982:[.694,0,1.041],7983:[.694,0,1.041],7984:[.694,.011,.264],7985:[.694,.011,.264],7986:[.694,.011,.264],7987:[.694,.011,.264],7988:[.694,.011,.264],7989:[.694,.011,.264],7990:[.694,.011,.264],7991:[.694,.011,.264],7992:[.694,0,.5],7993:[.694,0,.5],7994:[.694,0,.556],7995:[.694,0,.556],7996:[.694,0,.556],7997:[.694,0,.556],7998:[.694,0,.611],7999:[.694,0,.611],8e3:[.694,.011,.528],8001:[.694,.011,.528],8002:[.694,.011,.528],8003:[.694,.011,.528],8004:[.694,.011,.528],8005:[.694,.011,.528],8008:[.716,.022,.902],8009:[.716,.022,.902],8010:[.716,.022,.986],8011:[.716,.022,.986],8012:[.716,.022,.986],8013:[.716,.022,.986],8016:[.694,.011,.5],8017:[.694,.011,.5],8018:[.694,.011,.5],8019:[.694,.011,.5],8020:[.694,.011,.5],8021:[.694,.011,.5],8022:[.694,.011,.5],8023:[.694,.011,.5],8025:[.716,0,1],8027:[.716,0,1.056],8029:[.716,0,1.056],8031:[.716,0,1.111],8032:[.694,.011,.667],8033:[.694,.011,.667],8034:[.694,.011,.667],8035:[.694,.011,.667],8036:[.694,.011,.667],8037:[.694,.011,.667],8038:[.694,.011,.667],8039:[.694,.011,.667],8040:[.716,0,.916],8041:[.716,0,.916],8042:[.716,0,1],8043:[.716,0,1],8044:[.716,0,1],8045:[.716,0,1],8046:[.716,0,1.055],8047:[.716,0,1.055],8048:[.694,.011,.633],8049:[.694,.011,.633],8050:[.694,.011,.43],8051:[.694,.011,.43],8052:[.694,.195,.514],8053:[.694,.195,.514],8054:[.694,.011,.264],8055:[.694,.011,.264],8056:[.694,.011,.528],8057:[.694,.011,.528],8058:[.694,.011,.5],8059:[.694,.011,.5],8060:[.694,.011,.667],8061:[.694,.011,.667],8064:[.694,.238,.633],8065:[.694,.238,.633],8066:[.694,.238,.633],8067:[.694,.238,.633],8068:[.694,.238,.633],8069:[.694,.238,.633],8070:[.694,.238,.633],8071:[.694,.238,.633],8072:[.694,.223,.75],8073:[.694,.223,.722],8074:[.694,.223,.862],8075:[.694,.223,.862],8076:[.694,.223,.862],8077:[.694,.223,.862],8078:[.694,.223,.889],8079:[.694,.223,.889],8080:[.694,.238,.514],8081:[.694,.238,.514],8082:[.694,.238,.514],8083:[.694,.238,.514],8084:[.694,.238,.514],8085:[.694,.238,.514],8086:[.694,.238,.514],8087:[.694,.238,.514],8088:[.694,.223,.93],8089:[.694,.223,.93],8090:[.694,.223,.986],8091:[.694,.223,.986],8092:[.694,.223,.986],8093:[.694,.223,.986],8094:[.694,.223,1.041],8095:[.694,.223,1.041],8096:[.694,.238,.667],8097:[.694,.238,.667],8098:[.694,.238,.667],8099:[.694,.238,.667],8100:[.694,.238,.667],8101:[.694,.238,.667],8102:[.694,.238,.667],8103:[.694,.238,.667],8104:[.716,.223,.916],8105:[.716,.223,.916],8106:[.716,.223,1],8107:[.716,.223,1],8108:[.716,.223,1],8109:[.716,.223,1],8110:[.716,.223,1.055],8111:[.716,.223,1.055],8112:[.707,.011,.633],8113:[.575,.011,.633],8114:[.694,.238,.633],8115:[.455,.238,.633],8116:[.694,.238,.633],8118:[.625,.011,.633],8119:[.625,.238,.633],8120:[.962,0,.667],8121:[.83,0,.667],8122:[.694,0,.723],8123:[.694,0,.667],8124:[.694,.155,.811],8125:[.694,-.511,.222],8126:[.155,.155,.222],8127:[.694,-.511,.222],8128:[.625,-.535,.333],8129:[.694,-.512,.444],8130:[.694,.238,.514],8131:[.467,.238,.514],8132:[.694,.238,.514],8134:[.625,.195,.514],8135:[.625,.238,.514],8136:[.694,0,.764],8137:[.694,0,.764],8138:[.694,0,.875],8139:[.694,0,.875],8140:[.694,.155,.853],8141:[.694,-.495,.278,{ic:.017}],8142:[.694,-.495,.278,{ic:.009}],8143:[.694,-.489,.333],8144:[.707,.011,.278],8145:[.575,.011,.278],8146:[.694,.011,.264],8147:[.694,.011,.264],8150:[.625,.011,.264],8151:[.694,.011,.264],8152:[.962,0,.278,{ic:.055}],8153:[.83,0,.278,{ic:.055}],8154:[.694,0,.445],8155:[.694,0,.445],8157:[.694,-.495,.278,{ic:.017}],8158:[.694,-.495,.278,{ic:.01}],8159:[.694,-.489,.333],8160:[.707,.011,.5],8161:[.575,.011,.5],8162:[.694,.011,.5],8163:[.694,.011,.5],8164:[.694,.195,.472],8165:[.694,.195,.472],8166:[.625,.011,.5],8167:[.694,.011,.5],8168:[.962,0,.778],8169:[.83,0,.778],8170:[.716,0,.945],8171:[.716,0,.945],8172:[.694,0,.861],8173:[.694,-.495,.278],8174:[.694,-.495,.278],8175:[.694,-.495,.167,{ic:.01}],8178:[.694,.238,.667],8179:[.445,.238,.667],8180:[.694,.238,.667],8182:[.625,.011,.667],8183:[.625,.238,.667],8184:[.716,.022,.847],8185:[.716,.022,.819],8186:[.716,0,.861],8187:[.716,0,.805],8188:[.716,.155,.878],8189:[.694,-.495,.167],8190:[.694,-.511,.222]}},GKSSB:{"bold-sans-serif":{880:[.694,0,.645],881:[.522,0,.485],882:[.689,0,.733],883:[.516,0,.549],884:[.694,-.503,.214,{ic:.003}],885:[0,.191,.214,{ic:.001}],886:[.694,0,.794],887:[.522,0,.588],890:[-.068,.257,.11],891:[.46,.01,.459],892:[.46,.01,.459],893:[.46,.01,.459],894:[.458,.104,.305],895:[.694,.022,.519],900:[.694,-.503,.183,{ic:.024}],901:[.694,-.508,.318,{ic:.05}],902:[.694,0,.733],903:[.458,-.327,.305],904:[.694,0,.825],905:[.694,0,.977],906:[.694,0,.508],908:[.717,.022,.885],910:[.716,0,1.038],911:[.716,0,.885],912:[.694,.011,.278,{ic:.04}],938:[.871,0,.325,{ic:.025}],939:[.871,0,.855],940:[.694,.011,.633],941:[.694,.011,.43],942:[.694,.195,.514],943:[.694,.011,.278],944:[.694,.011,.5],970:[.625,.011,.264],971:[.625,.011,.5],972:[.694,.011,.528],973:[.694,.011,.5],974:[.694,.011,.71],975:[.694,.261,.694],976:[.733,.011,.51],979:[.716,0,.945],980:[.852,0,.778],983:[.458,.273,.555],984:[.705,0,.5],985:[.613,.064,.5],986:[.522,.205,.439],987:[.468,0,.5],988:[.691,0,.569],989:[.445,.195,.5],990:[.697,.038,.389],991:[.583,.145,.389],992:[.734,0,.639],993:[.722,0,.722],994:[.716,.235,.916],995:[.467,.232,.819],996:[.697,.189,.643],997:[.44,.278,.553],998:[.683,.206,.703],999:[.459,.193,.553],1e3:[.668,.162,.51],1001:[.468,.162,.51],1002:[.695,0,.787],1003:[.434,0,.567],1004:[.691,.011,.539],1005:[.471,.011,.539],1006:[.694,0,.753],1007:[.654,0,.514],1010:[.461,.011,.444],1011:[.655,.205,.267],1015:[.684,0,.639],1016:[.716,.195,.472],1017:[.706,.011,.639],1018:[.694,0,.875],1019:[.445,.205,.757],1020:[.455,.195,.472],1021:[.706,.011,.639],1022:[.706,.011,.639],1023:[.706,.011,.639],7936:[.703,.011,.633],7937:[.703,.011,.633],7938:[.706,.011,.633],7939:[.706,.011,.633],7940:[.706,.011,.633],7941:[.706,.011,.633],7942:[.78,.011,.633],7943:[.78,.011,.633],7944:[.717,0,.733],7945:[.717,0,.733],7946:[.717,0,.967],7947:[.717,0,.967],7948:[.717,0,.897],7949:[.717,0,.897],7950:[.717,0,.894],7951:[.717,0,.894],7952:[.703,.011,.43],7953:[.703,.011,.43],7954:[.706,.011,.43],7955:[.706,.011,.43],7956:[.706,.011,.43],7957:[.706,.011,.43],7960:[.717,0,.744],7961:[.717,0,.744],7962:[.717,0,.988],7963:[.717,0,.988],7964:[.717,0,.988],7965:[.717,0,.988],7968:[.71,.195,.514],7969:[.71,.195,.514],7970:[.713,.195,.514],7971:[.713,.195,.514],7972:[.713,.195,.514],7973:[.713,.195,.514],7974:[.819,.195,.514],7975:[.819,.195,.514],7976:[.717,0,.896],7977:[.717,0,.896],7978:[.717,0,1.14],7979:[.717,0,1.14],7980:[.717,0,1.14],7981:[.717,0,1.14],7982:[.717,0,1.137],7983:[.717,0,1.137],7984:[.703,.011,.278],7985:[.703,.011,.278],7986:[.706,.011,.278,{ic:.001}],7987:[.706,.011,.278,{ic:.001}],7988:[.706,.011,.278,{ic:.031}],7989:[.706,.011,.278,{ic:.031}],7990:[.812,.011,.278],7991:[.812,.011,.278],7992:[.717,0,.433],7993:[.717,0,.433],7994:[.717,0,.677],7995:[.717,0,.677],7996:[.717,0,.677],7997:[.717,0,.677],7998:[.717,0,.674],7999:[.717,0,.674],8e3:[.703,.011,.528],8001:[.703,.011,.528],8002:[.706,.011,.528],8003:[.706,.011,.528],8004:[.706,.011,.528],8005:[.706,.011,.528],8008:[.717,.022,.895],8009:[.717,.022,.881],8010:[.717,.022,1.155],8011:[.717,.022,1.145],8012:[.717,.022,1.065],8013:[.717,.022,1.045],8016:[.703,.011,.5],8017:[.703,.011,.5],8018:[.706,.011,.5],8019:[.706,.011,.5],8020:[.706,.011,.5],8021:[.706,.011,.5],8022:[.812,.011,.5],8023:[.812,.011,.5],8025:[.717,0,.988],8027:[.717,0,1.232],8029:[.717,0,1.182],8031:[.717,0,1.199],8032:[.703,.011,.667],8033:[.703,.011,.667],8034:[.706,.011,.667],8035:[.706,.011,.667],8036:[.706,.011,.667],8037:[.706,.011,.667],8038:[.812,.011,.667],8039:[.812,.011,.667],8040:[.717,0,.9],8041:[.717,0,.89],8042:[.717,0,1.17],8043:[.717,0,1.171],8044:[.717,0,1.084],8045:[.717,0,1.084],8046:[.717,0,1.111],8047:[.717,0,1.121],8048:[.706,.011,.633],8049:[.706,.011,.633],8050:[.706,.011,.43],8051:[.706,.011,.43],8052:[.713,.195,.514],8053:[.713,.195,.514],8054:[.706,.011,.278],8055:[.706,.011,.278],8056:[.706,.011,.528],8057:[.706,.011,.528],8058:[.706,.011,.5],8059:[.706,.011,.5],8060:[.706,.011,.667],8061:[.706,.011,.667],8064:[.703,.26,.633],8065:[.703,.26,.633],8066:[.706,.26,.633],8067:[.706,.26,.633],8068:[.706,.26,.633],8069:[.706,.26,.633],8070:[.78,.26,.633],8071:[.78,.26,.633],8072:[.717,.26,.733],8073:[.717,.26,.733],8074:[.717,.26,.967],8075:[.717,.26,.967],8076:[.717,.26,.897],8077:[.717,.26,.897],8078:[.717,.26,.894],8079:[.717,.26,.894],8080:[.71,.249,.514],8081:[.71,.249,.514],8082:[.713,.249,.514],8083:[.713,.249,.514],8084:[.713,.249,.514],8085:[.713,.249,.514],8086:[.819,.249,.514],8087:[.819,.249,.514],8088:[.717,.26,.896],8089:[.717,.26,.896],8090:[.717,.26,1.14],8091:[.717,.26,1.14],8092:[.717,.26,1.14],8093:[.717,.26,1.14],8094:[.717,.26,1.137],8095:[.717,.26,1.137],8096:[.703,.26,.667],8097:[.703,.26,.667],8098:[.706,.26,.667],8099:[.706,.26,.667],8100:[.706,.26,.667],8101:[.706,.26,.667],8102:[.812,.26,.667],8103:[.812,.26,.667],8104:[.717,.26,.9],8105:[.717,.26,.89],8106:[.717,.26,1.17],8107:[.717,.26,1.171],8108:[.717,.26,1.084],8109:[.717,.26,1.084],8110:[.717,.26,1.111],8111:[.717,.26,1.121],8112:[.692,.011,.633],8113:[.605,.011,.633],8114:[.706,.26,.633],8115:[.455,.26,.633],8116:[.694,.26,.633],8118:[.65,.011,.633],8119:[.65,.26,.633],8120:[.919,0,.733],8121:[.862,0,.733],8123:[.717,0,.733],8124:[.694,.26,.733],8125:[.694,-.511,.222],8126:[.154,.154,.2],8127:[.694,-.511,.222],8128:[.694,-.564,.374],8129:[.694,-.429,.367,{ic:.007}],8130:[.713,.249,.514],8131:[.467,.249,.514],8132:[.694,.249,.514],8134:[.657,.195,.514],8135:[.657,.249,.514],8136:[.717,0,.642],8137:[.717,0,.642],8138:[.717,0,.794],8139:[.717,0,.794],8140:[.694,.26,.794],8141:[.694,-.508,.38],8142:[.694,-.508,.38],8143:[.694,-.402,.367,{ic:.007}],8144:[.692,.011,.278],8145:[.605,.011,.278,{ic:.011}],8146:[.719,.011,.278,{ic:.03}],8147:[.719,.011,.278,{ic:.03}],8150:[.65,.011,.278],8151:[.785,.011,.278],8152:[.919,0,.331,{ic:.022}],8153:[.862,0,.331,{ic:.032}],8154:[.717,0,.331],8155:[.717,0,.331],8157:[.694,-.508,.38],8158:[.694,-.508,.38],8159:[.694,-.402,.222,{ic:.152}],8160:[.692,.011,.5],8161:[.605,.011,.5],8162:[.719,.011,.5],8163:[.719,.011,.5],8164:[.703,.195,.472],8165:[.703,.195,.472],8166:[.65,.011,.5],8167:[.785,.011,.5],8168:[.919,0,.855],8169:[.862,0,.855],8170:[.717,0,1.055],8171:[.717,0,1.015],8172:[.717,0,.805],8173:[.694,-.495,.305,{ic:.065}],8174:[.694,-.495,.305,{ic:.065}],8175:[.694,-.508,.278],8178:[.706,.26,.667],8179:[.445,.26,.667],8180:[.694,.26,.71],8182:[.65,.011,.667],8183:[.65,.26,.667],8184:[.717,.022,.968],8185:[.717,.022,.888],8186:[.717,0,.987],8187:[.717,0,.917],8188:[.716,.26,.794],8189:[.694,-.508,.278],8190:[.694,-.511,.222]}},GKSSI:{"sans-serif-italic":{880:[.694,0,.611],881:[.529,0,.414],882:[.689,0,.68,{ic:.036}],883:[.523,0,.5,{ic:.019}],884:[.694,-.495,.194,{ic:.141}],885:[0,.2,.194,{ic:.004}],886:[.694,0,.708],887:[.529,0,.555],890:[-.067,.239,.111],891:[.461,.011,.444],892:[.461,.011,.444,{ic:.008}],893:[.461,.011,.444],894:[.444,.127,.278],895:[.694,.022,.472],900:[.694,-.495,.167,{ic:.145}],901:[.694,-.495,.278,{ic:.118}],902:[.694,0,.667],903:[.444,-.361,.278],904:[.694,0,.764,{ic:.092}],905:[.694,0,.875,{ic:.054}],906:[.694,0,.445,{ic:.054}],908:[.716,.022,.819,{ic:.027}],910:[.716,0,.945,{ic:.065}],911:[.716,0,.805,{ic:.047}],912:[.694,.011,.264,{ic:.08}],938:[.822,0,.278,{ic:.146}],939:[.822,0,.778,{ic:.065}],940:[.694,.011,.633],941:[.694,.011,.43,{ic:.042}],942:[.694,.195,.514,{ic:.041}],943:[.694,.011,.264,{ic:.08}],944:[.694,.011,.5,{ic:.016}],970:[.625,.011,.264,{ic:.079}],971:[.625,.011,.5,{ic:.016}],972:[.694,.011,.472,{ic:.02}],973:[.694,.011,.5,{ic:.016}],974:[.694,.011,.667,{ic:.009}],975:[.694,.261,.694,{ic:.097}],976:[.733,.011,.51,{ic:.024}],979:[.716,0,.945,{ic:.065}],980:[.822,0,.778,{ic:.065}],983:[.458,.268,.555,{ic:.059}],984:[.705,0,.5,{ic:.073}],985:[.613,.064,.5,{ic:.046}],986:[.689,0,.548,{ic:.108}],987:[.468,0,.5,{ic:.043}],988:[.691,0,.569,{ic:.105}],989:[.445,.195,.5,{ic:.039}],990:[.693,.034,.389,{ic:.042}],991:[.583,.145,.389,{ic:.004}],992:[.702,0,.639],993:[.722,0,.722],994:[.716,.23,.916,{ic:.025}],995:[.467,.227,.819],996:[.694,.189,.643,{ic:.091}],997:[.436,.278,.553,{ic:.045}],998:[.683,.206,.703],999:[.459,.193,.553],1e3:[.668,.162,.51,{ic:.054}],1001:[.468,.162,.51,{ic:.006}],1002:[.695,0,.787,{ic:.073}],1003:[.434,0,.567,{ic:.027}],1004:[.691,.011,.539,{ic:.08}],1005:[.471,.011,.539,{ic:.033}],1006:[.694,0,.753,{ic:.084}],1007:[.654,0,.514,{ic:.058}],1010:[.461,.011,.444,{ic:.056}],1011:[.655,.205,.267,{ic:.056}],1015:[.684,0,.639,{ic:.046}],1016:[.716,.195,.472,{ic:.011}],1017:[.706,.011,.639,{ic:.08}],1018:[.694,0,.875],1019:[.445,.205,.757],1020:[.455,.195,.472,{ic:.01}],1021:[.706,.011,.639,{ic:.03}],1022:[.706,.011,.639,{ic:.08}],1023:[.706,.011,.639,{ic:.03}],7936:[.694,.011,.633],7937:[.694,.011,.633],7938:[.694,.011,.633],7939:[.694,.011,.633],7940:[.694,.011,.633],7941:[.694,.011,.633],7942:[.694,.011,.567,{ic:.009}],7943:[.694,.011,.633],7944:[.694,0,.75],7945:[.694,0,.722],7946:[.694,0,.862],7947:[.694,0,.862],7948:[.694,0,.862],7949:[.694,0,.862],7950:[.694,0,.889],7951:[.694,0,.889],7952:[.694,.011,.43,{ic:.027}],7953:[.694,.011,.43,{ic:.027}],7954:[.694,.011,.43,{ic:.045}],7955:[.694,.011,.43,{ic:.045}],7956:[.694,.011,.43,{ic:.082}],7957:[.694,.011,.43,{ic:.082}],7960:[.694,0,.819,{ic:.092}],7961:[.694,0,.819,{ic:.092}],7962:[.694,0,.875,{ic:.092}],7963:[.694,0,.875,{ic:.092}],7964:[.694,0,.875,{ic:.092}],7965:[.694,0,.875,{ic:.092}],7968:[.694,.195,.514,{ic:.02}],7969:[.694,.195,.514,{ic:.02}],7970:[.694,.195,.514,{ic:.044}],7971:[.694,.195,.514,{ic:.044}],7972:[.694,.195,.514,{ic:.081}],7973:[.694,.195,.514,{ic:.081}],7974:[.694,.195,.514,{ic:.099}],7975:[.694,.195,.514,{ic:.099}],7976:[.694,0,.93,{ic:.054}],7977:[.694,0,.93,{ic:.054}],7978:[.694,0,.986,{ic:.054}],7979:[.694,0,.986,{ic:.054}],7980:[.694,0,.986,{ic:.054}],7981:[.694,0,.986,{ic:.054}],7982:[.694,0,1.041,{ic:.054}],7983:[.694,0,1.041,{ic:.054}],7984:[.694,.011,.264,{ic:.045}],7985:[.694,.011,.264,{ic:.009}],7986:[.694,.011,.264,{ic:.083}],7987:[.694,.011,.264,{ic:.083}],7988:[.694,.011,.264,{ic:.12}],7989:[.694,.011,.264,{ic:.12}],7990:[.694,.011,.264,{ic:.138}],7991:[.694,.011,.264,{ic:.138}],7992:[.694,0,.5,{ic:.054}],7993:[.694,0,.5,{ic:.054}],7994:[.694,0,.556,{ic:.054}],7995:[.694,0,.556,{ic:.054}],7996:[.694,0,.556,{ic:.054}],7997:[.694,0,.556,{ic:.054}],7998:[.694,0,.611,{ic:.054}],7999:[.694,0,.611,{ic:.054}],8e3:[.694,.011,.472,{ic:.011}],8001:[.694,.011,.472,{ic:.011}],8002:[.694,.011,.472,{ic:.023}],8003:[.694,.011,.472,{ic:.023}],8004:[.694,.011,.472,{ic:.06}],8005:[.694,.011,.472,{ic:.06}],8008:[.716,.022,.902,{ic:.027}],8009:[.716,.022,.902,{ic:.027}],8010:[.716,.022,.986,{ic:.027}],8011:[.716,.022,.986,{ic:.027}],8012:[.716,.022,.986,{ic:.027}],8013:[.716,.022,.986,{ic:.027}],8016:[.694,.011,.5,{ic:.016}],8017:[.694,.011,.5,{ic:.016}],8018:[.694,.011,.5,{ic:.016}],8019:[.694,.011,.5,{ic:.016}],8020:[.694,.011,.5,{ic:.037}],8021:[.694,.011,.5,{ic:.037}],8022:[.694,.011,.5,{ic:.055}],8023:[.694,.011,.5,{ic:.055}],8025:[.716,0,1,{ic:.065}],8027:[.716,0,1.056,{ic:.065}],8029:[.716,0,1.056,{ic:.065}],8031:[.716,0,1.111,{ic:.065}],8032:[.694,.011,.667,{ic:.009}],8033:[.694,.011,.667,{ic:.009}],8034:[.694,.011,.667,{ic:.009}],8035:[.694,.011,.667,{ic:.009}],8036:[.694,.011,.667,{ic:.009}],8037:[.694,.011,.667,{ic:.009}],8038:[.694,.011,.667,{ic:.035}],8039:[.694,.011,.667,{ic:.035}],8040:[.716,0,.916,{ic:.047}],8041:[.716,0,.916,{ic:.047}],8042:[.716,0,1,{ic:.047}],8043:[.716,0,1,{ic:.047}],8044:[.716,0,1,{ic:.047}],8045:[.716,0,1,{ic:.047}],8046:[.716,0,1.055,{ic:.047}],8047:[.716,0,1.055,{ic:.047}],8048:[.694,.011,.633],8049:[.694,.011,.633],8050:[.694,.011,.43,{ic:.027}],8051:[.694,.011,.43,{ic:.042}],8052:[.694,.195,.514,{ic:.02}],8053:[.694,.195,.514,{ic:.041}],8054:[.694,.011,.264,{ic:.009}],8055:[.694,.011,.264,{ic:.08}],8056:[.694,.011,.472,{ic:.011}],8057:[.694,.011,.472,{ic:.02}],8058:[.694,.011,.5,{ic:.016}],8059:[.694,.011,.5,{ic:.016}],8060:[.694,.011,.667,{ic:.009}],8061:[.694,.011,.667,{ic:.009}],8064:[.694,.239,.633],8065:[.694,.239,.633],8066:[.694,.239,.633],8067:[.694,.239,.633],8068:[.694,.239,.633],8069:[.694,.239,.633],8070:[.694,.239,.633],8071:[.694,.239,.633],8072:[.694,.224,.75],8073:[.694,.224,.722],8074:[.694,.224,.862],8075:[.694,.224,.862],8076:[.694,.224,.862],8077:[.694,.224,.862],8078:[.694,.224,.889],8079:[.694,.224,.889],8080:[.694,.239,.514,{ic:.02}],8081:[.694,.239,.514,{ic:.02}],8082:[.694,.239,.514,{ic:.043}],8083:[.694,.239,.514,{ic:.044}],8084:[.694,.239,.514,{ic:.08}],8085:[.694,.239,.514,{ic:.08}],8086:[.694,.239,.514,{ic:.098}],8087:[.694,.239,.514,{ic:.098}],8088:[.694,.224,.93,{ic:.054}],8089:[.694,.224,.93,{ic:.054}],8090:[.694,.224,.986,{ic:.054}],8091:[.694,.224,.986,{ic:.054}],8092:[.694,.224,.986,{ic:.054}],8093:[.694,.224,.986,{ic:.054}],8094:[.694,.224,1.041,{ic:.054}],8095:[.694,.224,1.041,{ic:.054}],8096:[.694,.239,.667,{ic:.009}],8097:[.694,.239,.667,{ic:.009}],8098:[.694,.239,.667,{ic:.009}],8099:[.694,.239,.667,{ic:.009}],8100:[.694,.239,.667,{ic:.009}],8101:[.694,.239,.667,{ic:.009}],8102:[.694,.239,.667,{ic:.035}],8103:[.694,.239,.667,{ic:.035}],8104:[.716,.224,.916,{ic:.047}],8105:[.716,.224,.916,{ic:.047}],8106:[.716,.224,1,{ic:.047}],8107:[.716,.224,1,{ic:.047}],8108:[.716,.224,1,{ic:.047}],8109:[.716,.224,1,{ic:.047}],8110:[.716,.224,1.055,{ic:.047}],8111:[.716,.224,1.055,{ic:.047}],8112:[.687,.011,.633],8113:[.591,.011,.633],8114:[.694,.239,.633],8115:[.455,.239,.633],8116:[.694,.239,.633],8118:[.625,.011,.633],8119:[.625,.239,.633],8120:[.961,0,.667,{ic:.022}],8121:[.865,0,.667,{ic:.005}],8122:[.694,0,.723],8123:[.694,0,.667],8124:[.694,.155,.811],8125:[.694,-.511,.222,{ic:.11}],8126:[.155,.155,.222],8127:[.694,-.511,.222,{ic:.11}],8128:[.625,-.535,.333,{ic:.133}],8129:[.694,-.512,.444,{ic:.093}],8130:[.694,.239,.514,{ic:.02}],8131:[.467,.239,.514,{ic:.02}],8132:[.694,.239,.514,{ic:.041}],8134:[.625,.195,.514,{ic:.084}],8135:[.625,.239,.514,{ic:.084}],8136:[.694,0,.764,{ic:.092}],8137:[.694,0,.764,{ic:.092}],8138:[.694,0,.875,{ic:.054}],8139:[.694,0,.875,{ic:.054}],8140:[.694,.155,.853],8141:[.694,-.495,.278,{ic:.12}],8142:[.694,-.495,.278,{ic:.158}],8143:[.694,-.489,.333,{ic:.147}],8144:[.687,.011,.278,{ic:.119}],8145:[.591,.011,.278,{ic:.102}],8146:[.694,.011,.264,{ic:.064}],8147:[.694,.011,.264,{ic:.08}],8150:[.625,.011,.264,{ic:.123}],8151:[.694,.011,.264,{ic:.138}],8152:[.961,0,.278,{ic:.231}],8153:[.865,0,.278,{ic:.214}],8154:[.694,0,.445,{ic:.054}],8155:[.694,0,.445,{ic:.054}],8157:[.694,-.495,.278,{ic:.12}],8158:[.694,-.495,.278,{ic:.157}],8159:[.694,-.489,.333,{ic:.147}],8160:[.687,.011,.5,{ic:.051}],8161:[.591,.011,.5,{ic:.034}],8162:[.694,.011,.5,{ic:.016}],8163:[.694,.011,.5,{ic:.016}],8164:[.694,.195,.472,{ic:.011}],8165:[.694,.195,.472,{ic:.011}],8166:[.625,.011,.5,{ic:.04}],8167:[.694,.011,.5,{ic:.055}],8168:[.961,0,.778,{ic:.065}],8169:[.865,0,.778,{ic:.065}],8170:[.716,0,.945,{ic:.065}],8171:[.716,0,.945,{ic:.065}],8172:[.694,0,.861,{ic:.052}],8173:[.694,-.495,.278,{ic:.102}],8174:[.694,-.495,.278,{ic:.118}],8175:[.694,-.495,.167,{ic:.113}],8178:[.694,.239,.667,{ic:.009}],8179:[.445,.239,.667,{ic:.009}],8180:[.694,.239,.667,{ic:.009}],8182:[.625,.011,.667,{ic:.021}],8183:[.625,.239,.667,{ic:.021}],8184:[.716,.022,.847,{ic:.027}],8185:[.716,.022,.819,{ic:.027}],8186:[.716,0,.861,{ic:.047}],8187:[.716,0,.805,{ic:.047}],8188:[.716,.155,.878],8189:[.694,-.495,.167,{ic:.145}],8190:[.694,-.511,.222,{ic:.069}]}},GKSSBI:{"sans-serif-bold-italic":{880:[.694,0,.645],881:[.522,0,.485],882:[.689,0,.733,{ic:.105}],883:[.516,0,.549,{ic:.074}],884:[.694,-.503,.214,{ic:.151}],885:[0,.191,.214,{ic:.001}],886:[.694,0,.794,{ic:.055}],887:[.522,0,.588,{ic:.028}],890:[-.068,.257,.11],891:[.46,.01,.459,{ic:.026}],892:[.46,.01,.459,{ic:.053}],893:[.46,.01,.459,{ic:.026}],894:[.458,.104,.305],895:[.694,.022,.519,{ic:.055}],900:[.694,-.503,.183,{ic:.172}],901:[.694,-.495,.305,{ic:.186}],902:[.694,0,.733],903:[.458,-.327,.305],904:[.694,0,.825,{ic:.081}],905:[.694,0,.977,{ic:.052}],906:[.694,0,.508,{ic:.058}],908:[.717,.022,.885,{ic:.024}],910:[.716,0,1.038,{ic:.058}],911:[.716,0,.885,{ic:.042}],912:[.694,.011,.278,{ic:.162}],938:[.871,0,.325,{ic:.198}],939:[.871,0,.855,{ic:.061}],940:[.694,.011,.633,{ic:.015}],941:[.694,.011,.43,{ic:.08}],942:[.694,.195,.514,{ic:.046}],943:[.694,.011,.278,{ic:.109}],944:[.694,.011,.5,{ic:.07}],970:[.625,.011,.264,{ic:.102}],971:[.625,.011,.5,{ic:.017}],972:[.694,.011,.528,{ic:.032}],973:[.694,.011,.5,{ic:.02}],974:[.694,.011,.71],975:[.694,.261,.694,{ic:.118}],976:[.733,.011,.51,{ic:.027}],979:[.716,0,.945,{ic:.063}],980:[.852,0,.778,{ic:.063}],983:[.458,.273,.555,{ic:.075}],984:[.705,0,.5,{ic:.072}],985:[.613,.064,.5,{ic:.045}],986:[.522,.205,.439,{ic:.068}],987:[.468,0,.5,{ic:.043}],988:[.691,0,.569,{ic:.105}],989:[.445,.195,.5,{ic:.039}],990:[.697,.038,.389,{ic:.055}],991:[.583,.145,.389,{ic:.007}],992:[.734,0,.639],993:[.722,0,.722],994:[.716,.235,.916,{ic:.024}],995:[.467,.232,.819],996:[.697,.189,.643,{ic:.091}],997:[.44,.278,.553,{ic:.045}],998:[.683,.206,.703],999:[.459,.193,.553],1e3:[.668,.162,.51,{ic:.053}],1001:[.468,.162,.51,{ic:.006}],1002:[.695,0,.787,{ic:.07}],1003:[.434,0,.567,{ic:.024}],1004:[.691,.011,.539,{ic:.079}],1005:[.471,.011,.539,{ic:.033}],1006:[.694,0,.753,{ic:.089}],1007:[.654,0,.514,{ic:.064}],1010:[.461,.011,.444,{ic:.056}],1011:[.655,.205,.267,{ic:.056}],1015:[.684,0,.639,{ic:.046}],1016:[.716,.195,.472,{ic:.012}],1017:[.706,.011,.639,{ic:.081}],1018:[.694,0,.875],1019:[.445,.205,.757],1020:[.455,.195,.472,{ic:.009}],1021:[.706,.011,.639,{ic:.031}],1022:[.706,.011,.639,{ic:.081}],1023:[.706,.011,.639,{ic:.031}],7936:[.703,.011,.633,{ic:.013}],7937:[.703,.011,.633,{ic:.013}],7938:[.706,.011,.633,{ic:.013}],7939:[.706,.011,.633,{ic:.013}],7940:[.706,.011,.633,{ic:.037}],7941:[.706,.011,.633,{ic:.027}],7942:[.78,.011,.633,{ic:.022}],7943:[.78,.011,.633,{ic:.013}],7944:[.717,0,.733],7945:[.717,0,.733],7946:[.717,0,.967],7947:[.717,0,.967],7948:[.717,0,.897],7949:[.717,0,.897],7950:[.717,0,.894],7951:[.717,0,.894],7952:[.703,.011,.43,{ic:.03}],7953:[.703,.011,.43,{ic:.03}],7954:[.706,.011,.43,{ic:.083}],7955:[.706,.011,.43,{ic:.083}],7956:[.706,.011,.43,{ic:.122}],7957:[.706,.011,.43,{ic:.122}],7960:[.717,0,.744,{ic:.084}],7961:[.717,0,.744,{ic:.084}],7962:[.717,0,.988,{ic:.084}],7963:[.717,0,.988,{ic:.084}],7964:[.717,0,.988,{ic:.084}],7965:[.717,0,.988,{ic:.084}],7968:[.71,.195,.514,{ic:.019}],7969:[.71,.195,.514,{ic:.019}],7970:[.713,.195,.514,{ic:.043}],7971:[.713,.195,.514,{ic:.043}],7972:[.713,.195,.514,{ic:.123}],7973:[.713,.195,.514,{ic:.113}],7974:[.819,.195,.514,{ic:.124}],7975:[.819,.195,.514,{ic:.124}],7976:[.717,0,.896,{ic:.055}],7977:[.717,0,.896,{ic:.055}],7978:[.717,0,1.14,{ic:.055}],7979:[.717,0,1.14,{ic:.055}],7980:[.717,0,1.14,{ic:.055}],7981:[.717,0,1.14,{ic:.055}],7982:[.717,0,1.137,{ic:.055}],7983:[.717,0,1.137,{ic:.055}],7984:[.703,.011,.278,{ic:.011}],7985:[.703,.011,.278,{ic:.019}],7986:[.706,.011,.278,{ic:.112}],7987:[.706,.011,.278,{ic:.112}],7988:[.706,.011,.278,{ic:.181}],7989:[.706,.011,.278,{ic:.181}],7990:[.812,.011,.278,{ic:.173}],7991:[.812,.011,.278,{ic:.173}],7992:[.717,0,.433,{ic:.055}],7993:[.717,0,.433,{ic:.055}],7994:[.717,0,.677,{ic:.055}],7995:[.717,0,.677,{ic:.055}],7996:[.717,0,.677,{ic:.055}],7997:[.717,0,.677,{ic:.055}],7998:[.717,0,.674,{ic:.055}],7999:[.717,0,.674,{ic:.055}],8e3:[.703,.011,.528,{ic:.01}],8001:[.703,.011,.528,{ic:.01}],8002:[.706,.011,.528,{ic:.035}],8003:[.706,.011,.528,{ic:.035}],8004:[.706,.011,.528,{ic:.104}],8005:[.706,.011,.528,{ic:.094}],8008:[.717,.022,.895,{ic:.025}],8009:[.717,.022,.881,{ic:.025}],8010:[.717,.022,1.155,{ic:.025}],8011:[.717,.022,1.145,{ic:.025}],8012:[.717,.022,1.065,{ic:.025}],8013:[.717,.022,1.045,{ic:.025}],8016:[.703,.011,.5,{ic:.015}],8017:[.703,.011,.5,{ic:.015}],8018:[.706,.011,.5,{ic:.017}],8019:[.706,.011,.5,{ic:.017}],8020:[.706,.011,.5,{ic:.106}],8021:[.706,.011,.5,{ic:.076}],8022:[.812,.011,.5,{ic:.097}],8023:[.812,.011,.5,{ic:.097}],8025:[.717,0,.988,{ic:.061}],8027:[.717,0,1.232,{ic:.061}],8029:[.717,0,1.182,{ic:.061}],8031:[.717,0,1.199,{ic:.061}],8032:[.703,.011,.667,{ic:.009}],8033:[.703,.011,.667,{ic:.009}],8034:[.706,.011,.667,{ic:.009}],8035:[.706,.011,.667,{ic:.009}],8036:[.706,.011,.667,{ic:.045}],8037:[.706,.011,.667,{ic:.035}],8038:[.812,.011,.667,{ic:.026}],8039:[.812,.011,.667,{ic:.026}],8040:[.717,0,.9,{ic:.043}],8041:[.717,0,.89,{ic:.043}],8042:[.717,0,1.17,{ic:.043}],8043:[.717,0,1.171,{ic:.043}],8044:[.717,0,1.084,{ic:.043}],8045:[.717,0,1.084,{ic:.043}],8046:[.717,0,1.111,{ic:.043}],8047:[.717,0,1.121,{ic:.043}],8048:[.706,.011,.633,{ic:.013}],8049:[.706,.011,.633,{ic:.013}],8050:[.706,.011,.43,{ic:.03}],8051:[.706,.011,.43,{ic:.063}],8052:[.713,.195,.514,{ic:.019}],8053:[.713,.195,.514,{ic:.044}],8054:[.706,.011,.278],8055:[.706,.011,.278,{ic:.102}],8056:[.706,.011,.528,{ic:.01}],8057:[.706,.011,.528,{ic:.045}],8058:[.706,.011,.5,{ic:.015}],8059:[.706,.011,.5,{ic:.018}],8060:[.706,.011,.667,{ic:.009}],8061:[.706,.011,.667,{ic:.009}],8064:[.703,.26,.633,{ic:.013}],8065:[.703,.26,.633,{ic:.013}],8066:[.706,.26,.633,{ic:.013}],8067:[.706,.26,.633,{ic:.013}],8068:[.706,.26,.633,{ic:.037}],8069:[.706,.26,.633,{ic:.027}],8070:[.78,.26,.633,{ic:.022}],8071:[.78,.26,.633,{ic:.013}],8072:[.717,.26,.733],8073:[.717,.26,.733],8074:[.717,.26,.967],8075:[.717,.26,.967],8076:[.717,.26,.897],8077:[.717,.26,.897],8078:[.717,.26,.894],8079:[.717,.26,.894],8080:[.71,.249,.514,{ic:.019}],8081:[.71,.249,.514,{ic:.019}],8082:[.713,.249,.514,{ic:.043}],8083:[.713,.249,.514,{ic:.043}],8084:[.713,.249,.514,{ic:.123}],8085:[.713,.249,.514,{ic:.113}],8086:[.819,.249,.514,{ic:.124}],8087:[.819,.249,.514,{ic:.124}],8088:[.717,.26,.896,{ic:.055}],8089:[.717,.26,.896,{ic:.055}],8090:[.717,.26,1.14,{ic:.055}],8091:[.717,.26,1.14,{ic:.055}],8092:[.717,.26,1.14,{ic:.055}],8093:[.717,.26,1.14,{ic:.055}],8094:[.717,.26,1.137,{ic:.055}],8095:[.717,.26,1.137,{ic:.055}],8096:[.703,.26,.667,{ic:.009}],8097:[.703,.26,.667,{ic:.009}],8098:[.706,.26,.667,{ic:.009}],8099:[.706,.26,.667,{ic:.009}],8100:[.706,.26,.667,{ic:.045}],8101:[.706,.26,.667,{ic:.035}],8102:[.812,.26,.667,{ic:.026}],8103:[.812,.26,.667,{ic:.026}],8104:[.717,.26,.9,{ic:.043}],8105:[.717,.26,.89,{ic:.043}],8106:[.717,.26,1.17,{ic:.043}],8107:[.717,.26,1.171,{ic:.043}],8108:[.717,.26,1.084,{ic:.043}],8109:[.717,.26,1.084,{ic:.043}],8110:[.717,.26,1.111,{ic:.043}],8111:[.717,.26,1.121,{ic:.043}],8112:[.692,.011,.633,{ic:.013}],8113:[.605,.011,.633,{ic:.013}],8114:[.706,.26,.633,{ic:.013}],8115:[.455,.26,.633,{ic:.013}],8116:[.694,.26,.633,{ic:.015}],8118:[.65,.011,.633,{ic:.013}],8119:[.65,.26,.633,{ic:.013}],8120:[.919,0,.733,{ic:.009}],8121:[.862,0,.733,{ic:.011}],8123:[.717,0,.733],8124:[.694,.26,.733],8125:[.694,-.511,.222,{ic:.085}],8126:[.154,.154,.2],8127:[.694,-.511,.222,{ic:.085}],8128:[.694,-.564,.374,{ic:.148}],8129:[.694,-.429,.367,{ic:.155}],8130:[.713,.249,.514,{ic:.019}],8131:[.467,.249,.514,{ic:.019}],8132:[.694,.249,.514,{ic:.046}],8134:[.657,.195,.514,{ic:.08}],8135:[.657,.249,.514,{ic:.08}],8136:[.717,0,.642,{ic:.084}],8137:[.717,0,.642,{ic:.084}],8138:[.717,0,.794,{ic:.055}],8139:[.717,0,.794,{ic:.055}],8140:[.694,.26,.794,{ic:.055}],8141:[.694,-.508,.38,{ic:.106}],8142:[.694,-.508,.38,{ic:.146}],8143:[.694,-.402,.367,{ic:.155}],8144:[.692,.011,.278,{ic:.147}],8145:[.605,.011,.278,{ic:.136}],8146:[.719,.011,.278,{ic:.156}],8147:[.719,.011,.278,{ic:.156}],8150:[.65,.011,.278,{ic:.138}],8151:[.785,.011,.278,{ic:.167}],8152:[.919,0,.331,{ic:.21}],8153:[.862,0,.331,{ic:.212}],8154:[.717,0,.331,{ic:.055}],8155:[.717,0,.331,{ic:.055}],8157:[.694,-.508,.38,{ic:.106}],8158:[.694,-.508,.38,{ic:.146}],8159:[.694,-.402,.222,{ic:.3}],8160:[.692,.011,.5,{ic:.053}],8161:[.605,.011,.5,{ic:.041}],8162:[.719,.011,.5,{ic:.061}],8163:[.719,.011,.5,{ic:.061}],8164:[.703,.195,.472,{ic:.009}],8165:[.703,.195,.472,{ic:.009}],8166:[.65,.011,.5,{ic:.042}],8167:[.785,.011,.5,{ic:.071}],8168:[.919,0,.855,{ic:.061}],8169:[.862,0,.855,{ic:.061}],8170:[.717,0,1.055,{ic:.061}],8171:[.717,0,1.015,{ic:.061}],8172:[.717,0,.805,{ic:.045}],8173:[.694,-.495,.305,{ic:.186}],8174:[.694,-.495,.305,{ic:.186}],8175:[.694,-.508,.278,{ic:.069}],8178:[.706,.26,.667,{ic:.009}],8179:[.445,.26,.667,{ic:.009}],8180:[.694,.26,.71],8182:[.65,.011,.667,{ic:.009}],8183:[.65,.26,.667,{ic:.009}],8184:[.717,.022,.968,{ic:.025}],8185:[.717,.022,.888,{ic:.025}],8186:[.717,0,.987,{ic:.043}],8187:[.717,0,.917,{ic:.043}],8188:[.716,.26,.794,{ic:.043}],8189:[.694,-.508,.278,{ic:.109}],8190:[.694,-.511,.222,{ic:.068}]}}},"NCM"),{},["MJX-NCM-GKSS","MJX-NCM-GKSSB","MJX-NCM-GKSSI","MJX-NCM-GKSSBI"]);MathJax.loader&&MathJax.loader.checkVersion("[mathjax-newcm]/chtml/dynamic/greek-ss","4.0.0-beta.7","dynamic-font")})();