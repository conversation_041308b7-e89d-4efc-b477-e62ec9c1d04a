(()=>{"use strict";const d=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-newcm"].chtml_ts.MathJaxNewcmFont.dynamicSetup("","hebrew",d({HE:{normal:{1425:[-.068,.291,0,{dx:-.001}],1426:[.972,-.746,0,{dx:0}],1427:[1.033,-.746,0,{dx:-.001}],1428:[.972,-.746,0,{dx:0}],1429:[.976,-.746,0,{dx:0}],1430:[-.068,.307,0,{dx:-.001}],1431:[.959,-.746,0,{dx:-.001}],1432:[.911,-.746,0,{dx:0}],1433:[.985,-.746,0,{dx:-.001}],1434:[-.068,.291,0,{dx:-.001}],1435:[-.068,.307,0,{dx:-.001}],1436:[.985,-.746,0,{dx:-.001}],1437:[.985,-.746,0,{dx:-.001}],1438:[.985,-.746,0,{dx:-.001}],1439:[.961,-.746,0,{dx:-.001}],1440:[.961,-.746,0,{dx:-.001}],1441:[.935,-.746,0,{dx:-.001}],1442:[-.068,.291,0,{dx:-.001}],1443:[-.068,.294,0,{dx:0}],1444:[-.068,.291,0,{dx:-.001}],1445:[-.068,.307,0,{dx:-.001}],1446:[-.068,.307,0,{dx:-.001}],1447:[-.068,.291,0,{dx:-.001}],1448:[.985,-.746,0,{dx:-.001}],1449:[.961,-.746,0,{dx:-.001}],1450:[-.068,.203,0,{dx:-.001}],1451:[.969,-.746,0,{dx:-.001}],1452:[.972,-.746,0,{dx:0}],1453:[-.068,.307,0,{dx:-.001}],1454:[.911,-.746,0,{dx:0}],1455:[.969,-.746,0,{dx:-.001}],1456:[-.067,.294,0,{dx:0}],1457:[-.068,.294,0,{dx:0}],1458:[-.068,.294,0,{dx:0}],1459:[-.068,.294,0,{dx:0}],1460:[-.067,.158,0,{dx:0}],1461:[-.067,.158,0,{dx:.001}],1462:[-.067,.294,0,{dx:.001}],1463:[-.079,.147,0,{dx:0}],1464:[-.079,.266,0,{dx:0}],1465:[.837,-.746,0,{dx:-0}],1466:[.837,-.746,0,{dx:-0}],1467:[-.067,.294,0,{dx:-.001}],1468:[.384,-.293,0],1469:[-.068,.298,0,{dx:-.001}],1470:[.678,-.588,.488],1471:[.836,-.768,0,{dx:0}],1472:[.749,.095,.202],1473:[.798,-.707,0,{dx:-.091}],1474:[.798,-.707,0,{dx:-.096}],1475:[.487,0,.344],1476:[.881,-.746,0,{dx:-.001}],1477:[-.068,.203,0,{dx:-.001}],1478:[.719,0,.415],1479:[-.079,.277,0,{dx:0}],1488:[.725,.012,.691],1489:[.72,.04,.604],1490:[.721,.03,.517],1491:[.71,.017,.596],1492:[.71,.003,.652],1493:[.71,.007,.367],1494:[.719,.017,.383],1495:[.71,.003,.652],1496:[.71,0,.656],1497:[.72,-.339,.37],1498:[.71,.297,.605],1499:[.72,.04,.604],1500:[1.034,.005,.607],1501:[.71,0,.632],1502:[.721,.029,.659],1503:[.71,.299,.363],1504:[.72,.04,.429],1505:[.71,.013,.632],1506:[.721,.15,.656],1507:[.71,.297,.594],1508:[.72,.04,.604],1509:[.721,.303,.656],1510:[.721,.029,.652],1511:[.71,.3,.607],1512:[.71,.007,.614],1513:[.71,0,.806],1514:[.71,.007,.631],1519:[1.056,-.339,.741],1520:[.71,.007,.768],1521:[.72,.007,.754],1522:[.72,-.339,.741],1523:[.792,-.51,.27],1524:[.792,-.51,.482],1525:[.71,.007,.367],1527:[.71,.3,.607],64285:[.72,-.142,.37],64286:[.855,-.739,0,{dx:-.001}],64287:[.72,-.077,.741],64288:[.721,.03,.647],64289:[.725,.012,1.311],64290:[.71,.017,1.296],64291:[.71,.003,1.312],64292:[.72,.04,1.27],64293:[1.034,.005,1.267],64294:[.71,0,1.282],64295:[.71,.007,1.284],64296:[.71,.007,1.311],64297:[.571,-.241,.684],64298:[.798,0,.806],64299:[.798,0,.806],64300:[.798,0,.806],64301:[.798,0,.806],64302:[.725,.147,.691],64303:[.725,.266,.691],64304:[.725,.012,.691],64305:[.72,.04,.604],64306:[.721,.03,.517],64307:[.71,.017,.596],64308:[.71,.003,.652],64309:[.71,.007,.367],64310:[.719,.017,.373],64311:[.71,.003,.652],64312:[.71,0,.656],64313:[.72,-.339,.37],64314:[.71,.297,.605],64315:[.72,.04,.604],64316:[1.034,.005,.607],64317:[.71,0,.632],64318:[.721,.029,.659],64319:[.71,.299,.363],64320:[.72,.04,.429],64321:[.71,.013,.632],64322:[.721,.15,.656],64323:[.71,.297,.594],64324:[.72,.04,.604],64325:[.721,.303,.656],64326:[.721,.029,.652],64327:[.71,.3,.607],64328:[.71,.007,.614],64329:[.71,0,.806],64330:[.71,.007,.631],64331:[.836,.007,.367],64332:[.836,.04,.604],64333:[.836,.04,.604],64334:[.836,.04,.604],64335:[1.028,.012,.861]}},HEB:{bold:{1425:[-.068,.291,0,{dx:-.001}],1426:[.972,-.746,0,{dx:0}],1427:[1.035,-.744,0,{dx:-.001}],1428:[.972,-.746,0,{dx:0}],1429:[.976,-.746,0,{dx:0}],1430:[-.068,.314,0,{dx:-.004}],1431:[.97,-.735,0,{dx:-.001}],1432:[.911,-.746,0,{dx:0}],1433:[.992,-.746,0,{dx:.002}],1434:[-.066,.293,0,{dx:-.001}],1435:[-.068,.314,0,{dx:-.001}],1436:[.992,-.746,0,{dx:-.004}],1437:[.992,-.746,0,{dx:-.004}],1438:[.992,-.746,0,{dx:-.004}],1439:[.961,-.739,0,{dx:-.001}],1440:[.961,-.739,0,{dx:.003}],1441:[.935,-.746,0,{dx:-.001}],1442:[-.068,.291,0,{dx:-.001}],1443:[-.068,.294,0,{dx:0}],1444:[-.066,.293,0,{dx:-.001}],1445:[-.068,.314,0,{dx:.002}],1446:[-.068,.314,0,{dx:.002}],1447:[-.068,.291,0,{dx:-.001}],1448:[.992,-.746,0,{dx:.002}],1449:[.961,-.739,0,{dx:-.004}],1450:[-.068,.203,0,{dx:-.001}],1451:[.971,-.744,0,{dx:-.001}],1452:[.972,-.746,0,{dx:0}],1453:[-.068,.314,0,{dx:-.004}],1454:[.911,-.746,0,{dx:0}],1455:[.969,-.746,0,{dx:-.001}],1456:[-.068,.294,0,{dx:0}],1457:[-.068,.294,0,{dx:0}],1458:[-.068,.294,0,{dx:0}],1459:[-.068,.294,0,{dx:0}],1460:[-.068,.158,0,{dx:0}],1461:[-.068,.158,0,{dx:0}],1462:[-.068,.294,0,{dx:0}],1463:[-.079,.147,0,{dx:0}],1464:[-.079,.266,0,{dx:0}],1465:[.836,-.746,0,{dx:0}],1466:[.836,-.746,0,{dx:0}],1467:[-.068,.294,0,{dx:-.001}],1468:[.384,-.293,0],1469:[-.068,.298,0,{dx:-.001}],1470:[.678,-.588,.488],1471:[.836,-.768,0,{dx:0}],1472:[.757,.103,.202],1473:[.798,-.707,0,{dx:-.671}],1474:[.798,-.707,0,{dx:-.176}],1475:[.487,0,.344],1476:[.881,-.746,0,{dx:-.001}],1477:[-.068,.203,0,{dx:-.001}],1478:[.758,0,.415],1479:[-.079,.277,0,{dx:0}],1488:[.751,.012,.691],1489:[.721,.039,.604],1490:[.721,.03,.517],1491:[.709,.017,.596],1492:[.709,.014,.652],1493:[.709,.007,.367],1494:[.742,.018,.383],1495:[.709,.014,.652],1496:[.709,0,.656],1497:[.721,-.339,.37],1498:[.71,.297,.605],1499:[.72,.039,.604],1500:[1.074,.005,.607],1501:[.709,0,.632],1502:[.721,.028,.659],1503:[.71,.31,.363],1504:[.72,.039,.429],1505:[.709,.013,.632],1506:[.72,.15,.656],1507:[.71,.297,.594],1508:[.72,.039,.604],1509:[.72,.303,.656],1510:[.721,.028,.652],1511:[.71,.309,.607],1512:[.709,.007,.614],1513:[.709,0,.806],1514:[.709,.007,.631],1515:[0,0,1],1519:[1.057,-.339,.741],1520:[.709,.007,.768],1521:[.721,.007,.754],1522:[.721,-.339,.741],1523:[.792,-.512,.27],1524:[.792,-.512,.482],1525:[.709,.007,.367],1527:[.71,.309,.607],64285:[.721,-.142,.37],64286:[.855,-.739,0,{dx:-.001}],64287:[.721,-.077,.741],64288:[.721,.03,.647],64289:[.751,.012,1.311],64290:[.709,.017,1.296],64291:[.709,.014,1.312],64292:[.72,.039,1.27],64293:[1.074,.005,1.267],64294:[.709,0,1.282],64295:[.709,.007,1.284],64296:[.709,.007,1.311],64297:[.571,-.241,.684],64298:[.798,0,.806],64299:[.798,0,.806],64300:[.798,0,.806],64301:[.798,0,.806],64302:[.751,.147,.691],64303:[.751,.266,.691],64304:[.751,.147,.691],64305:[.721,.039,.604],64306:[.721,.03,.517],64307:[.709,.017,.596],64308:[.709,.014,.652],64309:[.709,.007,.367],64310:[.742,.018,.363],64311:[.709,.014,.652],64312:[.709,0,.656],64313:[.721,-.339,.37],64314:[.71,.297,.605],64315:[.72,.039,.604],64316:[1.074,.005,.607],64317:[.709,0,.632],64318:[.721,.028,.659],64319:[.71,.31,.363],64320:[.72,.039,.429],64321:[.709,.013,.632],64322:[.72,.15,.656],64323:[.71,.297,.594],64324:[.72,.039,.604],64325:[.72,.303,.656],64326:[.721,.028,.652],64327:[.71,.309,.607],64328:[.709,.007,.614],64329:[.709,0,.806],64330:[.709,.007,.631],64331:[.836,.007,.367],64332:[.836,.039,.604],64333:[.836,.039,.604],64334:[.836,.039,.604],64335:[1.027,.012,.861]}},HEI:{italic:{1425:[-.068,.291,0,{dx:-.001}],1426:[.972,-.746,0,{dx:0}],1427:[1.033,-.746,0,{dx:-.001}],1428:[.972,-.746,0,{dx:0}],1429:[.976,-.746,0,{dx:0}],1430:[-.068,.309,0,{dx:-.002}],1431:[.962,-.743,0,{dx:-.001}],1432:[.911,-.746,0,{dx:0}],1433:[.987,-.746,0,{dx:-.001}],1434:[-.068,.291,0,{dx:-.001}],1435:[-.068,.309,0,{dx:-.001}],1436:[.987,-.746,0,{dx:-.002}],1437:[.987,-.746,0,{dx:-.002}],1438:[.987,-.746,0,{dx:-.002}],1439:[.961,-.744,0,{dx:-.001}],1440:[.961,-.744,0,{dx:0}],1441:[.935,-.746,0,{dx:-.001}],1442:[-.068,.291,0,{dx:-.001}],1443:[-.068,.294,0,{dx:0}],1444:[-.068,.291,0,{dx:-.001}],1445:[-.068,.309,0,{dx:-.001}],1446:[-.068,.309,0,{dx:-.001}],1447:[-.068,.291,0,{dx:-.001}],1448:[.987,-.746,0,{dx:-.001}],1449:[.961,-.744,0,{dx:-.002}],1450:[-.068,.203,0,{dx:-.001}],1451:[.969,-.746,0,{dx:-.001}],1452:[.972,-.746,0,{dx:0}],1453:[-.068,.309,0,{dx:-.002}],1454:[.911,-.746,0,{dx:0}],1455:[.969,-.746,0,{dx:-.001}],1456:[-.068,.294,0,{dx:0}],1457:[-.068,.294,0,{dx:0}],1458:[-.068,.294,0,{dx:0}],1459:[-.068,.294,0,{dx:0}],1460:[-.068,.158,0,{dx:0}],1461:[-.068,.158,0,{dx:0}],1462:[-.068,.294,0,{dx:0}],1463:[-.079,.147,0,{dx:0}],1464:[-.079,.266,0,{dx:0}],1465:[.836,-.746,0,{dx:0}],1466:[.836,-.746,0,{dx:0}],1467:[-.068,.294,0,{dx:-.001}],1468:[.384,-.293,0],1469:[-.068,.298,0,{dx:-.001}],1470:[.678,-.588,.488],1471:[.836,-.768,0,{dx:0}],1472:[.751,.097,.202],1473:[.798,-.707,0,{dx:-.671}],1474:[.798,-.707,0,{dx:-.176}],1475:[.487,0,.344],1476:[.881,-.746,0,{dx:-.001}],1477:[-.068,.203,0,{dx:-.001}],1478:[.729,0,.415,{ic:.066}],1479:[-.079,.277,0,{dx:0}],1488:[.726,.012,.691,{ic:.075}],1489:[.72,.04,.604,{ic:.028}],1490:[.721,.029,.517],1491:[.71,.017,.596,{ic:.087}],1492:[.71,.006,.652,{ic:.082}],1493:[.71,.007,.367,{ic:.076}],1494:[.724,.017,.383,{ic:.093}],1495:[.71,.006,.652,{ic:.083}],1496:[.71,0,.656,{ic:.056}],1497:[.72,-.338,.37,{ic:.09}],1498:[.71,.297,.605,{ic:.077}],1499:[.72,.04,.604,{ic:.048}],1500:[1.04,.005,.607,{ic:.083}],1501:[.71,0,.632,{ic:.085}],1502:[.721,.03,.659,{ic:.041}],1503:[.71,.302,.363,{ic:.101}],1504:[.72,.04,.429,{ic:.075}],1505:[.71,.013,.632,{ic:.046}],1506:[.721,.149,.656,{ic:.078}],1507:[.71,.297,.594,{ic:.091}],1508:[.72,.04,.604,{ic:.048}],1509:[.721,.303,.656,{ic:.079}],1510:[.721,.03,.652,{ic:.052}],1511:[.71,.302,.607,{ic:.083}],1512:[.71,.007,.614,{ic:.076}],1513:[.71,0,.806,{ic:.093}],1514:[.71,.007,.631,{ic:.093}],1519:[1.056,-.338,.741,{ic:.028}],1520:[.71,.007,.768],1521:[.72,.007,.754],1522:[.72,-.338,.741,{ic:.028}],1523:[.792,-.51,.27],1524:[.792,-.51,.482],1525:[.71,.007,.367,{ic:.076}],1527:[.71,.302,.607,{ic:.083}],64285:[.72,-.142,.37,{ic:.09}],64286:[.858,-.739,0,{dx:-.001}],64287:[.72,-.077,.741,{ic:.029}],64288:[.721,.029,.647,{ic:.106}],64289:[.725,.012,1.311,{ic:.095}],64290:[.71,.017,1.296,{ic:.087}],64291:[.71,.006,1.312,{ic:.082}],64292:[.72,.04,1.27,{ic:.035}],64293:[1.04,.005,1.267,{ic:.083}],64294:[.71,0,1.282,{ic:.085}],64295:[.71,.007,1.284,{ic:.075}],64296:[.71,.007,1.311,{ic:.092}],64297:[.571,-.241,.684],64298:[.798,0,.806,{ic:.096}],64299:[.798,0,.806,{ic:.093}],64300:[.798,0,.806,{ic:.093}],64301:[.798,0,.806,{ic:.093}],64302:[.726,.147,.691,{ic:.075}],64303:[.726,.266,.691,{ic:.075}],64304:[.726,.147,.691,{ic:.075}],64305:[.72,.04,.604,{ic:.028}],64306:[.721,.029,.517],64307:[.71,.017,.596,{ic:.087}],64308:[.71,.006,.652,{ic:.082}],64309:[.71,.007,.367,{ic:.076}],64310:[.724,.017,.373,{ic:.092}],64311:[.71,.006,.652,{ic:.083}],64312:[.71,0,.656,{ic:.056}],64313:[.72,-.338,.37,{ic:.09}],64314:[.71,.297,.605,{ic:.077}],64315:[.72,.04,.604,{ic:.048}],64316:[1.04,.005,.607,{ic:.083}],64317:[.71,0,.632,{ic:.085}],64318:[.721,.03,.659,{ic:.041}],64319:[.71,.302,.363,{ic:.101}],64320:[.72,.04,.429,{ic:.075}],64321:[.71,.013,.632,{ic:.046}],64322:[.721,.149,.656,{ic:.078}],64323:[.71,.297,.594,{ic:.091}],64324:[.72,.04,.604,{ic:.048}],64325:[.721,.303,.656,{ic:.079}],64326:[.721,.03,.652,{ic:.052}],64327:[.71,.302,.607,{ic:.083}],64328:[.71,.007,.614,{ic:.076}],64329:[.71,0,.806,{ic:.093}],64330:[.71,.007,.631,{ic:.093}],64331:[.836,.007,.367,{ic:.076}],64332:[.836,.04,.604,{ic:.028}],64333:[.836,.04,.604,{ic:.048}],64334:[.836,.04,.604,{ic:.048}],64335:[1.028,.012,.861,{ic:.075}]}},HEBI:{"bold-italic":{1425:[-.068,.291,0,{dx:-.001}],1426:[.972,-.746,0,{dx:0}],1427:[1.035,-.744,0,{dx:-.001}],1428:[.972,-.746,0,{dx:0}],1429:[.976,-.746,0,{dx:0}],1430:[-.068,.314,0,{dx:-.004}],1431:[.97,-.735,0,{dx:-.001}],1432:[.911,-.746,0,{dx:0}],1433:[.992,-.746,0,{dx:.002}],1434:[-.066,.293,0,{dx:-.001}],1435:[-.068,.314,0,{dx:-.001}],1436:[.992,-.746,0,{dx:-.004}],1437:[.992,-.746,0,{dx:-.004}],1438:[.992,-.746,0,{dx:-.004}],1439:[.961,-.739,0,{dx:-.001}],1440:[.961,-.739,0,{dx:.003}],1441:[.935,-.746,0,{dx:-.001}],1442:[-.068,.291,0,{dx:-.001}],1443:[-.068,.294,0,{dx:0}],1444:[-.066,.293,0,{dx:-.001}],1445:[-.068,.314,0,{dx:.002}],1446:[-.068,.314,0,{dx:.002}],1447:[-.068,.291,0,{dx:-.001}],1448:[.992,-.746,0,{dx:.002}],1449:[.961,-.739,0,{dx:-.004}],1450:[-.068,.203,0,{dx:-.001}],1451:[.971,-.744,0,{dx:-.001}],1452:[.972,-.746,0,{dx:0}],1453:[-.068,.314,0,{dx:-.004}],1454:[.911,-.746,0,{dx:0}],1455:[.969,-.746,0,{dx:-.001}],1456:[-.068,.294,0,{dx:0}],1457:[-.068,.294,0,{dx:0}],1458:[-.068,.294,0,{dx:0}],1459:[-.068,.294,0,{dx:0}],1460:[-.068,.158,0,{dx:0}],1461:[-.068,.158,0,{dx:0}],1462:[-.068,.294,0,{dx:0}],1463:[-.079,.147,0,{dx:0}],1464:[-.079,.266,0,{dx:0}],1465:[.836,-.746,0,{dx:0}],1466:[.836,-.746,0,{dx:0}],1467:[-.068,.294,0,{dx:-.001}],1468:[.384,-.293,0],1469:[-.068,.298,0,{dx:-.001}],1470:[.678,-.588,.488],1471:[.836,-.768,0,{dx:0}],1472:[.757,.103,.202],1473:[.798,-.707,0,{dx:-.671}],1474:[.798,-.707,0,{dx:-.176}],1475:[.487,0,.344],1476:[.881,-.746,0,{dx:-.001}],1477:[-.068,.203,0,{dx:-.001}],1478:[.758,0,.415,{ic:.065}],1479:[-.079,.277,0,{dx:0}],1488:[.751,.012,.691,{ic:.072}],1489:[.721,.039,.604,{ic:.03}],1490:[.721,.03,.517],1491:[.709,.017,.596,{ic:.084}],1492:[.709,.014,.652,{ic:.08}],1493:[.709,.007,.367,{ic:.074}],1494:[.742,.018,.383,{ic:.09}],1495:[.709,.014,.652,{ic:.081}],1496:[.709,0,.656,{ic:.054}],1497:[.721,-.339,.37,{ic:.091}],1498:[.71,.297,.605,{ic:.075}],1499:[.72,.039,.604,{ic:.049}],1500:[1.074,.005,.607,{ic:.081}],1501:[.709,0,.632,{ic:.083}],1502:[.721,.028,.659,{ic:.04}],1503:[.71,.31,.363,{ic:.099}],1504:[.72,.039,.429,{ic:.074}],1505:[.709,.013,.632,{ic:.046}],1506:[.72,.15,.656,{ic:.077}],1507:[.71,.297,.594,{ic:.088}],1508:[.72,.039,.604,{ic:.049}],1509:[.72,.303,.656,{ic:.076}],1510:[.721,.028,.652,{ic:.051}],1511:[.71,.309,.607,{ic:.079}],1512:[.709,.007,.614,{ic:.074}],1513:[.709,0,.806,{ic:.089}],1514:[.709,.007,.631,{ic:.09}],1515:[0,0,1],1519:[1.057,-.339,.741,{ic:.029}],1520:[.709,.007,.768],1521:[.721,.007,.754],1522:[.721,-.339,.741,{ic:.029}],1523:[.792,-.512,.27],1524:[.792,-.512,.482],1525:[.709,.007,.367,{ic:.074}],1527:[.71,.309,.607,{ic:.079}],64285:[.721,-.142,.37,{ic:.091}],64286:[.855,-.739,0,{dx:-.001}],64287:[.721,-.077,.741,{ic:.029}],64288:[.721,.03,.647,{ic:.103}],64289:[.751,.012,1.311,{ic:.092}],64290:[.709,.017,1.296,{ic:.084}],64291:[.709,.014,1.312,{ic:.08}],64292:[.72,.039,1.27,{ic:.035}],64293:[1.074,.005,1.267,{ic:.081}],64294:[.709,0,1.282,{ic:.083}],64295:[.709,.007,1.284,{ic:.074}],64296:[.709,.007,1.311,{ic:.09}],64297:[.571,-.241,.684],64298:[.798,0,.806,{ic:.089}],64299:[.798,0,.806,{ic:.089}],64300:[.798,0,.806,{ic:.089}],64301:[.798,0,.806,{ic:.089}],64302:[.751,.147,.691,{ic:.072}],64303:[.751,.266,.691,{ic:.072}],64304:[.751,.147,.691,{ic:.072}],64305:[.721,.039,.604,{ic:.03}],64306:[.721,.03,.517],64307:[.709,.017,.596,{ic:.084}],64308:[.709,.014,.652,{ic:.08}],64309:[.709,.007,.367,{ic:.074}],64310:[.742,.018,.363,{ic:.09}],64311:[.709,.014,.652,{ic:.081}],64312:[.709,0,.656,{ic:.054}],64313:[.721,-.339,.37,{ic:.091}],64314:[.71,.297,.605,{ic:.075}],64315:[.72,.039,.604,{ic:.049}],64316:[1.074,.005,.607,{ic:.081}],64317:[.709,0,.632,{ic:.083}],64318:[.721,.028,.659,{ic:.04}],64319:[.71,.31,.363,{ic:.099}],64320:[.72,.039,.429,{ic:.074}],64321:[.709,.013,.632,{ic:.046}],64322:[.72,.15,.656,{ic:.077}],64323:[.71,.297,.594,{ic:.088}],64324:[.72,.039,.604,{ic:.049}],64325:[.72,.303,.656,{ic:.076}],64326:[.721,.028,.652,{ic:.051}],64327:[.71,.309,.607,{ic:.079}],64328:[.709,.007,.614,{ic:.074}],64329:[.709,0,.806,{ic:.089}],64330:[.709,.007,.631,{ic:.09}],64331:[.836,.007,.367,{ic:.074}],64332:[.836,.039,.604,{ic:.03}],64333:[.836,.039,.604,{ic:.049}],64334:[.836,.039,.604,{ic:.049}],64335:[1.027,.012,.861,{ic:.074}]}}},"NCM"),{},["MJX-NCM-HE","MJX-NCM-HEB","MJX-NCM-HEI","MJX-NCM-HEBI"]);MathJax.loader&&MathJax.loader.checkVersion("[mathjax-newcm]/chtml/dynamic/hebrew","4.0.0-beta.7","dynamic-font")})();