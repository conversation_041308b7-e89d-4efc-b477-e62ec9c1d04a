(()=>{"use strict";const t=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-newcm"].chtml_ts.MathJaxNewcmFont.dynamicSetup("","math",t({MM:{normal:{8714:[.447,-.054,.547],8717:[.447,-.054,.547],8731:[.046,.96,.833,{ic:.02}],8732:[.04,.96,.833,{ic:.02}],8762:[.504,.004,.778],8763:[.536,.036,.773],8782:[.49,-.01,.778],8783:[.49,-.133,.778],8785:[.601,.101,.778],8786:[.601,.101,.778],8787:[.601,.101,.778],8790:[.367,-.133,.778],8791:[.73,-.133,.778],8792:[.619,-.133,.778],8793:[.752,-.133,.778],8794:[.752,-.133,.778],8795:[.81,-.133,.778],8796:[.81,-.133,.778],8798:[.684,-.133,.778],8844:[.604,.02,.667],8886:[.4,-.1,1.078],8887:[.4,-.1,1.078],8888:[.4,-.1,.948],8889:[.603,.103,.818],8891:[.569,.136,.642],8892:[.684,.02,.642],8893:[.684,.018,.642],8894:[.679,.109,.9],8895:[.679,-.013,.778],8903:[.588,.088,.802],8912:[.543,.043,.698],8913:[.543,.043,.698],8914:[.604,.02,.658],8915:[.604,.02,.658],8916:[.65,.023,.839],8917:[.75,.25,.778],8918:[.547,.047,.778],8919:[.547,.047,.778],8920:[.547,.046,1.285],8921:[.546,.047,1.285],8922:[.849,.349,.778],8923:[.849,.349,.778],8924:[.631,.119,.778],8925:[.631,.119,.778],8926:[.639,.139,.738],8927:[.639,.139,.738],8928:[.73,.23,.738],8929:[.73,.23,.738],8932:[.627,.211,.778],8933:[.627,.211,.778],8934:[.668,.241,.776],8935:[.636,.209,.776],8936:[.682,.254,.773],8937:[.682,.254,.773],8946:[.543,.043,.839],8947:[.543,.043,.667],8948:[.447,-.054,.547],8949:[.741,.043,.667],8950:[.684,.043,.667],8951:[.614,-.054,.547],8952:[.543,.184,.667],8953:[.543,.043,.667],8954:[.543,.043,.839],8955:[.543,.043,.667],8956:[.447,-.054,.547],8957:[.684,.043,.667],8958:[.614,-.054,.547],8959:[.684,0,.556],10176:[.684,0,.778],10177:[.741,.005,.968],10178:[.684,0,.778],10179:[.543,.043,.778],10180:[.543,.043,.778],10181:[.719,.173,.466],10182:[.719,.173,.466],10183:[.605,.02,.667],10184:[.603,.103,1.168],10185:[.603,.103,1.168],10186:[.75,.25,.552],10187:[.505,.005,.623],10188:[.77,.27,.45,{ic:.214}],10189:[.505,.005,.623],10190:[.583,.083,.778],10191:[.583,.083,.778],10192:[.48,-.02,.5],10193:[.605,.02,.667],10194:[.503,.003,.667],10195:[.479,-.013,.578],10196:[.479,-.013,.578],10197:[.505,.005,.758],10198:[.505,.005,.758],10199:[.505,.005,.892],10202:[.684,0,1.026],10203:[.684,0,1.026],10204:[.4,-.1,.948],10207:[.683,.02,.4],10208:[.61,.11,.572],10209:[.501,.001,.614],10210:[.501,.001,.73],10211:[.501,.001,.73],10212:[.583,.083,.991],10213:[.583,.083,.991],10625:[.346,-.154,.291],10626:[.506,-.034,.291],10627:[.75,.25,.5],10628:[.75,.25,.5],10629:[.748,.248,.389],10630:[.748,.248,.389],10631:[.75,.25,.542],10632:[.75,.25,.542],10633:[.75,.25,.47],10634:[.75,.25,.47],10635:[.75,.381,.278],10636:[.75,.381,.278],10637:[.75,.25,.278,{ic:.005}],10638:[.75,.25,.278],10639:[.75,.25,.278,{ic:.005}],10640:[.75,.25,.278],10641:[.75,.25,.389],10642:[.75,.25,.389],10643:[.748,.248,.778],10644:[.748,.248,.778],10645:[.748,.248,.778],10646:[.748,.248,.778],10649:[.596,.096,.192],10650:[.605,.075,.62],10651:[.526,.109,.778],10652:[.679,-.013,.778],10653:[.679,-.013,.778],10654:[.724,-.013,.778],10655:[.424,-.013,.778],10656:[.562,.062,.778],10657:[.557,.057,.724],10658:[.724,-.013,.778],10659:[.724,-.013,.778],10660:[.724,.137,.778],10661:[.724,.137,.778],10662:[.362,-.107,.564],10663:[.362,-.106,.564],10664:[.739,.109,.778],10665:[.739,.109,.778],10666:[.739,.109,.778],10667:[.739,.109,.778],10668:[.666,.036,.778,{ic:.054}],10669:[.666,.036,.778,{ic:.053}],10670:[.666,.036,.778,{ic:.054}],10671:[.666,.036,.778,{ic:.053}],10672:[.597,.097,.778],10673:[.735,.097,.778],10674:[.803,.097,.778],10675:[.788,.097,.778],10676:[.788,.097,.778],10677:[.563,.063,1.047],10678:[.731,.231,.712],10679:[.563,.063,.712],10680:[.563,.063,.712],10681:[.563,.063,.712],10682:[.563,.063,.712],10683:[.597,.097,.778],10684:[.563,.063,.712],10685:[.869,.203,.712],10686:[.563,.063,.712],10687:[.563,.063,.712],10688:[.563,.063,.712],10689:[.563,.063,.712],10690:[.563,.063,.954],10691:[.563,.063,.984],10692:[.56,.06,.712],10693:[.56,.06,.712],10694:[.56,.06,.712],10695:[.56,.06,.712],10696:[.56,.06,.712],10697:[.56,.06,.712],10698:[.741,.005,.968],10699:[.741,.005,.968],10700:[.741,.005,.968],10701:[.741,.005,1.236],10702:[.787,.247,.738],10703:[.547,.047,.886],10704:[.547,.047,.886],10705:[.505,.005,.623],10706:[.505,.005,.623],10707:[.505,.005,.623],10708:[.505,.005,.623],10709:[.505,.005,.623],10710:[.51,0,.623],10711:[.51,0,.623],10712:[.93,.266,.46],10713:[.93,.266,.46],10714:[.93,.266,.85],10715:[.93,.266,.85],10716:[.442,.011,1],10717:[.663,.011,1],10718:[.59,.16,1],10719:[.4,-.1,1.15],10720:[.56,.06,.712],10721:[.424,.137,.778],10722:[.487,-.043,.784],10723:[.563,.032,.797],10724:[.61,.032,.797],10725:[.563,.032,.797],10726:[.564,.062,.778],10727:[.673,.173,.844],10728:[.748,0,.973],10729:[.748,0,.973],10730:[.809,.144,.778],10731:[.737,.199,.778],10732:[.741,.193,.712],10733:[.741,.193,.712],10734:[.748,.248,.566],10735:[.768,.248,.566],10736:[.748,.248,.742],10737:[.748,.248,.742],10738:[.748,.248,.648],10739:[.748,.248,.648],10740:[.51,.01,1.14],10742:[.72,.103,.556],10746:[.583,.083,.964],10747:[.583,.083,1.14],10748:[.75,.25,.389],10749:[.75,.25,.389],10750:[.583,.083,.778],10751:[.302,-.198,.778],10762:[.75,.25,1.056],10763:[.805,.306,.707],10781:[.658,.108,.929],10782:[.632,.134,.738,{sk:.039}],10783:[.58,.186,.28],10784:[.547,.047,1.118,{sk:.011}],10785:[.697,.203,.441,{sk:-.09}],10786:[.694,-.037,.778],10787:[.659,-.037,.778],10788:[.696,-.037,.778],10789:[.633,0,.778],10790:[.633,.001,.778],10791:[.583,.26,.827],10792:[.583,.083,.778],10793:[.646,-.23,.778],10794:[.27,.004,.778],10795:[.504,.004,.778],10796:[.504,.004,.778],10797:[.592,.092,.669],10798:[.592,.092,.669],10800:[.604,-.009,.778],10801:[.491,.12,.778],10802:[.491,-.009,.778],10803:[.58,.066,.778],10804:[.592,.092,.606],10805:[.592,.092,.606],10806:[.779,.092,.778],10807:[.592,.092,.778],10808:[.592,.092,.778],10809:[.741,.005,.968],10810:[.741,.005,.968],10811:[.741,.005,.968],10812:[.367,-.133,.667],10813:[.367,-.133,.667],10814:[.56,.186,.24],10816:[.604,.02,.667],10817:[.604,.02,.667],10818:[.72,.02,.667],10819:[.72,.02,.667],10820:[.604,.022,.667],10821:[.604,.022,.667],10822:[.604,.02,.607],10823:[.604,.02,.607],10824:[.72,.02,.607],10825:[.72,.02,.607],10826:[.374,-.1,1.057],10827:[.374,-.1,1.057],10828:[.604,.02,.846],10829:[.604,.02,.846],10830:[.604,.02,.667],10831:[.604,.02,.667],10832:[.604,.02,.846],10833:[.788,.02,.667],10834:[.788,.022,.667],10835:[.602,.022,.667],10836:[.602,.022,.667],10837:[.602,.02,.805],10838:[.602,.02,.805],10839:[.644,.022,.778],10840:[.644,.022,.778],10841:[.731,.02,.667],10842:[.602,.02,.667],10843:[.602,.02,.667],10844:[.602,.02,.771],10845:[.602,.02,.771],10846:[.831,.02,.667],10847:[.602,.173,.667],10848:[.602,.287,.667],10849:[.727,-.204,.642],10850:[.831,.022,.667],10851:[.601,.25,.667],10852:[.647,.157,.838],10853:[.647,.157,.838],10854:[.367,.101,.778],10855:[.698,-.036,.778],10856:[.67,.17,.778],10857:[.67,.17,.778],10858:[.536,-.134,.773],10859:[.536,.036,.773],10860:[.578,.078,.778],10861:[.726,-.036,.778],10862:[.739,-.133,.778],10863:[.679,-.043,.773],10864:[.773,-.036,.778],10865:[.767,-.037,.778],10866:[.767,-.037,.778],10867:[.552,-.036,.778],10868:[.422,-.078,.842],10869:[.367,-.133,.992],10870:[.367,-.123,1.312],10871:[.601,.101,.778],10872:[.698,-.036,.778],10873:[.547,.047,.778],10874:[.547,.047,.778],10875:[.705,.047,.778],10876:[.705,.047,.778],10879:[.634,.134,.778],10880:[.634,.134,.778],10881:[.634,.134,.778],10882:[.634,.134,.778],10883:[.816,.134,.778],10884:[.816,.134,.778],10893:[.718,.143,.776],10894:[.718,.143,.776],10895:[.883,.378,.776],10896:[.883,.378,.776],10897:[.955,.257,.778],10898:[.955,.257,.778],10899:[1.004,.389,.778],10900:[1.004,.389,.778],10903:[.629,.129,.778],10904:[.629,.129,.778],10905:[.807,.047,.778],10906:[.807,.047,.778],10907:[.791,.129,.778],10908:[.791,.129,.778],10909:[.636,.126,.776],10910:[.636,.125,.776],10911:[.898,.203,.778],10912:[.898,.203,.778],10913:[.547,.047,.77],10914:[.547,.047,.77],10915:[.629,.119,1.011],10916:[.547,.047,.876],10917:[.547,.047,1.526],10918:[.548,.047,.864],10919:[.548,.047,.864],10920:[.635,.134,.86],10921:[.635,.134,.86],10922:[.547,.047,.778],10923:[.547,.047,.778],10924:[.631,.119,.778],10925:[.631,.119,.778],10926:[.685,-.133,.778],10939:[.547,.047,1.248],10940:[.547,.047,1.248],10941:[.543,.043,.778],10942:[.543,.043,.778],10943:[.543,.265,.718],10944:[.543,.265,.718],10945:[.543,.225,.778],10946:[.543,.225,.778],10947:[.693,.127,.718],10948:[.693,.127,.718],10951:[.543,.17,.718],10952:[.543,.17,.718],10953:[.703,.203,.72],10954:[.703,.203,.72],10957:[.543,.043,1.384],10958:[.543,.043,1.384],10959:[.543,.043,.778],10960:[.543,.043,.778],10961:[.469,.127,.718],10962:[.469,.127,.718],10963:[.803,.163,.718],10964:[.803,.163,.718],10965:[.803,.163,.718],10966:[.803,.163,.718],10967:[.469,-.043,1.348],10968:[.469,-.043,1.348],10969:[.503,.004,.667],10970:[.685,.007,.839],10971:[.666,.007,.839],10972:[.513,.033,.667],10973:[.503,.003,.667],10988:[.367,.017,.667],10989:[.367,.017,.667],10990:[.75,.25,.388],10991:[.668,.02,.4],10992:[.668,.02,.4],10993:[.683,.02,.4],10998:[.58,.002,.278],10999:[.666,.166,1.013],11e3:[.666,.166,1.013],11001:[.634,.306,.778],11002:[.634,.306,.778],11003:[.75,.25,.779],11005:[.75,.25,.634],11006:[.75,.25,.398],11007:[.92,.32,.398]}}},"NCM"),{},["MJX-NCM-MM"]);MathJax.loader&&MathJax.loader.checkVersion("[mathjax-newcm]/chtml/dynamic/math","4.0.0-beta.7","dynamic-font")})();