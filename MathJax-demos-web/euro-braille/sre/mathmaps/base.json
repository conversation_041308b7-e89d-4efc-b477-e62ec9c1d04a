{"base/functions/algebra.min": [{"locale": "base"}, {"key": "deg", "category": "Algebra", "names": ["deg", "Deg"]}, {"key": "det", "category": "Algebra", "names": ["det", "Det"]}, {"key": "dim", "category": "Algebra", "names": ["dim", "<PERSON><PERSON>"]}, {"key": "hom", "category": "Algebra", "names": ["hom", "Hom", "HOM"]}, {"key": "ker", "category": "Algebra", "names": ["ker", "<PERSON><PERSON>"]}, {"key": "Tr", "category": "Algebra", "names": ["Tr", "tr"]}], "base/functions/elementary.min": [{"locale": "base"}, {"key": "log", "category": "Logarithm", "names": ["log"]}, {"key": "ln", "category": "Logarithm", "names": ["ln"]}, {"key": "lg", "category": "Logarithm", "names": ["lg"]}, {"key": "exp", "category": "Elementary", "names": ["exp", "expt"]}, {"key": "gcd", "category": "Elementary", "names": ["gcd", "GCD"]}, {"key": "lcm", "category": "Elementary", "names": ["lcm", "LCM"]}, {"key": "arg", "category": "Complex", "names": ["arg", "Arg"]}, {"key": "im", "category": "Complex", "names": ["im", "Im"]}, {"key": "re", "category": "Complex", "names": ["re", "Re"]}, {"key": "inf", "category": "Limits", "names": ["inf", "Inf"]}, {"key": "lim", "category": "Limits", "names": ["lim"]}, {"key": "liminf", "category": "Limits", "names": ["lim inf", "liminf"]}, {"key": "limsup", "category": "Limits", "names": ["lim sup", "limsup"]}, {"key": "max", "category": "Limits", "names": ["max", "Max"]}, {"key": "min", "category": "Limits", "names": ["min", "Min"]}, {"key": "sup", "category": "Limits", "names": ["sup", "<PERSON><PERSON>"]}, {"key": "<PERSON><PERSON><PERSON>", "category": "Limits", "names": ["<PERSON><PERSON><PERSON>", "inj lim"]}, {"key": "proj<PERSON>", "category": "Limits", "names": ["proj<PERSON>", "proj lim"]}, {"key": "mod", "category": "Elementary", "names": ["mod"]}, {"key": "Pr", "category": "Probability", "names": ["Pr"]}], "base/functions/hyperbolic.min": [{"locale": "base"}, {"key": "cosh", "category": "Hyperbolic", "names": ["cosh"]}, {"key": "coth", "category": "Hyperbolic", "names": ["coth"]}, {"key": "csch", "category": "Hyperbolic", "names": ["csch"]}, {"key": "sech", "category": "Hyperbolic", "names": ["sech"]}, {"key": "sinh", "category": "Hyperbolic", "names": ["sinh"]}, {"key": "tanh", "category": "Hyperbolic", "names": ["tanh"]}, {"key": "arcosh", "category": "Area", "names": ["arcosh", "arccosh"]}, {"key": "arcoth", "category": "Area", "names": ["arcoth", "arccoth"]}, {"key": "<PERSON><PERSON>", "category": "Area", "names": ["<PERSON><PERSON>", "arc<PERSON>ch"]}, {"key": "arsech", "category": "Area", "names": ["arsech", "arcsech"]}, {"key": "a<PERSON><PERSON><PERSON>", "category": "Area", "names": ["a<PERSON><PERSON><PERSON>", "arcsinh"]}, {"key": "artanh", "category": "Area", "names": ["artanh", "arctanh"]}], "base/functions/trigonometry.min": [{"locale": "base"}, {"key": "cos", "category": "Trigonometric", "names": ["cos", "cosine"]}, {"key": "cot", "category": "Trigonometric", "names": ["cot"]}, {"key": "csc", "category": "Trigonometric", "names": ["csc"]}, {"key": "sec", "category": "Trigonometric", "names": ["sec"]}, {"key": "sin", "category": "Trigonometric", "names": ["sin", "sine"]}, {"key": "tan", "category": "Trigonometric", "names": ["tan"]}, {"key": "arccos", "category": "Cyclometric", "names": ["arccos"]}, {"key": "<PERSON><PERSON>", "category": "Cyclometric", "names": ["<PERSON><PERSON>"]}, {"key": "arccsc", "category": "Cyclometric", "names": ["arccsc"]}, {"key": "arcsec", "category": "Cyclometric", "names": ["arcsec"]}, {"key": "arcsin", "category": "Cyclometric", "names": ["arcsin"]}, {"key": "arctan", "category": "Cyclometric", "names": ["arctan"]}], "base/symbols/digits_rest.min": [{"locale": "base"}, {"key": "00B2", "category": "No"}, {"key": "00B3", "category": "No"}, {"key": "00BC", "category": "No"}, {"key": "00BD", "category": "No"}, {"key": "00BE", "category": "No"}, {"key": "2150", "category": "No"}, {"key": "2151", "category": "No"}, {"key": "2152", "category": "No"}, {"key": "2153", "category": "No"}, {"key": "2154", "category": "No"}, {"key": "2155", "category": "No"}, {"key": "2156", "category": "No"}, {"key": "2157", "category": "No"}, {"key": "2158", "category": "No"}, {"key": "2159", "category": "No"}, {"key": "215A", "category": "No"}, {"key": "215B", "category": "No"}, {"key": "215C", "category": "No"}, {"key": "215D", "category": "No"}, {"key": "215E", "category": "No"}, {"key": "215F", "category": "No"}, {"key": "2189", "category": "No"}, {"key": "3248", "category": "No"}, {"key": "3249", "category": "No"}, {"key": "324A", "category": "No"}, {"key": "324B", "category": "No"}, {"key": "324C", "category": "No"}, {"key": "324D", "category": "No"}, {"key": "324E", "category": "No"}, {"key": "324F", "category": "No"}], "base/symbols/greek-rest.min": [{"locale": "base"}, {"key": "0394", "category": "<PERSON>"}], "base/symbols/greek-scripts.min": [{"locale": "base"}, {"key": "1D26", "category": "Ll"}, {"key": "1D27", "category": "Ll"}, {"key": "1D28", "category": "Ll"}, {"key": "1D29", "category": "Ll"}, {"key": "1D2A", "category": "Ll"}, {"key": "1D5E", "category": "Lm"}, {"key": "1D60", "category": "Lm"}, {"key": "1D66", "category": "Lm"}, {"key": "1D67", "category": "Lm"}, {"key": "1D68", "category": "Lm"}, {"key": "1D69", "category": "Lm"}, {"key": "1D6A", "category": "Lm"}], "base/symbols/greek-symbols.min": [{"locale": "base"}, {"key": "03D0", "category": "Ll"}, {"key": "03D7", "category": "Ll"}, {"key": "03F6", "category": "Sm"}, {"key": "1D7CA", "category": "<PERSON>"}, {"key": "1D7CB", "category": "Ll"}], "base/symbols/hebrew_letters.min": [{"locale": "base"}, {"key": "2135", "category": "Lo"}, {"key": "2136", "category": "Lo"}, {"key": "2137", "category": "Lo"}, {"key": "2138", "category": "Lo"}], "base/symbols/latin-lower-double-accent.min": [{"locale": "base"}, {"key": "01D6", "category": "Ll"}, {"key": "01D8", "category": "Ll"}, {"key": "01DA", "category": "Ll"}, {"key": "01DC", "category": "Ll"}, {"key": "01DF", "category": "Ll"}, {"key": "01E1", "category": "Ll"}, {"key": "01ED", "category": "Ll"}, {"key": "01FB", "category": "Ll"}, {"key": "022B", "category": "Ll"}, {"key": "022D", "category": "Ll"}, {"key": "0231", "category": "Ll"}, {"key": "1E09", "category": "Ll"}, {"key": "1E15", "category": "Ll"}, {"key": "1E17", "category": "Ll"}, {"key": "1E1D", "category": "Ll"}, {"key": "1E2F", "category": "Ll"}, {"key": "1E39", "category": "Ll"}, {"key": "1E4D", "category": "Ll"}, {"key": "1E4F", "category": "Ll"}, {"key": "1E51", "category": "Ll"}, {"key": "1E53", "category": "Ll"}, {"key": "1E5D", "category": "Ll"}, {"key": "1E65", "category": "Ll"}, {"key": "1E67", "category": "Ll"}, {"key": "1E69", "category": "Ll"}, {"key": "1E79", "category": "Ll"}, {"key": "1E7B", "category": "Ll"}, {"key": "1EA5", "category": "Ll"}, {"key": "1EA7", "category": "Ll"}, {"key": "1EA9", "category": "Ll"}, {"key": "1EAB", "category": "Ll"}, {"key": "1EAD", "category": "Ll"}, {"key": "1EAF", "category": "Ll"}, {"key": "1EB1", "category": "Ll"}, {"key": "1EB3", "category": "Ll"}, {"key": "1EB5", "category": "Ll"}, {"key": "1EB7", "category": "Ll"}, {"key": "1EBF", "category": "Ll"}, {"key": "1EC1", "category": "Ll"}, {"key": "1EC3", "category": "Ll"}, {"key": "1EC5", "category": "Ll"}, {"key": "1EC7", "category": "Ll"}, {"key": "1ED1", "category": "Ll"}, {"key": "1ED3", "category": "Ll"}, {"key": "1ED5", "category": "Ll"}, {"key": "1ED7", "category": "Ll"}, {"key": "1ED9", "category": "Ll"}, {"key": "1EDB", "category": "Ll"}, {"key": "1EDD", "category": "Ll"}, {"key": "1EDF", "category": "Ll"}, {"key": "1EE1", "category": "Ll"}, {"key": "1EE3", "category": "Ll"}, {"key": "1EE9", "category": "Ll"}, {"key": "1EEB", "category": "Ll"}, {"key": "1EED", "category": "Ll"}, {"key": "1EEF", "category": "Ll"}, {"key": "1EF1", "category": "Ll"}], "base/symbols/latin-lower-phonetic.min": [{"locale": "base"}, {"key": "00F8", "category": "Ll"}, {"key": "0111", "category": "Ll"}, {"key": "0127", "category": "Ll"}, {"key": "0142", "category": "Ll"}, {"key": "0167", "category": "Ll"}, {"key": "0180", "category": "Ll"}, {"key": "019B", "category": "Ll"}, {"key": "01B6", "category": "Ll"}, {"key": "01BE", "category": "Ll"}, {"key": "01E5", "category": "Ll"}, {"key": "01FF", "category": "Ll"}, {"key": "023C", "category": "Ll"}, {"key": "0247", "category": "Ll"}, {"key": "0249", "category": "Ll"}, {"key": "024D", "category": "Ll"}, {"key": "024F", "category": "Ll"}, {"key": "025F", "category": "Ll"}, {"key": "0268", "category": "Ll"}, {"key": "0284", "category": "Ll"}, {"key": "02A1", "category": "Ll"}, {"key": "02A2", "category": "Ll"}, {"key": "1D13", "category": "Ll"}, {"key": "1D7C", "category": "Ll"}, {"key": "1D7D", "category": "Ll"}, {"key": "1D7F", "category": "Ll"}, {"key": "1E9C", "category": "Ll"}, {"key": "1E9D", "category": "Ll"}, {"key": "018D", "category": "Ll"}, {"key": "1E9B", "category": "Ll"}, {"key": "1E9F", "category": "Ll"}, {"key": "0138", "category": "Ll"}, {"key": "017F", "category": "Ll"}, {"key": "0183", "category": "Ll"}, {"key": "0185", "category": "Ll"}, {"key": "0188", "category": "Ll"}, {"key": "018C", "category": "Ll"}, {"key": "0192", "category": "Ll"}, {"key": "0195", "category": "Ll"}, {"key": "0199", "category": "Ll"}, {"key": "019A", "category": "Ll"}, {"key": "019E", "category": "Ll"}, {"key": "01A1", "category": "Ll"}, {"key": "01A3", "category": "Ll"}, {"key": "01A5", "category": "Ll"}, {"key": "01A8", "category": "Ll"}, {"key": "01AA", "category": "Ll"}, {"key": "01AB", "category": "Ll"}, {"key": "01AD", "category": "Ll"}, {"key": "01B0", "category": "Ll"}, {"key": "01B4", "category": "Ll"}, {"key": "01B9", "category": "Ll"}, {"key": "01BA", "category": "Ll"}, {"key": "01BD", "category": "Ll"}, {"key": "01BF", "category": "Ll"}, {"key": "01C6", "category": "Ll"}, {"key": "01C9", "category": "Ll"}, {"key": "01CC", "category": "Ll"}, {"key": "01E3", "category": "Ll"}, {"key": "01EF", "category": "Ll"}, {"key": "01F3", "category": "Ll"}, {"key": "021D", "category": "Ll"}, {"key": "026E", "category": "Ll"}, {"key": "0292", "category": "Ll"}, {"key": "0293", "category": "Ll"}, {"key": "02A4", "category": "Ll"}, {"key": "01DD", "category": "Ll"}, {"key": "01FD", "category": "Ll"}, {"key": "0221", "category": "Ll"}, {"key": "0223", "category": "Ll"}, {"key": "0225", "category": "Ll"}, {"key": "0234", "category": "Ll"}, {"key": "0235", "category": "Ll"}, {"key": "0236", "category": "Ll"}, {"key": "0238", "category": "Ll"}, {"key": "0239", "category": "Ll"}, {"key": "023F", "category": "Ll"}, {"key": "0240", "category": "Ll"}, {"key": "0242", "category": "Ll"}, {"key": "024B", "category": "Ll"}, {"key": "0250", "category": "Ll"}, {"key": "0251", "category": "Ll"}, {"key": "0252", "category": "Ll"}, {"key": "0253", "category": "Ll"}, {"key": "0254", "category": "Ll"}, {"key": "0255", "category": "Ll"}, {"key": "0256", "category": "Ll"}, {"key": "0257", "category": "Ll"}, {"key": "0258", "category": "Ll"}, {"key": "0259", "category": "Ll"}, {"key": "025A", "category": "Ll"}, {"key": "025B", "category": "Ll"}, {"key": "025C", "category": "Ll"}, {"key": "025D", "category": "Ll"}, {"key": "025E", "category": "Ll"}, {"key": "0260", "category": "Ll"}, {"key": "0261", "category": "Ll"}, {"key": "0263", "category": "Ll"}, {"key": "0264", "category": "Ll"}, {"key": "0265", "category": "Ll"}, {"key": "0266", "category": "Ll"}, {"key": "0267", "category": "Ll"}, {"key": "0269", "category": "Ll"}, {"key": "026B", "category": "Ll"}, {"key": "026C", "category": "Ll"}, {"key": "026D", "category": "Ll"}, {"key": "026F", "category": "Ll"}, {"key": "0270", "category": "Ll"}, {"key": "0271", "category": "Ll"}, {"key": "0272", "category": "Ll"}, {"key": "0273", "category": "Ll"}, {"key": "0275", "category": "Ll"}, {"key": "0277", "category": "Ll"}, {"key": "0278", "category": "Ll"}, {"key": "0279", "category": "Ll"}, {"key": "027A", "category": "Ll"}, {"key": "027B", "category": "Ll"}, {"key": "027C", "category": "Ll"}, {"key": "027D", "category": "Ll"}, {"key": "027E", "category": "Ll"}, {"key": "027F", "category": "Ll"}, {"key": "0282", "category": "Ll"}, {"key": "0283", "category": "Ll"}, {"key": "0285", "category": "Ll"}, {"key": "0286", "category": "Ll"}, {"key": "0287", "category": "Ll"}, {"key": "0288", "category": "Ll"}, {"key": "0289", "category": "Ll"}, {"key": "028A", "category": "Ll"}, {"key": "028B", "category": "Ll"}, {"key": "028C", "category": "Ll"}, {"key": "028D", "category": "Ll"}, {"key": "028E", "category": "Ll"}, {"key": "0290", "category": "Ll"}, {"key": "0291", "category": "Ll"}, {"key": "0295", "category": "Ll"}, {"key": "0296", "category": "Ll"}, {"key": "0297", "category": "Ll"}, {"key": "0298", "category": "Ll"}, {"key": "029A", "category": "Ll"}, {"key": "029E", "category": "Ll"}, {"key": "02A0", "category": "Ll"}, {"key": "02A3", "category": "Ll"}, {"key": "02A5", "category": "Ll"}, {"key": "02A6", "category": "Ll"}, {"key": "02A7", "category": "Ll"}, {"key": "02A8", "category": "Ll"}, {"key": "02A9", "category": "Ll"}, {"key": "02AA", "category": "Ll"}, {"key": "02AB", "category": "Ll"}, {"key": "02AC", "category": "Ll"}, {"key": "02AD", "category": "Ll"}, {"key": "02AE", "category": "Ll"}, {"key": "02AF", "category": "Ll"}, {"key": "1D02", "category": "Ll"}, {"key": "1D08", "category": "Ll"}, {"key": "1D09", "category": "Ll"}, {"key": "1D11", "category": "Ll"}, {"key": "1D12", "category": "Ll"}, {"key": "1D14", "category": "Ll"}, {"key": "1D16", "category": "Ll"}, {"key": "1D17", "category": "Ll"}, {"key": "1D1D", "category": "Ll"}, {"key": "1D1E", "category": "Ll"}, {"key": "1D1F", "category": "Ll"}, {"key": "1D24", "category": "Ll"}, {"key": "1D25", "category": "Ll"}, {"key": "1D6B", "category": "Ll"}, {"key": "1D6C", "category": "Ll"}, {"key": "1D6D", "category": "Ll"}, {"key": "1D6E", "category": "Ll"}, {"key": "1D6F", "category": "Ll"}, {"key": "1D70", "category": "Ll"}, {"key": "1D71", "category": "Ll"}, {"key": "1D72", "category": "Ll"}, {"key": "1D73", "category": "Ll"}, {"key": "1D74", "category": "Ll"}, {"key": "1D75", "category": "Ll"}, {"key": "1D76", "category": "Ll"}, {"key": "1D77", "category": "Ll"}, {"key": "1D79", "category": "Ll"}, {"key": "1D7A", "category": "Ll"}, {"key": "1D80", "category": "Ll"}, {"key": "1D81", "category": "Ll"}, {"key": "1D82", "category": "Ll"}, {"key": "1D83", "category": "Ll"}, {"key": "1D84", "category": "Ll"}, {"key": "1D85", "category": "Ll"}, {"key": "1D86", "category": "Ll"}, {"key": "1D87", "category": "Ll"}, {"key": "1D88", "category": "Ll"}, {"key": "1D89", "category": "Ll"}, {"key": "1D8A", "category": "Ll"}, {"key": "1D8B", "category": "Ll"}, {"key": "1D8C", "category": "Ll"}, {"key": "1D8D", "category": "Ll"}, {"key": "1D8E", "category": "Ll"}, {"key": "1D8F", "category": "Ll"}, {"key": "1D90", "category": "Ll"}, {"key": "1D91", "category": "Ll"}, {"key": "1D92", "category": "Ll"}, {"key": "1D93", "category": "Ll"}, {"key": "1D94", "category": "Ll"}, {"key": "1D95", "category": "Ll"}, {"key": "1D96", "category": "Ll"}, {"key": "1D97", "category": "Ll"}, {"key": "1D98", "category": "Ll"}, {"key": "1D99", "category": "Ll"}, {"key": "1D9A", "category": "Ll"}, {"key": "0149", "category": "Ll"}, {"key": "014B", "category": "Ll"}], "base/symbols/latin-lower-single-accent.min": [{"locale": "base"}, {"key": "00E0", "category": "Ll"}, {"key": "00E1", "category": "Ll"}, {"key": "00E2", "category": "Ll"}, {"key": "00E3", "category": "Ll"}, {"key": "00E4", "category": "Ll"}, {"key": "00E5", "category": "Ll"}, {"key": "00E7", "category": "Ll"}, {"key": "00E8", "category": "Ll"}, {"key": "00E9", "category": "Ll"}, {"key": "00EA", "category": "Ll"}, {"key": "00EB", "category": "Ll"}, {"key": "00EC", "category": "Ll"}, {"key": "00ED", "category": "Ll"}, {"key": "00EE", "category": "Ll"}, {"key": "00EF", "category": "Ll"}, {"key": "00F1", "category": "Ll"}, {"key": "00F2", "category": "Ll"}, {"key": "00F3", "category": "Ll"}, {"key": "00F4", "category": "Ll"}, {"key": "00F5", "category": "Ll"}, {"key": "00F6", "category": "Ll"}, {"key": "00F9", "category": "Ll"}, {"key": "00FA", "category": "Ll"}, {"key": "00FB", "category": "Ll"}, {"key": "00FC", "category": "Ll"}, {"key": "00FD", "category": "Ll"}, {"key": "00FF", "category": "Ll"}, {"key": "0101", "category": "Ll"}, {"key": "0103", "category": "Ll"}, {"key": "0105", "category": "Ll"}, {"key": "0107", "category": "Ll"}, {"key": "0109", "category": "Ll"}, {"key": "010B", "category": "Ll"}, {"key": "010D", "category": "Ll"}, {"key": "010F", "category": "Ll"}, {"key": "0113", "category": "Ll"}, {"key": "0115", "category": "Ll"}, {"key": "0117", "category": "Ll"}, {"key": "0119", "category": "Ll"}, {"key": "011B", "category": "Ll"}, {"key": "011D", "category": "Ll"}, {"key": "011F", "category": "Ll"}, {"key": "0121", "category": "Ll"}, {"key": "0123", "category": "Ll"}, {"key": "0125", "category": "Ll"}, {"key": "0129", "category": "Ll"}, {"key": "012B", "category": "Ll"}, {"key": "012D", "category": "Ll"}, {"key": "012F", "category": "Ll"}, {"key": "0131", "category": "Ll"}, {"key": "0135", "category": "Ll"}, {"key": "0137", "category": "Ll"}, {"key": "013A", "category": "Ll"}, {"key": "013C", "category": "Ll"}, {"key": "013E", "category": "Ll"}, {"key": "0140", "category": "Ll"}, {"key": "0144", "category": "Ll"}, {"key": "0146", "category": "Ll"}, {"key": "0148", "category": "Ll"}, {"key": "014D", "category": "Ll"}, {"key": "014F", "category": "Ll"}, {"key": "0151", "category": "Ll"}, {"key": "0155", "category": "Ll"}, {"key": "0157", "category": "Ll"}, {"key": "0159", "category": "Ll"}, {"key": "015B", "category": "Ll"}, {"key": "015D", "category": "Ll"}, {"key": "015F", "category": "Ll"}, {"key": "0161", "category": "Ll"}, {"key": "0163", "category": "Ll"}, {"key": "0165", "category": "Ll"}, {"key": "0169", "category": "Ll"}, {"key": "016B", "category": "Ll"}, {"key": "016D", "category": "Ll"}, {"key": "016F", "category": "Ll"}, {"key": "0171", "category": "Ll"}, {"key": "0173", "category": "Ll"}, {"key": "0175", "category": "Ll"}, {"key": "0177", "category": "Ll"}, {"key": "017A", "category": "Ll"}, {"key": "017C", "category": "Ll"}, {"key": "017E", "category": "Ll"}, {"key": "01CE", "category": "Ll"}, {"key": "01D0", "category": "Ll"}, {"key": "01D2", "category": "Ll"}, {"key": "01D4", "category": "Ll"}, {"key": "01E7", "category": "Ll"}, {"key": "01E9", "category": "Ll"}, {"key": "01EB", "category": "Ll"}, {"key": "01F0", "category": "Ll"}, {"key": "01F5", "category": "Ll"}, {"key": "01F9", "category": "Ll"}, {"key": "0201", "category": "Ll"}, {"key": "0203", "category": "Ll"}, {"key": "0205", "category": "Ll"}, {"key": "0207", "category": "Ll"}, {"key": "0209", "category": "Ll"}, {"key": "020B", "category": "Ll"}, {"key": "020D", "category": "Ll"}, {"key": "020F", "category": "Ll"}, {"key": "0211", "category": "Ll"}, {"key": "0213", "category": "Ll"}, {"key": "0215", "category": "Ll"}, {"key": "0217", "category": "Ll"}, {"key": "0219", "category": "Ll"}, {"key": "021B", "category": "Ll"}, {"key": "021F", "category": "Ll"}, {"key": "0227", "category": "Ll"}, {"key": "0229", "category": "Ll"}, {"key": "022F", "category": "Ll"}, {"key": "0233", "category": "Ll"}, {"key": "0237", "category": "Ll"}, {"key": "1E01", "category": "Ll"}, {"key": "1E03", "category": "Ll"}, {"key": "1E05", "category": "Ll"}, {"key": "1E07", "category": "Ll"}, {"key": "1E0B", "category": "Ll"}, {"key": "1E0D", "category": "Ll"}, {"key": "1E0F", "category": "Ll"}, {"key": "1E11", "category": "Ll"}, {"key": "1E13", "category": "Ll"}, {"key": "1E19", "category": "Ll"}, {"key": "1E1B", "category": "Ll"}, {"key": "1E1F", "category": "Ll"}, {"key": "1E21", "category": "Ll"}, {"key": "1E23", "category": "Ll"}, {"key": "1E25", "category": "Ll"}, {"key": "1E27", "category": "Ll"}, {"key": "1E29", "category": "Ll"}, {"key": "1E2B", "category": "Ll"}, {"key": "1E2D", "category": "Ll"}, {"key": "1E31", "category": "Ll"}, {"key": "1E33", "category": "Ll"}, {"key": "1E35", "category": "Ll"}, {"key": "1E37", "category": "Ll"}, {"key": "1E3B", "category": "Ll"}, {"key": "1E3D", "category": "Ll"}, {"key": "1E3F", "category": "Ll"}, {"key": "1E41", "category": "Ll"}, {"key": "1E43", "category": "Ll"}, {"key": "1E45", "category": "Ll"}, {"key": "1E47", "category": "Ll"}, {"key": "1E49", "category": "Ll"}, {"key": "1E4B", "category": "Ll"}, {"key": "1E55", "category": "Ll"}, {"key": "1E57", "category": "Ll"}, {"key": "1E59", "category": "Ll"}, {"key": "1E5B", "category": "Ll"}, {"key": "1E5F", "category": "Ll"}, {"key": "1E61", "category": "Ll"}, {"key": "1E63", "category": "Ll"}, {"key": "1E6B", "category": "Ll"}, {"key": "1E6D", "category": "Ll"}, {"key": "1E6F", "category": "Ll"}, {"key": "1E71", "category": "Ll"}, {"key": "1E73", "category": "Ll"}, {"key": "1E75", "category": "Ll"}, {"key": "1E77", "category": "Ll"}, {"key": "1E7D", "category": "Ll"}, {"key": "1E7F", "category": "Ll"}, {"key": "1E81", "category": "Ll"}, {"key": "1E83", "category": "Ll"}, {"key": "1E85", "category": "Ll"}, {"key": "1E87", "category": "Ll"}, {"key": "1E89", "category": "Ll"}, {"key": "1E8B", "category": "Ll"}, {"key": "1E8D", "category": "Ll"}, {"key": "1E8F", "category": "Ll"}, {"key": "1E91", "category": "Ll"}, {"key": "1E93", "category": "Ll"}, {"key": "1E95", "category": "Ll"}, {"key": "1E96", "category": "Ll"}, {"key": "1E97", "category": "Ll"}, {"key": "1E98", "category": "Ll"}, {"key": "1E99", "category": "Ll"}, {"key": "1E9A", "category": "Ll"}, {"key": "1EA1", "category": "Ll"}, {"key": "1EA3", "category": "Ll"}, {"key": "1EB9", "category": "Ll"}, {"key": "1EBB", "category": "Ll"}, {"key": "1EBD", "category": "Ll"}, {"key": "1EC9", "category": "Ll"}, {"key": "1ECB", "category": "Ll"}, {"key": "1ECD", "category": "Ll"}, {"key": "1ECF", "category": "Ll"}, {"key": "1EE5", "category": "Ll"}, {"key": "1EE7", "category": "Ll"}, {"key": "1EF3", "category": "Ll"}, {"key": "1EF5", "category": "Ll"}, {"key": "1EF7", "category": "Ll"}, {"key": "1EF9", "category": "Ll"}], "base/symbols/latin-rest.min": [{"locale": "base"}, {"key": "210E", "category": "Ll"}, {"key": "0363", "category": "Mn"}, {"key": "0364", "category": "Mn"}, {"key": "0365", "category": "Mn"}, {"key": "0366", "category": "Mn"}, {"key": "0367", "category": "Mn"}, {"key": "0368", "category": "Mn"}, {"key": "0369", "category": "Mn"}, {"key": "036A", "category": "Mn"}, {"key": "036B", "category": "Mn"}, {"key": "036C", "category": "Mn"}, {"key": "036D", "category": "Mn"}, {"key": "036E", "category": "Mn"}, {"key": "036F", "category": "Mn"}, {"key": "1D62", "category": "Lm"}, {"key": "1D63", "category": "Lm"}, {"key": "1D64", "category": "Lm"}, {"key": "1D65", "category": "Lm"}, {"key": "1DCA", "category": "Mn"}, {"key": "1DD3", "category": "Mn"}, {"key": "1DD4", "category": "Mn"}, {"key": "1DD5", "category": "Mn"}, {"key": "1DD6", "category": "Mn"}, {"key": "1DD7", "category": "Mn"}, {"key": "1DD8", "category": "Mn"}, {"key": "1DD9", "category": "Mn"}, {"key": "1DDA", "category": "Mn"}, {"key": "1DDB", "category": "Mn"}, {"key": "1DDC", "category": "Mn"}, {"key": "1DDD", "category": "Mn"}, {"key": "1DDE", "category": "Mn"}, {"key": "1DDF", "category": "Mn"}, {"key": "1DE0", "category": "Mn"}, {"key": "1DE1", "category": "Mn"}, {"key": "1DE2", "category": "Mn"}, {"key": "1DE3", "category": "Mn"}, {"key": "1DE4", "category": "Mn"}, {"key": "1DE5", "category": "Mn"}, {"key": "1DE6", "category": "Mn"}, {"key": "2071", "category": "Lm"}, {"key": "207F", "category": "Lm"}, {"key": "2090", "category": "Lm"}, {"key": "2091", "category": "Lm"}, {"key": "2092", "category": "Lm"}, {"key": "2093", "category": "Lm"}, {"key": "2094", "category": "Lm"}, {"key": "2095", "category": "Lm"}, {"key": "2096", "category": "Lm"}, {"key": "2097", "category": "Lm"}, {"key": "2098", "category": "Lm"}, {"key": "2099", "category": "Lm"}, {"key": "209A", "category": "Lm"}, {"key": "209B", "category": "Lm"}, {"key": "209C", "category": "Lm"}, {"key": "2C7C", "category": "Lm"}, {"key": "1F12A", "category": "So"}, {"key": "1F12B", "category": "So"}, {"key": "1F12C", "category": "So"}, {"key": "1F18A", "category": "So"}], "base/symbols/latin-upper-double-accent.min": [{"locale": "base"}, {"key": "01D5", "category": "<PERSON>"}, {"key": "01D7", "category": "<PERSON>"}, {"key": "01D9", "category": "<PERSON>"}, {"key": "01DB", "category": "<PERSON>"}, {"key": "01DE", "category": "<PERSON>"}, {"key": "01E0", "category": "<PERSON>"}, {"key": "01EC", "category": "<PERSON>"}, {"key": "01FA", "category": "<PERSON>"}, {"key": "022A", "category": "<PERSON>"}, {"key": "022C", "category": "<PERSON>"}, {"key": "0230", "category": "<PERSON>"}, {"key": "1E08", "category": "<PERSON>"}, {"key": "1E14", "category": "<PERSON>"}, {"key": "1E16", "category": "<PERSON>"}, {"key": "1E1C", "category": "<PERSON>"}, {"key": "1E2E", "category": "<PERSON>"}, {"key": "1E38", "category": "<PERSON>"}, {"key": "1E4C", "category": "<PERSON>"}, {"key": "1E4E", "category": "<PERSON>"}, {"key": "1E50", "category": "<PERSON>"}, {"key": "1E52", "category": "<PERSON>"}, {"key": "1E5C", "category": "<PERSON>"}, {"key": "1E64", "category": "<PERSON>"}, {"key": "1E66", "category": "<PERSON>"}, {"key": "1E68", "category": "<PERSON>"}, {"key": "1E78", "category": "<PERSON>"}, {"key": "1E7A", "category": "<PERSON>"}, {"key": "1EA4", "category": "<PERSON>"}, {"key": "1EA6", "category": "<PERSON>"}, {"key": "1EA8", "category": "<PERSON>"}, {"key": "1EAA", "category": "<PERSON>"}, {"key": "1EAC", "category": "<PERSON>"}, {"key": "1EAE", "category": "<PERSON>"}, {"key": "1EB0", "category": "<PERSON>"}, {"key": "1EB2", "category": "<PERSON>"}, {"key": "1EB4", "category": "<PERSON>"}, {"key": "1EB6", "category": "<PERSON>"}, {"key": "1EBE", "category": "<PERSON>"}, {"key": "1EC0", "category": "<PERSON>"}, {"key": "1EC2", "category": "<PERSON>"}, {"key": "1EC4", "category": "<PERSON>"}, {"key": "1EC6", "category": "<PERSON>"}, {"key": "1ED0", "category": "<PERSON>"}, {"key": "1ED2", "category": "<PERSON>"}, {"key": "1ED4", "category": "<PERSON>"}, {"key": "1ED6", "category": "<PERSON>"}, {"key": "1ED8", "category": "<PERSON>"}, {"key": "1EDA", "category": "<PERSON>"}, {"key": "1EDC", "category": "<PERSON>"}, {"key": "1EDE", "category": "<PERSON>"}, {"key": "1EE0", "category": "<PERSON>"}, {"key": "1EE2", "category": "<PERSON>"}, {"key": "1EE8", "category": "<PERSON>"}, {"key": "1EEA", "category": "<PERSON>"}, {"key": "1EEC", "category": "<PERSON>"}, {"key": "1EEE", "category": "<PERSON>"}, {"key": "1EF0", "category": "<PERSON>"}], "base/symbols/latin-upper-single-accent.min": [{"locale": "base"}, {"key": "00C0", "category": "<PERSON>"}, {"key": "00C1", "category": "<PERSON>"}, {"key": "00C2", "category": "<PERSON>"}, {"key": "00C3", "category": "<PERSON>"}, {"key": "00C4", "category": "<PERSON>"}, {"key": "00C5", "category": "<PERSON>"}, {"key": "00C7", "category": "<PERSON>"}, {"key": "00C8", "category": "<PERSON>"}, {"key": "00C9", "category": "<PERSON>"}, {"key": "00CA", "category": "<PERSON>"}, {"key": "00CB", "category": "<PERSON>"}, {"key": "00CC", "category": "<PERSON>"}, {"key": "00CD", "category": "<PERSON>"}, {"key": "00CE", "category": "<PERSON>"}, {"key": "00CF", "category": "<PERSON>"}, {"key": "00D1", "category": "<PERSON>"}, {"key": "00D2", "category": "<PERSON>"}, {"key": "00D3", "category": "<PERSON>"}, {"key": "00D4", "category": "<PERSON>"}, {"key": "00D5", "category": "<PERSON>"}, {"key": "00D6", "category": "<PERSON>"}, {"key": "00D9", "category": "<PERSON>"}, {"key": "00DA", "category": "<PERSON>"}, {"key": "00DB", "category": "<PERSON>"}, {"key": "00DC", "category": "<PERSON>"}, {"key": "00DD", "category": "<PERSON>"}, {"key": "0100", "category": "<PERSON>"}, {"key": "0102", "category": "<PERSON>"}, {"key": "0104", "category": "<PERSON>"}, {"key": "0106", "category": "<PERSON>"}, {"key": "0108", "category": "<PERSON>"}, {"key": "010A", "category": "<PERSON>"}, {"key": "010C", "category": "<PERSON>"}, {"key": "010E", "category": "<PERSON>"}, {"key": "0112", "category": "<PERSON>"}, {"key": "0114", "category": "<PERSON>"}, {"key": "0116", "category": "<PERSON>"}, {"key": "0118", "category": "<PERSON>"}, {"key": "011A", "category": "<PERSON>"}, {"key": "011C", "category": "<PERSON>"}, {"key": "011E", "category": "<PERSON>"}, {"key": "0120", "category": "<PERSON>"}, {"key": "0122", "category": "<PERSON>"}, {"key": "0124", "category": "<PERSON>"}, {"key": "0128", "category": "<PERSON>"}, {"key": "012A", "category": "<PERSON>"}, {"key": "012C", "category": "<PERSON>"}, {"key": "012E", "category": "<PERSON>"}, {"key": "0130", "category": "<PERSON>"}, {"key": "0134", "category": "<PERSON>"}, {"key": "0136", "category": "<PERSON>"}, {"key": "0139", "category": "<PERSON>"}, {"key": "013B", "category": "<PERSON>"}, {"key": "013D", "category": "<PERSON>"}, {"key": "013F", "category": "<PERSON>"}, {"key": "0143", "category": "<PERSON>"}, {"key": "0145", "category": "<PERSON>"}, {"key": "0147", "category": "<PERSON>"}, {"key": "014C", "category": "<PERSON>"}, {"key": "014E", "category": "<PERSON>"}, {"key": "0150", "category": "<PERSON>"}, {"key": "0154", "category": "<PERSON>"}, {"key": "0156", "category": "<PERSON>"}, {"key": "0158", "category": "<PERSON>"}, {"key": "015A", "category": "<PERSON>"}, {"key": "015C", "category": "<PERSON>"}, {"key": "015E", "category": "<PERSON>"}, {"key": "0160", "category": "<PERSON>"}, {"key": "0162", "category": "<PERSON>"}, {"key": "0164", "category": "<PERSON>"}, {"key": "0168", "category": "<PERSON>"}, {"key": "016A", "category": "<PERSON>"}, {"key": "016C", "category": "<PERSON>"}, {"key": "016E", "category": "<PERSON>"}, {"key": "0170", "category": "<PERSON>"}, {"key": "0172", "category": "<PERSON>"}, {"key": "0174", "category": "<PERSON>"}, {"key": "0176", "category": "<PERSON>"}, {"key": "0178", "category": "<PERSON>"}, {"key": "0179", "category": "<PERSON>"}, {"key": "017B", "category": "<PERSON>"}, {"key": "017D", "category": "<PERSON>"}, {"key": "01CD", "category": "<PERSON>"}, {"key": "01CF", "category": "<PERSON>"}, {"key": "01D1", "category": "<PERSON>"}, {"key": "01D3", "category": "<PERSON>"}, {"key": "01E6", "category": "<PERSON>"}, {"key": "01E8", "category": "<PERSON>"}, {"key": "01EA", "category": "<PERSON>"}, {"key": "01F4", "category": "<PERSON>"}, {"key": "01F8", "category": "<PERSON>"}, {"key": "0200", "category": "<PERSON>"}, {"key": "0202", "category": "<PERSON>"}, {"key": "0204", "category": "<PERSON>"}, {"key": "0206", "category": "<PERSON>"}, {"key": "0208", "category": "<PERSON>"}, {"key": "020A", "category": "<PERSON>"}, {"key": "020C", "category": "<PERSON>"}, {"key": "020E", "category": "<PERSON>"}, {"key": "0210", "category": "<PERSON>"}, {"key": "0212", "category": "<PERSON>"}, {"key": "0214", "category": "<PERSON>"}, {"key": "0216", "category": "<PERSON>"}, {"key": "0218", "category": "<PERSON>"}, {"key": "021A", "category": "<PERSON>"}, {"key": "021E", "category": "<PERSON>"}, {"key": "0226", "category": "<PERSON>"}, {"key": "0228", "category": "<PERSON>"}, {"key": "022E", "category": "<PERSON>"}, {"key": "0232", "category": "<PERSON>"}, {"key": "1E00", "category": "<PERSON>"}, {"key": "1E02", "category": "<PERSON>"}, {"key": "1E04", "category": "<PERSON>"}, {"key": "1E06", "category": "<PERSON>"}, {"key": "1E0A", "category": "<PERSON>"}, {"key": "1E0C", "category": "<PERSON>"}, {"key": "1E0E", "category": "<PERSON>"}, {"key": "1E10", "category": "<PERSON>"}, {"key": "1E12", "category": "<PERSON>"}, {"key": "1E18", "category": "<PERSON>"}, {"key": "1E1A", "category": "<PERSON>"}, {"key": "1E1E", "category": "<PERSON>"}, {"key": "1E20", "category": "<PERSON>"}, {"key": "1E22", "category": "<PERSON>"}, {"key": "1E24", "category": "<PERSON>"}, {"key": "1E26", "category": "<PERSON>"}, {"key": "1E28", "category": "<PERSON>"}, {"key": "1E2A", "category": "<PERSON>"}, {"key": "1E2C", "category": "<PERSON>"}, {"key": "1E30", "category": "<PERSON>"}, {"key": "1E32", "category": "<PERSON>"}, {"key": "1E34", "category": "<PERSON>"}, {"key": "1E36", "category": "<PERSON>"}, {"key": "1E3A", "category": "<PERSON>"}, {"key": "1E3C", "category": "<PERSON>"}, {"key": "1E3E", "category": "<PERSON>"}, {"key": "1E40", "category": "<PERSON>"}, {"key": "1E42", "category": "<PERSON>"}, {"key": "1E44", "category": "<PERSON>"}, {"key": "1E46", "category": "<PERSON>"}, {"key": "1E48", "category": "<PERSON>"}, {"key": "1E4A", "category": "<PERSON>"}, {"key": "1E54", "category": "<PERSON>"}, {"key": "1E56", "category": "<PERSON>"}, {"key": "1E58", "category": "<PERSON>"}, {"key": "1E5A", "category": "<PERSON>"}, {"key": "1E5E", "category": "<PERSON>"}, {"key": "1E60", "category": "<PERSON>"}, {"key": "1E62", "category": "<PERSON>"}, {"key": "1E6A", "category": "<PERSON>"}, {"key": "1E6C", "category": "<PERSON>"}, {"key": "1E6E", "category": "<PERSON>"}, {"key": "1E70", "category": "<PERSON>"}, {"key": "1E72", "category": "<PERSON>"}, {"key": "1E74", "category": "<PERSON>"}, {"key": "1E76", "category": "<PERSON>"}, {"key": "1E7C", "category": "<PERSON>"}, {"key": "1E7E", "category": "<PERSON>"}, {"key": "1E80", "category": "<PERSON>"}, {"key": "1E82", "category": "<PERSON>"}, {"key": "1E84", "category": "<PERSON>"}, {"key": "1E86", "category": "<PERSON>"}, {"key": "1E88", "category": "<PERSON>"}, {"key": "1E8A", "category": "<PERSON>"}, {"key": "1E8C", "category": "<PERSON>"}, {"key": "1E8E", "category": "<PERSON>"}, {"key": "1E90", "category": "<PERSON>"}, {"key": "1E92", "category": "<PERSON>"}, {"key": "1E94", "category": "<PERSON>"}, {"key": "1EA0", "category": "<PERSON>"}, {"key": "1EA2", "category": "<PERSON>"}, {"key": "1EB8", "category": "<PERSON>"}, {"key": "1EBA", "category": "<PERSON>"}, {"key": "1EBC", "category": "<PERSON>"}, {"key": "1EC8", "category": "<PERSON>"}, {"key": "1ECA", "category": "<PERSON>"}, {"key": "1ECC", "category": "<PERSON>"}, {"key": "1ECE", "category": "<PERSON>"}, {"key": "1EE4", "category": "<PERSON>"}, {"key": "1EE6", "category": "<PERSON>"}, {"key": "1EF2", "category": "<PERSON>"}, {"key": "1EF4", "category": "<PERSON>"}, {"key": "1EF6", "category": "<PERSON>"}, {"key": "1EF8", "category": "<PERSON>"}], "base/symbols/math_angles.min": [{"locale": "base"}, {"key": "22BE", "category": "Sm"}, {"key": "237C", "category": "Sm"}, {"key": "27C0", "category": "Sm"}, {"key": "299B", "category": "Sm"}, {"key": "299C", "category": "Sm"}, {"key": "299D", "category": "Sm"}, {"key": "299E", "category": "Sm"}, {"key": "299F", "category": "Sm"}, {"key": "29A0", "category": "Sm"}, {"key": "29A1", "category": "Sm"}, {"key": "29A2", "category": "Sm"}, {"key": "29A3", "category": "Sm"}, {"key": "29A4", "category": "Sm"}, {"key": "29A5", "category": "Sm"}, {"key": "29A6", "category": "Sm"}, {"key": "29A7", "category": "Sm"}, {"key": "29A8", "category": "Sm"}, {"key": "29A9", "category": "Sm"}, {"key": "29AA", "category": "Sm"}, {"key": "29AB", "category": "Sm"}, {"key": "29AC", "category": "Sm"}, {"key": "29AD", "category": "Sm"}, {"key": "29AE", "category": "Sm"}, {"key": "29AF", "category": "Sm"}], "base/symbols/math_arrows.min": [{"locale": "base"}, {"key": "2190", "category": "Sm"}, {"key": "2191", "category": "Sm"}, {"key": "2192", "category": "Sm"}, {"key": "2193", "category": "Sm"}, {"key": "2194", "category": "Sm"}, {"key": "2195", "category": "So"}, {"key": "2196", "category": "So"}, {"key": "2197", "category": "So"}, {"key": "2198", "category": "So"}, {"key": "2199", "category": "So"}, {"key": "219A", "category": "Sm"}, {"key": "219B", "category": "Sm"}, {"key": "219C", "category": "So"}, {"key": "219D", "category": "So"}, {"key": "219E", "category": "So"}, {"key": "219F", "category": "So"}, {"key": "21A0", "category": "Sm"}, {"key": "21A1", "category": "So"}, {"key": "21A2", "category": "So"}, {"key": "21A3", "category": "Sm"}, {"key": "21A4", "category": "So"}, {"key": "21A5", "category": "So"}, {"key": "21A6", "category": "Sm"}, {"key": "21A7", "category": "So"}, {"key": "21A8", "category": "So"}, {"key": "21A9", "category": "So"}, {"key": "21AA", "category": "So"}, {"key": "21AB", "category": "So"}, {"key": "21AC", "category": "So"}, {"key": "21AD", "category": "So"}, {"key": "21AE", "category": "Sm"}, {"key": "21AF", "category": "So"}, {"key": "21B0", "category": "So"}, {"key": "21B1", "category": "So"}, {"key": "21B2", "category": "So"}, {"key": "21B3", "category": "So"}, {"key": "21B4", "category": "So"}, {"key": "21B5", "category": "So"}, {"key": "21B6", "category": "So"}, {"key": "21B7", "category": "So"}, {"key": "21B8", "category": "So"}, {"key": "21B9", "category": "So"}, {"key": "21BA", "category": "So"}, {"key": "21BB", "category": "So"}, {"key": "21C4", "category": "So"}, {"key": "21C5", "category": "So"}, {"key": "21C6", "category": "So"}, {"key": "21C7", "category": "So"}, {"key": "21C8", "category": "So"}, {"key": "21C9", "category": "So"}, {"key": "21CA", "category": "So"}, {"key": "21CD", "category": "So"}, {"key": "21CE", "category": "Sm"}, {"key": "21CF", "category": "Sm"}, {"key": "21D0", "category": "So"}, {"key": "21D1", "category": "So"}, {"key": "21D2", "category": "Sm"}, {"key": "21D3", "category": "So"}, {"key": "21D4", "category": "Sm"}, {"key": "21D5", "category": "So"}, {"key": "21D6", "category": "So"}, {"key": "21D7", "category": "So"}, {"key": "21D8", "category": "So"}, {"key": "21D9", "category": "So"}, {"key": "21DA", "category": "So"}, {"key": "21DB", "category": "So"}, {"key": "21DC", "category": "So"}, {"key": "21DD", "category": "So"}, {"key": "21DE", "category": "So"}, {"key": "21DF", "category": "So"}, {"key": "21E0", "category": "So"}, {"key": "21E1", "category": "So"}, {"key": "21E2", "category": "So"}, {"key": "21E3", "category": "So"}, {"key": "21E4", "category": "So"}, {"key": "21E5", "category": "So"}, {"key": "21E6", "category": "So"}, {"key": "21E7", "category": "So"}, {"key": "21E8", "category": "So"}, {"key": "21E9", "category": "So"}, {"key": "21EA", "category": "So"}, {"key": "21EB", "category": "So"}, {"key": "21EC", "category": "So"}, {"key": "21ED", "category": "So"}, {"key": "21EE", "category": "So"}, {"key": "21EF", "category": "So"}, {"key": "21F0", "category": "So"}, {"key": "21F1", "category": "So"}, {"key": "21F2", "category": "So"}, {"key": "21F3", "category": "So"}, {"key": "21F4", "category": "Sm"}, {"key": "21F5", "category": "Sm"}, {"key": "21F6", "category": "Sm"}, {"key": "21F7", "category": "Sm"}, {"key": "21F8", "category": "Sm"}, {"key": "21F9", "category": "Sm"}, {"key": "21FA", "category": "Sm"}, {"key": "21FB", "category": "Sm"}, {"key": "21FC", "category": "Sm"}, {"key": "21FD", "category": "Sm"}, {"key": "21FE", "category": "Sm"}, {"key": "21FF", "category": "Sm"}, {"key": "2301", "category": "So"}, {"key": "2303", "category": "So"}, {"key": "2304", "category": "So"}, {"key": "2324", "category": "So"}, {"key": "238B", "category": "So"}, {"key": "2794", "category": "So"}, {"key": "2798", "category": "So"}, {"key": "2799", "category": "So"}, {"key": "279A", "category": "So"}, {"key": "279B", "category": "So"}, {"key": "279C", "category": "So"}, {"key": "279D", "category": "So"}, {"key": "279E", "category": "So"}, {"key": "279F", "category": "So"}, {"key": "27A0", "category": "So"}, {"key": "27A1", "category": "So"}, {"key": "27A2", "category": "So"}, {"key": "27A3", "category": "So"}, {"key": "27A4", "category": "So"}, {"key": "27A5", "category": "So"}, {"key": "27A6", "category": "So"}, {"key": "27A7", "category": "So"}, {"key": "27A8", "category": "So"}, {"key": "27A9", "category": "So"}, {"key": "27AA", "category": "So"}, {"key": "27AB", "category": "So"}, {"key": "27AC", "category": "So"}, {"key": "27AD", "category": "So"}, {"key": "27AE", "category": "So"}, {"key": "27AF", "category": "So"}, {"key": "27B1", "category": "So"}, {"key": "27B2", "category": "So"}, {"key": "27B3", "category": "So"}, {"key": "27B4", "category": "So"}, {"key": "27B5", "category": "So"}, {"key": "27B6", "category": "So"}, {"key": "27B7", "category": "So"}, {"key": "27B8", "category": "So"}, {"key": "27B9", "category": "So"}, {"key": "27BA", "category": "So"}, {"key": "27BB", "category": "So"}, {"key": "27BC", "category": "So"}, {"key": "27BD", "category": "So"}, {"key": "27BE", "category": "So"}, {"key": "27F0", "category": "Sm"}, {"key": "27F1", "category": "Sm"}, {"key": "27F2", "category": "Sm"}, {"key": "27F3", "category": "Sm"}, {"key": "27F4", "category": "Sm"}, {"key": "27F5", "category": "Sm"}, {"key": "27F6", "category": "Sm"}, {"key": "27F7", "category": "Sm"}, {"key": "27F8", "category": "Sm"}, {"key": "27F9", "category": "Sm"}, {"key": "27FA", "category": "Sm"}, {"key": "27FB", "category": "Sm"}, {"key": "27FC", "category": "Sm"}, {"key": "27FD", "category": "Sm"}, {"key": "27FE", "category": "Sm"}, {"key": "27FF", "category": "Sm"}, {"key": "2900", "category": "Sm"}, {"key": "2901", "category": "Sm"}, {"key": "2902", "category": "Sm"}, {"key": "2903", "category": "Sm"}, {"key": "2904", "category": "Sm"}, {"key": "2905", "category": "Sm"}, {"key": "2906", "category": "Sm"}, {"key": "2907", "category": "Sm"}, {"key": "2908", "category": "Sm"}, {"key": "2909", "category": "Sm"}, {"key": "290A", "category": "Sm"}, {"key": "290B", "category": "Sm"}, {"key": "290C", "category": "Sm"}, {"key": "290D", "category": "Sm"}, {"key": "290E", "category": "Sm"}, {"key": "290F", "category": "Sm"}, {"key": "2910", "category": "Sm"}, {"key": "2911", "category": "Sm"}, {"key": "2912", "category": "Sm"}, {"key": "2913", "category": "Sm"}, {"key": "2914", "category": "Sm"}, {"key": "2915", "category": "Sm"}, {"key": "2916", "category": "Sm"}, {"key": "2917", "category": "Sm"}, {"key": "2918", "category": "Sm"}, {"key": "2919", "category": "Sm"}, {"key": "291A", "category": "Sm"}, {"key": "291B", "category": "Sm"}, {"key": "291C", "category": "Sm"}, {"key": "291D", "category": "Sm"}, {"key": "291E", "category": "Sm"}, {"key": "291F", "category": "Sm"}, {"key": "2920", "category": "Sm"}, {"key": "2921", "category": "Sm"}, {"key": "2922", "category": "Sm"}, {"key": "2923", "category": "Sm"}, {"key": "2924", "category": "Sm"}, {"key": "2925", "category": "Sm"}, {"key": "2926", "category": "Sm"}, {"key": "2927", "category": "Sm"}, {"key": "2928", "category": "Sm"}, {"key": "2929", "category": "Sm"}, {"key": "292A", "category": "Sm"}, {"key": "292D", "category": "Sm"}, {"key": "292E", "category": "Sm"}, {"key": "292F", "category": "Sm"}, {"key": "2930", "category": "Sm"}, {"key": "2931", "category": "Sm"}, {"key": "2932", "category": "Sm"}, {"key": "2933", "category": "Sm"}, {"key": "2934", "category": "Sm"}, {"key": "2935", "category": "Sm"}, {"key": "2936", "category": "Sm"}, {"key": "2937", "category": "Sm"}, {"key": "2938", "category": "Sm"}, {"key": "2939", "category": "Sm"}, {"key": "293A", "category": "Sm"}, {"key": "293B", "category": "Sm"}, {"key": "293C", "category": "Sm"}, {"key": "293D", "category": "Sm"}, {"key": "293E", "category": "Sm"}, {"key": "293F", "category": "Sm"}, {"key": "2940", "category": "Sm"}, {"key": "2941", "category": "Sm"}, {"key": "2942", "category": "Sm"}, {"key": "2943", "category": "Sm"}, {"key": "2944", "category": "Sm"}, {"key": "2945", "category": "Sm"}, {"key": "2946", "category": "Sm"}, {"key": "2947", "category": "Sm"}, {"key": "2948", "category": "Sm"}, {"key": "2949", "category": "Sm"}, {"key": "2970", "category": "Sm"}, {"key": "2971", "category": "Sm"}, {"key": "2972", "category": "Sm"}, {"key": "2973", "category": "Sm"}, {"key": "2974", "category": "Sm"}, {"key": "2975", "category": "Sm"}, {"key": "2976", "category": "Sm"}, {"key": "2977", "category": "Sm"}, {"key": "2978", "category": "Sm"}, {"key": "2979", "category": "Sm"}, {"key": "297A", "category": "Sm"}, {"key": "297B", "category": "Sm"}, {"key": "29B3", "category": "Sm"}, {"key": "29B4", "category": "Sm"}, {"key": "29BD", "category": "Sm"}, {"key": "29EA", "category": "Sm"}, {"key": "29EC", "category": "Sm"}, {"key": "29ED", "category": "Sm"}, {"key": "2A17", "category": "Sm"}, {"key": "2B00", "category": "So"}, {"key": "2B01", "category": "So"}, {"key": "2B02", "category": "So"}, {"key": "2B03", "category": "So"}, {"key": "2B04", "category": "So"}, {"key": "2B05", "category": "So"}, {"key": "2B06", "category": "So"}, {"key": "2B07", "category": "So"}, {"key": "2B08", "category": "So"}, {"key": "2B09", "category": "So"}, {"key": "2B0A", "category": "So"}, {"key": "2B0B", "category": "So"}, {"key": "2B0C", "category": "So"}, {"key": "2B0D", "category": "So"}, {"key": "2B0E", "category": "So"}, {"key": "2B0F", "category": "So"}, {"key": "2B10", "category": "So"}, {"key": "2B11", "category": "So"}, {"key": "2B30", "category": "Sm"}, {"key": "2B31", "category": "Sm"}, {"key": "2B32", "category": "Sm"}, {"key": "2B33", "category": "Sm"}, {"key": "2B34", "category": "Sm"}, {"key": "2B35", "category": "Sm"}, {"key": "2B36", "category": "Sm"}, {"key": "2B37", "category": "Sm"}, {"key": "2B38", "category": "Sm"}, {"key": "2B39", "category": "Sm"}, {"key": "2B3A", "category": "Sm"}, {"key": "2B3B", "category": "Sm"}, {"key": "2B3C", "category": "Sm"}, {"key": "2B3D", "category": "Sm"}, {"key": "2B3E", "category": "Sm"}, {"key": "2B3F", "category": "Sm"}, {"key": "2B40", "category": "Sm"}, {"key": "2B41", "category": "Sm"}, {"key": "2B42", "category": "Sm"}, {"key": "2B43", "category": "Sm"}, {"key": "2B44", "category": "Sm"}, {"key": "2B45", "category": "So"}, {"key": "2B46", "category": "So"}, {"key": "2B47", "category": "Sm"}, {"key": "2B48", "category": "Sm"}, {"key": "2B49", "category": "Sm"}, {"key": "2B4A", "category": "Sm"}, {"key": "2B4B", "category": "Sm"}, {"key": "2B4C", "category": "Sm"}, {"key": "FFE9", "category": "Sm"}, {"key": "FFEA", "category": "Sm"}, {"key": "FFEB", "category": "Sm"}, {"key": "FFEC", "category": "Sm"}], "base/symbols/math_characters.min": [{"locale": "base"}, {"key": "2113", "category": "Ll"}, {"key": "2118", "category": "Sm"}, {"key": "213C", "category": "Ll"}, {"key": "213D", "category": "Ll"}, {"key": "213E", "category": "<PERSON>"}, {"key": "213F", "category": "<PERSON>"}, {"key": "2140", "category": "Sm"}, {"key": "2145", "category": "<PERSON>"}, {"key": "2146", "category": "Ll"}, {"key": "2147", "category": "Ll"}, {"key": "2148", "category": "Ll"}, {"key": "2149", "category": "Ll"}, {"key": "1D6A4", "category": "Ll"}, {"key": "1D6A5", "category": "Ll"}], "base/symbols/math_delimiters.min": [{"locale": "base"}, {"key": "0028", "category": "Ps"}, {"key": "0029", "category": "Pe"}, {"key": "005B", "category": "Ps"}, {"key": "005D", "category": "Pe"}, {"key": "007B", "category": "Ps"}, {"key": "007D", "category": "Pe"}, {"key": "2045", "category": "Ps"}, {"key": "2046", "category": "Pe"}, {"key": "2308", "category": "Sm"}, {"key": "2309", "category": "Sm"}, {"key": "230A", "category": "Sm"}, {"key": "230B", "category": "Sm"}, {"key": "230C", "category": "So"}, {"key": "230D", "category": "So"}, {"key": "230E", "category": "So"}, {"key": "230F", "category": "So"}, {"key": "231C", "category": "So"}, {"key": "231D", "category": "So"}, {"key": "231E", "category": "So"}, {"key": "231F", "category": "So"}, {"key": "2320", "category": "Sm"}, {"key": "2321", "category": "Sm"}, {"key": "2329", "category": "Ps"}, {"key": "232A", "category": "Pe"}, {"key": "239B", "category": "Sm"}, {"key": "239C", "category": "Sm"}, {"key": "239D", "category": "Sm"}, {"key": "239E", "category": "Sm"}, {"key": "239F", "category": "Sm"}, {"key": "23A0", "category": "Sm"}, {"key": "23A1", "category": "Sm"}, {"key": "23A2", "category": "Sm"}, {"key": "23A3", "category": "Sm"}, {"key": "23A4", "category": "Sm"}, {"key": "23A5", "category": "Sm"}, {"key": "23A6", "category": "Sm"}, {"key": "23A7", "category": "Sm"}, {"key": "23A8", "category": "Sm"}, {"key": "23A9", "category": "Sm"}, {"key": "23AA", "category": "Sm"}, {"key": "23AB", "category": "Sm"}, {"key": "23AC", "category": "Sm"}, {"key": "23AD", "category": "Sm"}, {"key": "23AE", "category": "Sm"}, {"key": "23AF", "category": "Sm"}, {"key": "23B0", "category": "Sm"}, {"key": "23B1", "category": "Sm"}, {"key": "23B2", "category": "Sm"}, {"key": "23B3", "category": "Sm"}, {"key": "23B4", "category": "So"}, {"key": "23B5", "category": "So"}, {"key": "23B6", "category": "So"}, {"key": "23B7", "category": "So"}, {"key": "23B8", "category": "So"}, {"key": "23B9", "category": "So"}, {"key": "23DC", "category": "Sm"}, {"key": "23DD", "category": "Sm"}, {"key": "23DE", "category": "Sm"}, {"key": "23DF", "category": "Sm"}, {"key": "23E0", "category": "Sm"}, {"key": "23E1", "category": "Sm"}, {"key": "2768", "category": "Ps"}, {"key": "2769", "category": "Pe"}, {"key": "276A", "category": "Ps"}, {"key": "276B", "category": "Pe"}, {"key": "276C", "category": "Ps"}, {"key": "276D", "category": "Pe"}, {"key": "276E", "category": "Ps"}, {"key": "276F", "category": "Pe"}, {"key": "2770", "category": "Ps"}, {"key": "2771", "category": "Pe"}, {"key": "2772", "category": "Ps"}, {"key": "2773", "category": "Pe"}, {"key": "2774", "category": "Ps"}, {"key": "2775", "category": "Pe"}, {"key": "27C5", "category": "Ps"}, {"key": "27C6", "category": "Pe"}, {"key": "27E6", "category": "Ps"}, {"key": "27E7", "category": "Pe"}, {"key": "27E8", "category": "Ps"}, {"key": "27E9", "category": "Pe"}, {"key": "27EA", "category": "Ps"}, {"key": "27EB", "category": "Pe"}, {"key": "27EC", "category": "Ps"}, {"key": "27ED", "category": "Pe"}, {"key": "27EE", "category": "Ps"}, {"key": "27EF", "category": "Pe"}, {"key": "2983", "category": "Ps"}, {"key": "2984", "category": "Pe"}, {"key": "2985", "category": "Ps"}, {"key": "2986", "category": "Pe"}, {"key": "2987", "category": "Ps"}, {"key": "2988", "category": "Pe"}, {"key": "2989", "category": "Ps"}, {"key": "298A", "category": "Pe"}, {"key": "298B", "category": "Ps"}, {"key": "298C", "category": "Pe"}, {"key": "298D", "category": "Ps"}, {"key": "298E", "category": "Pe"}, {"key": "298F", "category": "Ps"}, {"key": "2990", "category": "Pe"}, {"key": "2991", "category": "Ps"}, {"key": "2992", "category": "Pe"}, {"key": "2993", "category": "Ps"}, {"key": "2994", "category": "Pe"}, {"key": "2995", "category": "Ps"}, {"key": "2996", "category": "Pe"}, {"key": "2997", "category": "Ps"}, {"key": "2998", "category": "Pe"}, {"key": "29D8", "category": "Ps"}, {"key": "29D9", "category": "Pe"}, {"key": "29DA", "category": "Ps"}, {"key": "29DB", "category": "Pe"}, {"key": "29FC", "category": "Ps"}, {"key": "29FD", "category": "Pe"}, {"key": "2E22", "category": "Ps"}, {"key": "2E23", "category": "Pe"}, {"key": "2E24", "category": "Ps"}, {"key": "2E25", "category": "Pe"}, {"key": "2E26", "category": "Ps"}, {"key": "2E27", "category": "Pe"}, {"key": "2E28", "category": "Ps"}, {"key": "2E29", "category": "Pe"}, {"key": "3008", "category": "Ps"}, {"key": "3009", "category": "Pe"}, {"key": "300A", "category": "Ps"}, {"key": "300B", "category": "Pe"}, {"key": "300C", "category": "Ps"}, {"key": "300D", "category": "Pe"}, {"key": "300E", "category": "Ps"}, {"key": "300F", "category": "Pe"}, {"key": "3010", "category": "Ps"}, {"key": "3011", "category": "Pe"}, {"key": "3014", "category": "Ps"}, {"key": "3015", "category": "Pe"}, {"key": "3016", "category": "Ps"}, {"key": "3017", "category": "Pe"}, {"key": "3018", "category": "Ps"}, {"key": "3019", "category": "Pe"}, {"key": "301A", "category": "Ps"}, {"key": "301B", "category": "Pe"}, {"key": "301D", "category": "Ps"}, {"key": "301E", "category": "Pe"}, {"key": "301F", "category": "Pe"}, {"key": "FD3E", "category": "Ps"}, {"key": "FD3F", "category": "Pe"}, {"key": "FE17", "category": "Ps"}, {"key": "FE18", "category": "Pe"}, {"key": "FE35", "category": "Ps"}, {"key": "FE36", "category": "Pe"}, {"key": "FE37", "category": "Ps"}, {"key": "FE38", "category": "Pe"}, {"key": "FE39", "category": "Ps"}, {"key": "FE3A", "category": "Pe"}, {"key": "FE3B", "category": "Ps"}, {"key": "FE3C", "category": "Pe"}, {"key": "FE3D", "category": "Ps"}, {"key": "FE3E", "category": "Pe"}, {"key": "FE3F", "category": "Ps"}, {"key": "FE40", "category": "Pe"}, {"key": "FE41", "category": "Ps"}, {"key": "FE42", "category": "Pe"}, {"key": "FE43", "category": "Ps"}, {"key": "FE44", "category": "Pe"}, {"key": "FE47", "category": "Ps"}, {"key": "FE48", "category": "Pe"}, {"key": "FE59", "category": "Ps"}, {"key": "FE5A", "category": "Pe"}, {"key": "FE5B", "category": "Ps"}, {"key": "FE5C", "category": "Pe"}, {"key": "FE5D", "category": "Ps"}, {"key": "FE5E", "category": "Pe"}, {"key": "FF08", "category": "Ps"}, {"key": "FF09", "category": "Pe"}, {"key": "FF3B", "category": "Ps"}, {"key": "FF3D", "category": "Pe"}, {"key": "FF5B", "category": "Ps"}, {"key": "FF5D", "category": "Pe"}, {"key": "FF5F", "category": "Ps"}, {"key": "FF60", "category": "Pe"}, {"key": "FF62", "category": "Ps"}, {"key": "FF63", "category": "Pe"}], "base/symbols/math_geometry.min": [{"locale": "base"}, {"key": "2500", "category": "So"}, {"key": "2501", "category": "So"}, {"key": "2502", "category": "So"}, {"key": "2503", "category": "So"}, {"key": "2504", "category": "So"}, {"key": "2505", "category": "So"}, {"key": "2506", "category": "So"}, {"key": "2507", "category": "So"}, {"key": "2508", "category": "So"}, {"key": "2509", "category": "So"}, {"key": "250A", "category": "So"}, {"key": "250B", "category": "So"}, {"key": "250C", "category": "So"}, {"key": "250D", "category": "So"}, {"key": "250E", "category": "So"}, {"key": "250F", "category": "So"}, {"key": "2510", "category": "So"}, {"key": "2511", "category": "So"}, {"key": "2512", "category": "So"}, {"key": "2513", "category": "So"}, {"key": "2514", "category": "So"}, {"key": "2515", "category": "So"}, {"key": "2516", "category": "So"}, {"key": "2517", "category": "So"}, {"key": "2518", "category": "So"}, {"key": "2519", "category": "So"}, {"key": "251A", "category": "So"}, {"key": "251B", "category": "So"}, {"key": "251C", "category": "So"}, {"key": "251D", "category": "So"}, {"key": "251E", "category": "So"}, {"key": "251F", "category": "So"}, {"key": "2520", "category": "So"}, {"key": "2521", "category": "So"}, {"key": "2522", "category": "So"}, {"key": "2523", "category": "So"}, {"key": "2524", "category": "So"}, {"key": "2525", "category": "So"}, {"key": "2526", "category": "So"}, {"key": "2527", "category": "So"}, {"key": "2528", "category": "So"}, {"key": "2529", "category": "So"}, {"key": "252A", "category": "So"}, {"key": "252B", "category": "So"}, {"key": "252C", "category": "So"}, {"key": "252D", "category": "So"}, {"key": "252E", "category": "So"}, {"key": "252F", "category": "So"}, {"key": "2530", "category": "So"}, {"key": "2531", "category": "So"}, {"key": "2532", "category": "So"}, {"key": "2533", "category": "So"}, {"key": "2534", "category": "So"}, {"key": "2535", "category": "So"}, {"key": "2536", "category": "So"}, {"key": "2537", "category": "So"}, {"key": "2538", "category": "So"}, {"key": "2539", "category": "So"}, {"key": "253A", "category": "So"}, {"key": "253B", "category": "So"}, {"key": "253C", "category": "So"}, {"key": "253D", "category": "So"}, {"key": "253E", "category": "So"}, {"key": "253F", "category": "So"}, {"key": "2540", "category": "So"}, {"key": "2541", "category": "So"}, {"key": "2542", "category": "So"}, {"key": "2543", "category": "So"}, {"key": "2544", "category": "So"}, {"key": "2545", "category": "So"}, {"key": "2546", "category": "So"}, {"key": "2547", "category": "So"}, {"key": "2548", "category": "So"}, {"key": "2549", "category": "So"}, {"key": "254A", "category": "So"}, {"key": "254B", "category": "So"}, {"key": "254C", "category": "So"}, {"key": "254D", "category": "So"}, {"key": "254E", "category": "So"}, {"key": "254F", "category": "So"}, {"key": "2550", "category": "So"}, {"key": "2551", "category": "So"}, {"key": "2552", "category": "So"}, {"key": "2553", "category": "So"}, {"key": "2554", "category": "So"}, {"key": "2555", "category": "So"}, {"key": "2556", "category": "So"}, {"key": "2557", "category": "So"}, {"key": "2558", "category": "So"}, {"key": "2559", "category": "So"}, {"key": "255A", "category": "So"}, {"key": "255B", "category": "So"}, {"key": "255C", "category": "So"}, {"key": "255D", "category": "So"}, {"key": "255E", "category": "So"}, {"key": "255F", "category": "So"}, {"key": "2560", "category": "So"}, {"key": "2561", "category": "So"}, {"key": "2562", "category": "So"}, {"key": "2563", "category": "So"}, {"key": "2564", "category": "So"}, {"key": "2565", "category": "So"}, {"key": "2566", "category": "So"}, {"key": "2567", "category": "So"}, {"key": "2568", "category": "So"}, {"key": "2569", "category": "So"}, {"key": "256A", "category": "So"}, {"key": "256B", "category": "So"}, {"key": "256C", "category": "So"}, {"key": "256D", "category": "So"}, {"key": "256E", "category": "So"}, {"key": "256F", "category": "So"}, {"key": "2570", "category": "So"}, {"key": "2571", "category": "So"}, {"key": "2572", "category": "So"}, {"key": "2573", "category": "So"}, {"key": "2574", "category": "So"}, {"key": "2575", "category": "So"}, {"key": "2576", "category": "So"}, {"key": "2577", "category": "So"}, {"key": "2578", "category": "So"}, {"key": "2579", "category": "So"}, {"key": "257A", "category": "So"}, {"key": "257B", "category": "So"}, {"key": "257C", "category": "So"}, {"key": "257D", "category": "So"}, {"key": "257E", "category": "So"}, {"key": "257F", "category": "So"}, {"key": "2580", "category": "So"}, {"key": "2581", "category": "So"}, {"key": "2582", "category": "So"}, {"key": "2583", "category": "So"}, {"key": "2584", "category": "So"}, {"key": "2585", "category": "So"}, {"key": "2586", "category": "So"}, {"key": "2587", "category": "So"}, {"key": "2588", "category": "So"}, {"key": "2589", "category": "So"}, {"key": "258A", "category": "So"}, {"key": "258B", "category": "So"}, {"key": "258C", "category": "So"}, {"key": "258D", "category": "So"}, {"key": "258E", "category": "So"}, {"key": "258F", "category": "So"}, {"key": "2590", "category": "So"}, {"key": "2591", "category": "So"}, {"key": "2592", "category": "So"}, {"key": "2593", "category": "So"}, {"key": "2594", "category": "So"}, {"key": "2595", "category": "So"}, {"key": "2596", "category": "So"}, {"key": "2597", "category": "So"}, {"key": "2598", "category": "So"}, {"key": "2599", "category": "So"}, {"key": "259A", "category": "So"}, {"key": "259B", "category": "So"}, {"key": "259C", "category": "So"}, {"key": "259D", "category": "So"}, {"key": "259E", "category": "So"}, {"key": "259F", "category": "So"}, {"key": "25A0", "category": "So"}, {"key": "25A1", "category": "So"}, {"key": "25A2", "category": "So"}, {"key": "25A3", "category": "So"}, {"key": "25A4", "category": "So"}, {"key": "25A5", "category": "So"}, {"key": "25A6", "category": "So"}, {"key": "25A7", "category": "So"}, {"key": "25A8", "category": "So"}, {"key": "25A9", "category": "So"}, {"key": "25AA", "category": "So"}, {"key": "25AB", "category": "So"}, {"key": "25AC", "category": "So"}, {"key": "25AD", "category": "So"}, {"key": "25AE", "category": "So"}, {"key": "25AF", "category": "So"}, {"key": "25B0", "category": "So"}, {"key": "25B1", "category": "So"}, {"key": "25B2", "category": "So"}, {"key": "25B3", "category": "So"}, {"key": "25B4", "category": "So"}, {"key": "25B5", "category": "So"}, {"key": "25B6", "category": "So"}, {"key": "25B7", "category": "Sm"}, {"key": "25B8", "category": "So"}, {"key": "25B9", "category": "So"}, {"key": "25BA", "category": "So"}, {"key": "25BB", "category": "So"}, {"key": "25BC", "category": "So"}, {"key": "25BD", "category": "So"}, {"key": "25BE", "category": "So"}, {"key": "25BF", "category": "So"}, {"key": "25C0", "category": "So"}, {"key": "25C1", "category": "Sm"}, {"key": "25C2", "category": "So"}, {"key": "25C3", "category": "So"}, {"key": "25C4", "category": "So"}, {"key": "25C5", "category": "So"}, {"key": "25C6", "category": "So"}, {"key": "25C7", "category": "So"}, {"key": "25C8", "category": "So"}, {"key": "25C9", "category": "So"}, {"key": "25CA", "category": "So"}, {"key": "25CB", "category": "So"}, {"key": "25CC", "category": "So"}, {"key": "25CD", "category": "So"}, {"key": "25CE", "category": "So"}, {"key": "25CF", "category": "So"}, {"key": "25D0", "category": "So"}, {"key": "25D1", "category": "So"}, {"key": "25D2", "category": "So"}, {"key": "25D3", "category": "So"}, {"key": "25D4", "category": "So"}, {"key": "25D5", "category": "So"}, {"key": "25D6", "category": "So"}, {"key": "25D7", "category": "So"}, {"key": "25D8", "category": "So"}, {"key": "25D9", "category": "So"}, {"key": "25DA", "category": "So"}, {"key": "25DB", "category": "So"}, {"key": "25DC", "category": "So"}, {"key": "25DD", "category": "So"}, {"key": "25DE", "category": "So"}, {"key": "25DF", "category": "So"}, {"key": "25E0", "category": "So"}, {"key": "25E1", "category": "So"}, {"key": "25E2", "category": "So"}, {"key": "25E3", "category": "So"}, {"key": "25E4", "category": "So"}, {"key": "25E5", "category": "So"}, {"key": "25E6", "category": "So"}, {"key": "25E7", "category": "So"}, {"key": "25E8", "category": "So"}, {"key": "25E9", "category": "So"}, {"key": "25EA", "category": "So"}, {"key": "25EB", "category": "So"}, {"key": "25EC", "category": "So"}, {"key": "25ED", "category": "So"}, {"key": "25EE", "category": "So"}, {"key": "25EF", "category": "So"}, {"key": "25F0", "category": "So"}, {"key": "25F1", "category": "So"}, {"key": "25F2", "category": "So"}, {"key": "25F3", "category": "So"}, {"key": "25F4", "category": "So"}, {"key": "25F5", "category": "So"}, {"key": "25F6", "category": "So"}, {"key": "25F7", "category": "So"}, {"key": "25F8", "category": "Sm"}, {"key": "25F9", "category": "Sm"}, {"key": "25FA", "category": "Sm"}, {"key": "25FB", "category": "Sm"}, {"key": "25FC", "category": "Sm"}, {"key": "25FD", "category": "Sm"}, {"key": "25FE", "category": "Sm"}, {"key": "25FF", "category": "Sm"}, {"key": "2B12", "category": "So"}, {"key": "2B13", "category": "So"}, {"key": "2B14", "category": "So"}, {"key": "2B15", "category": "So"}, {"key": "2B16", "category": "So"}, {"key": "2B17", "category": "So"}, {"key": "2B18", "category": "So"}, {"key": "2B19", "category": "So"}, {"key": "2B1A", "category": "So"}, {"key": "2B1B", "category": "So"}, {"key": "2B1C", "category": "So"}, {"key": "2B1D", "category": "So"}, {"key": "2B1E", "category": "So"}, {"key": "2B1F", "category": "So"}, {"key": "2B20", "category": "So"}, {"key": "2B21", "category": "So"}, {"key": "2B22", "category": "So"}, {"key": "2B23", "category": "So"}, {"key": "2B24", "category": "So"}, {"key": "2B25", "category": "So"}, {"key": "2B26", "category": "So"}, {"key": "2B27", "category": "So"}, {"key": "2B28", "category": "So"}, {"key": "2B29", "category": "So"}, {"key": "2B2A", "category": "So"}, {"key": "2B2B", "category": "So"}, {"key": "2B2C", "category": "So"}, {"key": "2B2D", "category": "So"}, {"key": "2B2E", "category": "So"}, {"key": "2B2F", "category": "So"}, {"key": "2B50", "category": "So"}, {"key": "2B51", "category": "So"}, {"key": "2B52", "category": "So"}, {"key": "2B53", "category": "So"}, {"key": "2B54", "category": "So"}, {"key": "2B55", "category": "So"}, {"key": "2B56", "category": "So"}, {"key": "2B57", "category": "So"}, {"key": "2B58", "category": "So"}, {"key": "2B59", "category": "So"}], "base/symbols/math_harpoons.min": [{"locale": "base"}, {"key": "21BC", "category": "So"}, {"key": "21BD", "category": "So"}, {"key": "21BE", "category": "So"}, {"key": "21BF", "category": "So"}, {"key": "21C0", "category": "So"}, {"key": "21C1", "category": "So"}, {"key": "21C2", "category": "So"}, {"key": "21C3", "category": "So"}, {"key": "21CB", "category": "So"}, {"key": "21CC", "category": "So"}, {"key": "294A", "category": "Sm"}, {"key": "294B", "category": "Sm"}, {"key": "294C", "category": "Sm"}, {"key": "294D", "category": "Sm"}, {"key": "294E", "category": "Sm"}, {"key": "294F", "category": "Sm"}, {"key": "2950", "category": "Sm"}, {"key": "2951", "category": "Sm"}, {"key": "2952", "category": "Sm"}, {"key": "2953", "category": "Sm"}, {"key": "2954", "category": "Sm"}, {"key": "2955", "category": "Sm"}, {"key": "2956", "category": "Sm"}, {"key": "2957", "category": "Sm"}, {"key": "2958", "category": "Sm"}, {"key": "2959", "category": "Sm"}, {"key": "295A", "category": "Sm"}, {"key": "295B", "category": "Sm"}, {"key": "295C", "category": "Sm"}, {"key": "295D", "category": "Sm"}, {"key": "295E", "category": "Sm"}, {"key": "295F", "category": "Sm"}, {"key": "2960", "category": "Sm"}, {"key": "2961", "category": "Sm"}, {"key": "2962", "category": "Sm"}, {"key": "2963", "category": "Sm"}, {"key": "2964", "category": "Sm"}, {"key": "2965", "category": "Sm"}, {"key": "2966", "category": "Sm"}, {"key": "2967", "category": "Sm"}, {"key": "2968", "category": "Sm"}, {"key": "2969", "category": "Sm"}, {"key": "296A", "category": "Sm"}, {"key": "296B", "category": "Sm"}, {"key": "296C", "category": "Sm"}, {"key": "296D", "category": "Sm"}, {"key": "296E", "category": "Sm"}, {"key": "296F", "category": "Sm"}, {"key": "297C", "category": "Sm"}, {"key": "297D", "category": "Sm"}, {"key": "297E", "category": "Sm"}, {"key": "297F", "category": "Sm"}], "base/symbols/math_non_characters.min": [{"locale": "base"}, {"key": "210F", "category": "Ll"}, {"key": "2114", "category": "So"}, {"key": "2116", "category": "So"}, {"key": "2117", "category": "So"}, {"key": "211E", "category": "So"}, {"key": "211F", "category": "So"}, {"key": "2120", "category": "So"}, {"key": "2121", "category": "So"}, {"key": "2122", "category": "So"}, {"key": "2123", "category": "So"}, {"key": "2125", "category": "So"}, {"key": "2126", "category": "<PERSON>"}, {"key": "2127", "category": "So"}, {"key": "212A", "category": "<PERSON>"}, {"key": "212B", "category": "<PERSON>"}, {"key": "212E", "category": "So"}, {"key": "2132", "category": "<PERSON>"}, {"key": "2139", "category": "Ll"}, {"key": "213A", "category": "So"}, {"key": "213B", "category": "So"}, {"key": "2141", "category": "Sm"}, {"key": "2142", "category": "Sm"}, {"key": "2143", "category": "Sm"}, {"key": "2144", "category": "Sm"}], "base/symbols/math_symbols.min": [{"locale": "base"}, {"key": "0021", "category": "Po"}, {"key": "0022", "category": "Po"}, {"key": "0023", "category": "Po"}, {"key": "0024", "category": "Sc"}, {"key": "0025", "category": "Po"}, {"key": "0026", "category": "Po"}, {"key": "0027", "category": "Po"}, {"key": "002A", "category": "Po"}, {"key": "002B", "category": "Sm"}, {"key": "002C", "category": "Po"}, {"key": "002D", "category": "Pd"}, {"key": "002E", "category": "Po"}, {"key": "002F", "category": "Po"}, {"key": "003A", "category": "Po"}, {"key": "003B", "category": "Po"}, {"key": "003C", "category": "Sm"}, {"key": "003D", "category": "Sm"}, {"key": "003E", "category": "Sm"}, {"key": "003F", "category": "Po"}, {"key": "0040", "category": "Po"}, {"key": "005C", "category": "Po"}, {"key": "005E", "category": "Sk"}, {"key": "005F", "category": "Pc"}, {"key": "0060", "category": "Sk"}, {"key": "007C", "category": "Sm"}, {"key": "007E", "category": "Sm"}, {"key": "00A1", "category": "Po"}, {"key": "00A2", "category": "Sc"}, {"key": "00A3", "category": "Sc"}, {"key": "00A4", "category": "Sc"}, {"key": "00A5", "category": "Sc"}, {"key": "00A6", "category": "So"}, {"key": "00A7", "category": "Po"}, {"key": "00A8", "category": "Sk"}, {"key": "00A9", "category": "So"}, {"key": "00AA", "category": "Lo"}, {"key": "00AB", "category": "Pi"}, {"key": "00AC", "category": "Sm"}, {"key": "00AE", "category": "So"}, {"key": "00AF", "category": "Sk"}, {"key": "00B0", "category": "So"}, {"key": "00B1", "category": "Sm"}, {"key": "00B4", "category": "Sk"}, {"key": "00B5", "category": "Ll"}, {"key": "00B6", "category": "Po"}, {"key": "00B7", "category": "Po"}, {"key": "00B8", "category": "Sk"}, {"key": "00BA", "category": "Lo"}, {"key": "00BB", "category": "Pf"}, {"key": "00BF", "category": "Po"}, {"key": "00D7", "category": "Sm"}, {"key": "00F7", "category": "Sm"}, {"key": "02D8", "category": "Sk"}, {"key": "02B9", "category": "Lm"}, {"key": "02BA", "category": "Lm"}, {"key": "02D9", "category": "Sk"}, {"key": "02DA", "category": "Sk"}, {"key": "02DB", "category": "Sk"}, {"key": "02DC", "category": "Sk"}, {"key": "02DD", "category": "Sk"}, {"key": "2010", "category": "Pd"}, {"key": "2011", "category": "Pd"}, {"key": "2012", "category": "Pd"}, {"key": "2013", "category": "Pd"}, {"key": "2014", "category": "Pd"}, {"key": "2015", "category": "Pd"}, {"key": "2016", "category": "Po"}, {"key": "2017", "category": "Po"}, {"key": "2018", "category": "Pi"}, {"key": "2019", "category": "Pf"}, {"key": "201A", "category": "Ps"}, {"key": "201B", "category": "Pi"}, {"key": "201C", "category": "Pi"}, {"key": "201D", "category": "Pf"}, {"key": "201E", "category": "Ps"}, {"key": "201F", "category": "Pi"}, {"key": "2020", "category": "Po"}, {"key": "2021", "category": "Po"}, {"key": "2022", "category": "Po"}, {"key": "2023", "category": "Po"}, {"key": "2024", "category": "Po"}, {"key": "2025", "category": "Po"}, {"key": "2026", "category": "Po"}, {"key": "2027", "category": "Po"}, {"key": "2030", "category": "Po"}, {"key": "2031", "category": "Po"}, {"key": "2032", "category": "Po"}, {"key": "2033", "category": "Po"}, {"key": "2034", "category": "Po"}, {"key": "2035", "category": "Po"}, {"key": "2036", "category": "Po"}, {"key": "2037", "category": "Po"}, {"key": "2038", "category": "Po"}, {"key": "2039", "category": "Pi"}, {"key": "203A", "category": "Pf"}, {"key": "203B", "category": "Po"}, {"key": "203C", "category": "Po"}, {"key": "203D", "category": "Po"}, {"key": "203E", "category": "Po"}, {"key": "203F", "category": "Pc"}, {"key": "2040", "category": "Pc"}, {"key": "2041", "category": "Po"}, {"key": "2042", "category": "Po"}, {"key": "2043", "category": "Po"}, {"key": "2044", "category": "Sm"}, {"key": "2047", "category": "Po"}, {"key": "2048", "category": "Po"}, {"key": "2049", "category": "Po"}, {"key": "204B", "category": "Po"}, {"key": "204C", "category": "Po"}, {"key": "204D", "category": "Po"}, {"key": "204E", "category": "Po"}, {"key": "204F", "category": "Po"}, {"key": "2050", "category": "Po"}, {"key": "2051", "category": "Po"}, {"key": "2052", "category": "Sm"}, {"key": "2053", "category": "Po"}, {"key": "2054", "category": "Pc"}, {"key": "2055", "category": "Po"}, {"key": "2056", "category": "Po"}, {"key": "2057", "category": "Po"}, {"key": "2058", "category": "Po"}, {"key": "2059", "category": "Po"}, {"key": "205A", "category": "Po"}, {"key": "205B", "category": "Po"}, {"key": "205C", "category": "Po"}, {"key": "205D", "category": "Po"}, {"key": "205E", "category": "Po"}, {"key": "207A", "category": "Sm"}, {"key": "207B", "category": "Sm"}, {"key": "207C", "category": "Sm"}, {"key": "207D", "category": "Ps"}, {"key": "207E", "category": "Pe"}, {"key": "208A", "category": "Sm"}, {"key": "208B", "category": "Sm"}, {"key": "208C", "category": "Sm"}, {"key": "208D", "category": "Ps"}, {"key": "208E", "category": "Pe"}, {"key": "214A", "category": "So"}, {"key": "214B", "category": "Sm"}, {"key": "214C", "category": "So"}, {"key": "214D", "category": "So"}, {"key": "214E", "category": "Ll"}, {"key": "2200", "category": "Sm"}, {"key": "2201", "category": "Sm"}, {"key": "2203", "category": "Sm"}, {"key": "2204", "category": "Sm"}, {"key": "2205", "category": "Sm"}, {"key": "2206", "category": "Sm"}, {"key": "2208", "category": "Sm"}, {"key": "2209", "category": "Sm"}, {"key": "220A", "category": "Sm"}, {"key": "220B", "category": "Sm"}, {"key": "220C", "category": "Sm"}, {"key": "220D", "category": "Sm"}, {"key": "220E", "category": "Sm"}, {"key": "220F", "category": "Sm"}, {"key": "2210", "category": "Sm"}, {"key": "2211", "category": "Sm"}, {"key": "2212", "category": "Sm"}, {"key": "2213", "category": "Sm"}, {"key": "2214", "category": "Sm"}, {"key": "2215", "category": "Sm"}, {"key": "2216", "category": "Sm"}, {"key": "2217", "category": "Sm"}, {"key": "2218", "category": "Sm"}, {"key": "2219", "category": "Sm"}, {"key": "221A", "category": "Sm"}, {"key": "221B", "category": "Sm"}, {"key": "221C", "category": "Sm"}, {"key": "221D", "category": "Sm"}, {"key": "221E", "category": "Sm"}, {"key": "221F", "category": "Sm"}, {"key": "2220", "category": "Sm"}, {"key": "2221", "category": "Sm"}, {"key": "2222", "category": "Sm"}, {"key": "2223", "category": "Sm"}, {"key": "2224", "category": "Sm"}, {"key": "2225", "category": "Sm"}, {"key": "2226", "category": "Sm"}, {"key": "2227", "category": "Sm"}, {"key": "2228", "category": "Sm"}, {"key": "2229", "category": "Sm"}, {"key": "222A", "category": "Sm"}, {"key": "222B", "category": "Sm"}, {"key": "222C", "category": "Sm"}, {"key": "222D", "category": "Sm"}, {"key": "222E", "category": "Sm"}, {"key": "222F", "category": "Sm"}, {"key": "2230", "category": "Sm"}, {"key": "2231", "category": "Sm"}, {"key": "2232", "category": "Sm"}, {"key": "2233", "category": "Sm"}, {"key": "2234", "category": "Sm"}, {"key": "2235", "category": "Sm"}, {"key": "2236", "category": "Sm"}, {"key": "2237", "category": "Sm"}, {"key": "2238", "category": "Sm"}, {"key": "2239", "category": "Sm"}, {"key": "223A", "category": "Sm"}, {"key": "223B", "category": "Sm"}, {"key": "223C", "category": "Sm"}, {"key": "223D", "category": "Sm"}, {"key": "223E", "category": "Sm"}, {"key": "223F", "category": "Sm"}, {"key": "2240", "category": "Sm"}, {"key": "2241", "category": "Sm"}, {"key": "2242", "category": "Sm"}, {"key": "2243", "category": "Sm"}, {"key": "2244", "category": "Sm"}, {"key": "2245", "category": "Sm"}, {"key": "2246", "category": "Sm"}, {"key": "2247", "category": "Sm"}, {"key": "2248", "category": "Sm"}, {"key": "2249", "category": "Sm"}, {"key": "224A", "category": "Sm"}, {"key": "224B", "category": "Sm"}, {"key": "224C", "category": "Sm"}, {"key": "224D", "category": "Sm"}, {"key": "224E", "category": "Sm"}, {"key": "224F", "category": "Sm"}, {"key": "2250", "category": "Sm"}, {"key": "2251", "category": "Sm"}, {"key": "2252", "category": "Sm"}, {"key": "2253", "category": "Sm"}, {"key": "2254", "category": "Sm"}, {"key": "2255", "category": "Sm"}, {"key": "2256", "category": "Sm"}, {"key": "2257", "category": "Sm"}, {"key": "2258", "category": "Sm"}, {"key": "2259", "category": "Sm"}, {"key": "225A", "category": "Sm"}, {"key": "225B", "category": "Sm"}, {"key": "225C", "category": "Sm"}, {"key": "225D", "category": "Sm"}, {"key": "225E", "category": "Sm"}, {"key": "225F", "category": "Sm"}, {"key": "2260", "category": "Sm"}, {"key": "2261", "category": "Sm"}, {"key": "2262", "category": "Sm"}, {"key": "2263", "category": "Sm"}, {"key": "2264", "category": "Sm"}, {"key": "2265", "category": "Sm"}, {"key": "2266", "category": "Sm"}, {"key": "2267", "category": "Sm"}, {"key": "2268", "category": "Sm"}, {"key": "2269", "category": "Sm"}, {"key": "226A", "category": "Sm"}, {"key": "226B", "category": "Sm"}, {"key": "226C", "category": "Sm"}, {"key": "226D", "category": "Sm"}, {"key": "226E", "category": "Sm"}, {"key": "226F", "category": "Sm"}, {"key": "2270", "category": "Sm"}, {"key": "2271", "category": "Sm"}, {"key": "2272", "category": "Sm"}, {"key": "2273", "category": "Sm"}, {"key": "2274", "category": "Sm"}, {"key": "2275", "category": "Sm"}, {"key": "2276", "category": "Sm"}, {"key": "2277", "category": "Sm"}, {"key": "2278", "category": "Sm"}, {"key": "2279", "category": "Sm"}, {"key": "227A", "category": "Sm"}, {"key": "227B", "category": "Sm"}, {"key": "227C", "category": "Sm"}, {"key": "227D", "category": "Sm"}, {"key": "227E", "category": "Sm"}, {"key": "227F", "category": "Sm"}, {"key": "2280", "category": "Sm"}, {"key": "2281", "category": "Sm"}, {"key": "2282", "category": "Sm"}, {"key": "2283", "category": "Sm"}, {"key": "2284", "category": "Sm"}, {"key": "2285", "category": "Sm"}, {"key": "2286", "category": "Sm"}, {"key": "2287", "category": "Sm"}, {"key": "2288", "category": "Sm"}, {"key": "2289", "category": "Sm"}, {"key": "228A", "category": "Sm"}, {"key": "228B", "category": "Sm"}, {"key": "228C", "category": "Sm"}, {"key": "228D", "category": "Sm"}, {"key": "228E", "category": "Sm"}, {"key": "228F", "category": "Sm"}, {"key": "2290", "category": "Sm"}, {"key": "2291", "category": "Sm"}, {"key": "2292", "category": "Sm"}, {"key": "2293", "category": "Sm"}, {"key": "2294", "category": "Sm"}, {"key": "2295", "category": "Sm"}, {"key": "2296", "category": "Sm"}, {"key": "2297", "category": "Sm"}, {"key": "2298", "category": "Sm"}, {"key": "2299", "category": "Sm"}, {"key": "229A", "category": "Sm"}, {"key": "229B", "category": "Sm"}, {"key": "229C", "category": "Sm"}, {"key": "229D", "category": "Sm"}, {"key": "229E", "category": "Sm"}, {"key": "229F", "category": "Sm"}, {"key": "22A0", "category": "Sm"}, {"key": "22A1", "category": "Sm"}, {"key": "22A2", "category": "Sm"}, {"key": "22A3", "category": "Sm"}, {"key": "22A4", "category": "Sm"}, {"key": "22A5", "category": "Sm"}, {"key": "22A6", "category": "Sm"}, {"key": "22A7", "category": "Sm"}, {"key": "22A8", "category": "Sm"}, {"key": "22A9", "category": "Sm"}, {"key": "22AA", "category": "Sm"}, {"key": "22AB", "category": "Sm"}, {"key": "22AC", "category": "Sm"}, {"key": "22AD", "category": "Sm"}, {"key": "22AE", "category": "Sm"}, {"key": "22AF", "category": "Sm"}, {"key": "22B0", "category": "Sm"}, {"key": "22B1", "category": "Sm"}, {"key": "22B2", "category": "Sm"}, {"key": "22B3", "category": "Sm"}, {"key": "22B4", "category": "Sm"}, {"key": "22B5", "category": "Sm"}, {"key": "22B6", "category": "Sm"}, {"key": "22B7", "category": "Sm"}, {"key": "22B8", "category": "Sm"}, {"key": "22B9", "category": "Sm"}, {"key": "22BA", "category": "Sm"}, {"key": "22BB", "category": "Sm"}, {"key": "22BC", "category": "Sm"}, {"key": "22BD", "category": "Sm"}, {"key": "22BF", "category": "Sm"}, {"key": "22C0", "category": "Sm"}, {"key": "22C1", "category": "Sm"}, {"key": "22C2", "category": "Sm"}, {"key": "22C3", "category": "Sm"}, {"key": "22C4", "category": "Sm"}, {"key": "22C5", "category": "Sm"}, {"key": "22C6", "category": "Sm"}, {"key": "22C7", "category": "Sm"}, {"key": "22C8", "category": "Sm"}, {"key": "22C9", "category": "Sm"}, {"key": "22CA", "category": "Sm"}, {"key": "22CB", "category": "Sm"}, {"key": "22CC", "category": "Sm"}, {"key": "22CD", "category": "Sm"}, {"key": "22CE", "category": "Sm"}, {"key": "22CF", "category": "Sm"}, {"key": "22D0", "category": "Sm"}, {"key": "22D1", "category": "Sm"}, {"key": "22D2", "category": "Sm"}, {"key": "22D3", "category": "Sm"}, {"key": "22D4", "category": "Sm"}, {"key": "22D5", "category": "Sm"}, {"key": "22D6", "category": "Sm"}, {"key": "22D7", "category": "Sm"}, {"key": "22D8", "category": "Sm"}, {"key": "22D9", "category": "Sm"}, {"key": "22DA", "category": "Sm"}, {"key": "22DB", "category": "Sm"}, {"key": "22DC", "category": "Sm"}, {"key": "22DD", "category": "Sm"}, {"key": "22DE", "category": "Sm"}, {"key": "22DF", "category": "Sm"}, {"key": "22E0", "category": "Sm"}, {"key": "22E1", "category": "Sm"}, {"key": "22E2", "category": "Sm"}, {"key": "22E3", "category": "Sm"}, {"key": "22E4", "category": "Sm"}, {"key": "22E5", "category": "Sm"}, {"key": "22E6", "category": "Sm"}, {"key": "22E7", "category": "Sm"}, {"key": "22E8", "category": "Sm"}, {"key": "22E9", "category": "Sm"}, {"key": "22EA", "category": "Sm"}, {"key": "22EB", "category": "Sm"}, {"key": "22EC", "category": "Sm"}, {"key": "22ED", "category": "Sm"}, {"key": "22EE", "category": "Sm"}, {"key": "22EF", "category": "Sm"}, {"key": "22F0", "category": "Sm"}, {"key": "22F1", "category": "Sm"}, {"key": "22F2", "category": "Sm"}, {"key": "22F3", "category": "Sm"}, {"key": "22F4", "category": "Sm"}, {"key": "22F5", "category": "Sm"}, {"key": "22F6", "category": "Sm"}, {"key": "22F7", "category": "Sm"}, {"key": "22F8", "category": "Sm"}, {"key": "22F9", "category": "Sm"}, {"key": "22FA", "category": "Sm"}, {"key": "22FB", "category": "Sm"}, {"key": "22FC", "category": "Sm"}, {"key": "22FD", "category": "Sm"}, {"key": "22FE", "category": "Sm"}, {"key": "22FF", "category": "Sm"}, {"key": "2300", "category": "So"}, {"key": "2302", "category": "So"}, {"key": "2305", "category": "So"}, {"key": "2306", "category": "So"}, {"key": "2307", "category": "So"}, {"key": "2310", "category": "So"}, {"key": "2311", "category": "So"}, {"key": "2312", "category": "So"}, {"key": "2313", "category": "So"}, {"key": "2314", "category": "So"}, {"key": "2795", "category": "So"}, {"key": "2796", "category": "So"}, {"key": "2797", "category": "So"}, {"key": "27B0", "category": "So"}, {"key": "27BF", "category": "So"}, {"key": "27C1", "category": "Sm"}, {"key": "27C2", "category": "Sm"}, {"key": "27C3", "category": "Sm"}, {"key": "27C4", "category": "Sm"}, {"key": "27C7", "category": "Sm"}, {"key": "27C8", "category": "Sm"}, {"key": "27C9", "category": "Sm"}, {"key": "27CA", "category": "Sm"}, {"key": "27CB", "category": "Sm"}, {"key": "27CC", "category": "Sm"}, {"key": "27CD", "category": "Sm"}, {"key": "27CE", "category": "Sm"}, {"key": "27CF", "category": "Sm"}, {"key": "27D0", "category": "Sm"}, {"key": "27D1", "category": "Sm"}, {"key": "27D2", "category": "Sm"}, {"key": "27D3", "category": "Sm"}, {"key": "27D4", "category": "Sm"}, {"key": "27D5", "category": "Sm"}, {"key": "27D6", "category": "Sm"}, {"key": "27D7", "category": "Sm"}, {"key": "27D8", "category": "Sm"}, {"key": "27D9", "category": "Sm"}, {"key": "27DA", "category": "Sm"}, {"key": "27DB", "category": "Sm"}, {"key": "27DC", "category": "Sm"}, {"key": "27DD", "category": "Sm"}, {"key": "27DE", "category": "Sm"}, {"key": "27DF", "category": "Sm"}, {"key": "27E0", "category": "Sm"}, {"key": "27E1", "category": "Sm"}, {"key": "27E2", "category": "Sm"}, {"key": "27E3", "category": "Sm"}, {"key": "27E4", "category": "Sm"}, {"key": "27E5", "category": "Sm"}, {"key": "292B", "category": "Sm"}, {"key": "292C", "category": "Sm"}, {"key": "2980", "category": "Sm"}, {"key": "2981", "category": "Sm"}, {"key": "2982", "category": "Sm"}, {"key": "2999", "category": "Sm"}, {"key": "299A", "category": "Sm"}, {"key": "29B0", "category": "Sm"}, {"key": "29B1", "category": "Sm"}, {"key": "29B2", "category": "Sm"}, {"key": "29B5", "category": "Sm"}, {"key": "29B6", "category": "Sm"}, {"key": "29B7", "category": "Sm"}, {"key": "29B8", "category": "Sm"}, {"key": "29B9", "category": "Sm"}, {"key": "29BA", "category": "Sm"}, {"key": "29BB", "category": "Sm"}, {"key": "29BC", "category": "Sm"}, {"key": "29BE", "category": "Sm"}, {"key": "29BF", "category": "Sm"}, {"key": "29C0", "category": "Sm"}, {"key": "29C1", "category": "Sm"}, {"key": "29C2", "category": "Sm"}, {"key": "29C3", "category": "Sm"}, {"key": "29C4", "category": "Sm"}, {"key": "29C5", "category": "Sm"}, {"key": "29C6", "category": "Sm"}, {"key": "29C7", "category": "Sm"}, {"key": "29C8", "category": "Sm"}, {"key": "29C9", "category": "Sm"}, {"key": "29CA", "category": "Sm"}, {"key": "29CB", "category": "Sm"}, {"key": "29CC", "category": "Sm"}, {"key": "29CD", "category": "Sm"}, {"key": "29CE", "category": "Sm"}, {"key": "29CF", "category": "Sm"}, {"key": "29D0", "category": "Sm"}, {"key": "29D1", "category": "Sm"}, {"key": "29D2", "category": "Sm"}, {"key": "29D3", "category": "Sm"}, {"key": "29D4", "category": "Sm"}, {"key": "29D5", "category": "Sm"}, {"key": "29D6", "category": "Sm"}, {"key": "29D7", "category": "Sm"}, {"key": "29DC", "category": "Sm"}, {"key": "29DD", "category": "Sm"}, {"key": "29DE", "category": "Sm"}, {"key": "29DF", "category": "Sm"}, {"key": "29E0", "category": "Sm"}, {"key": "29E1", "category": "Sm"}, {"key": "29E2", "category": "Sm"}, {"key": "29E3", "category": "Sm"}, {"key": "29E4", "category": "Sm"}, {"key": "29E5", "category": "Sm"}, {"key": "29E6", "category": "Sm"}, {"key": "29E7", "category": "Sm"}, {"key": "29E8", "category": "Sm"}, {"key": "29E9", "category": "Sm"}, {"key": "29EB", "category": "Sm"}, {"key": "29EE", "category": "Sm"}, {"key": "29EF", "category": "Sm"}, {"key": "29F0", "category": "Sm"}, {"key": "29F1", "category": "Sm"}, {"key": "29F2", "category": "Sm"}, {"key": "29F3", "category": "Sm"}, {"key": "29F4", "category": "Sm"}, {"key": "29F5", "category": "Sm"}, {"key": "29F6", "category": "Sm"}, {"key": "29F7", "category": "Sm"}, {"key": "29F8", "category": "Sm"}, {"key": "29F9", "category": "Sm"}, {"key": "29FA", "category": "Sm"}, {"key": "29FB", "category": "Sm"}, {"key": "29FE", "category": "Sm"}, {"key": "29FF", "category": "Sm"}, {"key": "2A00", "category": "Sm"}, {"key": "2A01", "category": "Sm"}, {"key": "2A02", "category": "Sm"}, {"key": "2A03", "category": "Sm"}, {"key": "2A04", "category": "Sm"}, {"key": "2A05", "category": "Sm"}, {"key": "2A06", "category": "Sm"}, {"key": "2A07", "category": "Sm"}, {"key": "2A08", "category": "Sm"}, {"key": "2A09", "category": "Sm"}, {"key": "2A0A", "category": "Sm"}, {"key": "2A0B", "category": "Sm"}, {"key": "2A0C", "category": "Sm"}, {"key": "2A0D", "category": "Sm"}, {"key": "2A0E", "category": "Sm"}, {"key": "2A0F", "category": "Sm"}, {"key": "2A10", "category": "Sm"}, {"key": "2A11", "category": "Sm"}, {"key": "2A12", "category": "Sm"}, {"key": "2A13", "category": "Sm"}, {"key": "2A14", "category": "Sm"}, {"key": "2A15", "category": "Sm"}, {"key": "2A16", "category": "Sm"}, {"key": "2A18", "category": "Sm"}, {"key": "2A19", "category": "Sm"}, {"key": "2A1A", "category": "Sm"}, {"key": "2A1B", "category": "Sm"}, {"key": "2A1C", "category": "Sm"}, {"key": "2A1D", "category": "Sm"}, {"key": "2A1E", "category": "Sm"}, {"key": "2A1F", "category": "Sm"}, {"key": "2A20", "category": "Sm"}, {"key": "2A21", "category": "Sm"}, {"key": "2A22", "category": "Sm"}, {"key": "2A23", "category": "Sm"}, {"key": "2A24", "category": "Sm"}, {"key": "2A25", "category": "Sm"}, {"key": "2A26", "category": "Sm"}, {"key": "2A27", "category": "Sm"}, {"key": "2A28", "category": "Sm"}, {"key": "2A29", "category": "Sm"}, {"key": "2A2A", "category": "Sm"}, {"key": "2A2B", "category": "Sm"}, {"key": "2A2C", "category": "Sm"}, {"key": "2A2D", "category": "Sm"}, {"key": "2A2E", "category": "Sm"}, {"key": "2A2F", "category": "Sm"}, {"key": "2A30", "category": "Sm"}, {"key": "2A31", "category": "Sm"}, {"key": "2A32", "category": "Sm"}, {"key": "2A33", "category": "Sm"}, {"key": "2A34", "category": "Sm"}, {"key": "2A35", "category": "Sm"}, {"key": "2A36", "category": "Sm"}, {"key": "2A37", "category": "Sm"}, {"key": "2A38", "category": "Sm"}, {"key": "2A39", "category": "Sm"}, {"key": "2A3A", "category": "Sm"}, {"key": "2A3B", "category": "Sm"}, {"key": "2A3C", "category": "Sm"}, {"key": "2A3D", "category": "Sm"}, {"key": "2A3E", "category": "Sm"}, {"key": "2A3F", "category": "Sm"}, {"key": "2A40", "category": "Sm"}, {"key": "2A41", "category": "Sm"}, {"key": "2A42", "category": "Sm"}, {"key": "2A43", "category": "Sm"}, {"key": "2A44", "category": "Sm"}, {"key": "2A45", "category": "Sm"}, {"key": "2A46", "category": "Sm"}, {"key": "2A47", "category": "Sm"}, {"key": "2A48", "category": "Sm"}, {"key": "2A49", "category": "Sm"}, {"key": "2A4A", "category": "Sm"}, {"key": "2A4B", "category": "Sm"}, {"key": "2A4C", "category": "Sm"}, {"key": "2A4D", "category": "Sm"}, {"key": "2A4E", "category": "Sm"}, {"key": "2A4F", "category": "Sm"}, {"key": "2A50", "category": "Sm"}, {"key": "2A51", "category": "Sm"}, {"key": "2A52", "category": "Sm"}, {"key": "2A53", "category": "Sm"}, {"key": "2A54", "category": "Sm"}, {"key": "2A55", "category": "Sm"}, {"key": "2A56", "category": "Sm"}, {"key": "2A57", "category": "Sm"}, {"key": "2A58", "category": "Sm"}, {"key": "2A59", "category": "Sm"}, {"key": "2A5A", "category": "Sm"}, {"key": "2A5B", "category": "Sm"}, {"key": "2A5C", "category": "Sm"}, {"key": "2A5D", "category": "Sm"}, {"key": "2A5E", "category": "Sm"}, {"key": "2A5F", "category": "Sm"}, {"key": "2A60", "category": "Sm"}, {"key": "2A61", "category": "Sm"}, {"key": "2A62", "category": "Sm"}, {"key": "2A63", "category": "Sm"}, {"key": "2A64", "category": "Sm"}, {"key": "2A65", "category": "Sm"}, {"key": "2A66", "category": "Sm"}, {"key": "2A67", "category": "Sm"}, {"key": "2A68", "category": "Sm"}, {"key": "2A69", "category": "Sm"}, {"key": "2A6A", "category": "Sm"}, {"key": "2A6B", "category": "Sm"}, {"key": "2A6C", "category": "Sm"}, {"key": "2A6D", "category": "Sm"}, {"key": "2A6E", "category": "Sm"}, {"key": "2A6F", "category": "Sm"}, {"key": "2A70", "category": "Sm"}, {"key": "2A71", "category": "Sm"}, {"key": "2A72", "category": "Sm"}, {"key": "2A73", "category": "Sm"}, {"key": "2A74", "category": "Sm"}, {"key": "2A75", "category": "Sm"}, {"key": "2A76", "category": "Sm"}, {"key": "2A77", "category": "Sm"}, {"key": "2A78", "category": "Sm"}, {"key": "2A79", "category": "Sm"}, {"key": "2A7A", "category": "Sm"}, {"key": "2A7B", "category": "Sm"}, {"key": "2A7C", "category": "Sm"}, {"key": "2A7D", "category": "Sm"}, {"key": "2A7E", "category": "Sm"}, {"key": "2A7F", "category": "Sm"}, {"key": "2A80", "category": "Sm"}, {"key": "2A81", "category": "Sm"}, {"key": "2A82", "category": "Sm"}, {"key": "2A83", "category": "Sm"}, {"key": "2A84", "category": "Sm"}, {"key": "2A85", "category": "Sm"}, {"key": "2A86", "category": "Sm"}, {"key": "2A87", "category": "Sm"}, {"key": "2A88", "category": "Sm"}, {"key": "2A89", "category": "Sm"}, {"key": "2A8A", "category": "Sm"}, {"key": "2A8B", "category": "Sm"}, {"key": "2A8C", "category": "Sm"}, {"key": "2A8D", "category": "Sm"}, {"key": "2A8E", "category": "Sm"}, {"key": "2A8F", "category": "Sm"}, {"key": "2A90", "category": "Sm"}, {"key": "2A91", "category": "Sm"}, {"key": "2A92", "category": "Sm"}, {"key": "2A93", "category": "Sm"}, {"key": "2A94", "category": "Sm"}, {"key": "2A95", "category": "Sm"}, {"key": "2A96", "category": "Sm"}, {"key": "2A97", "category": "Sm"}, {"key": "2A98", "category": "Sm"}, {"key": "2A99", "category": "Sm"}, {"key": "2A9A", "category": "Sm"}, {"key": "2A9B", "category": "Sm"}, {"key": "2A9C", "category": "Sm"}, {"key": "2A9D", "category": "Sm"}, {"key": "2A9E", "category": "Sm"}, {"key": "2A9F", "category": "Sm"}, {"key": "2AA0", "category": "Sm"}, {"key": "2AA1", "category": "Sm"}, {"key": "2AA2", "category": "Sm"}, {"key": "2AA3", "category": "Sm"}, {"key": "2AA4", "category": "Sm"}, {"key": "2AA5", "category": "Sm"}, {"key": "2AA6", "category": "Sm"}, {"key": "2AA7", "category": "Sm"}, {"key": "2AA8", "category": "Sm"}, {"key": "2AA9", "category": "Sm"}, {"key": "2AAA", "category": "Sm"}, {"key": "2AAB", "category": "Sm"}, {"key": "2AAC", "category": "Sm"}, {"key": "2AAD", "category": "Sm"}, {"key": "2AAE", "category": "Sm"}, {"key": "2AAF", "category": "Sm"}, {"key": "2AB0", "category": "Sm"}, {"key": "2AB1", "category": "Sm"}, {"key": "2AB2", "category": "Sm"}, {"key": "2AB3", "category": "Sm"}, {"key": "2AB4", "category": "Sm"}, {"key": "2AB5", "category": "Sm"}, {"key": "2AB6", "category": "Sm"}, {"key": "2AB7", "category": "Sm"}, {"key": "2AB8", "category": "Sm"}, {"key": "2AB9", "category": "Sm"}, {"key": "2ABA", "category": "Sm"}, {"key": "2ABB", "category": "Sm"}, {"key": "2ABC", "category": "Sm"}, {"key": "2ABD", "category": "Sm"}, {"key": "2ABE", "category": "Sm"}, {"key": "2ABF", "category": "Sm"}, {"key": "2AC0", "category": "Sm"}, {"key": "2AC1", "category": "Sm"}, {"key": "2AC2", "category": "Sm"}, {"key": "2AC3", "category": "Sm"}, {"key": "2AC4", "category": "Sm"}, {"key": "2AC5", "category": "Sm"}, {"key": "2AC6", "category": "Sm"}, {"key": "2AC7", "category": "Sm"}, {"key": "2AC8", "category": "Sm"}, {"key": "2AC9", "category": "Sm"}, {"key": "2ACA", "category": "Sm"}, {"key": "2ACB", "category": "Sm"}, {"key": "2ACC", "category": "Sm"}, {"key": "2ACD", "category": "Sm"}, {"key": "2ACE", "category": "Sm"}, {"key": "2ACF", "category": "Sm"}, {"key": "2AD0", "category": "Sm"}, {"key": "2AD1", "category": "Sm"}, {"key": "2AD2", "category": "Sm"}, {"key": "2AD3", "category": "Sm"}, {"key": "2AD4", "category": "Sm"}, {"key": "2AD5", "category": "Sm"}, {"key": "2AD6", "category": "Sm"}, {"key": "2AD7", "category": "Sm"}, {"key": "2AD8", "category": "Sm"}, {"key": "2AD9", "category": "Sm"}, {"key": "2ADA", "category": "Sm"}, {"key": "2ADB", "category": "Sm"}, {"key": "2ADC", "category": "Sm"}, {"key": "2ADD", "category": "Sm"}, {"key": "2ADE", "category": "Sm"}, {"key": "2ADF", "category": "Sm"}, {"key": "2AE0", "category": "Sm"}, {"key": "2AE1", "category": "Sm"}, {"key": "2AE2", "category": "Sm"}, {"key": "2AE3", "category": "Sm"}, {"key": "2AE4", "category": "Sm"}, {"key": "2AE5", "category": "Sm"}, {"key": "2AE6", "category": "Sm"}, {"key": "2AE7", "category": "Sm"}, {"key": "2AE8", "category": "Sm"}, {"key": "2AE9", "category": "Sm"}, {"key": "2AEA", "category": "Sm"}, {"key": "2AEB", "category": "Sm"}, {"key": "2AEC", "category": "Sm"}, {"key": "2AED", "category": "Sm"}, {"key": "2AEE", "category": "Sm"}, {"key": "2AEF", "category": "Sm"}, {"key": "2AF0", "category": "Sm"}, {"key": "2AF1", "category": "Sm"}, {"key": "2AF2", "category": "Sm"}, {"key": "2AF3", "category": "Sm"}, {"key": "2AF4", "category": "Sm"}, {"key": "2AF5", "category": "Sm"}, {"key": "2AF6", "category": "Sm"}, {"key": "2AF7", "category": "Sm"}, {"key": "2AF8", "category": "Sm"}, {"key": "2AF9", "category": "Sm"}, {"key": "2AFA", "category": "Sm"}, {"key": "2AFB", "category": "Sm"}, {"key": "2AFC", "category": "Sm"}, {"key": "2AFD", "category": "Sm"}, {"key": "2AFE", "category": "Sm"}, {"key": "2AFF", "category": "Sm"}, {"key": "301C", "category": "Pd"}, {"key": "FE10", "category": "Po"}, {"key": "FE13", "category": "Po"}, {"key": "FE14", "category": "Po"}, {"key": "FE15", "category": "Po"}, {"key": "FE16", "category": "Po"}, {"key": "FE19", "category": "Po"}, {"key": "FE30", "category": "Po"}, {"key": "FE31", "category": "Pd"}, {"key": "FE32", "category": "Pd"}, {"key": "FE33", "category": "Pc"}, {"key": "FE34", "category": "Pc"}, {"key": "FE45", "category": "Po"}, {"key": "FE46", "category": "Po"}, {"key": "FE49", "category": "Po"}, {"key": "FE4A", "category": "Po"}, {"key": "FE4B", "category": "Po"}, {"key": "FE4C", "category": "Po"}, {"key": "FE4D", "category": "Pc"}, {"key": "FE4E", "category": "Pc"}, {"key": "FE4F", "category": "Pc"}, {"key": "FE50", "category": "Po"}, {"key": "FE52", "category": "Po"}, {"key": "FE54", "category": "Po"}, {"key": "FE55", "category": "Po"}, {"key": "FE56", "category": "Po"}, {"key": "FE57", "category": "Po"}, {"key": "FE58", "category": "Pd"}, {"key": "FE5F", "category": "Po"}, {"key": "FE60", "category": "Po"}, {"key": "FE61", "category": "Po"}, {"key": "FE62", "category": "Sm"}, {"key": "FE63", "category": "Pd"}, {"key": "FE64", "category": "Sm"}, {"key": "FE65", "category": "Sm"}, {"key": "FE66", "category": "Sm"}, {"key": "FE68", "category": "Po"}, {"key": "FE69", "category": "Sc"}, {"key": "FE6A", "category": "Po"}, {"key": "FE6B", "category": "Po"}, {"key": "FF01", "category": "Po"}, {"key": "FF02", "category": "Po"}, {"key": "FF03", "category": "Po"}, {"key": "FF04", "category": "Sc"}, {"key": "FF05", "category": "Po"}, {"key": "FF06", "category": "Po"}, {"key": "FF07", "category": "Po"}, {"key": "FF0A", "category": "Po"}, {"key": "FF0B", "category": "Sm"}, {"key": "FF0C", "category": "Po"}, {"key": "FF0D", "category": "Pd"}, {"key": "FF0E", "category": "Po"}, {"key": "FF0F", "category": "Po"}, {"key": "FF1A", "category": "Po"}, {"key": "FF1B", "category": "Po"}, {"key": "FF1C", "category": "Sm"}, {"key": "FF1D", "category": "Sm"}, {"key": "FF1E", "category": "Sm"}, {"key": "FF1F", "category": "Po"}, {"key": "FF20", "category": "Po"}, {"key": "FF3C", "category": "Po"}, {"key": "FF3E", "category": "Sk"}, {"key": "FF3F", "category": "Pc"}, {"key": "FF40", "category": "Sk"}, {"key": "FF5C", "category": "Sm"}, {"key": "FF5E", "category": "Sm"}, {"key": "FFE0", "category": "Sc"}, {"key": "FFE1", "category": "Sc"}, {"key": "FFE2", "category": "Sm"}, {"key": "FFE3", "category": "Sk"}, {"key": "FFE4", "category": "So"}, {"key": "FFE5", "category": "Sc"}, {"key": "FFE6", "category": "Sc"}, {"key": "FFE8", "category": "So"}, {"key": "FFED", "category": "So"}, {"key": "FFEE", "category": "So"}], "base/symbols/math_whitespace.min": [{"locale": "base"}, {"key": "0020", "category": "Zs"}, {"key": "00A0", "category": "Zs"}, {"key": "00AD", "category": "Cf"}, {"key": "2000", "category": "Zs"}, {"key": "2001", "category": "Zs"}, {"key": "2002", "category": "Zs"}, {"key": "2003", "category": "Zs"}, {"key": "2004", "category": "Zs"}, {"key": "2005", "category": "Zs"}, {"key": "2006", "category": "Zs"}, {"key": "2007", "category": "Zs"}, {"key": "2008", "category": "Zs"}, {"key": "2009", "category": "Zs"}, {"key": "200A", "category": "Zs"}, {"key": "200B", "category": "Cf"}, {"key": "200C", "category": "Cf"}, {"key": "200D", "category": "Cf"}, {"key": "200E", "category": "Cf"}, {"key": "200F", "category": "Cf"}, {"key": "2028", "category": "Zl"}, {"key": "2029", "category": "Zp"}, {"key": "202A", "category": "Cf"}, {"key": "202B", "category": "Cf"}, {"key": "202C", "category": "Cf"}, {"key": "202D", "category": "Cf"}, {"key": "202E", "category": "Cf"}, {"key": "202F", "category": "Zs"}, {"key": "205F", "category": "Zs"}, {"key": "2060", "category": "Cf"}, {"key": "2061", "category": "Cf"}, {"key": "2062", "category": "Cf"}, {"key": "2063", "category": "Cf"}, {"key": "2064", "category": "Cf"}, {"key": "206A", "category": "Cf"}, {"key": "206B", "category": "Cf"}, {"key": "206E", "category": "Cf"}, {"key": "206F", "category": "Cf"}, {"key": "FEFF", "category": "Cf"}, {"key": "FFF9", "category": "Cf"}, {"key": "FFFA", "category": "Cf"}, {"key": "FFFB", "category": "Cf"}], "base/symbols/other_stars.min": [{"locale": "base"}, {"key": "23E8", "category": "So"}, {"key": "2605", "category": "So"}, {"key": "2606", "category": "So"}, {"key": "26AA", "category": "So"}, {"key": "26AB", "category": "So"}, {"key": "2705", "category": "So"}, {"key": "2713", "category": "So"}, {"key": "2714", "category": "So"}, {"key": "2715", "category": "So"}, {"key": "2716", "category": "So"}, {"key": "2717", "category": "So"}, {"key": "2718", "category": "So"}, {"key": "271B", "category": "So"}, {"key": "271C", "category": "So"}, {"key": "2720", "category": "So"}, {"key": "2721", "category": "So"}, {"key": "2722", "category": "So"}, {"key": "2723", "category": "So"}, {"key": "2724", "category": "So"}, {"key": "2725", "category": "So"}, {"key": "2726", "category": "So"}, {"key": "2727", "category": "So"}, {"key": "2728", "category": "So"}, {"key": "2729", "category": "So"}, {"key": "272A", "category": "So"}, {"key": "272B", "category": "So"}, {"key": "272C", "category": "So"}, {"key": "272D", "category": "So"}, {"key": "272E", "category": "So"}, {"key": "272F", "category": "So"}, {"key": "2730", "category": "So"}, {"key": "2731", "category": "So"}, {"key": "2732", "category": "So"}, {"key": "2733", "category": "So"}, {"key": "2734", "category": "So"}, {"key": "2735", "category": "So"}, {"key": "2736", "category": "So"}, {"key": "2739", "category": "So"}, {"key": "273A", "category": "So"}, {"key": "273B", "category": "So"}, {"key": "273C", "category": "So"}, {"key": "273D", "category": "So"}, {"key": "273E", "category": "So"}, {"key": "273F", "category": "So"}, {"key": "2740", "category": "So"}, {"key": "2741", "category": "So"}, {"key": "2742", "category": "So"}, {"key": "2743", "category": "So"}, {"key": "2744", "category": "So"}, {"key": "2745", "category": "So"}, {"key": "2746", "category": "So"}, {"key": "2747", "category": "So"}, {"key": "2748", "category": "So"}, {"key": "2749", "category": "So"}, {"key": "274A", "category": "So"}, {"key": "274B", "category": "So"}, {"key": "274C", "category": "So"}, {"key": "274D", "category": "So"}], "base/units/area.min": [{"locale": "base"}, {"key": "sq", "category": "other", "names": ["sq", "sq."]}, {"key": "sq inch", "category": "area", "names": ["sq in", "sq. in.", "sq inch", "sq. inch"]}, {"key": "sq rd", "category": "area", "names": ["sq rd", "sq. rd."]}, {"key": "sq ft", "category": "area", "names": ["sq ft", "sq. ft."]}, {"key": "sq yd", "category": "area", "names": ["sq yd", "sq. yd."]}, {"key": "sq mi", "category": "area", "names": ["sq mi", "sq. mi."]}, {"key": "acr", "category": "area", "names": ["ac", "ac.", "acr", "acr."]}, {"key": "ha", "category": "area", "names": ["ha"]}], "base/units/currency.min": [{"locale": "base"}, {"key": "$", "category": "currency", "names": ["$", "💲", "＄", "﹩", "USD"]}, {"key": "£", "category": "currency", "names": ["£", "￡", "GBP"]}, {"key": "¥", "category": "currency", "names": ["¥", "￥", "JPY"]}, {"key": "€", "category": "currency", "names": ["€", "EUR"]}, {"key": "₡", "category": "currency", "names": ["₡", "CRC"]}, {"key": "₢", "category": "currency", "names": ["₢"]}, {"key": "₣", "category": "currency", "names": ["₣"]}, {"key": "₤", "category": "currency", "names": ["₤"]}, {"key": "₥", "category": "currency", "names": ["₥"]}, {"key": "₦", "category": "currency", "names": ["₦", "NGN"]}, {"key": "₧", "category": "currency", "names": ["₧"]}, {"key": "₨", "category": "currency", "names": ["₨", "₹", "INR", "NPR", "PKR", "LKR"]}, {"key": "₩", "category": "currency", "names": ["₩", "￦", "KRW"]}, {"key": "₪", "category": "currency", "names": ["₪"]}, {"key": "₫", "category": "currency", "names": ["₫"]}, {"key": "₭", "category": "currency", "names": ["₭"]}, {"key": "₮", "category": "currency", "names": ["₮"]}, {"key": "₯", "category": "currency", "names": ["₯"]}, {"key": "₰", "category": "currency", "names": ["₰"]}, {"key": "₱", "category": "currency", "names": ["₱"]}, {"key": "₲", "category": "currency", "names": ["₲"]}, {"key": "₳", "category": "currency", "names": ["₳"]}, {"key": "₴", "category": "currency", "names": ["₴", "UAH"]}, {"key": "₵", "category": "currency", "names": ["₵", "GHS"]}, {"key": "₸", "category": "currency", "names": ["₸", "KZT"]}, {"key": "₺", "category": "currency", "names": ["₺", "TRY"]}, {"key": "元", "category": "currency", "names": ["元"]}, {"key": "¢", "category": "currency", "names": ["￠", "¢"]}], "base/units/energy.min": [{"locale": "base"}, {"key": "W", "category": "energy", "si": true, "names": ["W", "w"]}, {"key": "kwh", "category": "energy", "names": ["kwh", "kWh"]}, {"key": "J", "category": "energy", "si": true, "names": ["J"]}, {"key": "N", "category": "energy", "si": true, "names": ["N"]}, {"key": "A", "category": "energy", "si": true, "names": ["A"]}, {"key": "V", "category": "energy", "si": true, "names": ["V"]}, {"key": "ohm", "category": "energy", "names": ["Ohm", "ohm"]}, {"key": "Ω", "category": "energy", "si": true, "names": ["Ω"]}], "base/units/length.min": [{"locale": "base"}, {"key": "ft", "category": "length", "names": ["ft", "ft."]}, {"key": "in", "category": "length", "names": ["in", "in."]}, {"key": "mi", "category": "length", "names": ["mi", "mi."]}, {"key": "yd", "category": "length", "names": ["yd", "yd."]}, {"key": "link", "category": "length", "names": ["li", "li."]}, {"key": "rod", "category": "length", "names": ["rd", "rd."]}, {"key": "chain", "category": "length", "names": ["ch", "ch."]}, {"key": "furlong", "category": "length", "names": ["fur", "fur."]}, {"key": "n.m.", "category": "length", "names": ["n.m."]}, {"key": "m", "category": "length", "si": true, "names": ["m"]}], "base/units/memory.min": [{"locale": "base"}, {"key": "b", "category": "memory", "names": ["b"]}, {"key": "B", "category": "memory", "si": true, "names": ["B"]}, {"key": "KB", "category": "memory", "names": ["KB"]}], "base/units/other.min": [{"locale": "base"}, {"key": "doz", "category": "other", "names": ["doz", "doz.", "dz", "dz."]}], "base/units/speed.min": [{"locale": "base"}, {"key": "kt", "category": "speed", "names": ["kt", "kt."]}, {"key": "mph", "category": "speed", "names": ["mph"]}, {"key": "rpm", "category": "speed", "names": ["rpm"]}, {"key": "kmh", "category": "speed", "names": ["kmh", "kph"]}], "base/units/temperature.min": [{"locale": "base"}, {"key": "F", "category": "temperature", "names": ["F", "F.", "°F"]}, {"key": "C", "category": "temperature", "names": ["C", "°C"]}, {"key": "K", "category": "temperature", "names": ["K", "°K"]}], "base/units/time.min": [{"locale": "base"}, {"key": "s", "category": "time", "si": true, "names": ["s"]}, {"key": "″", "category": "time", "names": ["″"]}, {"key": "min", "category": "time", "names": ["min", "′"]}, {"key": "°", "category": "time", "names": ["°"]}, {"key": "h", "category": "time", "names": ["h", "hr"]}], "base/units/volume.min": [{"locale": "base"}, {"key": "cu", "category": "volume", "names": ["cu", "cu."]}, {"key": "cu inch", "category": "volume", "names": ["cu in", "cu. in."]}, {"key": "cu ft", "category": "volume", "names": ["cu ft", "cu. ft."]}, {"key": "cu yd", "category": "volume", "names": ["cu yd", "cu. yd."]}, {"key": "bbl", "category": "volume", "names": ["bbl.", "bbl"]}, {"key": "fl. oz.", "category": "volume", "names": ["fl. oz.", "fl oz"]}, {"key": "gal", "category": "volume", "names": ["gal", "gal."]}, {"key": "pt", "category": "volume", "names": ["pt", "pt."]}, {"key": "qt", "category": "volume", "names": ["qt", "qt."]}, {"key": "fluid dram", "category": "volume", "names": ["fl dr", "fl. dr."]}, {"key": "tbsp", "category": "volume", "names": ["tbsp", "tbsp.", "Tbsp", "Tbsp."]}, {"key": "tsp", "category": "volume", "names": ["tsp", "tsp."]}, {"key": "cup", "category": "volume", "names": ["cp", "cp."]}, {"key": "cc", "category": "volume", "names": ["cc", "ccm"]}, {"key": "l", "category": "volume", "si": true, "names": ["l"]}], "base/units/weight.min": [{"locale": "base"}, {"key": "dram", "category": "weight", "names": ["dr", "dr."]}, {"key": "oz", "category": "weight", "names": ["oz", "oz."]}, {"key": "lb", "category": "weight", "names": ["lb", "lb."]}, {"key": "st", "category": "weight", "names": ["st", "st."]}, {"key": "qtr", "category": "weight", "names": ["qtr", "qtr."]}, {"key": "cwt", "category": "weight", "names": ["cwt", "cwt."]}, {"key": "LT", "category": "weight", "names": ["LT", "L.T."]}, {"key": "gr", "category": "weight", "names": ["gr"]}, {"key": "g", "category": "weight", "si": true, "names": ["g"]}, {"key": "mcg", "category": "weight", "names": ["mcg"]}, {"key": "t", "category": "weight", "names": ["t", "T"]}], "base/rules/clearspeak_base.min": {"locale": "base", "domain": "clearspeak", "modality": "speech", "kind": "abstract", "rules": [["Rule", "direct-speech", "default", "[t] @ext-speech", "self::*[@ext-speech]", "priority=Infinity"], ["Rule", "stree", "default", "[n] ./*[1]", "self::stree"], ["Rule", "unknown", "default", "[n] text()", "self::unknown"], ["Rule", "protected", "default", "[n] text() (grammar:ignoreCaps)", "self::number", "contains(@grammar, \"protected\")"], ["Rule", "omit-empty", "default", "[p] (pause:short)", "self::empty"], ["Rule", "omit-font", "default", "[n] self::* (grammar:ignoreFont=@font)", "self::identifier[@font=\"italic\"]", "string-length(text())=1", "not(contains(@grammar, \"ignoreFont\"))"], ["SpecializedRule", "omit-font", "default", "Caps_SayCaps"], ["Rule", "text", "default", "[n] text()", "self::text"], ["Rule", "capital", "default", "[n] text() (pitch:0.6, grammar:ignoreCaps)", "self::identifier", "@role=\"latinletter\" or @role=\"greekletter\" or @role=\"simple function\"", ".[@category=\"Lu\"]"], ["Rule", "capital-cap", "Caps_SayCaps", "[n] text()", "self::identifier", "@role=\"latinletter\" or @role=\"greekletter\"", ".[@category=\"Lu\"]"], ["Rule", "capital-cap-l", "Caps_SayCaps", "[p] (pause:short); [n] text()", "self::identifier", "@role=\"latinletter\" or @role=\"greekletter\"", ".[@category=\"Lu\"]", "preceding-sibling::*[1]", "not(name(preceding-sibling::*[1])=\"function\")", "not(contains(@grammar, \"angle\"))"], ["Rule", "capital-cap-r", "Caps_SayCaps", "[n] text() (pause:short)", "self::identifier", "@role=\"latinletter\" or @role=\"greekletter\"", ".[@category=\"Lu\"]", "following-sibling::*[1]"], ["Rule", "capital-cap-lr", "Caps_SayCaps", "[p] (pause:short); [n] text() (pause:short)", "self::identifier", "@role=\"latinletter\" or @role=\"greekletter\"", ".[@category=\"Lu\"]", "preceding-sibling::*[1]", "following-sibling::*[1]", "not(name(preceding-sibling::*[1])=\"function\")", "not(contains(@grammar, \"angle\"))"], ["Precondition", "collapsed", "default", "self::*[@alternative]", "not(contains(@grammar, \"collapsed\"))"], ["Precondition", "font", "default", "self::*[@font]", "not(contains(@grammar, \"ignoreFont\"))", "@font!=\"normal\""], ["Specialized", "font", "default", "Caps_SayCaps"], ["<PERSON><PERSON>", "font", "self::identifier[@font=\"normal\"]", "string-length(text())=1", "not(contains(@grammar, \"ignoreFont\"))", "@role!=\"unit\""], ["Precondition", "punctuation-lr", "default", "self::punctuation", "@role=\"comma\""], ["Precondition", "punctuation", "default", "self::punctuation", "@role=\"comma\"", "not(preceding-sibling::*[1]/children)", "not(following-sibling::*[1]/children)"], ["Precondition", "punctuation-l", "default", "self::punctuation", "@role=\"comma\"", "not(following-sibling::*[1]/children)"], ["Precondition", "punctuation-r", "default", "self::punctuation", "@role=\"comma\"", "not(preceding-sibling::*[1]/children)"], ["Precondition", "ellipsis", "Ellipses_AndSoOn", "self::punctuation", "@role=\"ellipsis\"", "not(following-sibling::*[1])", "not(preceding-sibling::*[last()][@role=\"ellipsis\"])"], ["Precondition", "ellipsis-andsoon", "Ellipses_AndSoOn", "self::punctuation", "@role=\"ellipsis\"", "preceding-sibling::*[1]", "following-sibling::*[1]"], ["Precondition", "vbar-evaluated", "default", "self::punctuated", "@role=\"endpunct\"", "content/*[1][@role=\"vbar\"]", "content/*[1][@embellished]", "name(content/*[1])=\"subscript\""], ["Precondition", "vbar-evaluated-both", "default", "self::punctuated", "@role=\"endpunct\"", "content/*[1][@role=\"vbar\"]", "content/*[1][@embellished]", "name(content/*[1])=\"superscript\"", "name(content/*[1]/children/*[1])=\"subscript\""], ["Precondition", "vbar-such-that", "VerticalLine_SuchThat", "self::punctuation", "@role=\"vbar\"", "not(parent::*/parent::*[@embellished=\"punctuation\"])"], ["Precondition", "vbar-divides", "default", "self::punctuation", "@role=\"vbar\"", "not(parent::*/parent::*[@embellished=\"punctuation\"])", "parent::*/parent::*[@role=\"sequence\"]"], ["Precondition", "vbar-always-divides", "VerticalLine_Divides", "self::punctuation", "@role=\"vbar\"", "not(parent::*/parent::*[@embellished=\"punctuation\"])"], ["Precondition", "vbar-given", "VerticalLine_Given", "self::punctuation", "@role=\"vbar\"", "not(parent::*/parent::*[@embellished=\"punctuation\"])"], ["Precondition", "member", "default", "self::operator", "@role=\"element\""], ["Precondition", "member-member", "SetMemberSymbol_Member", "self::operator", "@role=\"element\""], ["Precondition", "member-element", "SetMemberSymbol_Element", "self::operator", "@role=\"element\""], ["Precondition", "member-in", "SetMemberSymbol_In", "self::operator", "@role=\"element\""], ["Precondition", "member-belongs", "SetMemberSymbol_Belongs", "self::operator", "@role=\"element\""], ["Precondition", "not-member", "default", "self::operator", "@role=\"nonelement\""], ["Precondition", "not-member-member", "SetMemberSymbol_Member", "self::operator", "@role=\"nonelement\""], ["Precondition", "not-member-element", "SetMemberSymbol_Element", "self::operator", "@role=\"nonelement\""], ["Precondition", "not-member-in", "SetMemberSymbol_In", "self::operator", "@role=\"nonelement\""], ["Precondition", "not-member-belongs", "SetMemberSymbol_Belongs", "self::operator", "@role=\"nonelement\""], ["Precondition", "set-member", "default", "self::operator", "@role=\"element\"", "contains(@annotation, \"set:intensional\")"], ["Precondition", "set-member-member", "SetMemberSymbol_Member", "self::operator", "@role=\"element\"", "contains(@annotation, \"set:intensional\")"], ["Precondition", "set-member-element", "SetMemberSymbol_Element", "self::operator", "@role=\"element\"", "contains(@annotation, \"set:intensional\")"], ["Precondition", "set-member-in", "SetMemberSymbol_In", "self::operator", "@role=\"element\"", "contains(@annotation, \"set:intensional\")"], ["Precondition", "set-member-belongs", "SetMemberSymbol_Belongs", "self::operator", "@role=\"element\"", "contains(@annotation, \"set:intensional\")"], ["Precondition", "set-not-member", "default", "self::operator", "@role=\"nonelement\"", "contains(@annotation, \"set:intensional\")"], ["Precondition", "set-not-member-member", "SetMemberSymbol_Member", "self::operator", "@role=\"nonelement\"", "contains(@annotation, \"set:intensional\")"], ["Precondition", "set-not-member-element", "SetMemberSymbol_Element", "self::operator", "@role=\"nonelement\"", "contains(@annotation, \"set:intensional\")"], ["Precondition", "set-not-member-in", "SetMemberSymbol_In", "self::operator", "@role=\"nonelement\"", "contains(@annotation, \"set:intensional\")"], ["Precondition", "set-not-member-belongs", "SetMemberSymbol_Belongs", "self::operator", "@role=\"nonelement\"", "contains(@annotation, \"set:intensional\")"], ["Precondition", "prime", "default", "self::superscript", "children/*[2]", "children/*[2][@role=\"prime\"]", "self::*"], ["Precondition", "degrees", "default", "self::punctuated[@role=\"sequence\"]", "content/*[1][@role=\"degree\"]"], ["Precondition", "feet", "default", "self::superscript", "children/*[2][@role=\"prime\"]", "name(children/*[1])=\"number\"", "children/*[2][text()=\"′\"]", "not(contains(@grammar, \"degree\"))"], ["Precondition", "foot", "default", "self::superscript", "children/*[2][@role=\"prime\"]", "name(children/*[1])=\"number\"", "children/*[2][text()=\"′\"]", "children/*[1][text()=\"1\"]", "not(contains(@grammar, \"degree\"))"], ["Precondition", "inches", "default", "self::superscript", "children/*[2][@role=\"prime\"]", "name(children/*[1])=\"number\"", "children/*[2][text()=\"″\"]", "not(contains(@grammar, \"degree\"))"], ["Precondition", "inch", "default", "self::superscript", "children/*[2][@role=\"prime\"]", "name(children/*[1])=\"number\"", "children/*[2][text()=\"″\"]", "children/*[1][text()=\"1\"]", "not(contains(@grammar, \"degree\"))"], ["Precondition", "minutes", "default", "self::superscript", "children/*[2][@role=\"prime\"]", "children/*[2][text()=\"′\"]", "contains(@grammar, \"degree\")"], ["Precondition", "minute", "default", "self::superscript", "children/*[2][@role=\"prime\"]", "children/*[2][text()=\"′\"]", "contains(@grammar, \"degree\")", "children/*[1][text()=\"1\"]"], ["Precondition", "seconds", "default", "self::superscript", "children/*[2][@role=\"prime\"]", "children/*[2][text()=\"″\"]", "contains(@grammar, \"degree\")"], ["Precondition", "second", "default", "self::superscript", "children/*[2][@role=\"prime\"]", "children/*[2][text()=\"″\"]", "contains(@grammar, \"degree\")", "children/*[1][text()=\"1\"]"], ["Precondition", "degrees-angle", "default", "self::punctuation", "@role=\"degree\""], ["Precondition", "degree-angle", "default", "self::punctuation", "@role=\"degree\"", "preceding-sibling::*[text()=\"1\"]"], ["Precondition", "minutes-angle", "<PERSON><PERSON><PERSON><PERSON>", "self::superscript", "children/*[2][@role=\"prime\"]", "children/*[2][text()=\"′\"]", "not(contains(@grammar, \"degree\"))", "name(children/*[1])=\"number\" or (children/*[1][@role=\"latinletter\"] and \"\"=translate(children/*[1]/text(),\"abcdefghijklmnopqrstuvwxyz\", \"\"))"], ["Precondition", "minute-angle", "<PERSON><PERSON><PERSON><PERSON>", "self::superscript", "children/*[2][@role=\"prime\"]", "children/*[2][text()=\"′\"]", "not(contains(@grammar, \"degree\"))", "children/*[1][text()=\"1\"]"], ["Precondition", "seconds-angle", "<PERSON><PERSON><PERSON><PERSON>", "self::superscript", "children/*[2][@role=\"prime\"]", "children/*[2][text()=\"″\"]", "not(contains(@grammar, \"degree\"))", "name(children/*[1])=\"number\" or (children/*[1][@role=\"latinletter\"] and \"\"=translate(children/*[1]/text(),\"abcdefghijklmnopqrstuvwxyz\", \"\"))"], ["Precondition", "second-angle", "<PERSON><PERSON><PERSON><PERSON>", "self::superscript", "children/*[2][@role=\"prime\"]", "children/*[2][text()=\"″\"]", "not(contains(@grammar, \"degree\"))", "children/*[1][text()=\"1\"]"], ["Precondition", "feet-length", "Prime_Length", "self::superscript", "children/*[2][@role=\"prime\"]", "children/*[2][text()=\"′\"]", "not(contains(@grammar, \"degree\"))", "name(children/*[1])=\"number\" or (children/*[1][@role=\"latinletter\"] and \"\"=translate(children/*[1]/text(),\"abcdefghijklmnopqrstuvwxyz\", \"\"))"], ["Precondition", "foot-length", "Prime_Length", "self::superscript", "children/*[2][@role=\"prime\"]", "children/*[2][text()=\"′\"]", "not(contains(@grammar, \"degree\"))", "children/*[1][text()=\"1\"]"], ["Precondition", "inches-length", "Prime_Length", "self::superscript", "children/*[2][@role=\"prime\"]", "children/*[2][text()=\"″\"]", "not(contains(@grammar, \"degree\"))", "name(children/*[1])=\"number\" or (children/*[1][@role=\"latinletter\"] and \"\"=translate(children/*[1]/text(),\"abcdefghijklmnopqrstuvwxyz\", \"\"))"], ["Precondition", "inch-length", "Prime_Length", "self::superscript", "children/*[2][@role=\"prime\"]", "children/*[2][text()=\"″\"]", "not(contains(@grammar, \"degree\"))", "children/*[1][text()=\"1\"]"], ["Precondition", "punctuated", "default", "self::punctuated"], ["Precondition", "function", "default", "self::function"], ["Precondition", "article", "default", "self::*[contains(@grammar, \"addArticle\")]", "not(contains(@grammar, \"noArticle\"))"], ["Precondition", "appl", "default", "self::appl"], ["Precondition", "appl-simple", "default", "self::appl", "@role=\"simple function\"", "name(children/*[2])=\"appl\""], ["Precondition", "appl-simple-fenced", "default", "self::appl", "@role=\"simple function\"", "name(children/*[2])=\"fenced\"", "name(children/*[2]/children/*[1])=\"appl\""], ["Precondition", "appl-times", "Functions_None", "self::appl"], ["Precondition", "function-prefix", "default", "self::appl", "@role=\"prefix function\""], ["Precondition", "binary-operation", "ImpliedTimes_MoreImpliedTimes", "self::appl", "@role=\"prefix function\"", "parent::*/parent::infixop[@role=\"implicit\"]", "following-sibling::*", "not(contains(@grammar, \"impliedTimes\"))"], ["Precondition", "function-prefix-simple-arg", "default", "self::appl", "@role=\"prefix function\"", "name(children/*[2])=\"fenced\"", "contains(children/*[2]/children/*[1]/@annotation, \"clearspeak:simple\")", "name(children/*[2]/children/*[1])!=\"number\"", "name(children/*[2]/children/*[1])!=\"identifier\"", "name(children/*[2]/children/*[1])!=\"appl\""], ["Precondition", "function-prefix-embell", "default", "self::appl", "@role=\"prefix function\"", "name(children/*[1])!=\"function\""], ["Precondition", "function-prefix-fenced-or-frac-arg", "default", "self::appl", "@role=\"prefix function\"", "(name(children/*[2])=\"fenced\" and not(contains(children/*[2]/children/*[1]/@annotation, \"clearspeak:simple\"))) or name(children/*[2])=\"fraction\" or (name(children/*[2])!=\"fenced\" and not(contains(children/*[2]/@annotation, \"clearspeak:simple\")))", "self::*"], ["Precondition", "function-prefix-subscript", "default", "self::appl", "@role=\"prefix function\"", "name(children/*[1])=\"subscript\"", "self::*"], ["Precondition", "function-ln", "default", "self::appl", "@role=\"prefix function\"", "content/*[2][text()=\"ln\"]", "not(following-sibling::*)", "not(contains(@grammar, \"NatLog\"))"], ["Precondition", "function-ln-pause", "default", "self::appl", "@role=\"prefix function\"", "content/*[2][text()=\"ln\"]", "not(contains(@grammar, \"NatLog\"))"], ["Precondition", "function-ln-of", "default", "self::appl", "@role=\"prefix function\"", "content/*[2][text()=\"ln\"]", "name(children/*[2])=\"fenced\"", "not(contains(@grammar, \"NatLog\"))"], ["Precondition", "function-ln-natlog", "Log_LnAsNaturalLog", "self::appl", "@role=\"prefix function\"", "content/*[2][text()=\"ln\"]", "not(following-sibling::*)", "not(contains(@grammar, \"NatLog\"))"], ["Precondition", "function-ln-natlog-pause", "Log_LnAsNaturalLog", "self::appl", "@role=\"prefix function\"", "content/*[2][text()=\"ln\"]", "not(contains(@grammar, \"NatLog\"))"], ["Precondition", "function-prefix-as-exp", "default", "self::appl", "@role=\"prefix function\"", "name(parent::*/parent::*)=\"superscript\"", "not(following-sibling::*)", "(name(children/*[2])=\"fenced\" and not(contains(children/*[2]/children/*[1]/@annotation, \"clearspeak:simple\"))) or name(children/*[2])=\"fraction\" or (name(children/*[2])!=\"fenced\" and not(contains(children/*[2]/@annotation, \"clearspeak:simple\")))"], ["Precondition", "function-prefix-subscript-as-exp", "default", "self::appl", "@role=\"prefix function\"", "name(parent::*/parent::*)=\"superscript\"", "not(following-sibling::*)", "name(children/*[1])=\"subscript\""], ["Precondition", "function-prefix-hyper", "default", "self::appl", "@role=\"prefix function\"", "children/*[1][@category=\"Hyperbolic\"]"], ["Precondition", "function-prefix-inverse", "default", "self::appl", "@role=\"prefix function\"", "name(children/*[1])=\"superscript\"", "name(children/*[1]/children/*[2])=\"prefixop\"", "children/*[1]/children/*[2][@role=\"negative\"]", "children/*[1]/children/*[2]/children/*[1][text()=\"1\"]", "not(contains(@grammar, \"functions_none\"))"], ["Precondition", "appl-triginverse", "Trig_TrigInverse", "self::appl", "@role=\"prefix function\"", "name(children/*[1])=\"superscript\"", "name(children/*[1]/children/*[2])=\"prefixop\"", "children/*[1]/children/*[2][@role=\"negative\"]", "children/*[1]/children/*[2]/children/*[1][text()=\"1\"]"], ["Precondition", "function-prefix-arc-simple", "Trig_ArcTrig", "self::appl", "@role=\"prefix function\"", "name(children/*[1])=\"superscript\"", "name(children/*[1]/children/*[2])=\"prefixop\"", "children/*[1]/children/*[2][@role=\"negative\"]", "children/*[1]/children/*[2]/children/*[1][text()=\"1\"]", "not(contains(@grammar, \"functions_none\"))"], ["Precondition", "function-prefix-arc-simple-fenced", "Trig_ArcTrig", "self::appl", "@role=\"prefix function\"", "name(children/*[1])=\"superscript\"", "name(children/*[1]/children/*[2])=\"prefixop\"", "children/*[1]/children/*[2][@role=\"negative\"]", "children/*[1]/children/*[2]/children/*[1][text()=\"1\"]", "name(children/*[2])=\"fenced\"", "children/*[2]/children/*[1][@role=\"prefix function\"]", "contains(children/*[2]/children/*[1]/@annotation, \"clearspeak:simple\")", "not(contains(@grammar, \"functions_none\"))"], ["Precondition", "function-prefix-arc", "Trig_ArcTrig", "self::appl", "@role=\"prefix function\"", "name(children/*[1])=\"superscript\"", "name(children/*[1]/children/*[2])=\"prefixop\"", "children/*[1]/children/*[2][@role=\"negative\"]", "children/*[1]/children/*[2]/children/*[1][text()=\"1\"]", "not(contains(@grammar, \"functions_none\"))", "(name(children/*[2])=\"fenced\" and not(contains(children/*[2]/children/*[1]/@annotation, \"clearspeak:simple\"))) or (name(children/*[2])=\"fraction\" and children/*[2][@role!=\"vulgar\"])"], ["Precondition", "function-inverse", "default", "self::superscript", "@role=\"prefix function\" or @role=\"simple function\"", "name(children/*[2])=\"prefixop\"", "children/*[2][@role=\"negative\"]", "children/*[2]/children/*[1][text()=\"1\"]", "not(contains(@grammar, \"functions_none\"))"], ["Precondition", "superscript-prefix-function", "default", "self::superscript", "@role=\"prefix function\"", "name(children/*[2])=\"number\"", "children/*[2][@role=\"integer\"]"], ["<PERSON><PERSON>", "superscript-prefix-function", "self::superscript[@role=\"prefix function\"]", "name(children/*[2])=\"identifier\""], ["Precondition", "function-no-inverse", "Functions_None", "self::superscript", "@role=\"prefix function\" or @role=\"simple function\"", "name(children/*[2])=\"prefixop\"", "children/*[2][@role=\"negative\"]", "children/*[2]/children/*[1][text()=\"1\"]", "not(contains(@grammar, \"functions_none\"))"], ["Precondition", "superscript", "default", "self::superscript"], ["Precondition", "superscript-simple-exponent", "default", "self::superscript", "not(descendant::superscript)"], ["Precondition", "superscript-simple-exponent-end", "default", "self::superscript", "not(descendant::superscript)", "not(following-sibling::*)"], ["<PERSON><PERSON>", "superscript-simple-exponent", "self::superscript", "children/superscript/children/*[2][text()=\"2\"] or children/superscript/children/*[2][text()=\"3\"]", "name(children/superscript/children/*[1])=\"number\"", "contains(children/superscript/children/*[1]/@annotation, \"clearspeak:simple\")"], ["<PERSON><PERSON>", "superscript-simple-exponent", "self::superscript", "children/superscript/children/*[2][text()=\"2\"] or children/superscript/children/*[2][text()=\"3\"]", "name(children/superscript/children/*[1])=\"fraction\"", "contains(children/superscript/children/*[1]/@annotation, \"clearspeak:simple\")"], ["<PERSON><PERSON>", "superscript-simple-exponent", "self::superscript", "children/superscript/children/*[2][text()=\"2\"] or children/superscript/children/*[2][text()=\"3\"]", "name(children/superscript/children/*[1])=\"identifier\""], ["<PERSON><PERSON>", "superscript-simple-exponent", "self::superscript", "children/*[2][@role=\"implicit\"]", "count(children/*[2]/children/*)=2", "contains(children/*[2]/children/*[1]/@annotation, \"simple\")", "name(children/*[2]/children/*[2])=\"superscript\"", "(name(children/*[2]/children/*[2]/children/*[1])=\"number\" and contains(children/*[2]/children/*[2]/children/*[1]/@annotation, \"clearspeak:simple\")) or name(children/*[2]/children/*[2]/children/*[1])=\"identifier\"", "children/*[2]/children/*[2]/children/*[2][text()=\"2\"] or children/*[2]/children/*[2]/children/*[2][text()=\"3\"]"], ["Precondition", "superscript-ordinal", "default", "self::superscript", "name(children/*[2])=\"number\"", "children/*[2][@role=\"integer\"]"], ["<PERSON><PERSON>", "superscript-ordinal", "self::superscript", "name(children/*[2])=\"identifier\"", "children/*[2][@role=\"latinletter\" or @role=\"greekletter\" or @role=\"otherletter\"]"], ["Precondition", "superscript-non-ordinal", "default", "self::superscript", "children/*[2][@role=\"negative\"]", "name(children/*[2]/children/*[1])=\"number\"", "children/*[2]/children/*[1][@role=\"integer\"]"], ["Precondition", "superscript-simple-function", "default", "self::superscript", "name(children/*[1])=\"identifier\"", "children/*[1][@role=\"simple function\"]", "children/*[2][@role!=\"prime\"]", "not(contains(@grammar, \"functions_none\"))"], ["Precondition", "superscript-simple-function-none", "Functions_None", "self::superscript", "name(children/*[1])=\"identifier\"", "children/*[1][@role=\"simple function\"]", "not(contains(@grammar, \"functions_none\"))"], ["Precondition", "superscript-ordinal-number", "Exponent_Ordinal", "self::superscript", "name(children/*[2])=\"number\"", "children/*[2][@role=\"integer\"]"], ["<PERSON><PERSON>", "superscript-ordinal-number", "self::superscript", "name(children/*[2])=\"identifier\"", "children/*[2][@role=\"latinletter\" or @role=\"greekletter\" or @role=\"otherletter\"]"], ["Precondition", "superscript-ordinal-negative", "Exponent_Ordinal", "self::superscript", "name(children/*[2])=\"prefixop\"", "children/*[2][@role=\"negative\"]", "name(children/*[2]/children/*[1])=\"number\"", "children/*[2]/children/*[1][@role=\"integer\"]"], ["Precondition", "superscript-ordinal-default", "Exponent_Ordinal", "self::superscript", "children//superscript"], ["Precondition", "superscript-ordinal-power-number", "Exponent_OrdinalPower", "self::superscript", "name(children/*[2])=\"number\"", "children/*[2][@role=\"integer\"]"], ["Precondition", "superscript-ordinal-power-negative", "Exponent_OrdinalPower", "self::superscript", "name(children/*[2])=\"prefixop\"", "children/*[2][@role=\"negative\"]", "name(children/*[2]/children/*[1])=\"number\"", "children/*[2]/children/*[1][@role=\"integer\"]"], ["Precondition", "superscript-ordinal-power-identifier", "Exponent_OrdinalPower", "self::superscript", "name(children/*[2])=\"identifier\"", "children/*[2][@role=\"latinletter\" or @role=\"greekletter\" or @role=\"otherletter\"]"], ["Precondition", "superscript-ordinal-power-default", "Exponent_OrdinalPower", "self::superscript", "children//superscript"], ["Precondition", "superscript-power", "Exponent_AfterPower", "self::superscript"], ["Precondition", "superscript-power-default", "Exponent_AfterPower", "self::superscript", "children//superscript"], ["Precondition", "exponent", "default", "self::identifier", "contains(@grammar, \"ordinal\")"], ["Precondition", "exponent-number", "default", "self::number", "@role=\"integer\"", "contains(@grammar, \"ordinal\")", "text()!=\"0\"", "not(contains(@annotation, \"general:basenumber\"))"], ["Precondition", "exponent-ordinal", "Exponent_Ordinal", "self::number", "@role=\"integer\"", "contains(@grammar, \"ordinal\")", "text()!=\"0\"", "not(contains(@annotation, \"general:basenumber\"))"], ["Precondition", "exponent-ordinal-zero", "Exponent_Ordinal", "self::number", "@role=\"integer\"", "contains(@grammar, \"ordinal\")", "text()=\"0\""], ["Precondition", "exponent-ordinalpower", "Exponent_OrdinalPower", "self::number", "@role=\"integer\"", "contains(@grammar, \"ordinal\")", "text()!=\"0\"", "not(contains(@annotation, \"general:basenumber\"))"], ["Precondition", "exponent-ordinalpower-zero", "Exponent_OrdinalPower", "self::number", "@role=\"integer\"", "contains(@grammar, \"ordinal\")", "text()=\"0\""], ["Precondition", "square", "default", "self::superscript", "@role!=\"unit\"", "children/*[2][text()=\"2\"]", "name(children/*[1])!=\"text\" or not(name(children/*[1])=\"text\" and (name(../../../punctuated[@role=\"text\"]/..)=\"stree\" or name(..)=\"stree\"))", "self::*"], ["Precondition", "cube", "default", "self::superscript", "@role!=\"unit\"", "children/*[2][text()=\"3\"]", "name(children/*[1])!=\"text\" or not(name(children/*[1])=\"text\" and (name(../../../punctuated[@role=\"text\"]/..)=\"stree\" or name(..)=\"stree\"))", "self::*"], ["Precondition", "paren-simple", "default", "self::fenced", "@role=\"leftright\"", "contains(children/*[1]/@annotation, \"clearspeak:simple\")", "name(../..)!=\"superscript\" and name(../..)!=\"subscript\""], ["Precondition", "paren-simple-exp", "default", "self::fenced", "@role=\"leftright\"", "name(../..)=\"superscript\"", "children/*[1][@role=\"integer\"] or children/*[1][@role=\"float\"] or (children/*[1][@role=\"vulgar\"] and contains(children/*[1]/@annotation, \"clearspeak:simple\")) or children/*[1][@role=\"latinletter\"] or children/*[1][@role=\"greekletter\"] or children/*[1][@role=\"otherletter\"]"], ["Precondition", "paren-simple-nested-func", "default", "self::fenced", "@role=\"leftright\"", "name(../*[1])=\"identifier\" or name(../*[1])=\"function\"", "parent::*/parent::*[@role=\"simple function\" or @role=\"prefix function\"]", "children/*[1][@role=\"simple function\" or @role=\"prefix function\"]", "contains(children/*[1]/children/*[2]/children/*[1]/@annotation, \"clearspeak:simple\") or name(children/*[1]/children/*[2]/children/*[1])=\"subscript\" or name(children/*[1]/children/*[2]/children/*[1])=\"superscript\" or children/*[1]/children/*[2]/children/*[1][@role=\"vulgar\"] "], ["Precondition", "paren-simple-nested-func-no-bracket", "Functions_None", "self::fenced", "@role=\"leftright\"", "name(../*[1])=\"identifier\" or name(../*[1])=\"function\"", "parent::*/parent::*[@role=\"simple function\" or @role=\"prefix function\"]", "children/*[1][@role=\"simple function\" or @role=\"prefix function\"]", "name(children/*[1]/children/*[1])=\"identifier\" or name(children/*[1]/children/*[1])=\"function\"", "contains(children/*[1]/children/*[2]/children/*[1]/@annotation, \"clearspeak:simple\")", "name(children/*[1]/children/*[2]/children/*[1])=\"identifier\" or name(children/*[1]/children/*[2]/children/*[1])=\"number\""], ["Precondition", "fences-open-close", "default", "self::fenced", "@role=\"leftright\""], ["<PERSON><PERSON>", "fences-open-close", "self::fenced"], ["Specialized", "fences-open-close", "default", "self::fenced", "@role=\"metric\""], ["Precondition", "paren-simple-nested-func-default", "default", "self::fenced", "@role=\"leftright\"", "name(../*[1])=\"identifier\" or name(../*[1])=\"function\"", "parent::*/parent::*[@role=\"simple function\" or @role=\"prefix function\"]", "not(contains(children/*[1]/@annotation, \"clearspeak:simple\"))"], ["Precondition", "paren-simple-nested-func-none", "Functions_None", "self::fenced", "@role=\"leftright\"", "name(../*[1])=\"identifier\" or name(../*[1])=\"function\"", "parent::*/parent::*[@role=\"simple function\" or @role=\"prefix function\"]", "children/*[1][@role=\"simple function\" or @role=\"prefix function\"]", "contains(children/*[1]/children/*[2]/children/*[1]/@annotation, \"clearspeak:simple\") or name(children/*[1]/children/*[2]/children/*[1])=\"subscript\" or name(children/*[1]/children/*[2]/children/*[1])=\"superscript\" or children/*[1]/children/*[2]/children/*[1][@role=\"vulgar\"] "], ["Specialized", "fences-open-close", "default", "<PERSON><PERSON>_<PERSON>"], ["<PERSON><PERSON>", "fences-open-close", "self::fenced", "@role=\"composed function\""], ["Precondition", "fence-silent", "<PERSON><PERSON>_<PERSON>", "self::fenced"], ["Precondition", "fences-open-close-none", "ImpliedTimes_None", "self::fenced", "@role=\"leftright\"", "parent::*/parent::*[@role!=\"simple function\"]", "parent::*/parent::*[@role!=\"prefix function\"]"], ["Precondition", "fence-nesting", "<PERSON><PERSON>_SpeakNestingLevel", "self::fence", "contains(@grammar, \"spokenFence\")", "CQFmatchingFences"], ["Precondition", "fence-no-nesting", "<PERSON><PERSON>_SpeakNestingLevel", "self::fence"], ["Precondition", "fences-points", "Paren_CoordPoint", "self::fenced", "name(children/*[1])=\"punctuated\"", "children/*[1][@role=\"sequence\"]"], ["Precondition", "fences-interval", "<PERSON><PERSON>_Interval", "self::fenced", "not(contains(@grammar, \"interval\"))", "name(children/*[1])=\"punctuated\"", "children/*[1][@role=\"sequence\"]", "count(./children/*[1]/content/*)=1", "children/*[1]/content/*[1][@role=\"comma\"]"], ["Precondition", "interval-open", "<PERSON><PERSON>_Interval", "self::fenced", "contains(@grammar, \"interval\")", "content/*[1]/text()=\"(\"", "content/*[2]/text()=\")\""], ["Precondition", "interval-closed-open", "<PERSON><PERSON>_Interval", "self::fenced", "contains(@grammar, \"interval\")", "content/*[1]/text()=\"[\"", "content/*[2]/text()=\")\""], ["Precondition", "interval-open-closed", "<PERSON><PERSON>_Interval", "self::fenced", "contains(@grammar, \"interval\")", "content/*[1]/text()=\"(\"", "content/*[2]/text()=\"]\""], ["Precondition", "interval-closed", "<PERSON><PERSON>_Interval", "self::fenced", "contains(@grammar, \"interval\")", "content/*[1]/text()=\"[\"", "content/*[2]/text()=\"]\""], ["Precondition", "interval-open-inf-r", "<PERSON><PERSON>_Interval", "self::fenced", "contains(@grammar, \"interval\")", "content/*[1]/text()=\"(\"", "content/*[2]/text()=\")\"", "children/*[1]/children/*[3]/text()=\"∞\" or (name(children/*[1]/children/*[3])=\"prefixop\" and children/*[1]/children/*[3]/children/*[1]/text()=\"∞\")"], ["Precondition", "interval-open-inf-l", "<PERSON><PERSON>_Interval", "self::fenced", "contains(@grammar, \"interval\")", "content/*[1]/text()=\"(\"", "content/*[2]/text()=\")\"", "children/*[1]/children/*[1]/text()=\"∞\" or (name(children/*[1]/children/*[1])=\"prefixop\" and children/*[1]/children/*[1]/children/*[1]/text()=\"∞\")"], ["Precondition", "interval-open-inf-lr", "<PERSON><PERSON>_Interval", "self::fenced", "contains(@grammar, \"interval\")", "content/*[1]/text()=\"(\"", "content/*[2]/text()=\")\"", "children/*[1]/children/*[3]/text()=\"∞\" or (name(children/*[1]/children/*[3])=\"prefixop\" and children/*[1]/children/*[3]/children/*[1]/text()=\"∞\")", "children/*[1]/children/*[1]/text()=\"∞\" or (name(children/*[1]/children/*[1])=\"prefixop\" and children/*[1]/children/*[1]/children/*[1]/text()=\"∞\")"], ["Precondition", "interval-closed-open-inf", "<PERSON><PERSON>_Interval", "self::fenced", "contains(@grammar, \"interval\")", "content/*[1]/text()=\"[\"", "content/*[2]/text()=\")\"", "children/*[1]/children/*[3]/text()=\"∞\" or (name(children/*[1]/children/*[3])=\"prefixop\" and children/*[1]/children/*[3]/children/*[1]/text()=\"∞\")"], ["Precondition", "interval-open-closed-inf", "<PERSON><PERSON>_Interval", "self::fenced", "contains(@grammar, \"interval\")", "content/*[1]/text()=\"(\"", "content/*[2]/text()=\"]\"", "children/*[1]/children/*[1]/text()=\"∞\" or (name(children/*[1]/children/*[1])=\"prefixop\" and children/*[1]/children/*[1]/children/*[1]/text()=\"∞\")"], ["Precondition", "paren-nested-embellished-funcs", "Functions_None", "self::fenced", "@role=\"leftright\"", "name(../..)=\"appl\"", "name(children/*[1]) = \"appl\"", "preceding-sibling::*/descendant-or-self::*[@role=\"subsup\"] or children/*[1]/descendant-or-self::*[@role=\"subsup\"]"], ["Precondition", "set-empty", "default", "self::fenced", "@role=\"set empty\""], ["Precondition", "set-extended", "default", "self::fenced", "@role=\"set extended\""], ["Precondition", "set-collection", "default", "self::fenced", "@role=\"set collection\""], ["<PERSON><PERSON>", "set-collection", "self::fenced", "@role=\"set singleton\""], ["Precondition", "set-extended-woall", "Sets_woAll", "self::fenced", "@role=\"set extended\""], ["Precondition", "set-collection-<PERSON><PERSON><PERSON>", "Sets_SilentBracket", "self::fenced", "@role=\"set collection\""], ["Precondition", "subscript", "default", "self::subscript"], ["Precondition", "logarithm-base", "default", "self::subscript", "children/*[1][@category=\"Logarithm\"]"], ["Precondition", "subscript-index", "default", "self::subscript", "contains(@grammar, \"simpleDet\")"], ["Precondition", "fraction", "default", "self::fraction"], ["Precondition", "fraction-none", "Functions_None", "self::fraction", "name(children/*[1])=\"appl\" or name(children/*[2])=\"appl\""], ["Precondition", "simple-fraction", "default", "self::fraction", "contains(children/*[1]/@annotation, \"clearspeak:simple\") or contains(children/*[1]/@annotation, \"clearspeak:unit\")", "contains(children/*[2]/@annotation, \"clearspeak:simple\") or contains(children/*[2]/@annotation, \"clearspeak:unit\")"], ["Precondition", "simple-vulgar-fraction", "default", "self::fraction", "@role=\"vulgar\""], ["Precondition", "simple-text-fraction", "default", "self::fraction", "name(children/*[1])=\"text\"", "name(children/*[2])=\"text\""], ["<PERSON><PERSON>", "simple-text-fraction", "self::fraction", "name(children/*[1])=\"infixop\"", "children/*[1][@role=\"unit\"]", "name(children/*[2])=\"text\""], ["Precondition", "vulgar-fraction", "default", "self::fraction", "@role=\"vulgar\"", "CQFvulgarFractionSmall"], ["Precondition", "fraction-over", "Fraction_Over", "self::fraction"], ["Precondition", "fraction-overendfrac", "Fraction_OverEndFrac", "self::fraction"], ["Precondition", "fraction-fracover", "Fraction_FracOver", "self::fraction"], ["Precondition", "fraction-per", "Fraction_Per", "self::fraction"], ["Precondition", "fraction-general<PERSON><PERSON><PERSON>", "Fraction_GeneralEndFrac", "self::fraction"], ["Precondition", "fraction-general", "Fraction_General", "self::fraction"], ["Precondition", "simple-vulgar-fraction-ordinal", "Fraction_Ordinal", "self::fraction", "@role=\"vulgar\""], ["Precondition", "fraction-endfrac", "Fraction_EndFrac", "self::fraction", "not(contains(@grammar, \"endfrac\"))", "not(contains(children/*[1]/@annotation, \"clearspeak:unit\"))", "not(contains(children/*[2]/@annotation, \"clearspeak:unit\"))"], ["Precondition", "vulgar-fraction-endfrac", "Fraction_EndFrac", "self::fraction", "name(children/*[1])=\"fraction\"", "name(children/*[2])=\"fraction\"", "contains(children/*[1]/@annotation, \"clearspeak:simple\")", "contains(children/*[2]/@annotation, \"clearspeak:simple\")"], ["Precondition", "simple-vulgar-fraction-endfrac", "Fraction_EndFrac", "self::fraction", "@role=\"vulgar\"", "contains(@annotation, \"clearspeak:simple\")", "self::*"], ["Precondition", "sqrt", "default", "self::sqrt"], ["Precondition", "sqrt-nested", "default", "self::sqrt", "not(preceding-sibling::*)", "ancestor::sqrt|ancestor::root"], ["Precondition", "negative-sqrt", "default", "self::prefixop", "@role=\"negative\"", "name(children/*[1])=\"sqrt\""], ["Precondition", "negative-sqrt-default", "default", "self::prefixop", "@role=\"negative\"", "name(children/*[1])=\"sqrt\"", "not(preceding-sibling::*)", "ancestor::sqrt|ancestor::root"], ["Precondition", "sqrt-plus-minus", "Roots_PosNegSqRoot", "self::sqrt", "parent::stree or not(parent::*/parent::infixop[@role=\"addition\"]) or (parent::*/parent::*[1]/text()!=\"±\" and parent::*/parent::*/text()!=\"∓\")"], ["Precondition", "sqrt-nested-plus-minus", "Roots_PosNegSqRoot", "self::sqrt", "not(preceding-sibling::*)", "ancestor::sqrt|ancestor::root", "parent::stree or not(parent::*/parent::infixop[@role=\"addition\"]) or (parent::*/parent::*[1]/text()!=\"±\" and parent::*/parent::*/text()!=\"∓\")"], ["Precondition", "sqrt-plus-minus-posnegsqrootend", "Roots_PosNegSqRootEnd", "self::sqrt", "parent::stree or not(parent::*/parent::infixop[@role=\"addition\"]) or (parent::*/parent::*[1]/text()!=\"±\" and parent::*/parent::*/text()!=\"∓\")"], ["Precondition", "sqrt-nested-plus-minus-posnegsqrootend", "Roots_PosNegSqRootEnd", "self::sqrt", "not(preceding-sibling::*)", "ancestor::sqrt|ancestor::root", "parent::stree or not(parent::*/parent::infixop[@role=\"addition\"]) or (parent::*/parent::*[1]/text()!=\"±\" and parent::*/parent::*/text()!=\"∓\")"], ["Precondition", "sqrt-endroot", "Roots_RootEnd", "self::sqrt", "not(contains(@grammar, \"EndRoot\"))"], ["Precondition", "negative-sqrt-endroot", "Roots_RootEnd", "self::prefixop", "@role=\"negative\"", "name(children/*[1])=\"sqrt\"", "not(contains(@grammar, \"EndRoot\"))"], ["Precondition", "sqrt-posnegsqrootend", "Roots_PosNegSqRootEnd", "self::sqrt", "not(contains(@grammar, \"EndRoot\"))"], ["Precondition", "negative-sqrt-posnegsqrootend", "Roots_PosNegSqRootEnd", "self::prefixop", "@role=\"negative\"", "name(children/*[1])=\"sqrt\"", "not(contains(@grammar, \"EndRoot\"))"], ["Precondition", "cubic", "default", "self::root", "children/*[1][text()=\"3\"]"], ["Precondition", "cubic-nested", "default", "self::root", "children/*[1][text()=\"3\"]", "not(preceding-sibling::*)", "ancestor::sqrt|ancestor::root"], ["Precondition", "root", "default", "self::root"], ["Precondition", "root-nested", "default", "self::root", "not(preceding-sibling::*)", "ancestor::sqrt|ancestor::root"], ["Precondition", "root-endroot", "Roots_RootEnd", "self::root", "not(contains(@grammar, \"EndRoot\"))"], ["Precondition", "root-posnegsqrootend", "Roots_PosNegSqRootEnd", "self::root", "not(contains(@grammar, \"EndRoot\"))"], ["Precondition", "negative", "default", "self::prefixop", "@role=\"negative\""], ["Precondition", "positive", "default", "self::prefixop", "@role=\"positive\""], ["Precondition", "angle-measure", "default", "self::infixop", "content/*[1]/text()=\"∠\"", "children/*[1][text()=\"m\"]"], ["Precondition", "prefix", "default", "self::prefixop"], ["Precondition", "postfix", "default", "self::postfixop"], ["Precondition", "set-prefix-operators", "default", "self::*[@grammar]", "contains(@grammar,\"prefix\")", "descendant-or-self::*/text()=\"∩\" or descendant-or-self::*/text()=\"∪\"", "self::*", "self::*", "self::*"], ["Precondition", "binary-operation-default", "default", "self::infixop"], ["Precondition", "division", "default", "self::infixop", "@role=\"division\"", "count(children/*)=2"], ["Precondition", "binary-operation-moreimpliedtimes", "ImpliedTimes_MoreImpliedTimes", "self::infixop", "@role=\"implicit\""], ["Precondition", "binary-operation-pause", "default", "self::infixop", "@role=\"implicit\"", "name(children/*[1])=\"appl\""], ["Precondition", "binary-operation-pause-r", "default", "self::infixop", "@role=\"implicit\"", "name(children/*[last()])=\"appl\""], ["Precondition", "binary-operation-pause-lr", "default", "self::infixop", "@role=\"implicit\"", "name(children/*[1])=\"appl\"", "name(children/*[last()])=\"appl\""], ["Precondition", "implicit-times", "default", "self::operator", "@role=\"multiplication\"", "text()=\"⁢\""], ["Precondition", "implicit-times-default", "default", "self::operator", "@role=\"multiplication\"", "text()=\"⁢\"", "CQFsimpleArguments"], ["Precondition", "implicit-times-simple", "default", "self::operator", "@role=\"multiplication\"", "text()=\"⁢\"", "CQFfencedArguments"], ["Precondition", "implicit-times-moreimpliedtimes", "ImpliedTimes_MoreImpliedTimes", "self::operator", "@role=\"multiplication\"", "text()=\"⁢\""], ["Precondition", "implicit-times-none", "ImpliedTimes_None", "self::operator", "@role=\"multiplication\"", "text()=\"⁢\""], ["Precondition", "binary-operation-simple", "default", "self::infixop", "@role=\"implicit\"", "contains(@annotation, \"clearspeak:simple\")", "not(contains(@grammar, \"inFrac\"))"], ["Precondition", "simple-in-fraction", "default", "self::*[@annotation]", "contains(@annotation, \"clearspeak:simple\")", "not(contains(@grammar, \"inFrac\"))", "name(.)!=\"identifier\"", "name(.)!=\"function\"", "name(.)!=\"number\"", "name(parent::*/parent::*)=\"fraction\"", "not(preceding-sibling::*)"], ["Precondition", "operators-after-power", "Exponent_AfterPower", "self::infixop", "@role=\"implicit\"", "contains(@grammar, \"afterPower\")"], ["Precondition", "relseq", "default", "self::relseq"], ["Precondition", "mult<PERSON>", "default", "self::multirel"], ["Precondition", "natural-numbers", "default", "self::identifier[@role=\"numbersetletter\"]", "text()=\"ℕ\" or (text()=\"N\" and @font=\"double-struck\")"], ["Precondition", "integers", "default", "self::identifier[@role=\"numbersetletter\"]", "text()=\"ℤ\" or (text()=\"Z\" and @font=\"double-struck\")"], ["Precondition", "rational-numbers", "default", "self::identifier[@role=\"numbersetletter\"]", "text()=\"ℚ\" or (text()=\"Q\" and @font=\"double-struck\")"], ["Precondition", "real-numbers", "default", "self::identifier[@role=\"numbersetletter\"]", "text()=\"ℝ\" or (text()=\"R\" and @font=\"double-struck\")"], ["Precondition", "complex-numbers", "default", "self::identifier[@role=\"numbersetletter\"]", "text()=\"ℂ\" or (text()=\"C\" and @font=\"double-struck\")"], ["Precondition", "natural-numbers-super", "default", "self::superscript", "children/*[1]/text()=\"ℕ\" or (children/*[1]/text()=\"N\" and children/*[1]/@font=\"double-struck\")", "self::*", "self::*", "self::*"], ["Precondition", "integers-super", "default", "self::superscript", "children/*[1]/text()=\"ℤ\" or (children/*[1]/text()=\"Z\" and children/*[1]/@font=\"double-struck\")", "self::*", "self::*", "self::*"], ["Precondition", "rational-numbers-super", "default", "self::superscript", "children/*[1]/text()=\"ℚ\" or (children/*[1]/text()=\"Q\" and children/*[1]/@font=\"double-struck\")", "self::*", "self::*", "self::*"], ["Precondition", "real-numbers-super", "default", "self::superscript", "children/*[1]/text()=\"ℝ\" or (children/*[1]/text()=\"R\" and children/*[1]/@font=\"double-struck\")", "self::*", "self::*", "self::*"], ["Precondition", "complex-numbers-super", "default", "self::superscript", "children/*[1]/text()=\"ℂ\" or (children/*[1]/text()=\"C\" and children/*[1]/@font=\"double-struck\")", "self::*", "self::*", "self::*"], ["Precondition", "natural-numbers-with-zero", "default", "self::subscript", "children/*[1]/text()=\"ℕ\" or (children/*[1]/text()=\"N\" and children/*[1]/@font=\"double-struck\")", "children/*[2]/text()=\"0\""], ["Precondition", "positive-integers", "default", "self::superscript", "children/*[1]/text()=\"ℤ\" or (children/*[1]/text()=\"Z\" and children/*[1]/@font=\"double-struck\")", "children/*[2]/text()=\"+\"", "self::*", "self::*", "self::*"], ["Precondition", "negative-integers", "default", "self::superscript", "children/*[1]/text()=\"ℤ\" or (children/*[1]/text()=\"Z\" and children/*[1]/@font=\"double-struck\")", "children/*[2]/text()=\"-\"", "self::*", "self::*", "self::*"], ["Precondition", "positive-rational-numbers", "default", "self::superscript", "children/*[1]/text()=\"ℚ\" or (children/*[1]/text()=\"Q\" and children/*[1]/@font=\"double-struck\")", "children/*[2]/text()=\"+\"", "self::*", "self::*", "self::*"], ["Precondition", "negative-rational-numbers", "default", "self::superscript", "children/*[1]/text()=\"ℚ\" or (children/*[1]/text()=\"Q\" and children/*[1]/@font=\"double-struck\")", "children/*[2]/text()=\"-\"", "self::*", "self::*", "self::*"], ["Precondition", "fences-neutral", "default", "self::fenced", "@role=\"neutral\""], ["Precondition", "fences-neutral-absend", "AbsoluteValue_AbsEnd", "self::fenced", "@role=\"neutral\""], ["Precondition", "fences-neutral-cardinality", "AbsoluteValue_Cardinality", "self::fenced", "@role=\"neutral\""], ["Precondition", "fences-neutral-determinant", "AbsoluteValue_Determinant", "self::fenced", "@role=\"neutral\""], ["Precondition", "fences-metric", "default", "self::fenced", "@role=\"metric\""], ["Precondition", "fences-metric-absend", "AbsoluteValue_AbsEnd", "self::fenced", "@role=\"metric\""], ["Precondition", "matrix", "default", "self::matrix"], ["Precondition", "matrix-simple", "default", "self::matrix", "count(children/*)<4", "count(children/*[1]/children/*)<4", "CQFcellsSimple"], ["Precondition", "matrix-trivial", "default", "self::vector", "@role=\"squarematrix\""], ["Precondition", "determinant", "default", "self::matrix", "@role=\"determinant\"", "count(children/*)<4", "CQFcellsSimple"], ["Precondition", "determinant-simple", "default", "self::matrix", "@role=\"determinant\""], ["Precondition", "matrix-vector", "default", "self::vector"], ["Specialized", "matrix-vector", "default", "Matrix_SpeakColNum"], ["Precondition", "matrix-vector-simple", "default", "self::vector", "count(children/*)<4", "CQFcellsSimple", "@role!=\"squarematrix\""], ["Precondition", "matrix-vector-simple-silentcolnum", "Matrix_SilentColNum", "self::vector"], ["Precondition", "matrix-row-vector", "default", "self::matrix", "@role=\"rowvector\""], ["Specialized", "matrix-row-vector", "default", "Matrix_SpeakColNum"], ["Precondition", "matrix-row-vector-simple", "default", "self::matrix", "@role=\"rowvector\"", "count(children/*[1]/children/*)<4", "CQFcellsSimple"], ["Precondition", "matrix-row-vector-simple-silentcolnum", "Matrix_SilentColNum", "self::matrix", "@role=\"rowvector\""], ["Precondition", "matrix-row-simple", "default", "self::row", "contains(@grammar, \"simpleDet\")"], ["Precondition", "matrix-row-simple-silent<PERSON>lnum", "Matrix_SilentColNum", "self::row"], ["Precondition", "line-simple", "default", "self::line", "contains(@grammar, \"simpleDet\")"], ["Precondition", "matrix-row", "default", "self::row"], ["Specialized", "matrix-row", "default", "Matrix_SpeakColNum"], ["Precondition", "matrix-cell", "default", "self::cell"], ["Precondition", "matrix-end-matrix", "Matrix_EndMatrix", "self::matrix", "not(contains(@grammar, \"EndMatrix\"))"], ["Precondition", "matrix-end-vector", "Matrix_EndMatrix", "self::vector", "not(contains(@grammar, \"EndMatrix\"))"], ["Precondition", "matrix-end-determinant", "Matrix_EndMatrix", "self::matrix", "@role=\"determinant\"", "not(contains(@grammar, \"EndMatrix\"))"], ["Precondition", "vector", "Matrix_Vector", "self::vector"], ["Specialized", "vector", "Matrix_Vector", "Matrix_EndVector"], ["Precondition", "vector-simple", "Matrix_Vector", "self::vector", "count(children/*)<4", "CQFcellsSimple"], ["Specialized", "vector-simple", "Matrix_Vector", "Matrix_EndVector"], ["Precondition", "row-vector", "Matrix_Vector", "self::matrix", "@role=\"rowvector\""], ["Specialized", "row-vector", "Matrix_Vector", "Matrix_EndVector"], ["Precondition", "row-vector-simple", "Matrix_Vector", "self::matrix", "@role=\"rowvector\"", "count(children/*[1]/children/*)<4", "CQFcellsSimple"], ["Specialized", "row-vector-simple", "Matrix_Vector", "Matrix_EndVector"], ["Precondition", "vector-end-matrix", "Matrix_EndVector", "self::matrix", "not(contains(@grammar, \"EndMatrix\"))", "self::*"], ["Precondition", "vector-end-vector", "Matrix_EndVector", "self::vector", "not(contains(@grammar, \"EndMatrix\"))", "self::*"], ["Precondition", "vector-end-vector-endvector", "Matrix_EndVector", "self::matrix", "@role=\"rowvector\"", "not(contains(@grammar, \"EndMatrix\"))", "self::*"], ["Precondition", "vector-end-determinant", "Matrix_EndVector", "self::matrix", "@role=\"determinant\"", "not(contains(@grammar, \"EndMatrix\"))", "self::*"], ["Precondition", "binomial", "Matrix_Combinatoric", "self::vector", "@role=\"binomial\""], ["Precondition", "lines-summary", "default", "self::multiline", "not(contains(@grammar, \"layoutSummary\"))", "self::*"], ["<PERSON><PERSON>", "lines-summary", "self::table", "not(contains(@grammar, \"layoutSummary\"))", "self::*"], ["Precondition", "lines-summary-none", "MultiLineOverview_None", "self::multiline", "not(contains(@grammar, \"layoutSummary\"))", "self::*"], ["<PERSON><PERSON>", "lines-summary-none", "self::table", "not(contains(@grammar, \"layoutSummary\"))", "self::*"], ["Precondition", "cases-summary", "default", "self::cases", "not(contains(@grammar, \"layoutSummary\"))"], ["Precondition", "cases-summary-none", "MultiLineOverview_None", "self::cases", "not(contains(@grammar, \"layoutSummary\"))", "self::*"], ["Precondition", "lines", "default", "self::table"], ["<PERSON><PERSON>", "lines", "self::multiline"], ["Precondition", "line", "default", "self::line"], ["Precondition", "row-medium", "default", "self::row", "@role=\"table\""], ["<PERSON><PERSON>", "row-medium", "self::row", "@role=\"cases\""], ["Precondition", "row-long", "MultiLinePausesBetweenColumns_Long", "self::row", "@role=\"table\""], ["<PERSON><PERSON>", "row-long", "self::row", "@role=\"cases\""], ["Precondition", "row-short", "MultiLinePausesBetweenColumns_Short", "self::row", "@role=\"table\""], ["<PERSON><PERSON>", "row-short", "self::row", "@role=\"cases\""], ["Precondition", "blank-cell", "default", "self::cell", "count(children/*)=0"], ["Precondition", "blank-line", "default", "self::line", "count(children/*)=0"], ["Precondition", "blank-cell-empty", "default", "self::empty", "count(../*)=1", "name(../..)=\"cell\""], ["Precondition", "blank-line-empty", "default", "self::empty", "count(../*)=1", "name(../..)=\"line\""], ["Precondition", "cases", "default", "self::cases"], ["Precondition", "lines-cases-summary", "MultiLineLabel_Case", "self::multiline", "not(contains(@grammar, \"layoutSummary\"))"], ["<PERSON><PERSON>", "lines-cases-summary", "self::table", "not(contains(@grammar, \"layoutSummary\"))"], ["Precondition", "lines-cases", "MultiLineLabel_Case", "self::table"], ["<PERSON><PERSON>", "lines-cases", "self::multiline"], ["Precondition", "lines-equations-summary", "MultiLineLabel_Equation", "self::multiline", "not(contains(@grammar, \"layoutSummary\"))"], ["<PERSON><PERSON>", "lines-equations-summary", "self::table", "not(contains(@grammar, \"layoutSummary\"))"], ["Precondition", "lines-equations", "MultiLineLabel_Equation", "self::table"], ["<PERSON><PERSON>", "lines-equations", "self::multiline"], ["Precondition", "lines-steps-summary", "MultiLineLabel_Step", "self::multiline", "not(contains(@grammar, \"layoutSummary\"))"], ["<PERSON><PERSON>", "lines-steps-summary", "self::table", "not(contains(@grammar, \"layoutSummary\"))"], ["Precondition", "lines-steps", "MultiLineLabel_Step", "self::table"], ["<PERSON><PERSON>", "lines-steps", "self::multiline"], ["Precondition", "lines-rows-summary", "MultiLineLabel_Row", "self::multiline", "not(contains(@grammar, \"layoutSummary\"))"], ["<PERSON><PERSON>", "lines-rows-summary", "self::table", "not(contains(@grammar, \"layoutSummary\"))"], ["Precondition", "lines-rows", "MultiLineLabel_Row", "self::table"], ["<PERSON><PERSON>", "lines-rows", "self::multiline"], ["Precondition", "lines-constraints-summary", "MultiLineLabel_Constraint", "self::multiline", "not(contains(@grammar, \"layoutSummary\"))"], ["<PERSON><PERSON>", "lines-constraints-summary", "self::table", "not(contains(@grammar, \"layoutSummary\"))"], ["Precondition", "lines-constraints", "MultiLineLabel_Constraint", "self::table"], ["<PERSON><PERSON>", "lines-constraints", "self::multiline"], ["Precondition", "lines-none", "MultiLineLabel_None", "self::table", "contains(@grammar, \"layoutSummary\")"], ["<PERSON><PERSON>", "lines-none", "self::multiline", "contains(@grammar, \"layoutSummary\")"], ["<PERSON><PERSON>", "lines-none", "self::cases", "contains(@grammar, \"layoutSummary\")"], ["Precondition", "bigop", "default", "self::bigop"], ["Precondition", "limboth", "default", "self::limboth"], ["Precondition", "limlower", "default", "self::lim<PERSON>er"], ["Precondition", "limupper", "default", "self::limupper"], ["Precondition", "integral", "default", "self::integral"], ["Precondition", "integral-novar", "default", "self::integral", "name(children/*[3])=\"empty\""], ["Precondition", "overscript", "default", "self::overscore"], ["Precondition", "overscript-accent", "default", "self::overscore", "children/*[2][@role=\"overaccent\"]"], ["Precondition", "overscript-limits", "default", "self::overscore", "children/*[2][@role!=\"overaccent\"]", "name(children/*[1])=\"underscore\"", "children/*[1]/children/*[2][@role!=\"underaccent\"]"], ["Precondition", "underscript", "default", "self::underscore"], ["Precondition", "underscript-limits", "default", "self::underscore", "@role=\"underover\"", "children/*[2][@role!=\"underaccent\"]"], ["Precondition", "number", "default", "self::number"], ["Precondition", "mixed-number", "default", "self::number", "@role=\"mixed\""], ["Precondition", "number-with-chars", "default", "self::number", "@role=\"othernumber\"", "\"\" != translate(text(), \"0123456789.,\", \"\")", "not(contains(@grammar, \"protected\"))"], ["Precondition", "decimal-period", "default", "self::punctuated", "@role=\"sequence\"", "count(./content/*)=1", "./content/*[1][@role=\"fullstop\"]", "name(children/*[1])=\"number\"", "children/*[1][@role=\"integer\"]", "name(children/*[3])=\"overscore\"", "children/*[3][@role=\"integer\"]", "children/*[3]/children/*[2][@role=\"overaccent\"]", "children/*[3]/children/*[2][contains(@annotation, \"accent:bar\")]"], ["Precondition", "decimal-period-float", "default", "self::infixop", "@role=\"implicit\"", "count(./children/*)=2", "name(children/*[1])=\"number\"", "children/*[1][@role=\"float\"]", "name(children/*[2])=\"overscore\"", "children/*[2][@role=\"integer\"]", "children/*[2]/children/*[2][@role=\"overaccent\"]", "children/*[2]/children/*[2][contains(@annotation, \"accent:bar\")]"], ["Precondition", "decimal-period-singular", "default", "self::punctuated", "@role=\"sequence\"", "count(./content/*)=1", "./content/*[1][@role=\"fullstop\"]", "name(children/*[1])=\"number\"", "children/*[1][@role=\"integer\"]", "name(children/*[3])=\"overscore\"", "children/*[3][@role=\"integer\"]", "children/*[3]/children/*[2][@role=\"overaccent\"]", "children/*[3]/children/*[2][contains(@annotation, \"accent:bar\")]", "string-length(./children/*[3]/children/*[1]/text())=1"], ["Precondition", "decimal-period-singular-float", "default", "self::infixop", "@role=\"implicit\"", "count(./children/*)=2", "name(children/*[1])=\"number\"", "children/*[1][@role=\"float\"]", "name(children/*[2])=\"overscore\"", "children/*[2][@role=\"integer\"]", "children/*[2]/children/*[2][@role=\"overaccent\"]", "children/*[2]/children/*[2][contains(@annotation, \"accent:bar\")]", "string-length(./children/*[2]/children/*[1]/text())=1"], ["Precondition", "number-with-spaces", "default", "self::number", "contains(@grammar, \"spaceout\")"], ["Precondition", "decimal-point", "default", "self::punctuation", "@role=\"fullstop\"", "contains(@grammar,\"number\")"], ["Precondition", "line-segment", "default", "self::overscore", "@role=\"implicit\"", "children/*[2][@role=\"overaccent\"]", "children/*[2][contains(@annotation, \"accent:bar\")]", "name(children/*[1])=\"infixop\"", "count(./children/*[1]/children/*)=2"], ["Precondition", "conjugate", "Bar_Conjugate", "self::overscore", "children/*[2][@role=\"overaccent\"]", "children/*[2][contains(@annotation, \"accent:bar\")]"], ["Precondition", "defined-by", "default", "self::overscore", "@role=\"equality\"", "@embellished=\"relation\"", "name(children/*[2])=\"text\"", "children/*[2][text()]=\"def\""], ["Precondition", "adorned-sign", "default", "self::overscore", "@embellished", "name(children/*[1])=\"operator\" or name(children/*[1])=\"relation\""], ["Precondition", "factorial", "default", "self::punctuation", "text()=\"!\"", "name(preceding-sibling::*[1])!=\"text\""], ["Precondition", "tensor-base", "default", "self::tensor"], ["Precondition", "left-super", "default", "self::*[@role=\"leftsuper\"]", "not(contains(@grammar,\"combinatorics\"))"], ["Precondition", "left-super-list", "default", "self::punctuated[@role=\"leftsuper\"]", "not(contains(@grammar,\"combinatorics\"))"], ["Precondition", "left-sub", "default", "self::*[@role=\"leftsub\"]", "not(contains(@grammar,\"combinatorics\"))"], ["Precondition", "left-sub-list", "default", "self::punctuated[@role=\"leftsub\"]", "not(contains(@grammar,\"combinatorics\"))"], ["Precondition", "right-super", "default", "self::*[@role=\"rightsuper\"]", "not(contains(@grammar,\"combinatorics\"))"], ["Precondition", "right-super-list", "default", "self::punctuated[@role=\"rightsuper\"]", "not(contains(@grammar,\"combinatorics\"))"], ["Precondition", "right-sub", "default", "self::*[@role=\"rightsub\"]", "not(contains(@grammar,\"combinatorics\"))"], ["Precondition", "right-sub-list", "default", "self::punctuated[@role=\"rightsub\"]", "not(contains(@grammar,\"combinatorics\"))"], ["Precondition", "empty-index", "default", "self::empty[@role=\"rightsub\" or @role=\"rightsuper\" or @role=\"leftsub\" or @role=\"leftsuper\"]"], ["Precondition", "combinatorics", "default", "self::tensor", "name(children/*[3])=\"empty\"", "name(children/*[5])=\"empty\"", "children/*[1][text()=\"P\" or text()=\"C\"]"], ["Precondition", "choose", "CombinationPermutation_ChoosePermute", "self::tensor", "name(children/*[3])=\"empty\"", "name(children/*[5])=\"empty\"", "children/*[1][text()=\"C\"]"], ["Precondition", "permute", "CombinationPermutation_ChoosePermute", "self::tensor", "name(children/*[3])=\"empty\"", "name(children/*[5])=\"empty\"", "children/*[1][text()=\"P\"]"], ["Precondition", "unit-singular", "default", "self::identifier[@role=\"unit\"]"], ["Precondition", "unit-plural", "default", "self::identifier[@role=\"unit\"]", "not(contains(@grammar, \"singular\"))"], ["Precondition", "unit-square", "default", "self::superscript[@role=\"unit\"]", "children/*[2][text()=2]", "name(children/*[1])=\"identifier\""], ["Precondition", "unit-cubic", "default", "self::superscript[@role=\"unit\"]", "children/*[2][text()=3]", "name(children/*[1])=\"identifier\""], ["Precondition", "unit-reciprocal", "default", "self::superscript[@role=\"unit\"]", "name(children/*[1])=\"identifier\"", "name(children/*[2])=\"prefixop\"", "children/*[2][@role=\"negative\"]", "children/*[2]/children/*[1][text()=1]", "count(preceding-sibling::*)=0 or preceding-sibling::*[@role!=\"unit\"]"], ["Precondition", "unit-reciprocal-singular", "default", "self::superscript[@role=\"unit\"]", "name(children/*[1])=\"identifier\"", "name(children/*[2])=\"prefixop\"", "children/*[2][@role=\"negative\"]", "children/*[2]/children/*[1][text()=1]", "preceding-sibling::*[@role=\"unit\"]"], ["Precondition", "unit-combine", "default", "self::infixop[@role=\"unit\"]"], ["Precondition", "unit-combine-singular", "default", "self::infixop[@role=\"unit\"]", "name(children/*[1])=\"number\"", "children/*[1][text()=1]"], ["Precondition", "unit-divide", "default", "self::fraction[@role=\"unit\"]"], ["Precondition", "currency", "default", "self::infixop", "contains(@annotation, \"clearspeak:unit\")", "children/*[1][@role=\"unit\"]", "children/*[1][@category=\"unit:currency\"]"], ["Precondition", "currency-position", "Currency_Position", "self::infixop", "contains(@annotation, \"clearspeak:unit\")"], ["Specialized", "currency-position", "Currency_Position", "Currency_Prefix"], ["Precondition", "currency-prefix", "Currency_Prefix", "self::infixop", "contains(@annotation, \"clearspeak:unit\")", "children/*[last()][@role=\"unit\"]", "children/*[last()][@category=\"unit:currency\"]"], ["Precondition", "enclose", "default", "self::enclose"], ["Precondition", "enclose-end", "Enclosed_EndEnclose", "self::enclose"], ["Precondition", "overbar", "default", "self::enclose", "@role=\"top\""], ["Precondition", "underbar", "default", "self::enclose", "@role=\"bottom\""], ["Precondition", "leftbar", "default", "self::enclose", "@role=\"left\""], ["Precondition", "rightbar", "default", "self::enclose", "@role=\"right\""], ["Precondition", "crossout", "default", "self::enclose", "@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\""], ["Precondition", "crossout-end", "Enclosed_EndEnclose", "self::enclose", "@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\""], ["Precondition", "cancel-over", "default", "self::overscore", "@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\""], ["<PERSON><PERSON>", "cancel-over", "self::underscore", "@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\""], ["Precondition", "cancel-under", "default", "self::underscore", "name(children/*[2])=\"enclose\"", "children/*[2][@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\"]"], ["<PERSON><PERSON>", "cancel-under", "self::overscore", "name(children/*[2])=\"enclose\"", "children/*[2][@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\"]"], ["Precondition", "cancel-over-end", "Enclosed_EndEnclose", "self::overscore", "@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\""], ["<PERSON><PERSON>", "cancel-over-end", "self::underscore", "@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\""], ["Precondition", "cancel-under-end", "Enclosed_EndEnclose", "self::underscore", "name(children/*[2])=\"enclose\"", "children/*[2][@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\"]"], ["<PERSON><PERSON>", "cancel-under-end", "self::overscore", "name(children/*[2])=\"enclose\"", "children/*[2][@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\"]"]], "annotators": ["simple", "unit"]}, "base/rules/clearspeak_base_actions.min": {"domain": "clearspeak", "locale": "base", "modality": "speech", "kind": "actions", "rules": [["Action", "punctuation-lr", "[p] (pause:short); [n] text() (pause:short)"], ["Action", "punctuation", "[n] text()"], ["Action", "punctuation-l", "[p] (pause:short); [n] text()"], ["Action", "punctuation-r", "[n] text() (pause:short)"], ["Action", "prime", "[n] children/*[1]; [n] children/*[2]"], ["Action", "degrees", "[m] children/* (grammar:degree)"], ["Action", "feet", "[n] children/*[1]; [t] \"ft\" (grammar:annotation=\"unit\":translate:plural)"], ["Action", "foot", "[n] children/*[1]; [t] \"ft\" (grammar:annotation=\"unit\":translate)"], ["Action", "inches", "[n] children/*[1]; [t] \"in\" (grammar:annotation=\"unit\":translate:plural)"], ["Action", "inch", "[n] children/*[1]; [t] \"in\" (grammar:annotation=\"unit\":translate)"], ["Action", "minutes", "[n] children/*[1]; [t] children/*[2]/text() (grammar:annotation=\"unit\":translate:plural)"], ["Action", "minute", "[n] children/*[1]; [t] children/*[2]/text() (grammar:annotation=\"unit\":translate)"], ["Action", "seconds", "[n] children/*[1]; [t] children/*[2]/text() (grammar:annotation=\"unit\":translate:plural)"], ["Action", "second", "[n] children/*[1]; [t] children/*[2]/text() (grammar:annotation=\"unit\":translate)"], ["Action", "degrees-angle", "[t] text() (grammar:annotation=\"unit\":translate:plural, pause:short)"], ["Action", "degree-angle", "[t] text() (grammar:annotation=\"unit\":translate, pause:short)"], ["Action", "minutes-angle", "[n] children/*[1]; [t] children/*[2]/text() (grammar:annotation=\"unit\":translate:plural)"], ["Action", "minute-angle", "[n] children/*[1]; [t] children/*[2]/text() (grammar:annotation=\"unit\":translate)"], ["Action", "seconds-angle", "[n] children/*[1]; [t] children/*[2]/text() (grammar:annotation=\"unit\":translate:plural)"], ["Action", "second-angle", "[n] children/*[1]; [t] children/*[2]/text() (grammar:annotation=\"unit\":translate)"], ["Action", "feet-length", "[n] children/*[1]; [t] \"ft\" (grammar:annotation=\"unit\":translate:plural, pause:short)"], ["Action", "foot-length", "[n] children/*[1]; [t] \"ft\" (grammar:annotation=\"unit\":translate, pause:short)"], ["Action", "inches-length", "[n] children/*[1]; ; [t] \"in\" (grammar:annotation=\"unit\":translate:plural, pause:short)"], ["Action", "inch-length", "[n] children/*[1]; ; [t] \"in\" (grammar:annotation=\"unit\":translate, pause:short)"], ["Action", "punctuated", "[m] children/*"], ["Action", "function", "[n] text()"], ["Action", "binary-operation", "[n] . (grammar:impliedTimes, pause:short)"], ["Action", "function-ln-natlog", "[n] . (grammar:NatLog)"], ["Action", "function-ln-natlog-pause", "[n] . (grammar:<PERSON><PERSON><PERSON>, pause:short)"], ["Action", "function-no-inverse", "[n] . (grammar:functions_none)"], ["Action", "paren-simple", "[n] children/*[1]"], ["Action", "paren-simple-exp", "[n] children/*[1]"], ["Action", "paren-simple-nested-func", "[n] children/*[1]"], ["Action", "paren-simple-nested-func-no-bracket", "[n] children/*[1]"], ["Action", "fences-open-close", "[p] (pause:short); [n] content/*[1] (grammar:spokenFence, pause:short); [n] children/*[1] (pause:short); [n] content/*[2] (grammar:spokenFence, pause:short)"], ["Action", "paren-simple-nested-func-default", "[p] (pause:short); [n] content/*[1] (pause:short); [n] children/*[1] (pause:short); [n] content/*[2] (pause:short)"], ["Action", "paren-simple-nested-func-none", "[p] (pause:short); [n] content/*[1] (grammar:spokenFence, pause:short); [n] children/*[1] (pause:short); [n] content/*[2] (grammar:spokenFence, pause:short)"], ["Action", "fence-silent", "[p] (pause:short); [n] children/*[1] (pause:short)"], ["Action", "fences-open-close-none", "[p] (pause:short); [n] content/*[1] (grammar:spokenFence, pause:short); [n] children/*[1] (pause:short); [n] content/*[2] (grammar:spokenFence, pause:short)"], ["Action", "fence-nesting", "[n] text() (grammar:insertNesting=CSFnestingDepth)"], ["Action", "fence-no-nesting", "[n] text()"], ["Action", "interval-open-inf-lr", ""], ["Action", "paren-nested-embellished-funcs", "[p] (pause:short); [n] content/*[1] (pause:short); [n] children/*[1] (pause:short); [n] content/*[2] (pause:short)"], ["Action", "set-collection-<PERSON><PERSON><PERSON>", "[n] children/*[1]"], ["Action", "prefix", "[m] content/* (grammar:?prefix); [n] children/*[1]"], ["Action", "postfix", "[n] children/*[1]; [m] content/* (grammar:postfix)"], ["Action", "binary-operation-default", "[m] children/* (sepFunc:CTFcontentIterator)"], ["Action", "binary-operation-moreimpliedtimes", "[m] children/* (sepFunc:CTFcontentIterator)"], ["Action", "binary-operation-pause", "[p] (pause:short); [m] children/* (sepFunc:CTFcontentIterator)"], ["Action", "binary-operation-pause-r", "[m] children/* (sepFunc:CTFcontentIterator, pause:short)"], ["Action", "binary-operation-pause-lr", "[p] (pause:short); [m] children/* (sepFunc:CTFcontentIterator, pause:short)"], ["Action", "implicit-times", "[p] (pause:short)"], ["Action", "implicit-times-default", ""], ["Action", "implicit-times-simple", "[n] text()"], ["Action", "implicit-times-moreimpliedtimes", "[n] text()"], ["Action", "implicit-times-none", ""], ["Action", "binary-operation-simple", "[m] children/* (rate:\"0.5\", pause:short)"], ["Action", "simple-in-fraction", "[n] . (rate:\"0.5\", grammar:inFrac)"], ["Action", "relseq", "[m] children/* (sepFunc:CTFcontentIterator)"], ["Action", "mult<PERSON>", "[m] children/* (sepFunc:CTFcontentIterator)"], ["Action", "natural-numbers-super", "[t] \"n\" (join:\"-\",grammar:translate); [n] children/*[2] (grammar:numbers2alpha)"], ["Action", "integers-super", "[t] \"z\" (join:\"-\",grammar:translate); [n] children/*[2] (grammar:numbers2alpha)"], ["Action", "rational-numbers-super", "[t] \"q\" (join:\"-\",grammar:translate); [n] children/*[2] (grammar:numbers2alpha)"], ["Action", "real-numbers-super", "[t] \"r\" (join:\"-\",grammar:translate); [n] children/*[2] (grammar:numbers2alpha)"], ["Action", "complex-numbers-super", "[t] \"c\" (join:\"-\",grammar:translate); [n] children/*[2] (grammar:numbers2alpha)"], ["Action", "matrix-row-simple", "[m] children/* (sepFunc:CTFpauseSeparator, separator:\"short\")"], ["Action", "matrix-row-simple-silent<PERSON>lnum", "[m] children/* (sepFunc:CTFpauseSeparator, separator:\"short\")"], ["Action", "line-simple", "[n] children/*[1]"], ["Action", "matrix-cell", "[n] children/*[1]"], ["Action", "lines-summary-none", "[n] . (grammar:?layoutSummary)"], ["Action", "cases-summary-none", "[n] . (grammar:?layoutSummary)"], ["Action", "line", "[n] children/*[1]"], ["Action", "row-medium", "[m] children/* (sepFunc:CTFpauseSeparator, separator:\"medium\")"], ["Action", "row-long", "[m] children/* (sepFunc:CTFpauseSeparator, separator:\"long\")"], ["Action", "row-short", "[m] children/* (sepFunc:CTFpauseSeparator, separator:\"short\")"], ["Action", "lines-none", "[p] (pause:short); [m] children/* (sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "overscript-accent", "[n] children/*[1]; [n] children/*[2]"], ["Action", "number", "[n] text()"], ["Action", "number-with-spaces", "[m] CQFspaceoutNumber (grammar:!spaceout:number)"], ["Action", "tensor-base", "[n] children/*[2]; [n] children/*[3]; [n] children/*[1]; [n] children/*[4]; [n] children/*[5]"], ["Action", "empty-index", "[p] (pause:medium)"], ["Action", "combinatorics", "[n] children/*[2] (grammar:combinatorics); [n] children/*[1]; [n] children/*[4] (grammar:combinatorics)"], ["Action", "unit-singular", "[t] text() (grammar:annotation=\"unit\":translate)"], ["Action", "unit-plural", "[t] text() (grammar:annotation=\"unit\":translate:plural)"], ["Action", "unit-combine", "[m] children/*"], ["Action", "unit-combine-singular", "[n] children/*[1]; [n] children/*[2] (grammar:singular); [m] children/*[position()>2]"], ["Action", "currency", "[m] children/*[position()>1]; [n] children/*[1]"], ["Action", "currency-position", "[m] children/*"], ["Action", "currency-prefix", "[n] children/*[last()]; [m] children/*[position()<last()]"]]}, "base/rules/clearspeak_base_romance.min": {"locale": "romance", "domain": "clearspeak", "modality": "speech", "kind": "abstract", "inherits": "base", "rules": [["Precondition", "function-prefix-reciprocal", "Trig_Reciprocal", "self::appl", "@role=\"prefix function\"", "name(children/*[1])=\"superscript\"", "name(children/*[1]/children/*[2])=\"prefixop\"", "children/*[1]/children/*[2][@role=\"negative\"]", "children/*[1]/children/*[2]/children/*[1][text()=\"1\"]", "not(contains(@grammar, \"functions_none\"))"], ["Precondition", "function-prefix-reciprocal-simple", "Trig_Reciprocal", "self::appl", "@role=\"prefix function\"", "name(children/*[1])=\"superscript\"", "name(children/*[1]/children/*[2])=\"prefixop\"", "contains(children/*[2]/@annotation, \"clearspeak:simple\")", "children/*[1]/children/*[2][@role=\"negative\"]", "children/*[1]/children/*[2]/children/*[1][text()=\"1\"]", "not(contains(@grammar, \"functions_none\"))"], ["Precondition", "function-reciprocal", "Functions_Reciprocal", "self::superscript", "@role=\"prefix function\" or @role=\"simple function\"", "name(children/*[2])=\"prefixop\"", "children/*[2][@role=\"negative\"]", "children/*[2]/children/*[1][text()=\"1\"]", "not(contains(@grammar, \"functions_none\"))"]]}, "base/rules/mathspeak_base.min": {"domain": "mathspeak", "locale": "base", "modality": "speech", "kind": "abstract", "rules": [["Rule", "direct-speech", "default", "[t] @ext-speech", "self::*[@ext-speech]", "priority=Infinity"], ["Rule", "stree", "default", "[n] ./*[1]", "self::stree", "CQFresetNesting"], ["Rule", "unknown", "default", "[n] text()", "self::unknown"], ["Rule", "protected", "default", "[n] text() (grammar:ignoreCaps)", "self::number", "contains(@grammar, \"protected\")"], ["Rule", "omit-empty", "default", "[p] (pause:100)", "self::empty"], ["Rule", "omit-font", "default", "[n] . (grammar:ignoreFont=@font)", "self::identifier[@font=\"italic\"]", "string-length(text())=1", "not(contains(@grammar, \"ignoreFont\"))"], ["Precondition", "collapsed", "default", "self::*[@alternative]", "not(contains(@grammar, \"collapsed\"))"], ["Specialized", "collapsed", "default", "brief"], ["Specialized", "collapsed", "brief", "sbrief"], ["Precondition", "blank-cell-empty", "default", "self::empty", "count(../*)=1", "name(../..)=\"cell\""], ["Precondition", "blank-line-empty", "default", "self::empty", "count(../*)=1", "name(../..)=\"line\""], ["Precondition", "font", "default", "self::*[@font]", "not(contains(@grammar, \"ignoreFont\"))", "@font!=\"normal\""], ["<PERSON><PERSON>", "font", "self::identifier", "string-length(text())=1", "@font=\"normal\"", "not(contains(@grammar, \"ignoreFont\"))", "\"\"=translate(text(), \"abcdefghijklmnopqrstuvwxyzαβγδεζηθικλμνξοπρςστυφχψωABCDEFGHIJKLMNOPQRSTUVWXYZΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΣΤΥΦΧΨΩ\", \"\")", "@role!=\"unit\""], ["<PERSON><PERSON>", "font", "self::identifier", "string-length(text())=1", "@font=\"normal\"", "not(contains(@grammar, \"ignoreFont\"))", "@role!=\"unit\""], ["Precondition", "number", "default", "self::number"], ["Precondition", "mixed-number", "default", "self::number", "@role=\"mixed\""], ["Precondition", "number-with-chars", "default", "self::number[@role=\"othernumber\"]", "\"\" != translate(text(), \"0123456789.,\", \"\")", "not(contains(@grammar, \"protected\"))"], ["Precondition", "number-with-chars-brief", "brief", "self::number[@role=\"othernumber\"]", "\"\" != translate(text(), \"0123456789.,\", \"\")", "not(contains(@grammar, \"protected\"))"], ["Specialized", "number-with-chars-brief", "brief", "sbrief"], ["Precondition", "number-as-upper-word", "default", "self::number[@role=\"othernumber\"]", "string-length(text())>1", "text()=translate(text(), \"abcdefghijklmnopqrstuvwxyzαβγδεζηθικλμνξοπρςστυφχψω\", \"ABCDEFGHIJKLMNOPQRSTUVWXYZΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΣΤΥΦΧΨΩ\")", "\"\"=translate(text(), \"ABCDEFGHIJKLMNOPQRSTUVWXYZΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΣΤΥΦΧΨΩ\",\"\")"], ["Specialized", "number-as-upper-word", "default", "brief"], ["Specialized", "number-as-upper-word", "default", "sbrief"], ["Precondition", "number-baseline", "default", "self::number", "not(contains(@grammar, \"ignoreFont\"))", "preceding-sibling::identifier", "not(contains(@grammar, \"baseline\"))", "preceding-sibling::*[1][contains(@role,\"letter\")]", "parent::*/parent::infixop[@role=\"implicit\"]"], ["Precondition", "number-baseline-brief", "brief", "self::number", "not(contains(@grammar, \"ignoreFont\"))", "preceding-sibling::identifier", "not(contains(@grammar, \"baseline\"))", "preceding-sibling::*[1][contains(@role,\"letter\")]", "parent::*/parent::infixop[@role=\"implicit\"]"], ["Specialized", "number-baseline-brief", "brief", "sbrief"], ["Precondition", "number-baseline-font", "default", "self::number[@font!=\"normal\"]", "not(contains(@grammar, \"ignoreFont\"))", "preceding-sibling::identifier", "preceding-sibling::*[contains(@role,\"letter\")]", "parent::*/parent::infixop[@role=\"implicit\"]"], ["Precondition", "number-baseline-font-brief", "brief", "self::number[@font!=\"normal\"]", "not(contains(@grammar, \"ignoreFont\"))", "preceding-sibling::identifier", "preceding-sibling::*[contains(@role,\"letter\")]", "parent::*/parent::infixop[@role=\"implicit\"]"], ["Specialized", "number-baseline-font-brief", "brief", "sbrief"], ["Precondition", "identifier-spacing", "default", "self::identifier", "string-length(text())>1", "@role!=\"unit\"", "not(@font) or @font=\"normal\" or contains(@grammar, \"ignoreFont\")", "text()!=translate(text(), \"abcdefghijklmnopqrstuvwxyzαβγδεζηθικλμνξοπρςστυφχψωABCDEFGHIJKLMNOPQRSTUVWXYZΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΣΤΥΦΧΨΩ\", \"\")"], ["Precondition", "identifier", "default", "self::identifier"], ["Precondition", "negative-number", "default", "self::prefixop", "@role=\"negative\"", "children/identifier"], ["<PERSON><PERSON>", "negative-number", "self::prefixop", "@role=\"negative\"", "children/number"], ["<PERSON><PERSON>", "negative-number", "self::prefixop", "@role=\"negative\"", "children/fraction[@role=\"vulgar\"]"], ["Precondition", "negative", "default", "self::prefixop", "@role=\"negative\""], ["Precondition", "prefix", "default", "self::prefixop"], ["Precondition", "postfix", "default", "self::postfixop"], ["Precondition", "binary-operation", "default", "self::infixop"], ["Precondition", "division", "default", "self::infixop", "@role=\"division\"", "count(children/*)=2"], ["Precondition", "implicit", "default", "self::infixop", "@role=\"implicit\""], ["<PERSON><PERSON>", "implicit", "self::infixop", "@role=\"leftsuper\" or @role=\"leftsub\" or @role=\"rightsuper\" or @role=\"rightsub\""], ["Precondition", "subtraction", "default", "self::infixop", "@role=\"subtraction\""], ["Precondition", "function-unknown", "default", "self::appl"], ["Precondition", "function-prefix", "default", "self::appl", "children/*[1][@role=\"prefix function\"]"], ["Precondition", "fences-open-close", "default", "self::fenced", "@role=\"leftright\""], ["<PERSON><PERSON>", "fences-open-close", "self::fenced"], ["Precondition", "fences-neutral", "default", "self::fenced", "@role=\"neutral\""], ["Precondition", "fences-neutral-sbrief", "sbrief", "self::fenced", "@role=\"neutral\""], ["Precondition", "fences-metric", "default", "self::fenced", "@role=\"metric\""], ["Precondition", "fences-metric-sbrief", "sbrief", "self::fenced", "@role=\"metric\""], ["Precondition", "empty-set", "default", "self::fenced[@role=\"set empty\"]", "not(name(../..)=\"appl\")"], ["Specialized", "empty-set", "default", "sbrief"], ["Precondition", "fences-set", "default", "self::fenced", "contains(@role,\"set \")", "not(name(../..)=\"appl\")"], ["Precondition", "fences-set-sbrief", "sbrief", "self::fenced", "contains(@role,\"set \")", "not(name(../..)=\"appl\")"], ["Precondition", "text", "default", "self::text"], ["Precondition", "factorial", "default", "self::punctuation", "text()=\"!\"", "name(preceding-sibling::*[1])!=\"text\""], ["Precondition", "minus", "default", "self::operator", "text()=\"-\""], ["Precondition", "fraction", "default", "self::fraction"], ["Precondition", "fraction-brief", "brief", "self::fraction"], ["Precondition", "fraction-sbrief", "sbrief", "self::fraction"], ["Precondition", "vulgar-fraction", "default", "self::fraction", "@role=\"vulgar\"", "CQFvulgarFractionSmall"], ["Specialized", "vulgar-fraction", "default", "brief"], ["Specialized", "vulgar-fraction", "default", "sbrief"], ["Precondition", "continued-fraction-outer", "default", "self::fraction", "not(ancestor::fraction)", "children/*[2]/descendant-or-self::*[@role=\"ellipsis\" and not(following-sibling::*)]"], ["Precondition", "continued-fraction-outer-brief", "brief", "self::fraction", "not(ancestor::fraction)", "children/*[2]/descendant-or-self::*[@role=\"ellipsis\" and not(following-sibling::*)]"], ["Specialized", "continued-fraction-outer-brief", "brief", "sbrief"], ["Precondition", "continued-fraction-inner", "default", "self::fraction", "ancestor::fraction", "children/*[2]/descendant-or-self::*[@role=\"ellipsis\" and not(following-sibling::*)]"], ["Precondition", "continued-fraction-inner-brief", "brief", "self::fraction", "ancestor::fraction", "children/*[2]/descendant-or-self::*[@role=\"ellipsis\" and not(following-sibling::*)]"], ["Precondition", "continued-fraction-inner-sbrief", "sbrief", "self::fraction", "ancestor::fraction", "children/*[2]/descendant-or-self::*[@role=\"ellipsis\" and not(following-sibling::*)]"], ["Precondition", "sqrt", "default", "self::sqrt"], ["Precondition", "sqrt-brief", "brief", "self::sqrt"], ["Precondition", "sqrt-sbrief", "sbrief", "self::sqrt"], ["Precondition", "root-small", "default", "self::root", "CQFisSmallRoot"], ["Precondition", "root-small-brief", "brief", "self::root", "CQFisSmallRoot"], ["Precondition", "root-small-sbrief", "sbrief", "self::root", "CQFisSmallRoot"], ["Precondition", "root", "default", "self::root"], ["Precondition", "root-brief", "brief", "self::root"], ["Precondition", "root-sbrief", "sbrief", "self::root"], ["Precondition", "limboth", "default", "self::limboth", "name(../..)=\"underscore\" or name(../..)=\"overscore\"", "following-sibling::*[@role!=\"underaccent\" and @role!=\"overaccent\"]"], ["Precondition", "limlower", "default", "self::lim<PERSON>er", "name(../..)=\"underscore\" or name(../..)=\"overscore\"", "following-sibling::*[@role!=\"underaccent\" and @role!=\"overaccent\"]"], ["Precondition", "limupper", "default", "self::limupper", "name(../..)=\"underscore\" or name(../..)=\"overscore\"", "following-sibling::*[@role!=\"underaccent\" and @role!=\"overaccent\"]"], ["<PERSON><PERSON>", "limlower", "self::underscore", "@role=\"limit function\"", "name(../..)=\"underscore\" or name(../..)=\"overscore\"", "following-sibling::*[@role!=\"underaccent\" and @role!=\"overaccent\"]"], ["<PERSON><PERSON>", "limlower", "self::underscore", "children/*[2][@role!=\"underaccent\"]", "name(../..)=\"underscore\" or name(../..)=\"overscore\"", "following-sibling::*[@role!=\"underaccent\" and @role!=\"overaccent\"]"], ["<PERSON><PERSON>", "limupper", "self::overscore", "children/*[2][@role!=\"overaccent\"]", "name(../..)=\"underscore\" or name(../..)=\"overscore\"", "following-sibling::*[@role!=\"underaccent\" and @role!=\"overaccent\"]"], ["Precondition", "limboth-end", "default", "self::limboth"], ["Precondition", "limlower-end", "default", "self::lim<PERSON>er"], ["Precondition", "limupper-end", "default", "self::limupper"], ["<PERSON><PERSON>", "limlower-end", "self::underscore", "@role=\"limit function\"", "name(../..)=\"underscore\" or name(../..)=\"overscore\"", "following-sibling::*[@role!=\"underaccent\" and @role!=\"overaccent\"]"], ["<PERSON><PERSON>", "limlower-end", "self::underscore"], ["<PERSON><PERSON>", "limupper-end", "self::overscore"], ["Precondition", "integral-index", "default", "self::integral"], ["Precondition", "integral", "default", "self::limboth", "@role=\"integral\""], ["Precondition", "integral-brief", "brief", "self::limboth", "@role=\"integral\""], ["Specialized", "integral-brief", "brief", "sbrief"], ["Precondition", "bigop", "default", "self::bigop"], ["Precondition", "relseq", "default", "self::relseq"], ["Precondition", "equality", "default", "self::relseq", "@role=\"equality\"", "count(./children/*)=2"], ["Precondition", "multi-equality", "default", "self::relseq", "@role=\"equality\"", "count(./children/*)>2"], ["Precondition", "mult<PERSON>", "default", "self::multirel"], ["Precondition", "subscript", "default", "self::subscript"], ["Precondition", "subscript-brief", "brief", "self::subscript"], ["Specialized", "subscript-brief", "brief", "sbrief"], ["Precondition", "subscript-simple", "default", "self::subscript", "name(./children/*[1])=\"identifier\"", "name(./children/*[2])=\"number\"", "./children/*[2][@role!=\"mixed\"]", "./children/*[2][@role!=\"othernumber\"]"], ["Specialized", "subscript-simple", "default", "brief"], ["Specialized", "subscript-simple", "default", "sbrief"], ["Precondition", "subscript-baseline", "default", "self::subscript", "following-sibling::*", "not(name(following-sibling::subscript/children/*[1])=\"empty\" or (name(following-sibling::infixop[@role=\"implicit\"]/children/*[1])=\"subscript\" and name(following-sibling::*/children/*[1]/children/*[1])=\"empty\")) and @role!=\"subsup\"", "not(following-sibling::*[@role=\"rightsuper\" or @role=\"rightsub\" or @role=\"leftsub\" or @role=\"leftsub\"])"], ["Precondition", "subscript-baseline-brief", "brief", "self::subscript", "following-sibling::*", "not(name(following-sibling::subscript/children/*[1])=\"empty\" or (name(following-sibling::infixop[@role=\"implicit\"]/children/*[1])=\"subscript\" and name(following-sibling::*/children/*[1]/children/*[1])=\"empty\")) and @role!=\"subsup\"", "not(following-sibling::*[@role=\"rightsuper\" or @role=\"rightsub\" or @role=\"leftsub\" or @role=\"leftsub\"])"], ["Specialized", "subscript-baseline-brief", "brief", "sbrief"], ["<PERSON><PERSON>", "subscript-baseline", "self::subscript", "not(following-sibling::*)", "ancestor::fenced|ancestor::root|ancestor::sqrt|ancestor::punctuated|ancestor::fraction", "not(ancestor::punctuated[@role=\"leftsuper\" or @role=\"rightsub\" or @role=\"rightsuper\" or @role=\"rightsub\"])"], ["<PERSON><PERSON>", "subscript-baseline", "self::subscript", "not(following-sibling::*)", "ancestor::relse<PERSON>|ancestor::multirel", "CGFbaselineConstraint"], ["<PERSON><PERSON>", "subscript-baseline", "self::subscript", "not(following-sibling::*)", "@embellished"], ["<PERSON><PERSON>", "subscript-baseline-brief", "self::subscript", "not(following-sibling::*)", "ancestor::fenced|ancestor::root|ancestor::sqrt|ancestor::punctuated|ancestor::fraction", "not(ancestor::punctuated[@role=\"leftsuper\" or @role=\"rightsub\" or @role=\"rightsuper\" or @role=\"rightsub\"])"], ["<PERSON><PERSON>", "subscript-baseline-brief", "self::subscript", "not(following-sibling::*)", "ancestor::relse<PERSON>|ancestor::multirel", "CGFbaselineConstraint"], ["<PERSON><PERSON>", "subscript-baseline-brief", "self::subscript", "not(following-sibling::*)", "@embellished"], ["Precondition", "subscript-empty-sup", "default", "self::subscript", "name(children/*[2])=\"infixop\"", "name(children/*[2][@role=\"implicit\"]/children/*[1])=\"superscript\"", "name(children/*[2]/children/*[1]/children/*[1])=\"empty\""], ["Specialized", "subscript-empty-sup", "default", "brief"], ["Specialized", "subscript-empty-sup", "brief", "sbrief"], ["<PERSON><PERSON>", "subscript-empty-sup", "self::subscript", "name(children/*[2])=\"superscript\"", "name(children/*[2]/children/*[1])=\"empty\""], ["Precondition", "superscript", "default", "self::superscript"], ["Precondition", "superscript-brief", "brief", "self::superscript"], ["Specialized", "superscript-brief", "brief", "sbrief"], ["Precondition", "superscript-baseline", "default", "self::superscript", "following-sibling::*", "not(name(following-sibling::superscript/children/*[1])=\"empty\" or (name(following-sibling::infixop[@role=\"implicit\"]/children/*[1])=\"superscript\" and name(following-sibling::*/children/*[1]/children/*[1])=\"empty\")) and not(following-sibling::*[@role=\"rightsuper\" or @role=\"rightsub\" or @role=\"leftsub\" or @role=\"leftsub\"])"], ["Precondition", "superscript-baseline-brief", "brief", "self::superscript", "following-sibling::*", "not(name(following-sibling::superscript/children/*[1])=\"empty\" or (name(following-sibling::infixop[@role=\"implicit\"]/children/*[1])=\"superscript\" and name(following-sibling::*/children/*[1]/children/*[1])=\"empty\")) and not(following-sibling::*[@role=\"rightsuper\" or @role=\"rightsub\" or @role=\"leftsub\" or @role=\"leftsub\"])"], ["Specialized", "superscript-baseline-brief", "brief", "sbrief"], ["<PERSON><PERSON>", "superscript-baseline", "self::superscript", "not(following-sibling::*)", "ancestor::punctuated", "ancestor::*/following-sibling::* and not(ancestor::punctuated[@role=\"leftsuper\" or @role=\"rightsub\" or @role=\"rightsuper\" or @role=\"rightsub\"])"], ["<PERSON><PERSON>", "superscript-baseline", "self::superscript", "not(following-sibling::*)", "ancestor::fraction|ancestor::fenced|ancestor::root|ancestor::sqrt"], ["<PERSON><PERSON>", "superscript-baseline", "self::superscript", "not(following-sibling::*)", "ancestor::relse<PERSON>|ancestor::multirel", "not(@embellished)", "CGFbaselineConstraint"], ["<PERSON><PERSON>", "superscript-baseline", "self::superscript", "not(following-sibling::*)", "@embellished", "not(children/*[2][@role=\"prime\"])"], ["<PERSON><PERSON>", "superscript-baseline-brief", "self::superscript", "not(following-sibling::*)", "ancestor::punctuated", "ancestor::*/following-sibling::* and not(ancestor::punctuated[@role=\"leftsuper\" or @role=\"rightsub\" or @role=\"rightsuper\" or @role=\"rightsub\"])"], ["<PERSON><PERSON>", "superscript-baseline-brief", "self::superscript", "not(following-sibling::*)", "ancestor::fraction|ancestor::fenced|ancestor::root|ancestor::sqrt"], ["<PERSON><PERSON>", "superscript-baseline-brief", "self::superscript", "not(following-sibling::*)", "ancestor::relse<PERSON>|ancestor::multirel", "not(@embellished)", "CGFbaselineConstraint"], ["<PERSON><PERSON>", "superscript-baseline-brief", "self::superscript", "not(following-sibling::*)", "@embellished", "not(children/*[2][@role=\"prime\"])"], ["Precondition", "superscript-empty-sub", "default", "self::superscript", "name(children/*[2])=\"infixop\"", "name(children/*[2][@role=\"implicit\"]/children/*[1])=\"subscript\"", "name(children/*[2]/children/*[1]/children/*[1])=\"empty\""], ["Specialized", "superscript-empty-sub", "default", "brief"], ["Specialized", "superscript-empty-sub", "brief", "sbrief"], ["<PERSON><PERSON>", "superscript-empty-sub", "self::superscript", "name(children/*[2])=\"subscript\"", "name(children/*[2]/children/*[1])=\"empty\""], ["Precondition", "square", "default", "self::superscript", "children/*[2]", "children/*[2][text()=2]", "name(children/*[1])!=\"text\" or not(name(children/*[1])=\"text\" and (name(../../../punctuated[@role=\"text\"]/..)=\"stree\" or name(..)=\"stree\"))", "name(children/*[1])!=\"subscript\" or (name(children/*[1])=\"subscript\" and name(children/*[1]/children/*[1])=\"identifier\" and name(children/*[1]/children/*[2])=\"number\" and children/*[1]/children/*[2][@role!=\"mixed\"] and children/*[1]/children/*[2][@role!=\"othernumber\"])", "not(@embellished)"], ["Specialized", "square", "default", "brief"], ["Specialized", "square", "default", "sbrief"], ["<PERSON><PERSON>", "square", "self::superscript", "children/*[2]", "children/*[2][text()=2]", "@embellished", "children/*[1][@role=\"prefix operator\"]"], ["Precondition", "cube", "default", "self::superscript", "children/*[2]", "children/*[2][text()=3]", "name(children/*[1])!=\"text\" or not(name(children/*[1])=\"text\" and (name(../../../punctuated[@role=\"text\"]/..)=\"stree\" or name(..)=\"stree\"))", "name(children/*[1])!=\"subscript\" or (name(children/*[1])=\"subscript\" and name(children/*[1]/children/*[1])=\"identifier\" and name(children/*[1]/children/*[2])=\"number\" and children/*[1]/children/*[2][@role!=\"mixed\"] and children/*[1]/children/*[2][@role!=\"othernumber\"])", "not(@embellished)"], ["Specialized", "cube", "default", "brief"], ["Specialized", "cube", "default", "sbrief"], ["<PERSON><PERSON>", "cube", "self::superscript", "children/*[2]", "children/*[2][text()=3]", "@embellished", "children/*[1][@role=\"prefix operator\"]"], ["Precondition", "prime", "default", "self::superscript", "children/*[2]", "children/*[2][@role=\"prime\"]"], ["Specialized", "prime", "default", "brief"], ["Specialized", "prime", "default", "sbrief"], ["Precondition", "double-prime", "default", "self::punctuated", "@role=\"prime\"", "count(children/*)=2"], ["<PERSON><PERSON>", "double-prime", "self::operator", "@role=\"prime\"", "string-length(text())=2"], ["Precondition", "triple-prime", "default", "self::punctuated", "@role=\"prime\"", "count(children/*)=3"], ["<PERSON><PERSON>", "triple-prime", "self::operator", "@role=\"prime\"", "string-length(text())=3"], ["Precondition", "quadruple-prime", "default", "self::punctuated", "@role=\"prime\"", "count(children/*)=4"], ["<PERSON><PERSON>", "quadruple-prime", "self::operator", "@role=\"prime\"", "string-length(text())=4"], ["Precondition", "counted-prime", "default", "self::punctuated", "@role=\"prime\""], ["Precondition", "counted-prime-multichar", "default", "self::operator", "@role=\"prime\"", "string-length(text())>4"], ["Precondition", "prime-subscript", "default", "self::superscript", "children/*[2][@role=\"prime\"]", "name(children/*[1])=\"subscript\"", "not(following-sibling::*)"], ["Precondition", "prime-subscript-brief", "brief", "self::superscript", "children/*[2][@role=\"prime\"]", "name(children/*[1])=\"subscript\"", "not(following-sibling::*)"], ["Specialized", "prime-subscript-brief", "brief", "sbrief"], ["Precondition", "prime-subscript-baseline", "default", "self::superscript", "children/*[2][@role=\"prime\"]", "name(children/*[1])=\"subscript\"", "following-sibling::*"], ["Precondition", "prime-subscript-baseline-brief", "brief", "self::superscript", "children/*[2][@role=\"prime\"]", "name(children/*[1])=\"subscript\"", "following-sibling::*"], ["Specialized", "prime-subscript-baseline-brief", "brief", "sbrief"], ["<PERSON><PERSON>", "prime-subscript-baseline", "self::superscript", "children/*[2][@role=\"prime\"]", "name(children/*[1])=\"subscript\"", "not(following-sibling::*)", "@embellished"], ["<PERSON><PERSON>", "prime-subscript-baseline-brief", "self::superscript", "children/*[2][@role=\"prime\"]", "name(children/*[1])=\"subscript\"", "not(following-sibling::*)", "@embellished"], ["Precondition", "prime-subscript-simple", "default", "self::superscript", "children/*[2][@role=\"prime\"]", "name(children/*[1])=\"subscript\"", "name(children/*[1]/children/*[1])=\"identifier\"", "name(children/*[1]/children/*[2])=\"number\"", "children/*[1]/children/*[2][@role!=\"mixed\"]", "children/*[1]/children/*[2][@role!=\"othernumber\"]"], ["Specialized", "prime-subscript-simple", "default", "brief"], ["Specialized", "prime-subscript-simple", "default", "sbrief"], ["Precondition", "overscore", "default", "self::overscore", "children/*[2][@role=\"overaccent\"]"], ["Precondition", "overscore-brief", "brief", "self::overscore", "children/*[2][@role=\"overaccent\"]"], ["Specialized", "overscore-brief", "brief", "sbrief"], ["Precondition", "double-overscore", "default", "self::overscore", "children/*[2][@role=\"overaccent\"]", "name(children/*[1])=\"overscore\"", "children/*[1]/children/*[2][@role=\"overaccent\"]"], ["Precondition", "double-overscore-brief", "brief", "self::overscore", "children/*[2][@role=\"overaccent\"]", "name(children/*[1])=\"overscore\"", "children/*[1]/children/*[2][@role=\"overaccent\"]"], ["Specialized", "double-overscore-brief", "brief", "sbrief"], ["Precondition", "underscore", "default", "self::underscore", "children/*[2][@role=\"underaccent\"]"], ["Precondition", "underscore-brief", "brief", "self::underscore", "children/*[2][@role=\"underaccent\"]"], ["Specialized", "underscore-brief", "brief", "sbrief"], ["Precondition", "double-underscore", "default", "self::underscore", "children/*[2][@role=\"underaccent\"]", "name(children/*[1])=\"underscore\"", "children/*[1]/children/*[2][@role=\"underaccent\"]"], ["Precondition", "double-underscore-brief", "brief", "self::underscore", "children/*[2][@role=\"underaccent\"]", "name(children/*[1])=\"underscore\"", "children/*[1]/children/*[2][@role=\"underaccent\"]"], ["Specialized", "double-underscore-brief", "brief", "sbrief"], ["Precondition", "overbar", "default", "self::overscore", "contains(@role,\"letter\")", "children/*[2][@role=\"overaccent\"]", "children/*[2][contains(@annotation, \"accent:bar\")]"], ["Specialized", "overbar", "default", "brief"], ["Specialized", "overbar", "default", "sbrief"], ["Precondition", "underbar", "default", "self::underscore", "contains(@role,\"letter\")", "children/*[2][@role=\"underaccent\"]", "children/*[2][contains(@annotation, \"accent:bar\")]"], ["Specialized", "underbar", "default", "brief"], ["Specialized", "underbar", "default", "sbrief"], ["Precondition", "overtilde", "default", "self::overscore", "children/*[2][@role=\"overaccent\"]", "contains(@role,\"letter\")", "children/*[2][contains(@annotation, \"accent:tilde\")]"], ["Specialized", "overtilde", "default", "brief"], ["Specialized", "overtilde", "default", "sbrief"], ["Precondition", "undertilde", "default", "self::underscore", "contains(@role,\"letter\")", "children/*[2][@role=\"underaccent\"]", "children/*[2][contains(@annotation, \"accent:tilde\")]"], ["Specialized", "undertilde", "default", "brief"], ["Specialized", "undertilde", "default", "sbrief"], ["Precondition", "matrix", "default", "self::matrix"], ["Precondition", "matrix-sbrief", "sbrief", "self::matrix"], ["<PERSON><PERSON>", "matrix", "self::vector"], ["<PERSON><PERSON>", "matrix-sbrief", "self::vector"], ["Precondition", "matrix-row", "default", "self::row"], ["Precondition", "row-with-label", "default", "self::row", "content"], ["Precondition", "row-with-label-brief", "brief", "self::row", "content"], ["Specialized", "row-with-label-brief", "brief", "sbrief"], ["Precondition", "row-with-text-label", "sbrief", "self::row", "content", "name(content/cell/children/*[1])=\"text\""], ["Precondition", "empty-row", "default", "self::row", "count(children/*)=0"], ["Precondition", "matrix-cell", "default", "self::cell"], ["Precondition", "empty-cell", "default", "self::cell", "count(children/*)=0"], ["Precondition", "determinant", "default", "self::matrix", "@role=\"determinant\""], ["Precondition", "determinant-sbrief", "sbrief", "self::matrix", "@role=\"determinant\""], ["Precondition", "determinant-simple", "default", "self::matrix", "@role=\"determinant\"", "CQFdetIsSimple"], ["Precondition", "determinant-simple-sbrief", "sbrief", "self::matrix", "@role=\"determinant\"", "CQFdetIsSimple"], ["Precondition", "row-simple", "default", "self::row", "@role=\"determinant\"", "contains(@grammar, \"simpleDet\")"], ["Precondition", "layout", "default", "self::table"], ["Precondition", "layout-sbrief", "sbrief", "self::table"], ["Precondition", "binomial", "default", "self::vector", "@role=\"binomial\""], ["Precondition", "binomial-sbrief", "sbrief", "self::vector", "@role=\"binomial\""], ["Precondition", "cases", "default", "self::cases"], ["Precondition", "cases-sbrief", "sbrief", "self::cases"], ["<PERSON><PERSON>", "layout", "self::multiline"], ["<PERSON><PERSON>", "layout-sbrief", "self::multiline"], ["Precondition", "line", "default", "self::line"], ["Precondition", "line-with-label", "default", "self::line", "content"], ["Precondition", "line-with-label-brief", "brief", "self::line", "content"], ["Specialized", "line-with-label-brief", "brief", "sbrief"], ["Precondition", "line-with-text-label", "sbrief", "self::line", "content", "name(content/cell/children/*[1])=\"text\""], ["Precondition", "empty-line", "default", "self::line", "count(children/*)=0", "not(content)"], ["Specialized", "empty-line", "default", "brief"], ["Specialized", "empty-line", "brief", "sbrief"], ["Precondition", "empty-line-with-label", "default", "self::line", "count(children/*)=0", "content"], ["Precondition", "empty-line-with-label-brief", "brief", "self::line", "count(children/*)=0", "content"], ["Specialized", "empty-line-with-label-brief", "brief", "sbrief"], ["Precondition", "enclose", "default", "self::enclose"], ["<PERSON><PERSON>", "overbar", "self::enclose", "@role=\"top\""], ["<PERSON><PERSON>", "underbar", "self::enclose", "@role=\"bottom\""], ["Precondition", "leftbar", "default", "self::enclose", "@role=\"left\""], ["Precondition", "rightbar", "default", "self::enclose", "@role=\"right\""], ["Precondition", "crossout", "default", "self::enclose", "@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\""], ["Precondition", "cancel", "default", "self::overscore", "@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\""], ["Specialized", "cancel", "default", "brief"], ["Specialized", "cancel", "default", "sbrief"], ["<PERSON><PERSON>", "cancel", "self::underscore", "@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\""], ["Precondition", "cancel-reverse", "default", "self::overscore", "name(children/*[2])=\"enclose\"", "children/*[2][@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\"]"], ["Specialized", "cancel-reverse", "default", "brief"], ["Specialized", "cancel-reverse", "default", "sbrief"], ["<PERSON><PERSON>", "cancel-reverse", "self::underscore", "name(children/*[2])=\"enclose\"", "children/*[2][@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\"]"], ["Precondition", "end-punct", "default", "self::punctuated", "@role=\"endpunct\""], ["Precondition", "start-punct", "default", "self::punctuated", "@role=\"startpunct\""], ["Precondition", "punctuated", "default", "self::punctuated"], ["Precondition", "unit", "default", "self::identifier[@role=\"unit\"]"], ["Precondition", "unit-square", "default", "self::superscript[@role=\"unit\"]", "children/*[2][text()=2]", "name(children/*[1])=\"identifier\""], ["Specialized", "unit-square", "default", "brief"], ["Specialized", "unit-square", "brief", "sbrief"], ["Precondition", "unit-cubic", "default", "self::superscript[@role=\"unit\"]", "children/*[2][text()=3]", "name(children/*[1])=\"identifier\""], ["Specialized", "unit-cubic", "default", "brief"], ["Specialized", "unit-cubic", "brief", "sbrief"], ["Precondition", "unit-combine", "default", "self::infixop[@role=\"unit\"]"], ["Precondition", "multi-inference", "default", "self::inference"], ["Precondition", "inference", "default", "self::inference", "count(children/*[2]/children/*)<2"], ["Precondition", "premise", "default", "self::premises"], ["Precondition", "conclusion", "default", "self::conclusion"], ["Precondition", "label", "default", "self::rulelabel"], ["Precondition", "axiom", "default", "self::inference[@role=\"axiom\"]"], ["Precondition", "empty-axiom", "default", "self::inference[@role=\"axiom\"]", "name(children/*[1])=\"empty\""], ["Generator", "CGFtensorRules"]]}, "base/rules/mathspeak_base_actions.min": {"domain": "mathspeak", "locale": "base", "modality": "speech", "kind": "actions", "rules": [["Action", "number", "[n] text()"], ["Action", "identifier-spacing", "[m] CQFspaceoutIdentifier"], ["Action", "identifier", "[n] text()"], ["Action", "prefix", "[m] content/*; [n] children/*[1]"], ["Action", "postfix", "[n] children/*[1]; [m] content/*"], ["Action", "binary-operation", "[m] children/* (sepFunc:CTFcontentIterator);"], ["Action", "implicit", "[m] children/*"], ["Action", "function-unknown", "[n] children/*[1]; [n] children/*[2]"], ["Action", "function-prefix", "[n] children/*[1]; [n] children/*[2]"], ["Action", "fences-open-close", "[n] content/*[1]; [n] children/*[1]; [n] content/*[2]"], ["Action", "text", "[n] text()"], ["Action", "matrix-cell", "[n] children/*[1] (pause: 300)"], ["Action", "row-simple", "[m] children/*;"], ["Action", "line", "[m] children/*"], ["Action", "end-punct", "[m] children/*"], ["Action", "start-punct", "[n] content/*[1]; [m] children/*[position()>1]"], ["Action", "punctuated", "[m] children/*"], ["Action", "fraction", "[t] CSFopenFracVerbose; [n] children/*[1]; [t] CSFoverFracVerbose; [n] children/*[2]; [t] CSFcloseFracVerbose"], ["Action", "fraction-brief", "[t] CSFopenFracBrief; [n] children/*[1]; [t] CSFoverFracVerbose; [n] children/*[2]; [t] CSFcloseFracBrief"], ["Action", "fraction-sbrief", "[t] CSFopenFracSbrief; [n] children/*[1]; [t] CSFoverFracSbrief; [n] children/*[2]; [t] CSFcloseFracSbrief"], ["Action", "vulgar-fraction", "[t] CSFvulgarFraction"], ["Action", "sqrt", "[t] CSFopenRadicalVerbose; [n] children/*[1]; [t] CSFcloseRadicalVerbose"], ["Action", "sqrt-brief", "[t] CSFopenRadicalBrief; [n] children/*[1]; [t] CSFcloseRadicalBrief"], ["Action", "sqrt-sbrief", "[t] CSFopenRadicalSbrief; [n] children/*[1]; [t] CSFcloseRadicalBrief"], ["Action", "root-small", "[t] CSFopenRadicalVerbose; [n] children/*[2]; [t] CSFcloseRadicalVerbose"], ["Action", "root-small-brief", "[t] CSFopenRadicalBrief; [n] children/*[2]; [t] CSFcloseRadicalBrief"], ["Action", "root-small-sbrief", "[t] CSFopenRadicalSbrief; [n] children/*[2]; [t] CSFcloseRadicalBrief"], ["Action", "root", "[t] CSFindexRadicalVerbose; [n] children/*[1];[t] CSFopenRadicalVerbose; [n] children/*[2]; [t] CSFcloseRadicalVerbose"], ["Action", "root-brief", "[t] CSFindexRadicalBrief; [n] children/*[1];[t] CSFopenRadicalBrief; [n] children/*[2]; [t] CSFcloseRadicalBrief"], ["Action", "root-sbrief", "[t] CSFindexRadicalSbrief; [n] children/*[1];[t] CSFopenRadicalSbrief; [n] children/*[2]; [t] CSFcloseRadicalBrief"], ["Action", "integral-index", "[n] children/*[1]; [n] children/*[2]; [n] children/*[3];"], ["Action", "bigop", "[n] children/*[1]; [n] children/*[2];"], ["Action", "relseq", "[m] children/* (sepFunc:CTFcontentIterator)"], ["Action", "equality", "[n] children/*[1]; [n] content/*[1]; [n] children/*[2]"], ["Action", "multi-equality", "[m] children/* (sepFunc:CTFcontentIterator)"], ["Action", "mult<PERSON>", "[m] children/* (sepFunc:CTFcontentIterator)"], ["Action", "subscript", "[n] children/*[1]; [t] CSFsubscriptVerbose; [n] children/*[2]"], ["Action", "subscript-brief", "[n] children/*[1]; [t] CSFsubscriptBrief; [n] children/*[2]"], ["Action", "subscript-simple", "[n] children/*[1]; [n] children/*[2]"], ["Action", "subscript-baseline", "[n] children/*[1]; [t] CSFsubscriptVerbose; [n] children/*[2]; [t] CSFbaselineVerbose"], ["Action", "subscript-baseline-brief", "[n] children/*[1]; [t] CSFsubscriptBrief; [n] children/*[2]; [t] CSFbaselineBrief"], ["Action", "subscript-empty-sup", "[n] children/*[1]; [n] children/*[2]"], ["Action", "superscript", "[n] children/*[1]; [t] CSFsuperscriptVerbose; [n] children/*[2]"], ["Action", "superscript-brief", "[n] children/*[1]; [t] CSFsuperscriptBrief; [n] children/*[2]"], ["Action", "superscript-baseline", "[n] children/*[1]; [t] CSFsuperscriptVerbose; [n] children/*[2];[t] CSFbaselineVerbose"], ["Action", "superscript-baseline-brief", "[n] children/*[1]; [t] CSFsuperscriptBrief; [n] children/*[2];[t] CSFbaselineBrief"], ["Action", "superscript-empty-sub", "[n] children/*[1]; [n] children/*[2]"], ["Action", "double-prime", "[t] \"″\" (grammar:translate)"], ["Action", "triple-prime", "[t] \"‴\" (grammar:translate)"], ["Action", "quadruple-prime", "[t] \"⁗\" (grammar:translate)"], ["Action", "prime-subscript", "[n] children/*[1]/children/*[1]; [n] children/*[2]; [t] CSFsubscriptVerbose; [n] children/*[1]/children/*[2]"], ["Action", "prime-subscript-brief", "[n] children/*[1]/children/*[1]; [n] children/*[2]; [t] CSFsubscriptBrief; [n] children/*[1]/children/*[2]"], ["Action", "prime-subscript-baseline", "[n] children/*[1]/children/*[1]; [n] children/*[2]; [t] CSFsubscriptVerbose; [n] children/*[1]/children/*[2]; [t] CSFbaselineVerbose"], ["Action", "prime-subscript-baseline-brief", "[n] children/*[1]/children/*[1]; [n] children/*[2]; [t] CSFsubscriptBrief; [n] children/*[1]/children/*[2]; [t] CSFbaselineBrief"], ["Action", "prime-subscript-simple", "[n] children/*[1]/children/*[1]; [n] children/*[2];[n] children/*[1]/children/*[2]"], ["Action", "unit", "[t] text() (grammar:annotation=\"unit\":translate:plural)"], ["Action", "unit-combine", "[m] children/*"], ["Action", "limboth", "[n] children/*[1]; [t] CSFunderscript; [n] children/*[2]; [t] CSFoverscript; [n] children/*[3]"], ["Action", "limlower", "[n] children/*[1]; [t] CSFunderscript; [n] children/*[2]"], ["Action", "limupper", "[n] children/*[1]; [t] CSFoverscript; [n] children/*[2]"], ["Action", "limboth-end", "[n] children/*[1]; [t] CSFunderscript; [n] children/*[2]; [t] CSFoverscript; [n] children/*[3]; [t] CSFendscripts"], ["Action", "limlower-end", "[n] children/*[1]; [t] CSFunderscript; [n] children/*[2]; [t] CSFendscripts"], ["Action", "limupper-end", "[n] children/*[1]; [t] CSFoverscript; [n] children/*[2]; [t] CSFendscripts"]]}, "base/rules/mathspeak_base_romance.min": {"domain": "mathspeak", "locale": "romance", "modality": "speech", "kind": "abstract", "inherits": "base", "rules": [["Precondition", "logarithm-base", "default", "self::subscript", "children/*[1][@category=\"Logarithm\"]", "name(./children/*[2])=\"identifier\" or name(./children/*[2])=\"number\"", "./children/*[2][@role!=\"mixed\"]", "./children/*[2][@role!=\"othernumber\"]"], ["Specialized", "logarithm-base", "default", "brief"], ["Specialized", "logarithm-base", "default", "sbrief"]]}, "base/rules/prefix_base.min": {"modality": "prefix", "domain": "default", "locale": "base", "kind": "abstract", "rules": [["Precondition", "numerator", "default", "self::*", "name(../..)=\"fraction\"", "count(preceding-sibling::*)=0"], ["Precondition", "denominator", "default", "self::*", "name(../..)=\"fraction\"", "count(preceding-sibling::*)=1"], ["Precondition", "base", "default", "self::*", "count(preceding-sibling::*)=0", "name(../..)=\"superscript\""], ["<PERSON><PERSON>", "base", "self::*", "count(preceding-sibling::*)=0", "name(../..)=\"subscript\""], ["<PERSON><PERSON>", "base", "self::*", "count(preceding-sibling::*)=0", "name(../..)=\"overscore\""], ["<PERSON><PERSON>", "base", "self::*", "count(preceding-sibling::*)=0", "name(../..)=\"underscore\""], ["<PERSON><PERSON>", "base", "self::*", "count(preceding-sibling::*)=0", "name(../..)=\"tensor\""], ["<PERSON><PERSON>", "base", "self::*", "count(preceding-sibling::*)=0", "name(../..)=\"limlower\""], ["<PERSON><PERSON>", "base", "self::*", "count(preceding-sibling::*)=0", "name(../..)=\"limupper\""], ["<PERSON><PERSON>", "base", "self::*", "count(preceding-sibling::*)=0", "name(../..)=\"limboth\""], ["Precondition", "exponent", "default", "self::*", "name(../..)=\"superscript\"", "count(preceding-sibling::*)=1"], ["Precondition", "subscript", "default", "self::*", "name(../..)=\"subscript\"", "count(preceding-sibling::*)=1"], ["Precondition", "overscript", "default", "self::*", "count(preceding-sibling::*)=1", "name(../..)=\"overscore\""], ["<PERSON><PERSON>", "overscript", "self::*", "count(preceding-sibling::*)=1", "name(../..)=\"limupper\""], ["<PERSON><PERSON>", "overscript", "self::*", "count(preceding-sibling::*)=2", "name(../..)=\"limboth\""], ["Precondition", "underscript", "default", "self::*", "count(preceding-sibling::*)=1", "name(../..)=\"underscore\""], ["<PERSON><PERSON>", "underscript", "self::*", "count(preceding-sibling::*)=1", "name(../..)=\"limlower\""], ["<PERSON><PERSON>", "underscript", "self::*", "count(preceding-sibling::*)=1", "name(../..)=\"limboth\""], ["Precondition", "radicand", "default", "self::*", "name(../..)=\"sqrt\""], ["<PERSON><PERSON>", "radicand", "self::*", "name(../..)=\"root\"", "count(preceding-sibling::*)=1"], ["Precondition", "index", "default", "self::*", "name(../..)=\"root\"", "count(preceding-sibling::*)=0"], ["Precondition", "leftsub", "default", "self::*", "name(../..)=\"tensor\"", "@role=\"leftsub\""], ["Precondition", "leftsub-counted", "default", "self::*", "name(../..)=\"punctuated\"", "name(../../../..)=\"tensor\"", "../../@role=\"leftsub\""], ["Precondition", "leftsuper", "default", "self::*", "name(../..)=\"tensor\"", "@role=\"leftsuper\""], ["Precondition", "leftsuper-counted", "default", "self::*", "name(../..)=\"punctuated\"", "name(../../../..)=\"tensor\"", "../../@role=\"leftsuper\""], ["Precondition", "rightsub", "default", "self::*", "name(../..)=\"tensor\"", "@role=\"rightsub\""], ["Precondition", "rightsub-counted", "default", "self::*", "name(../..)=\"punctuated\"", "name(../../../..)=\"tensor\"", "../../@role=\"rightsub\""], ["Precondition", "<PERSON><PERSON>r", "default", "self::*", "name(../..)=\"tensor\"", "@role=\"rightsuper\""], ["Precondition", "rightsuper-counted", "default", "self::*", "name(../..)=\"punctuated\"", "name(../../../..)=\"tensor\"", "../../@role=\"rightsuper\""], ["Precondition", "choice", "default", "self::line", "@role=\"binomial\"", "parent::*/parent::vector", "count(preceding-sibling::*)=0"], ["Precondition", "select", "default", "self::line", "@role=\"binomial\"", "parent::*/parent::vector", "count(preceding-sibling::*)=1"], ["Precondition", "row", "default", "self::row"], ["<PERSON><PERSON>", "row", "self::line"], ["Precondition", "cell", "default", "self::cell", "contains(@grammar,\"depth\")"], ["Precondition", "cell-simple", "default", "self::cell"]]}, "base/rules/summary_base.min": {"modality": "summary", "locale": "base", "kind": "abstract", "rules": [["Rule", "stree", "default.default", "[n] ./*[1]", "self::stree"], ["Precondition", "abstr-identifier-long", "default.default", "self::identifier", "contains(@grammar, \"collapsed\")"], ["Precondition", "abstr-identifier", "default.default", "self::identifier"], ["Precondition", "abstr-number-long", "default.default", "self::number", "contains(@grammar, \"collapsed\")"], ["Precondition", "abstr-number", "default.default", "self::number"], ["Precondition", "abstr-mixed-number-long", "default.default", "self::number", "@role=\"mixed\"", "contains(@grammar, \"collapsed\")"], ["Precondition", "abstr-mixed-number", "default.default", "self::number", "@role=\"mixed\""], ["Precondition", "abstr-text", "default.default", "self::text"], ["Precondition", "abstr-function", "default.default", "self::function"], ["Precondition", "abstr-function-brief", "mathspeak.brief", "self::function"], ["Specialized", "abstr-function-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-lim", "default.default", "self::function", "@role=\"limit function\""], ["Precondition", "abstr-lim-brief", "mathspeak.brief", "self::function", "@role=\"limit function\""], ["Specialized", "abstr-lim-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-fraction", "default.default", "self::fraction"], ["Precondition", "abstr-fraction-brief", "mathspeak.brief", "self::fraction"], ["Specialized", "abstr-fraction-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-continued-fraction", "default.default", "self::fraction", "children/*[2]/descendant-or-self::*[@role=\"ellipsis\"]"], ["Precondition", "abstr-continued-fraction-brief", "mathspeak.brief", "self::fraction", "children/*[2]/descendant-or-self::*[@role=\"ellipsis\"]"], ["Specialized", "abstr-continued-fraction-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-sqrt", "default.default", "self::sqrt"], ["Precondition", "abstr-sqrt-nested", "default.default", "self::sqrt", "children/*/descendant-or-self::sqrt or children/*/descendant-or-self::root"], ["Precondition", "abstr-root-end", "default.default", "self::root", "contains(@grammar, \"collapsed\")", "following-sibling::* or ancestor::*/following-sibling::*"], ["Precondition", "abstr-root", "default.default", "self::root"], ["Precondition", "abstr-root-brief", "mathspeak.brief", "self::root"], ["Specialized", "abstr-root-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-root-nested-end", "default.default", "self::root", "contains(@grammar, \"collapsed\")", "children/*/descendant-or-self::sqrt or children/*/descendant-or-self::root", "following-sibling::* or ancestor::*/following-sibling::*"], ["Precondition", "abstr-root-nested", "default.default", "self::root", "children/*/descendant-or-self::sqrt or children/*/descendant-or-self::root"], ["Precondition", "abstr-root-nested-brief", "mathspeak.brief", "self::root", "children/*/descendant-or-self::sqrt or children/*/descendant-or-self::root"], ["Specialized", "abstr-root-nested-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-superscript", "default.default", "self::superscript"], ["Precondition", "abstr-subscript", "default.default", "self::subscript"], ["Precondition", "abstr-subsup", "default.default", "self::superscript", "name(children/*[1])=\"subscript\""], ["Precondition", "abstr-infixop", "default.default", "self::infixop"], ["Precondition", "abstr-infixop-var", "default.default", "self::infixop", "count(./children/*)>2", "./children/punctuation[@role=\"ellipsis\"]"], ["Precondition", "abstr-infixop-brief", "mathspeak.brief", "self::infixop"], ["Specialized", "abstr-infixop-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-addition", "default.default", "self::infixop", "@role=\"addition\""], ["Precondition", "abstr-addition-brief", "mathspeak.brief", "self::infixop", "@role=\"addition\""], ["Specialized", "abstr-addition-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-addition-var", "default.default", "self::infixop", "@role=\"addition\"", "count(./children/*)>2", "./children/punctuation[@role=\"ellipsis\"]"], ["Precondition", "abstr-multiplication", "default.default", "self::infixop", "@role=\"multiplication\""], ["Precondition", "abstr-multiplication-brief", "mathspeak.brief", "self::infixop", "@role=\"multiplication\""], ["Specialized", "abstr-multiplication-brief", "mathspeak.brief", "mathspeak.sbrief"], ["<PERSON><PERSON>", "abstr-multiplication", "self::infixop", "@role=\"implicit\""], ["<PERSON><PERSON>", "abstr-multiplication-brief", "self::infixop", "@role=\"implicit\""], ["Precondition", "abstr-multiplication-var", "default.default", "self::infixop", "@role=\"multiplication\"", "count(./children/*)>2", "./children/punctuation[@role=\"ellipsis\"]"], ["<PERSON><PERSON>", "abstr-multiplication-var", "self::infixop", "@role=\"implicit\"", "count(./children/*)>2", "./children/punctuation[@role=\"ellipsis\"]"], ["Precondition", "abstr-vector", "default.default", "self::vector"], ["Precondition", "abstr-vector-brief", "mathspeak.brief", "self::vector"], ["Specialized", "abstr-vector-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-vector-var", "default.default", "self::vector", "./children/*/children/punctuation[@role=\"ellipsis\"]"], ["Precondition", "abstr-binomial", "default.default", "self::vector", "@role=\"binomial\""], ["Specialized", "abstr-binomial", "default.default", "mathspeak.brief"], ["Specialized", "abstr-binomial", "default.default", "mathspeak.sbrief"], ["Precondition", "abstr-determinant", "default.default", "self::matrix", "@role=\"determinant\""], ["Precondition", "abstr-determinant-brief", "mathspeak.brief", "self::matrix", "@role=\"determinant\""], ["Specialized", "abstr-determinant-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-determinant-var", "default.default", "self::matrix", "@role=\"determinant\"", "./children/*/children/*/children/punctuation[@role=\"ellipsis\"]"], ["Precondition", "abstr-squarematrix", "default.default", "self::matrix", "@role=\"squarematrix\""], ["Precondition", "abstr-squarematrix-brief", "mathspeak.brief", "self::matrix", "@role=\"squarematrix\""], ["Specialized", "abstr-squarematrix-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-rowvector", "default.default", "self::matrix", "@role=\"rowvector\""], ["Precondition", "abstr-rowvector-brief", "mathspeak.brief", "self::matrix", "@role=\"rowvector\""], ["Specialized", "abstr-rowvector-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-rowvector-var", "default.default", "self::matrix", "@role=\"rowvector\"", "./children/*/children/*/children/punctuation[@role=\"ellipsis\"]"], ["Precondition", "abstr-matrix", "default.default", "self::matrix"], ["Precondition", "abstr-matrix-brief", "mathspeak.brief", "self::matrix"], ["Specialized", "abstr-matrix-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-matrix-var", "default.default", "self::matrix", "./children/*/children/*/children/punctuation[@role=\"ellipsis\"]"], ["Precondition", "abstr-cases", "default.default", "self::cases"], ["Precondition", "abstr-cases-brief", "mathspeak.brief", "self::cases"], ["Specialized", "abstr-cases-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-cases-var", "default.default", "self::cases", "./children/row/children/cell/children/punctuation[@role=\"ellipsis\"]or ./children/line/children/punctuation[@role=\"ellipsis\"]"], ["Precondition", "abstr-punctuated", "default.default", "self::punctuated"], ["Precondition", "abstr-punctuated-brief", "mathspeak.brief", "self::punctuated"], ["Specialized", "abstr-punctuated-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-punctuated-var", "default.default", "self::punctuated", "./children/punctuation[@role=\"ellipsis\"]"], ["Precondition", "abstr-bigop", "default.default", "self::bigop"], ["Precondition", "abstr-integral", "default.default", "self::*", "@role=\"integral\""], ["Precondition", "abstr-relation", "default.default", "self::relseq", "count(./children/*)=2"], ["Precondition", "abstr-relation-seq", "default.default", "self::relseq", "count(./children/*)>2"], ["Precondition", "abstr-relation-seq-brief", "mathspeak.brief", "self::relseq", "count(./children/*)>2"], ["Specialized", "abstr-relation-seq-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-relation-var", "default.default", "self::relseq", "count(./children/*)>2", "./children/punctuation[@role=\"ellipsis\"]"], ["<PERSON><PERSON>", "abstr-relation", "self::multirel", "@role!=\"unknown\"", "count(./children/*)>2"], ["<PERSON><PERSON>", "abstr-relation-var", "self::multirel", "@role!=\"unknown\"", "count(./children/*)>2", "./children/punctuation[@role=\"ellipsis\"]"], ["Precondition", "abstr-multirel", "default.default", "self::multirel", "count(./children/*)>2"], ["Precondition", "abstr-multirel-brief", "mathspeak.brief", "self::multirel", "count(./children/*)>2"], ["Specialized", "abstr-multirel-brief", "mathspeak.brief", "mathspeak.sbrief"], ["Precondition", "abstr-multirel-var", "default.default", "self::multirel", "count(./children/*)>2", "./children/punctuation[@role=\"ellipsis\"]"], ["Precondition", "abstr-table", "default.default", "self::table"], ["Precondition", "abstr-line", "default.default", "self::line"], ["Precondition", "abstr-row", "default.default", "self::row"], ["Precondition", "abstr-cell", "default.default", "self::cell"]]}, "base/rules/summary_base_romance.min": {"modality": "summary", "locale": "romance", "kind": "abstract", "inherits": "base", "rules": [["Precondition", "collapsed-masculine", "default.default", "self::*[@grammar]", "contains(@grammar, \"gender:m\")", "contains(@grammar, \"collapsed\")"], ["Specialized", "collapsed-masculine", "default.default", "mathspeak.brief"], ["Specialized", "collapsed-masculine", "default.default", "mathspeak.sbrief"], ["Precondition", "collapsed-feminine", "default.default", "self::*[@grammar]", "contains(@grammar, \"gender:f\")", "contains(@grammar, \"collapsed\")"], ["Specialized", "collapsed-feminine", "default.default", "mathspeak.brief"], ["Specialized", "collapsed-feminine", "default.default", "mathspeak.sbrief"], ["Rule", "no-collapsed", "default.default", "[t] \"\"", "self::*[@grammar]", "contains(@grammar, \"gender\")", "not(contains(@grammar, \"collapsed\"))"], ["SpecializedRule", "no-collapsed", "default.default", "mathspeak.brief"], ["SpecializedRule", "no-collapsed", "default.default", "mathspeak.sbrief"]]}}