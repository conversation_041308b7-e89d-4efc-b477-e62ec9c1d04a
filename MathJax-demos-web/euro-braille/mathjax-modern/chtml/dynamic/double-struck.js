(()=>{"use strict";const s=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-modern"].chtml_ts.MathJaxModernFont.dynamicSetup("","double-struck",s({DS:{normal:{8450:[.705,.022,.667],8461:[.683,0,.722],8469:[.683,0,.722],8473:[.683,0,.639,{sk:-.072}],8474:[.705,.194,.667],8477:[.683,0,.639,{sk:-.072}],8484:[.683,0,.667],8508:[.431,0,.517],8509:[.431,.216,.472],8510:[.683,0,.611,{sk:.028}],8511:[.683,0,.667],8512:[.683,0,.667],8517:[.683,0,.694],8518:[.694,.022,.5,{ic:.047}],8519:[.453,.022,.472],8520:[.691,0,.279,{ic:.053}],8521:[.691,.216,.389,{ic:.053}],120120:[.683,0,.611],120121:[.683,0,.639,{sk:-.072}],120123:[.683,0,.694,{sk:-.1}],120124:[.683,0,.611,{sk:.029}],120125:[.683,0,.611,{sk:.029}],120126:[.705,.022,.667],120128:[.683,0,.334],120129:[.683,.022,.639,{sk:.151}],120130:[.683,0,.639,{sk:.013}],120131:[.683,0,.611,{sk:-.139}],120132:[.683,0,.722],120134:[.705,.022,.667],120138:[.705,.022,.611,{sk:-.015}],120139:[.683,0,.611],120140:[.683,.022,.722],120141:[.683,0,.611],120142:[.683,0,.833],120143:[.683,0,.667],120144:[.683,0,.611],120146:[.453,.022,.5,{sk:-.014}],120147:[.694,.022,.628,{sk:-.175}],120148:[.453,.022,.472],120149:[.694,.022,.5,{sk:.174}],120150:[.453,.022,.472],120151:[.716,0,.389,{sk:.03}],120152:[.453,.216,.5,{sk:-.014}],120153:[.694,0,.572,{sk:-.147}],120154:[.691,0,.279],120155:[.691,.216,.389,{sk:.054}],120156:[.694,0,.544,{sk:-.133}],120157:[.694,0,.279],120158:[.453,0,.722,{sk:.059}],120159:[.453,0,.572,{sk:.06}],120160:[.453,.022,.472],120161:[.453,.194,.628,{sk:.077}],120162:[.453,.194,.5,{sk:-.014}],120163:[.453,0,.544,{sk:.074}],120164:[.453,.022,.389],120165:[.694,.022,.417,{sk:-.07}],120166:[.431,.022,.528],120167:[.431,0,.472],120168:[.431,0,.667],120169:[.431,0,.472],120170:[.431,.216,.472],120171:[.431,0,.472],120792:[.666,.022,.556],120793:[.644,0,.556],120794:[.666,0,.556],120795:[.666,.022,.556,{sk:.011}],120796:[.644,0,.556,{sk:.118}],120797:[.644,.022,.556],120798:[.666,.022,.556,{sk:-.012}],120799:[.644,0,.556],120800:[.666,.022,.556],120801:[.666,.022,.556]},"double-struck":{305:[.431,0,.279],567:[.431,.216,.389,{sk:.054}]}}},"MM"));MathJax.loader&&MathJax.loader.checkVersion("[mathjax-modern]/chtml/dynamic/double-struck","4.0.0-beta.7","dynamic-font")})();