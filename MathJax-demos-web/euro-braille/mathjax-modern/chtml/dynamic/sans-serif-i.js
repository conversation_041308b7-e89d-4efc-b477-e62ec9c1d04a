(()=>{"use strict";const i=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-modern"].chtml_ts.MathJaxModernFont.dynamicSetup("","sans-serif-i",i({SSLI:{"sans-serif-italic":{192:[.868,0,.667],193:[.868,0,.667,{ic:.041}],194:[.87,0,.667],195:[.855,0,.667,{ic:.015}],196:[.816,0,.667],197:[.862,0,.667],198:[.694,0,.861,{ic:.091}],199:[.716,.214,.639,{ic:.083}],200:[.868,0,.597,{ic:.09}],201:[.868,0,.597,{ic:.09}],202:[.87,0,.597,{ic:.09}],203:[.816,0,.597,{ic:.09}],204:[.868,0,.278,{ic:.063}],205:[.868,0,.278,{ic:.286}],206:[.87,0,.278,{ic:.173}],207:[.816,0,.278,{ic:.161}],208:[.694,0,.722,{ic:.024}],209:[.855,0,.708,{ic:.051}],210:[.868,.022,.736,{ic:.026}],211:[.868,.022,.736,{ic:.026}],212:[.87,.022,.736,{ic:.026}],213:[.855,.022,.736,{ic:.026}],214:[.816,.022,.736,{ic:.026}],216:[.759,.065,.778,{ic:.026}],217:[.868,.022,.688,{ic:.053}],218:[.868,.022,.688,{ic:.053}],219:[.87,.022,.688,{ic:.053}],220:[.816,.022,.688,{ic:.053}],221:[.868,0,.667,{ic:.144}],222:[.694,0,.583,{ic:.027}],223:[.705,.011,.481,{ic:.065}],224:[.694,.011,.481],225:[.694,.011,.481,{ic:.048}],226:[.697,.011,.481,{ic:.026}],227:[.67,.011,.481,{ic:.068}],228:[.651,.011,.481,{ic:.01}],229:[.685,.011,.481],230:[.461,.011,.722,{ic:.028}],231:[.461,.214,.444,{ic:.055}],232:[.694,.011,.444,{ic:.028}],233:[.694,.011,.444,{ic:.067}],234:[.697,.011,.444,{ic:.045}],235:[.651,.011,.444,{ic:.029}],236:[.694,0,.239,{ic:.029}],237:[.694,0,.239,{ic:.201}],238:[.697,0,.239,{ic:.147}],239:[.651,0,.239,{ic:.131}],241:[.67,0,.517,{ic:.05}],242:[.694,.011,.5,{ic:.022}],243:[.694,.011,.5,{ic:.039}],244:[.697,.011,.5,{ic:.022}],245:[.67,.011,.5,{ic:.059}],246:[.651,.011,.5,{ic:.022}],248:[.557,.112,.5,{ic:.068}],249:[.694,.011,.517,{ic:.012}],250:[.694,.011,.517,{ic:.03}],251:[.697,.011,.517,{ic:.012}],252:[.651,.011,.517,{ic:.012}],253:[.694,.205,.461,{ic:.079}],254:[.694,.194,.517,{ic:.018}],255:[.651,.205,.461,{ic:.079}],256:[.82,0,.667,{ic:.021}],257:[.643,.011,.481,{ic:.077}],258:[.924,0,.667,{ic:.031}],259:[.7,.011,.481,{ic:.077}],260:[.694,.212,.667],261:[.461,.212,.481],262:[.868,.011,.639,{ic:.087}],263:[.694,.011,.444,{ic:.089}],264:[.87,.011,.639,{ic:.079}],265:[.697,.011,.444,{ic:.067}],266:[.825,.011,.639,{ic:.079}],267:[.655,.011,.444,{ic:.055}],268:[.87,.011,.639,{ic:.079}],269:[.697,.011,.444,{ic:.103}],270:[.87,0,.722,{ic:.024}],271:[.694,.011,.517,{ic:.176}],272:[.694,0,.722,{ic:.024}],273:[.694,.011,.517,{ic:.123}],274:[.82,0,.597,{ic:.09}],275:[.643,.011,.444,{ic:.096}],276:[.924,0,.597,{ic:.09}],277:[.7,.011,.444,{ic:.096}],278:[.825,0,.597,{ic:.09}],279:[.655,.011,.444,{ic:.028}],280:[.691,.212,.597,{ic:.09}],281:[.461,.212,.444,{ic:.028}],282:[.87,0,.597,{ic:.09}],283:[.697,.011,.444,{ic:.081}],284:[.87,.011,.667,{ic:.062}],285:[.697,.206,.5,{ic:.071}],286:[.924,.011,.667,{ic:.062}],287:[.7,.206,.5,{ic:.071}],288:[.825,.011,.667,{ic:.062}],289:[.655,.206,.5,{ic:.071}],290:[.706,.266,.667,{ic:.062}],291:[.727,.206,.5,{ic:.071}],292:[.87,0,.708,{ic:.053}],293:[.87,0,.517,{ic:.053}],294:[.694,0,.708,{ic:.075}],295:[.694,0,.517],296:[.855,0,.278,{ic:.21}],297:[.67,0,.239,{ic:.189}],298:[.82,0,.278,{ic:.216}],299:[.643,0,.239,{ic:.198}],300:[.924,0,.278,{ic:.226}],301:[.7,0,.239,{ic:.198}],302:[.694,.212,.278,{ic:.053}],303:[.68,.212,.239,{ic:.069}],304:[.825,0,.278,{ic:.08}],306:[.694,.022,.722,{ic:.064}],307:[.655,.205,.482,{ic:.056}],308:[.87,.022,.472,{ic:.185}],309:[.697,.205,.267,{ic:.14}],310:[.694,.266,.694,{ic:.091}],311:[.694,.266,.489,{ic:.054}],313:[.868,0,.542,{ic:.022}],314:[.868,0,.239,{ic:.312}],315:[.694,.266,.542],316:[.694,.266,.239,{ic:.065}],317:[.694,0,.542],318:[.694,0,.239,{ic:.176}],319:[.694,0,.542],320:[.694,0,.288,{ic:.106}],321:[.694,0,.625],322:[.694,0,.324,{ic:.091}],323:[.868,0,.708,{ic:.051}],324:[.694,0,.517,{ic:.03}],325:[.694,.266,.708,{ic:.051}],326:[.455,.266,.517],327:[.87,0,.708,{ic:.051}],328:[.697,0,.517,{ic:.044}],330:[.716,.022,.708],331:[.455,.209,.517],332:[.82,.022,.736,{ic:.026}],333:[.643,.011,.5,{ic:.068}],334:[.924,.022,.736,{ic:.026}],335:[.7,.011,.5,{ic:.068}],336:[.913,.022,.736,{ic:.026}],337:[.694,.011,.5,{ic:.064}],338:[.716,.022,.972,{ic:.091}],339:[.461,.011,.778,{ic:.028}],340:[.868,0,.646,{ic:.054}],341:[.694,0,.342,{ic:.099}],342:[.694,.266,.646,{ic:.054}],343:[.455,.266,.342,{ic:.082}],344:[.87,0,.646,{ic:.054}],345:[.697,0,.342,{ic:.132}],346:[.868,.022,.556,{ic:.097}],347:[.694,.011,.383,{ic:.098}],348:[.87,.022,.556,{ic:.051}],349:[.697,.011,.383,{ic:.076}],350:[.716,.214,.556,{ic:.051}],351:[.461,.214,.383,{ic:.051}],352:[.87,.022,.556,{ic:.063}],353:[.697,.011,.383,{ic:.112}],354:[.688,.192,.681,{ic:.109}],355:[.571,.192,.361,{ic:.049}],356:[.87,0,.681,{ic:.109}],357:[.668,.011,.361,{ic:.067}],360:[.855,.022,.688,{ic:.053}],361:[.67,.011,.517,{ic:.05}],362:[.82,.022,.688,{ic:.053}],363:[.643,.011,.517,{ic:.059}],364:[.924,.022,.688,{ic:.053}],365:[.7,.011,.517,{ic:.059}],366:[.862,.022,.688,{ic:.053}],367:[.685,.011,.517,{ic:.012}],368:[.913,.022,.688,{ic:.053}],369:[.694,.011,.517,{ic:.055}],370:[.694,.212,.688,{ic:.053}],371:[.444,.212,.517,{ic:.012}],372:[.87,0,.944,{ic:.133}],373:[.697,0,.683,{ic:.079}],374:[.87,0,.667,{ic:.144}],375:[.697,.205,.461,{ic:.079}],376:[.816,0,.667,{ic:.144}],377:[.868,0,.611,{ic:.091}],378:[.694,0,.435,{ic:.071}],379:[.825,0,.611,{ic:.091}],380:[.655,0,.435,{ic:.059}],381:[.87,0,.611,{ic:.091}],382:[.697,0,.435,{ic:.085}],383:[.705,0,.286,{ic:.189}],398:[.691,0,.597,{ic:.104}],402:[.705,0,.306,{ic:.189}],416:[.778,.022,.736,{ic:.043}],417:[.528,.011,.5,{ic:.082}],431:[.778,.022,.687,{ic:.168}],432:[.528,.011,.544,{ic:.102}],461:[.87,0,.667,{ic:.007}],462:[.697,.011,.481,{ic:.062}],463:[.87,0,.278,{ic:.202}],464:[.697,0,.239,{ic:.183}],465:[.87,.022,.736,{ic:.026}],466:[.697,.011,.5,{ic:.053}],467:[.87,.022,.688,{ic:.053}],468:[.697,.011,.517,{ic:.044}],471:[.99,.022,.688,{ic:.057}],472:[.829,.011,.517,{ic:.108}],473:[.992,.022,.688,{ic:.053}],474:[.831,.011,.517,{ic:.074}],475:[.99,.022,.688,{ic:.053}],476:[.829,.011,.517,{ic:.012}],477:[.461,.011,.444,{ic:.024}],486:[.87,.011,.667,{ic:.062}],487:[.697,.206,.5,{ic:.071}],490:[.716,.212,.736,{ic:.026}],491:[.461,.212,.5,{ic:.022}],496:[.697,.205,.267,{ic:.176}],500:[.868,.011,.667,{ic:.062}],501:[.694,.206,.5,{ic:.071}],506:[1.019,0,.667,{ic:.025}],507:[.842,.011,.481,{ic:.081}],508:[.868,0,.861,{ic:.091}],509:[.694,.011,.722,{ic:.028}],510:[.868,.065,.778,{ic:.026}],511:[.694,.112,.5,{ic:.068}],512:[.913,0,.667],513:[.694,.011,.481,{ic:.013}],516:[.913,0,.597,{ic:.09}],517:[.694,.011,.444,{ic:.032}],520:[.913,0,.278,{ic:.161}],521:[.694,0,.239,{ic:.134}],524:[.913,.022,.736,{ic:.026}],525:[.694,.011,.5,{ic:.022}],528:[.913,0,.646,{ic:.054}],529:[.694,0,.342,{ic:.083}],532:[.913,.022,.688,{ic:.053}],533:[.694,.011,.517,{ic:.012}],536:[.716,.266,.556,{ic:.051}],537:[.461,.266,.383,{ic:.051}],538:[.688,.266,.681,{ic:.109}],539:[.571,.266,.361,{ic:.049}],7692:[.694,.2,.722,{ic:.024}],7693:[.694,.2,.517,{ic:.065}],7694:[.694,.187,.722,{ic:.024}],7695:[.694,.187,.517,{ic:.065}],7716:[.694,.2,.708,{ic:.053}],7717:[.694,.2,.517],7718:[.816,0,.708,{ic:.053}],7719:[.816,0,.517,{ic:.041}],7722:[.694,.244,.708,{ic:.053}],7723:[.694,.244,.517],7726:[.99,0,.278,{ic:.262}],7727:[.829,0,.239,{ic:.247}],7734:[.694,.2,.542],7735:[.694,.2,.239,{ic:.065}],7736:[.82,.2,.542],7737:[.82,.2,.239,{ic:.247}],7746:[.694,.2,.875,{ic:.047}],7747:[.455,.2,.794],7748:[.825,0,.708,{ic:.051}],7749:[.655,0,.517],7750:[.694,.2,.708,{ic:.051}],7751:[.455,.2,.517],7768:[.825,0,.646,{ic:.054}],7769:[.655,0,.342,{ic:.082}],7770:[.694,.2,.646,{ic:.054}],7771:[.455,.2,.342,{ic:.082}],7772:[.82,.2,.646,{ic:.054}],7773:[.643,.2,.342,{ic:.147}],7778:[.716,.2,.556,{ic:.051}],7779:[.461,.2,.383,{ic:.051}],7788:[.688,.2,.681,{ic:.109}],7789:[.571,.2,.361,{ic:.049}],7790:[.688,.187,.681,{ic:.109}],7791:[.571,.187,.361,{ic:.049}],7808:[.868,0,.944,{ic:.133}],7809:[.694,0,.683,{ic:.079}],7810:[.868,0,.944,{ic:.133}],7811:[.694,0,.683,{ic:.079}],7812:[.816,0,.944,{ic:.133}],7813:[.651,0,.683,{ic:.079}],7826:[.694,.2,.611,{ic:.091}],7827:[.444,.2,.435,{ic:.059}],7831:[.693,.011,.361,{ic:.053}],7840:[.694,.2,.667],7841:[.461,.2,.481],7842:[.951,0,.667],7843:[.718,.011,.481],7844:[1.027,0,.667,{ic:.027}],7845:[.816,.011,.481,{ic:.075}],7846:[1.027,0,.667,{ic:.001}],7847:[.816,.011,.481,{ic:.05}],7848:[1.1,0,.667,{ic:.079}],7849:[.889,.011,.481,{ic:.127}],7850:[1.044,0,.667,{ic:.055}],7851:[.833,.011,.481,{ic:.103}],7852:[.87,.2,.667],7853:[.697,.2,.481,{ic:.026}],7854:[1.081,0,.667,{ic:.04}],7855:[.857,.011,.481,{ic:.085}],7856:[1.081,0,.667,{ic:.031}],7857:[.857,.011,.481,{ic:.077}],7858:[1.154,0,.667,{ic:.031}],7859:[.93,.011,.481,{ic:.077}],7860:[1.098,0,.667,{ic:.066}],7861:[.874,.011,.481,{ic:.112}],7862:[.924,.2,.667,{ic:.031}],7863:[.7,.2,.481,{ic:.077}],7864:[.691,.2,.597,{ic:.09}],7865:[.461,.2,.444,{ic:.028}],7866:[.951,0,.597,{ic:.09}],7867:[.718,.011,.444,{ic:.028}],7868:[.855,0,.597,{ic:.09}],7869:[.67,.011,.444,{ic:.087}],7870:[1.027,0,.597,{ic:.09}],7871:[.816,.011,.444,{ic:.094}],7872:[1.027,0,.597,{ic:.09}],7873:[.816,.011,.444,{ic:.069}],7874:[1.1,0,.597,{ic:.115}],7875:[.889,.011,.444,{ic:.146}],7876:[1.044,0,.597,{ic:.091}],7877:[.833,.011,.444,{ic:.122}],7878:[.87,.2,.597,{ic:.09}],7879:[.697,.2,.444,{ic:.045}],7880:[.951,0,.278,{ic:.145}],7881:[.718,0,.239,{ic:.115}],7882:[.694,.2,.278,{ic:.053}],7883:[.655,.2,.239,{ic:.064}],7884:[.716,.2,.736,{ic:.026}],7885:[.461,.2,.5,{ic:.022}],7886:[.951,.022,.736,{ic:.026}],7887:[.718,.011,.5,{ic:.022}],7888:[1.027,.022,.736,{ic:.026}],7889:[.816,.011,.5,{ic:.066}],7890:[1.027,.022,.736,{ic:.026}],7891:[.816,.011,.5,{ic:.041}],7892:[1.1,.022,.736,{ic:.045}],7893:[.889,.011,.5,{ic:.118}],7894:[1.044,.022,.736,{ic:.026}],7895:[.833,.011,.5,{ic:.094}],7896:[.87,.2,.736,{ic:.026}],7897:[.697,.2,.5,{ic:.022}],7898:[.868,.022,.736,{ic:.043}],7899:[.694,.011,.5,{ic:.082}],7900:[.868,.022,.736,{ic:.043}],7901:[.694,.011,.5,{ic:.082}],7902:[.951,.022,.736,{ic:.043}],7903:[.718,.011,.5,{ic:.082}],7904:[.855,.022,.736,{ic:.043}],7905:[.67,.011,.5,{ic:.082}],7906:[.778,.2,.736,{ic:.043}],7907:[.528,.2,.5,{ic:.082}],7908:[.694,.2,.688,{ic:.053}],7909:[.444,.2,.517,{ic:.012}],7910:[.951,.022,.688,{ic:.053}],7911:[.718,.011,.517,{ic:.012}],7912:[.868,.022,.687,{ic:.168}],7913:[.694,.011,.544,{ic:.102}],7914:[.868,.022,.687,{ic:.168}],7915:[.694,.011,.544,{ic:.102}],7916:[.951,.022,.687,{ic:.168}],7917:[.718,.011,.544,{ic:.102}],7918:[.855,.022,.687,{ic:.168}],7919:[.67,.011,.544,{ic:.102}],7920:[.778,.2,.687,{ic:.168}],7921:[.528,.2,.544,{ic:.102}],7922:[.868,0,.667,{ic:.144}],7923:[.694,.205,.461,{ic:.079}],7924:[.694,.2,.667,{ic:.144}],7925:[.444,.205,.461,{ic:.079}],7926:[.951,0,.667,{ic:.144}],7927:[.718,.205,.461,{ic:.079}],7928:[.855,0,.667,{ic:.144}],7929:[.67,.205,.461,{ic:.079}]}}},"MM"),{},["MJX-MM-SSLI"]);MathJax.loader&&MathJax.loader.checkVersion("[mathjax-modern]/chtml/dynamic/sans-serif-i","4.0.0-beta.7","dynamic-font")})();