(()=>{"use strict";const c=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-modern"].chtml_ts.MathJaxModernFont.dynamicSetup("","script",c({S:{normal:{119964:[.713,.012,.885,{ic:.17}],8492:[.73,.006,.972,{ic:.144}],119966:[.744,.015,.751,{ic:.123}],119967:[.733,.008,.923,{ic:.127}],8496:[.746,.015,.68,{ic:.144}],8497:[.724,.019,.85,{ic:.26}],119970:[.74,.015,.703,{ic:.168}],8459:[.732,.012,.961,{ic:.2}],8464:[.729,.015,.937,{ic:.186}],119973:[.725,.199,.944,{ic:.151}],119974:[.721,.015,1.016,{ic:.165}],8466:[.759,.01,1.025,{ic:.138}],8499:[.726,.005,1.046,{ic:.14}],119977:[.723,.013,.988,{ic:.185}],119978:[.716,.015,.711,{ic:.129}],119979:[.718,.019,.949,{ic:.089}],119980:[.716,.033,.708,{ic:.13}],8475:[.723,.017,.946,{ic:.144}],119982:[.741,.015,.939,{ic:.229}],119983:[.745,.013,.828,{ic:.308}],119984:[.727,.006,.836,{ic:.071}],119985:[.727,.01,.784,{ic:.287}],119986:[.722,.012,.862,{ic:.325}],119987:[.721,.012,.908,{ic:.187}],119988:[.723,.249,.908,{ic:.177}],119989:[.719,.005,.978,{ic:.1}],119990:[.48,.012,.774],119991:[.854,.014,.747,{ic:.08}],119992:[.48,.02,.54,{ic:.029}],119993:[.785,.01,.733,{ic:.167}],8495:[.411,.019,.516],119995:[.853,.341,.957,{ic:.166}],8458:[.41,.344,.896],119997:[.847,.013,.681,{ic:.164}],119998:[.708,.022,.438,{ic:.07}],119999:[.708,.35,.872,{ic:.114}],12e4:[.854,.017,.845,{ic:.032}],120001:[.86,.017,.737,{ic:.054}],120002:[.477,.016,1.147],120003:[.477,.015,.843],8500:[.411,.019,.578],120005:[.477,.401,1.143],120006:[.48,.401,.817],120007:[.469,0,.747],120008:[.603,.015,.542],120009:[.714,.02,.656],120010:[.459,.021,.745],120011:[.469,.022,.652],120012:[.469,.022,.959],120013:[.479,.02,.817],120014:[.459,.403,.991],120015:[.498,.017,.781],8467:[.705,.012,.417],8472:[.453,.216,.636]},script:{305:[.451,.022,.455],567:[.451,.343,.943]}},SB:{normal:{120016:[.713,.012,.881,{ic:.17}],120017:[.732,.006,.994,{ic:.126}],120018:[.744,.015,.754,{ic:.12}],120019:[.735,.008,.91,{ic:.131}],120020:[.746,.015,.693,{ic:.131}],120021:[.726,.019,.862,{ic:.259}],120022:[.74,.015,.721,{ic:.148}],120023:[.733,.012,.95,{ic:.2}],120024:[.73,.015,.929,{ic:.187}],120025:[.726,.194,.898,{ic:.187}],120026:[.722,.015,.982,{ic:.187}],120027:[.758,.009,1.019,{ic:.133}],120028:[.727,.005,1.055,{ic:.121}],120029:[.723,.013,.975,{ic:.187}],120030:[.717,.015,.709,{ic:.129}],120031:[.717,.015,.949,{ic:.093}],120032:[.717,.032,.709,{ic:.129}],120033:[.724,.015,.951,{ic:.133}],120034:[.741,.015,.926,{ic:.231}],120035:[.747,.013,.814,{ic:.313}],120036:[.728,.006,.816,{ic:.089}],120037:[.728,.012,.777,{ic:.287}],120038:[.723,.011,.887,{ic:.286}],120039:[.722,.012,.898,{ic:.187}],120040:[.736,.242,.898,{ic:.177}],120041:[.721,.005,.969,{ic:.1}],120042:[.48,.013,.782],120043:[.846,.015,.775,{ic:.064}],120044:[.48,.02,.633],120045:[.779,.011,.951],120046:[.48,.02,.633],120047:[.844,.332,.903,{ic:.214}],120048:[.479,.38,1.003],120049:[.839,.013,.891],120050:[.693,.02,.403,{ic:.165}],120051:[.693,.329,.807,{ic:.162}],120052:[.846,.016,.823,{ic:.06}],120053:[.8,.017,.718,{ic:.064}],120054:[.475,.015,1.137],120055:[.473,.012,.848],120056:[.48,.02,.699],120057:[.477,.378,1.129],120058:[.48,.381,.828],120059:[.469,0,.759],120060:[.596,.018,.576],120061:[.704,.02,.678],120062:[.462,.02,.761],120063:[.47,.02,.674],120064:[.47,.02,.968],120065:[.479,.02,.835],120066:[.463,.378,.992],120067:[.494,.019,.799]},"bold-script":{305:[.431,.019,.444],567:[.431,.307,.87]}}},"MM"));MathJax.loader&&MathJax.loader.checkVersion("[mathjax-modern]/chtml/dynamic/script","4.0.0-beta.7","dynamic-font")})();