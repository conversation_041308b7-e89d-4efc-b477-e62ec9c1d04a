(()=>{"use strict";const c=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-modern"].chtml_ts.MathJaxModernFont.dynamicSetup("","PUA",c({PU:{normal:{59264:[.705,.29,.785],59265:[.738,.206,.5],59266:[.683,.29,.778],59267:[.694,.29,.528],59268:[.683,.29,.625],59269:[.694,.29,.278],59270:[.683,.29,.75],59271:[.442,.29,.556],59272:[.683,.29,.736],59273:[.442,.29,.392],59395:[.705,0,.816],59908:[.873,-.518,.5],59909:[-.061,.233,.5],59910:[-.061,.233,.5],59913:[.873,-.518,.5],59915:[.899,-.518,.5],59917:[.69,-.518,.5],59920:[.823,-.518,.5],59927:[.832,-.516,.5],59930:[.832,-.516,.5],59932:[.858,-.516,.5],59934:[.782,-.516,.5],59935:[-.066,.29,.5],59942:[.697,-.511,.5,{sk:-.024}],59946:[.87,-.557,.5],59948:[.83,-.557,.5],59951:[.87,-.557,.5],59957:[.709,-.5,.5],59962:[-.131,.162,.5],59966:[-.131,.162,.5],59970:[.888,-.529,.75],59973:[-.1,.193,.5],60163:[.909,.211,.75],60164:[.698,.211,.5],60168:[.683,0,.683],60175:[.683,0,.683],60176:[0,0,0],60177:[0,0,0],60178:[0,0,0],60182:[.603,.025,.5],60185:[-.094,.2,.278],60190:[.909,.211,.681],60191:[.698,.211,.444],60200:[.705,.022,1.111],60201:[.5,.256,.472],60203:[.728,.045,.785],60209:[.341,-.088,.333],60213:[.909,.211,.361],60214:[.698,.211,.278],60218:[.909,.022,.514],60219:[.698,.205,.306],60224:[.746,.05,1,{ic:.011}],60232:[.909,.211,.778],60233:[.698,.211,.5],60237:[.347,.056,.392],60257:[.392,-.28,.278],60259:[.639,-.491,.333,{ic:.26}],60261:[.666,-.539,.278,{ic:.233}],60270:[.683,.233,.75],60271:[.442,.233,.556],60424:[.829,0,.75],60425:[.84,0,.556],60426:[.829,0,.625],60427:[.84,0,.278,{ic:.028}],60428:[.829,0,.722],60429:[.76,.011,.389],60430:[.831,0,.722],60432:[.878,.194,.778],60433:[.625,.194,.5],63166:[.442,.205,.306],63198:[.277,-.255,.75]}},PUB:{bold:{59395:[.7,0,.945],59908:[.907,-.514,.575],59909:[-.028,.215,.575],59910:[-.028,.215,.575],59913:[.907,-.514,.575],59915:[.909,-.514,.575],59917:[.7,-.513,.575],59920:[.864,-.514,.575],59927:[.874,-.503,.575],59930:[.874,-.503,.575],59932:[.876,-.503,.575],59934:[.831,-.503,.575],59935:[-.033,.295,.575],59942:[.704,-.509,.575],59946:[.9,-.537,.575],59948:[.884,-.537,.575],59951:[.9,-.537,.575],59957:[.703,-.51,.575],59962:[-.096,.148,.575],59966:[-.096,.148,.575],59970:[.895,-.521,.869],59973:[-.068,.176,.575],60163:[.909,.206,.869],60164:[.704,.206,.559],60168:[.686,0,.686],60175:[.686,0,.686],60176:[0,0,0],60177:[0,0,0],60178:[0,0,0],60182:[.608,.024,.575],60185:[-.044,.2,.319],60190:[.909,.206,.756],60191:[.704,.206,.527],60200:[.697,.011,1.278],60201:[.5,.269,.543],60203:[.758,.072,.904],60209:[.406,-.085,.383],60213:[.909,.206,.436],60214:[.704,.206,.319],60218:[.909,.011,.594],60219:[.704,.2,.351],60224:[.775,.079,1.15,{ic:.017}],60232:[.909,.206,.864],60233:[.704,.206,.575],60237:[.347,.056,.45],60257:[.402,-.278,.319,{ic:.003}],60259:[.654,-.499,.397,{ic:.309}],60261:[.668,-.542,.319,{ic:.277}],60270:[.686,.215,.885],60271:[.45,.215,.639],60424:[.836,0,.9],60425:[.844,0,.639],60426:[.836,0,.692],60427:[.844,0,.319,{ic:.032}],60428:[.836,0,.8],60429:[.794,.006,.447],60430:[.868,0,.8],60432:[.881,.194,.864],60433:[.639,.194,.575],63166:[.45,.2,.351],63198:[.292,-.256,.863]}},PUI:{italic:{59395:[.705,.205,.792,{ic:.041}],59908:[.865,-.516,.511,{ic:.056}],59909:[-.062,.232,.511],59910:[-.062,.232,.511],59913:[.865,-.516,.511,{ic:.052}],59915:[.895,-.516,.511,{ic:.052}],59917:[.686,-.516,.511,{ic:.021}],59920:[.827,-.516,.511,{ic:.101}],59927:[.816,-.518,.511,{ic:.043}],59930:[.816,-.518,.511,{ic:.017}],59932:[.846,-.518,.511,{ic:.121}],59934:[.778,-.518,.511,{ic:.088}],59935:[-.066,.29,.511],59942:[.696,-.506,.511],59946:[.867,-.553,.511,{ic:.09}],59948:[.816,-.553,.511,{ic:.086}],59951:[.867,-.553,.511,{ic:.032}],59957:[.705,-.496,.511],59962:[-.132,.162,.511],59966:[-.132,.162,.511],59970:[.884,-.529,.831],59973:[-.096,.197,.511],60163:[.906,.211,.743],60164:[.696,.211,.511,{ic:.028}],60168:[.683,0,.683],60175:[.683,0,.683],60176:[0,0,0],60177:[0,0,0],60178:[0,0,0],60182:[.603,.025,.511,{ic:.053}],60185:[-.094,.2,.307],60190:[.906,.211,.678,{ic:.065}],60191:[.696,.211,.46,{ic:.053}],60200:[.705,.022,1.124,{ic:.07}],60201:[.5,.256,.511],60203:[.728,.045,.774,{ic:.038}],60209:[.327,-.077,.358,{ic:.001}],60213:[.906,.211,.386,{ic:.162}],60214:[.696,.211,.307,{ic:.13}],60218:[.906,.022,.525,{ic:.149}],60219:[.696,.205,.307,{ic:.13}],60224:[.736,.039,.971,{ic:.159}],60232:[.906,.211,.767,{ic:.021}],60233:[.696,.211,.511,{ic:.028}],60237:[.347,.056,.408],60257:[.392,-.28,.256,{ic:.087}],60259:[.639,-.49,.358,{ic:.398}],60261:[.666,-.538,.307,{ic:.385}],60270:[.683,.232,.743,{ic:.109}],60271:[.442,.232,.537,{ic:.024}],60424:[.837,0,.743,{ic:.109}],60425:[.848,.011,.511,{ic:.106}],60426:[.837,0,.627],60427:[.848,.011,.256,{ic:.233}],60428:[.837,0,.716,{ic:.09}],60429:[.798,.011,.332,{ic:.183}],60430:[.832,0,.716,{ic:.09}],60432:[.874,.19,.767,{ic:.021}],60433:[.621,.19,.511],63166:[.442,.205,.307,{ic:.014}],63198:[.277,-.255,.767,{ic:.027}]}},PUBI:{"bold-italic":{59395:[.702,.202,.935,{ic:.039}],59908:[.893,-.512,.591,{ic:.038}],59909:[-.036,.215,.591],59910:[-.036,.215,.591],59913:[.893,-.512,.591,{ic:.038}],59915:[.908,-.512,.591,{ic:.038}],59917:[.69,-.511,.591,{ic:.006}],59920:[.865,-.512,.591,{ic:.094}],59927:[.863,-.497,.591,{ic:.025}],59930:[.863,-.497,.591,{ic:.01}],59932:[.878,-.497,.591,{ic:.116}],59934:[.835,-.497,.591,{ic:.086}],59935:[-.051,.307,.591],59942:[.699,-.503,.591],59946:[.899,-.535,.591,{ic:.073}],59948:[.886,-.535,.591,{ic:.095}],59951:[.899,-.535,.591,{ic:.045}],59957:[.702,-.5,.591],59962:[-.101,.151,.591],59966:[-.101,.151,.591],59970:[.904,-.534,.949],59973:[-.066,.185,.591],60163:[.918,.204,.866],60164:[.699,.204,.591,{ic:.024}],60168:[.686,0,.686],60175:[.686,0,.686],60176:[0,0,0],60177:[0,0,0],60178:[0,0,0],60182:[.608,.024,.591,{ic:.056}],60185:[-.053,.2,.356],60190:[.918,.204,.757,{ic:.048}],60191:[.699,.204,.532,{ic:.035}],60200:[.703,.017,1.3,{ic:.053}],60201:[.5,.275,.591],60203:[.758,.072,.895,{ic:.013}],60209:[.384,-.067,.414,{ic:.001}],60213:[.918,.204,.472,{ic:.137}],60214:[.699,.204,.356,{ic:.122}],60218:[.918,.017,.611,{ic:.111}],60219:[.699,.202,.356,{ic:.122}],60224:[.762,.065,1.121,{ic:.16}],60232:[.918,.204,.855,{ic:.01}],60233:[.699,.204,.591,{ic:.005}],60237:[.347,.056,.475],60257:[.402,-.278,.297,{ic:.111}],60259:[.654,-.508,.433,{ic:.457}],60261:[.668,-.542,.356,{ic:.432}],60270:[.686,.215,.881,{ic:.107}],60271:[.452,.215,.621,{ic:.034}],60424:[.86,0,.896,{ic:.107}],60425:[.868,.008,.591,{ic:.094}],60426:[.86,0,.698],60427:[.868,.008,.297,{ic:.24}],60428:[.86,0,.796,{ic:.076}],60429:[.818,.008,.385,{ic:.184}],60430:[.873,0,.796,{ic:.076}],60432:[.881,.194,.855,{ic:.01}],60433:[.639,.194,.591,{ic:.004}],63166:[.452,.202,.356,{ic:.019}],63198:[.292,-.256,.887,{ic:.021}]}}},"MM"),{},["MJX-MM-PU","MJX-MM-PUB","MJX-MM-PUI","MJX-MM-PUBI"]);MathJax.loader&&MathJax.loader.checkVersion("[mathjax-modern]/chtml/dynamic/PUA","4.0.0-beta.7","dynamic-font")})();