(()=>{"use strict";const i=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-modern"].chtml_ts.MathJaxModernFont.dynamicSetup("","sans-serif-ex",i({SSX:{"sans-serif":{161:[.5,.194,.319],162:[.508,.064,.444],164:[.492,-.009,.778],166:[.75,.25,.278],169:[.694,0,.694],170:[.716,-.311,.372],171:[.439,0,.5],173:[.251,-.193,.333],174:[.694,0,.694],186:[.716,-.311,.419],187:[.439,0,.5],188:[.716,0,.788],189:[.716,0,.788],190:[.716,0,.788],191:[.5,.205,.472],3647:[.758,.064,.667],8218:[.084,.124,.278],8222:[.084,.124,.472],8226:[.407,-.121,.778],8240:[.75,.056,1.154],8241:[.75,.056,1.471],8249:[.439,0,.333],8250:[.439,0,.333],8251:[.492,-.009,.778],8253:[.726,0,.472],8255:[-.07,.194,.597],8256:[.692,-.568,.597],8261:[.751,.309,.361],8262:[.751,.249,.361],8274:[.751,-.001,.5],8276:[-.07,.194,.597],8353:[.79,.096,.639],8358:[.694,0,.708],8361:[.694,0,.944],8363:[.694,.064,.517],8369:[.694,0,.639],8451:[.717,.021,.861],8470:[.695,0,.819],8471:[.694,0,.694],8478:[.694,0,.646],8480:[.694,-.252,.858],8482:[.698,-.282,.933],8494:[.712,.01,.669],8960:[.583,.083,.778],9250:[.694,.011,.517],9251:[.24,.118,.5],64256:[.705,0,.583,{ic:.041}],64257:[.705,0,.536],64258:[.705,0,.536],64259:[.705,0,.814],64260:[.705,0,.814],184:[-.024,.192,.444],702:[.638,-.287,.396],703:[.638,-.287,.396],731:[0,.212,.5],733:[.694,-.527,.5],777:[.718,-.503,0,{dx:.24}],779:[.694,-.527,0,{dx:.228}],783:[.694,-.527,0,{dx:.252}],785:[.7,-.522,0,{dx:.239}],803:[-.111,.2,0,{dx:.24}],806:[-.079,.266,0,{dx:.24}],814:[-.066,.244,0,{dx:.239}],815:[-.066,.244,0,{dx:.239}],816:[-.095,.214,0,{dx:.239}],817:[-.123,.187,0,{dx:.239}],818:[-.123,.187,0,{dx:.239}],9834:[.723,.03,.611],9901:[.473,-.029,.5,{ic:.089}],9902:[.615,.113,.5,{ic:.2}],59395:[.705,0,.792],59908:[.857,-.522,.5],59909:[-.066,.244,.5],59910:[-.066,.244,.5],59913:[.857,-.522,.5],59915:[.93,-.522,.5],59917:[.7,-.522,.5],59920:[.874,-.522,.5],59927:[.816,-.525,.5],59930:[.816,-.525,.5],59932:[.889,-.525,.5],59934:[.833,-.525,.5],59935:[-.079,.266,.5],59942:[.694,-.527,.5],59946:[.829,-.571,.5],59948:[.831,-.571,.5],59951:[.829,-.571,.5],59957:[.718,-.503,.5],59962:[-.123,.187,.5],59966:[-.123,.187,.5],59970:[.842,-.517,.667],59973:[-.095,.214,.5],60163:[.868,.212,.667],60164:[.694,.212,.481],60168:[.694,0,.694],60175:[.694,0,.694],60176:[0,0,0],60177:[0,0,0],60178:[0,0,0],60182:[.626,.027,.5],60185:[-.111,.2,.278],60190:[.868,.212,.597],60191:[.694,.212,.444],60200:[.716,.022,1.111],60201:[.5,.226,.472],60203:[.79,.096,.667],60209:[.345,-.086,.333],60213:[.868,.212,.278,{ic:.102}],60214:[.694,.212,.239,{ic:.053}],60218:[.868,.022,.472,{ic:.116}],60219:[.694,.205,.267,{ic:.053}],60224:[.77,.047,1,{ic:.012}],60232:[.868,.212,.736],60233:[.694,.212,.5],60237:[.347,.056,.392],60257:[.398,-.266,.239,{ic:.006}],60259:[.639,-.477,.294,{ic:.305}],60261:[.692,-.568,.239,{ic:.278}],60270:[.694,.244,.688],60271:[.444,.244,.517],60424:[.855,0,.708],60425:[.855,0,.517],60426:[.855,0,.542],60427:[.855,0,.239,{ic:.047}],60428:[.855,0,.681],60429:[.732,.011,.361],60430:[.816,0,.681],60432:[.881,.186,.736],60433:[.631,.186,.5],63166:[.444,.205,.267],63198:[.305,-.244,.75],8592:[.511,.009,1],8593:[.694,.194,.5],8594:[.511,.009,1],8595:[.694,.194,.5],8730:[.763,.104,.472,{ic:.05}],8738:[.583,.083,.778],9001:[.751,.249,.389],9002:[.751,.249,.389],9702:[.407,-.121,.778],10214:[.751,.249,.403],10215:[.751,.249,.403]}},SSBX:{"bold-sans-serif":{161:[.5,.194,.367],162:[.558,.1,.489],164:[.523,.022,.856],166:[.75,.25,.319],169:[.694,0,.694],170:[.716,-.276,.416],171:[.5,0,.55],173:[.265,-.193,.367],174:[.694,0,.694],186:[.716,-.276,.461],187:[.5,0,.55],188:[.716,.022,.88],189:[.716,.022,.978],190:[.716,.022,.88],191:[.5,.205,.519],3647:[.794,.1,.733],8218:[.129,.108,.306],8222:[.129,.108,.529],8226:[.418,-.114,.856],8240:[.75,.056,1.4],8241:[.75,.056,1.845],8249:[.5,0,.319],8250:[.5,0,.319],8251:[.523,.022,.856],8253:[.744,0,.519],8255:[-.063,.194,.649],8256:[.694,-.563,.649],8261:[.751,.297,.397],8262:[.751,.249,.397],8274:[.751,-.001,.55],8276:[-.063,.194,.649],8353:[.844,.15,.703],8358:[.694,0,.794],8361:[.694,0,1.039],8363:[.694,.1,.561],8369:[.694,0,.703],8451:[.717,.021,.947],8470:[.695,-.001,.916],8471:[.694,0,.694],8478:[.694,0,.703],8480:[.694,-.252,.953],8482:[.698,-.282,1.027],8494:[.712,.01,.758],8960:[.617,.117,.856],9250:[.694,.011,.561],9251:[.275,.122,.55],64256:[.705,0,.642,{ic:.045}],64257:[.705,0,.586],64258:[.705,0,.586],64259:[.705,0,.892],64260:[.705,0,.892],184:[-.024,.192,.489],702:[.628,-.267,.436],703:[.628,-.267,.436],731:[.018,.213,.55],733:[.694,-.537,.55],777:[.719,-.513,0,{dx:.262}],779:[.694,-.537,0,{dx:.25}],783:[.694,-.537,0,{dx:.274}],785:[.687,-.545,0,{dx:.262}],803:[-.085,.2,0,{dx:.263}],806:[-.079,.292,0,{dx:.262}],814:[-.071,.213,0,{dx:.262}],815:[-.071,.213,0,{dx:.262}],816:[-.076,.207,0,{dx:.262}],817:[-.099,.184,0,{dx:.262}],818:[-.099,.184,0,{dx:.262}],9834:[.723,.052,.672],9901:[.48,-.022,.55,{ic:.112}],9902:[.624,.122,.55,{ic:.234}],59395:[.705,0,.864],59908:[.866,-.545,.55],59909:[-.071,.213,.55],59910:[-.071,.213,.55],59913:[.866,-.545,.55],59915:[.923,-.545,.55],59917:[.687,-.545,.55],59920:[.888,-.545,.55],59927:[.841,-.535,.55],59930:[.841,-.535,.55],59932:[.898,-.535,.55],59934:[.863,-.535,.55],59935:[-.079,.292,.55],59942:[.694,-.537,.55],59946:[.858,-.564,.55],59948:[.846,-.564,.55],59951:[.858,-.564,.55],59957:[.719,-.513,.55],59962:[-.099,.184,.55],59966:[-.099,.184,.55],59970:[.863,-.526,.733],59973:[-.076,.207,.55],60163:[.885,.213,.733,{ic:.003}],60164:[.694,.213,.525],60168:[.694,0,.694],60175:[.694,0,.694],60176:[0,0,0],60177:[0,0,0],60178:[0,0,0],60182:[.631,.025,.55],60185:[-.085,.2,.306],60190:[.885,.213,.642],60191:[.694,.213,.511],60200:[.716,.022,1.222],60201:[.5,.244,.519],60203:[.844,.15,.733],60209:[.356,-.053,.367],60213:[.885,.213,.331,{ic:.072}],60214:[.694,.213,.256,{ic:.039}],60218:[.885,.022,.519,{ic:.075}],60219:[.694,.205,.286,{ic:.039}],60224:[.797,.073,1.1,{ic:.024}],60232:[.885,.213,.794],60233:[.694,.213,.55],60237:[.347,.056,.485],60257:[.416,-.292,.256,{ic:.061}],60259:[.667,-.486,.336,{ic:.367}],60261:[.694,-.563,.256,{ic:.334}],60270:[.694,.213,.764],60271:[.458,.213,.561],60424:[.877,0,.794],60425:[.877,0,.561],60426:[.877,0,.581],60427:[.877,0,.256,{ic:.055}],60428:[.877,0,.733],60429:[.772,.011,.404],60430:[.849,0,.733],60432:[.889,.194,.794],60433:[.653,.194,.55],63166:[.458,.205,.286],63198:[.319,-.247,.825],8592:[.547,.046,1.1],8593:[.694,.194,.622],8594:[.548,.045,1.1],8595:[.694,.194,.622],8730:[.808,.082,.519,{ic:.075}],8738:[.617,.117,.856],9001:[.751,.249,.428],9002:[.751,.249,.428],9702:[.418,-.114,.856],10214:[.751,.249,.443],10215:[.751,.249,.443]}},SSIX:{"sans-serif-italic":{161:[.5,.194,.319],162:[.508,.064,.444,{ic:.055}],164:[.492,-.009,.778],166:[.75,.25,.278,{ic:.035}],169:[.694,0,.694,{ic:.082}],170:[.716,-.311,.372,{ic:.093}],171:[.439,0,.5,{ic:.037}],173:[.251,-.193,.333],174:[.694,0,.694,{ic:.082}],186:[.716,-.311,.419,{ic:.094}],187:[.439,0,.5],188:[.716,0,.788,{ic:.012}],189:[.716,0,.788],190:[.716,0,.788,{ic:.012}],191:[.5,.205,.472],3647:[.758,.064,.667,{ic:.03}],8218:[.084,.124,.278],8222:[.084,.124,.472],8226:[.407,-.121,.778],8240:[.75,.056,1.154],8241:[.75,.056,1.471],8249:[.439,0,.333,{ic:.037}],8250:[.439,0,.333],8251:[.492,-.009,.778],8253:[.726,0,.472,{ic:.051}],8255:[-.07,.194,.597],8256:[.692,-.568,.597,{ic:.043}],8261:[.751,.309,.361,{ic:.107}],8262:[.751,.249,.361,{ic:.064}],8274:[.751,-.001,.5,{ic:.1}],8276:[-.07,.194,.597],8353:[.79,.096,.639,{ic:.083}],8358:[.694,0,.708,{ic:.051}],8361:[.694,0,.944,{ic:.133}],8363:[.694,.064,.517,{ic:.098}],8369:[.694,0,.639,{ic:.052}],8451:[.717,.021,.861,{ic:.083}],8470:[.695,0,.819,{ic:.011}],8471:[.694,0,.694,{ic:.082}],8478:[.694,0,.646,{ic:.054}],8480:[.694,-.252,.858,{ic:.051}],8482:[.698,-.282,.933,{ic:.066}],8494:[.712,.01,.669,{ic:.062}],8960:[.583,.083,.778,{ic:.063}],9250:[.694,.011,.517,{ic:.018}],9251:[.24,.118,.5,{ic:.005}],64256:[.705,0,.583,{ic:.189}],64257:[.705,0,.536,{ic:.069}],64258:[.705,0,.536,{ic:.065}],64259:[.705,0,.814,{ic:.069}],64260:[.705,0,.814,{ic:.065}],184:[-.024,.192,.444],702:[.638,-.287,.397,{ic:.046}],703:[.638,-.287,.397,{ic:.068}],731:[0,.212,.5],733:[.694,-.527,.5,{ic:.064}],777:[.718,-.503,0,{dx:.099}],779:[.694,-.527,0,{dx:.098}],783:[.694,-.527,0,{dx:.122}],785:[.7,-.522,0,{dx:.127}],803:[-.111,.2,0,{dx:.274}],806:[-.079,.266,0,{dx:.277}],814:[-.066,.244,0,{dx:.255}],815:[-.066,.244,0,{dx:.29}],816:[-.095,.214,0,{dx:.272}],817:[-.123,.187,0,{dx:.273}],818:[-.123,.187,0,{dx:.273}],9834:[.723,.03,.611],9901:[.473,-.029,.5,{ic:.147}],9902:[.615,.113,.5,{ic:.259}],59395:[.705,0,.792,{ic:.051}],59908:[.857,-.522,.5,{ic:.076}],59909:[-.066,.244,.5],59910:[-.066,.244,.5],59913:[.857,-.522,.5,{ic:.068}],59915:[.93,-.522,.5,{ic:.068}],59917:[.7,-.522,.5,{ic:.034}],59920:[.874,-.522,.5,{ic:.103}],59927:[.816,-.525,.5,{ic:.066}],59930:[.816,-.525,.5,{ic:.041}],59932:[.889,-.525,.5,{ic:.118}],59934:[.833,-.525,.5,{ic:.094}],59935:[-.079,.266,.5],59942:[.694,-.527,.5,{ic:.004}],59946:[.829,-.571,.5,{ic:.117}],59948:[.831,-.571,.5,{ic:.083}],59951:[.829,-.571,.5,{ic:.001}],59957:[.718,-.503,.5],59962:[-.123,.187,.5],59966:[-.123,.187,.5],59970:[.842,-.517,.738],59973:[-.095,.214,.5],60163:[.868,.212,.667,{ic:.041}],60164:[.694,.212,.481,{ic:.048}],60168:[.694,0,.694,{ic:.082}],60175:[.694,0,.694,{ic:.082}],60176:[0,0,0],60177:[0,0,0],60178:[0,0,0],60182:[.626,.027,.5,{ic:.036}],60185:[-.111,.2,.278],60190:[.868,.212,.597,{ic:.09}],60191:[.694,.212,.444,{ic:.067}],60200:[.716,.022,1.111,{ic:.052}],60201:[.5,.226,.472],60203:[.79,.096,.667,{ic:.066}],60209:[.345,-.086,.333,{ic:.017}],60213:[.868,.212,.278,{ic:.286}],60214:[.694,.212,.239,{ic:.201}],60218:[.868,.022,.472,{ic:.3}],60219:[.694,.205,.267,{ic:.201}],60224:[.77,.047,1,{ic:.166}],60232:[.868,.212,.736,{ic:.026}],60233:[.694,.212,.5,{ic:.039}],60237:[.347,.056,.392],60257:[.398,-.266,.239,{ic:.079}],60259:[.639,-.477,.294,{ic:.409}],60261:[.692,-.568,.239,{ic:.401}],60270:[.694,.244,.688,{ic:.053}],60271:[.444,.244,.517,{ic:.012}],60424:[.855,0,.708,{ic:.053}],60425:[.855,0,.517,{ic:.09}],60426:[.855,0,.542],60427:[.855,0,.239,{ic:.23}],60428:[.855,0,.681,{ic:.109}],60429:[.732,.011,.361,{ic:.102}],60430:[.816,0,.681,{ic:.109}],60432:[.881,.186,.736,{ic:.026}],60433:[.631,.186,.5,{ic:.022}],63166:[.444,.205,.267,{ic:.011}],63198:[.305,-.244,.75,{ic:.064}],8592:[.511,.009,1],8593:[.694,.194,.5,{ic:.071}],8594:[.511,.009,1],8595:[.694,.194,.5],8730:[.763,.104,.472,{ic:.209}],8738:[.583,.083,.778,{ic:.045}],9001:[.751,.249,.389,{ic:.099}],9002:[.751,.249,.389],9702:[.407,-.121,.778],10214:[.751,.249,.403,{ic:.137}],10215:[.751,.249,.403,{ic:.041}]}},SSBIX:{"sans-serif-bold-italic":{161:[.5,.194,.367],162:[.558,.1,.489,{ic:.052}],164:[.523,.022,.856],166:[.75,.25,.319,{ic:.024}],169:[.694,0,.694,{ic:.082}],170:[.716,-.276,.416,{ic:.106}],171:[.5,0,.55,{ic:.043}],173:[.265,-.193,.367],174:[.694,0,.694,{ic:.082}],186:[.716,-.276,.461,{ic:.095}],187:[.5,0,.55],188:[.716,.022,.88,{ic:.01}],189:[.716,.022,.978,{ic:.01}],190:[.716,.022,.88,{ic:.01}],191:[.5,.205,.519],3647:[.794,.1,.733,{ic:.021}],8218:[.129,.108,.306],8222:[.129,.108,.529],8226:[.418,-.114,.856],8240:[.75,.056,1.4],8241:[.75,.056,1.845],8249:[.5,0,.319,{ic:.043}],8250:[.5,0,.319],8251:[.523,.022,.856],8253:[.744,0,.519,{ic:.046}],8255:[-.063,.194,.649],8256:[.694,-.563,.649,{ic:.066}],8261:[.751,.297,.397,{ic:.101}],8262:[.751,.249,.397,{ic:.079}],8274:[.751,-.001,.55,{ic:.089}],8276:[-.063,.194,.649],8353:[.844,.15,.703,{ic:.075}],8358:[.694,0,.794,{ic:.077}],8361:[.694,0,1.039,{ic:.117}],8363:[.694,.1,.561,{ic:.126}],8369:[.694,0,.703,{ic:.047}],8451:[.717,.021,.947,{ic:.074}],8470:[.695,-.001,.916],8471:[.694,0,.694,{ic:.082}],8478:[.694,0,.703,{ic:.049}],8480:[.694,-.252,.953,{ic:.049}],8482:[.698,-.282,1.027,{ic:.064}],8494:[.712,.01,.758,{ic:.06}],8960:[.617,.117,.856,{ic:.061}],9250:[.694,.011,.561,{ic:.022}],9251:[.275,.122,.55,{ic:.008}],64256:[.705,0,.642,{ic:.189}],64257:[.705,0,.586,{ic:.079}],64258:[.705,0,.586,{ic:.078}],64259:[.705,0,.892,{ic:.079}],64260:[.705,0,.892,{ic:.078}],184:[-.024,.192,.489],702:[.628,-.267,.436,{ic:.041}],703:[.628,-.267,.436,{ic:.059}],731:[.018,.213,.55],733:[.694,-.537,.55,{ic:.052}],777:[.719,-.513,0,{dx:.122}],779:[.694,-.537,0,{dx:.119}],783:[.694,-.537,0,{dx:.139}],785:[.687,-.545,0,{dx:.137}],803:[-.085,.2,0,{dx:.293}],806:[-.079,.292,0,{dx:.301}],814:[-.071,.213,0,{dx:.285}],815:[-.071,.213,0,{dx:.298}],816:[-.076,.207,0,{dx:.291}],817:[-.099,.184,0,{dx:.291}],818:[-.099,.184,0,{dx:.291}],9834:[.723,.052,.672],9901:[.48,-.022,.55,{ic:.173}],9902:[.624,.122,.55,{ic:.296}],59395:[.705,0,.864,{ic:.032}],59908:[.866,-.545,.55,{ic:.063}],59909:[-.071,.213,.55],59910:[-.071,.213,.55],59913:[.866,-.545,.55,{ic:.053}],59915:[.923,-.545,.55,{ic:.053}],59917:[.687,-.545,.55,{ic:.04}],59920:[.888,-.545,.55,{ic:.094}],59927:[.841,-.535,.55,{ic:.056}],59930:[.841,-.535,.55,{ic:.034}],59932:[.898,-.535,.55,{ic:.116}],59934:[.863,-.535,.55,{ic:.088}],59935:[-.079,.292,.55],59942:[.694,-.537,.55,{ic:.007}],59946:[.858,-.564,.55,{ic:.112}],59948:[.846,-.564,.55,{ic:.078}],59951:[.858,-.564,.55,{ic:.047}],59957:[.719,-.513,.55],59962:[-.099,.184,.55],59966:[-.099,.184,.55],59970:[.863,-.526,.8],59973:[-.076,.207,.55],60163:[.885,.213,.733,{ic:.027}],60164:[.694,.213,.525,{ic:.039}],60168:[.694,0,.694,{ic:.082}],60175:[.694,0,.694,{ic:.082}],60176:[0,0,0],60177:[0,0,0],60178:[0,0,0],60182:[.631,.025,.55,{ic:.059}],60185:[-.085,.2,.306],60190:[.885,.213,.642,{ic:.077}],60191:[.694,.213,.511,{ic:.046}],60200:[.716,.022,1.222,{ic:.041}],60201:[.5,.244,.519],60203:[.844,.15,.733,{ic:.055}],60209:[.356,-.053,.367,{ic:.007}],60213:[.885,.213,.331,{ic:.258}],60214:[.694,.213,.256,{ic:.184}],60218:[.885,.022,.519,{ic:.261}],60219:[.694,.205,.286,{ic:.184}],60224:[.797,.073,1.1,{ic:.178}],60232:[.885,.213,.794,{ic:.026}],60233:[.694,.213,.55,{ic:.026}],60237:[.347,.056,.485],60257:[.416,-.292,.256,{ic:.143}],60259:[.667,-.486,.336,{ic:.477}],60261:[.694,-.563,.256,{ic:.459}],60270:[.694,.213,.764,{ic:.048}],60271:[.458,.213,.561,{ic:.029}],60424:[.877,0,.794,{ic:.048}],60425:[.877,0,.561,{ic:.087}],60426:[.877,0,.581],60427:[.877,0,.256,{ic:.239}],60428:[.877,0,.733,{ic:.098}],60429:[.772,.011,.404,{ic:.095}],60430:[.849,0,.733,{ic:.098}],60432:[.889,.194,.794,{ic:.026}],60433:[.653,.194,.55,{ic:.025}],63166:[.458,.205,.286,{ic:.028}],63198:[.319,-.247,.825,{ic:.06}],8592:[.547,.046,1.1],8593:[.694,.194,.622,{ic:.058}],8594:[.548,.045,1.1],8595:[.694,.194,.622],8730:[.808,.082,.519,{ic:.238}],8738:[.617,.117,.856,{ic:.036}],9001:[.751,.249,.428,{ic:.09}],9002:[.751,.249,.428],9702:[.418,-.114,.856],10214:[.751,.249,.443,{ic:.127}],10215:[.751,.249,.443,{ic:.042}]}}},"MM"),{},["MJX-MM-SSX","MJX-MM-SSBX","MJX-MM-SSIX","MJX-MM-SSBIX"]);MathJax.loader&&MathJax.loader.checkVersion("[mathjax-modern]/chtml/dynamic/sans-serif-ex","4.0.0-beta.7","dynamic-font")})();