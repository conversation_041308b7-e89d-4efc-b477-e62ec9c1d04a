(()=>{"use strict";const c=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-modern"].chtml_ts.MathJaxModernFont.dynamicSetup("","latin-i",c({LI:{italic:{192:[.906,0,.743],193:[.906,0,.743],194:[.855,0,.743],195:[.837,0,.743],196:[.832,0,.743],197:[.892,0,.743],198:[.683,0,.883,{ic:.066}],199:[.705,.192,.716,{ic:.096}],200:[.906,0,.678,{ic:.065}],201:[.906,0,.678,{ic:.065}],202:[.855,0,.678,{ic:.065}],203:[.832,0,.678,{ic:.065}],204:[.906,0,.386,{ic:.115}],205:[.906,0,.386,{ic:.162}],206:[.855,0,.386,{ic:.133}],207:[.832,0,.386,{ic:.156}],208:[.683,0,.755,{ic:.02}],209:[.837,0,.743,{ic:.109}],210:[.906,.022,.767,{ic:.021}],211:[.906,.022,.767,{ic:.021}],212:[.855,.022,.767,{ic:.021}],213:[.837,.022,.767,{ic:.021}],214:[.832,.022,.767,{ic:.021}],216:[.743,.06,.767,{ic:.055}],217:[.906,.022,.743,{ic:.109}],218:[.906,.022,.743,{ic:.109}],219:[.855,.022,.743,{ic:.109}],220:[.832,.022,.743,{ic:.109}],221:[.906,0,.743,{ic:.131}],222:[.683,0,.627,{ic:.02}],223:[.705,.205,.537,{ic:.041}],224:[.696,.011,.511,{ic:.014}],225:[.696,.011,.511,{ic:.028}],226:[.683,.011,.511,{ic:.018}],227:[.651,.011,.511,{ic:.057}],228:[.649,.011,.511,{ic:.032}],229:[.705,.011,.511,{ic:.014}],230:[.442,.011,.716,{ic:.003}],231:[.442,.192,.46,{ic:.01}],232:[.696,.011,.46,{ic:.008}],233:[.696,.011,.46,{ic:.053}],234:[.683,.011,.46,{ic:.043}],235:[.649,.011,.46,{ic:.057}],236:[.696,.011,.307,{ic:.037}],237:[.696,.011,.307,{ic:.13}],238:[.683,.011,.307,{ic:.12}],239:[.649,.011,.307,{ic:.134}],241:[.651,.011,.562,{ic:.032}],242:[.696,.011,.511],243:[.696,.011,.511,{ic:.028}],244:[.683,.011,.511,{ic:.018}],245:[.651,.011,.511,{ic:.057}],246:[.649,.011,.511,{ic:.032}],248:[.538,.107,.511,{ic:.039}],249:[.696,.011,.537,{ic:.024}],250:[.696,.011,.537,{ic:.024}],251:[.683,.011,.537,{ic:.024}],252:[.649,.011,.537,{ic:.024}],253:[.696,.205,.486,{ic:.04}],254:[.694,.194,.511],255:[.649,.205,.486,{ic:.044}],256:[.786,0,.743],257:[.616,.011,.511,{ic:.061}],258:[.916,0,.743],259:[.686,.011,.511,{ic:.052}],260:[.716,.211,.743],261:[.442,.211,.511,{ic:.014}],262:[.906,.022,.716,{ic:.096}],263:[.696,.011,.46,{ic:.076}],264:[.855,.022,.716,{ic:.096}],265:[.683,.011,.46,{ic:.066}],266:[.864,.022,.716,{ic:.096}],267:[.654,.011,.46,{ic:.01}],268:[.855,.022,.716,{ic:.096}],269:[.683,.011,.46,{ic:.102}],270:[.855,0,.755,{ic:.02}],271:[.694,.011,.511,{ic:.16}],272:[.683,0,.755,{ic:.02}],273:[.694,.011,.511,{ic:.077}],274:[.786,0,.678,{ic:.065}],275:[.616,.011,.46,{ic:.086}],276:[.916,0,.678,{ic:.065}],277:[.686,.011,.46,{ic:.077}],278:[.864,0,.678,{ic:.065}],279:[.654,.011,.46,{ic:.008}],280:[.68,.211,.678,{ic:.065}],281:[.442,.211,.46,{ic:.008}],282:[.855,0,.678,{ic:.065}],283:[.683,.011,.46,{ic:.079}],284:[.855,.022,.774,{ic:.038}],285:[.683,.205,.46,{ic:.057}],286:[.916,.022,.774,{ic:.038}],287:[.686,.205,.46,{ic:.091}],288:[.864,.022,.774,{ic:.038}],289:[.654,.205,.46,{ic:.027}],290:[.705,.29,.774,{ic:.038}],291:[.735,.205,.46,{ic:.027}],292:[.855,0,.743,{ic:.109}],293:[.866,.011,.511,{ic:.074}],294:[.683,0,.743,{ic:.109}],295:[.694,.011,.511,{ic:.024}],296:[.837,0,.386,{ic:.165}],297:[.651,.011,.307,{ic:.159}],298:[.786,0,.386,{ic:.165}],299:[.616,.011,.307,{ic:.163}],300:[.916,0,.386,{ic:.171}],301:[.686,.011,.307,{ic:.154}],302:[.683,.211,.386,{ic:.115}],303:[.656,.211,.307,{ic:.024}],304:[.864,0,.386,{ic:.115}],306:[.683,.022,.872,{ic:.097}],307:[.654,.205,.583,{ic:.06}],308:[.855,.022,.525,{ic:.12}],309:[.683,.205,.307,{ic:.12}],310:[.683,.29,.769,{ic:.09}],311:[.694,.29,.46,{ic:.042}],313:[.906,0,.627],314:[.917,.011,.256,{ic:.23}],315:[.683,.29,.627],316:[.694,.29,.256,{ic:.05}],317:[.683,0,.627,{ic:.02}],318:[.694,.011,.256,{ic:.161}],319:[.683,0,.627,{ic:.037}],320:[.694,.011,.256,{ic:.122}],321:[.683,0,.627],322:[.694,.011,.319,{ic:.03}],323:[.906,0,.743,{ic:.109}],324:[.696,.011,.562,{ic:.024}],325:[.683,.29,.743,{ic:.109}],326:[.442,.29,.562,{ic:.024}],327:[.855,0,.743,{ic:.109}],328:[.683,.011,.562,{ic:.029}],330:[.705,.022,.743,{ic:.066}],331:[.442,.216,.498,{ic:.02}],332:[.786,.022,.767,{ic:.021}],333:[.616,.011,.511,{ic:.061}],334:[.916,.022,.767,{ic:.021}],335:[.686,.011,.511,{ic:.052}],336:[.936,.022,.767,{ic:.021}],337:[.696,.011,.511,{ic:.065}],338:[.705,.022,.985,{ic:.066}],339:[.443,.012,.716,{ic:.003}],340:[.906,.022,.729],341:[.696,.011,.422,{ic:.072}],342:[.683,.29,.729],343:[.442,.29,.422,{ic:.067}],344:[.855,.022,.729],345:[.683,.011,.422,{ic:.098}],346:[.906,.022,.562,{ic:.075}],347:[.696,.011,.409,{ic:.079}],348:[.855,.022,.562,{ic:.07}],349:[.683,.011,.409,{ic:.069}],350:[.705,.194,.562,{ic:.07}],351:[.442,.192,.409,{ic:.01}],352:[.855,.022,.562,{ic:.071}],353:[.683,.011,.409,{ic:.105}],354:[.677,.192,.716,{ic:.09}],355:[.626,.192,.332,{ic:.041}],356:[.855,0,.716,{ic:.09}],357:[.689,.011,.332,{ic:.108}],360:[.837,.022,.743,{ic:.109}],361:[.651,.011,.537,{ic:.044}],362:[.786,.022,.743,{ic:.109}],363:[.616,.011,.537,{ic:.048}],364:[.916,.022,.743,{ic:.109}],365:[.686,.011,.537,{ic:.039}],366:[.892,.022,.743,{ic:.109}],367:[.705,.011,.537,{ic:.024}],368:[.936,.022,.743,{ic:.109}],369:[.696,.011,.537,{ic:.052}],370:[.683,.211,.743,{ic:.109}],371:[.442,.211,.537,{ic:.024}],372:[.855,.022,.999,{ic:.125}],373:[.683,.011,.664,{ic:.033}],374:[.855,0,.743,{ic:.131}],375:[.683,.205,.486,{ic:.03}],376:[.832,0,.743,{ic:.131}],377:[.906,0,.613,{ic:.091}],378:[.696,.011,.409,{ic:.079}],379:[.864,0,.613,{ic:.091}],380:[.654,.011,.409,{ic:.055}],381:[.855,0,.613,{ic:.091}],382:[.683,.011,.409,{ic:.105}],383:[.705,.205,.307,{ic:.145}],398:[.68,0,.678,{ic:.115}],402:[.705,.205,.307,{ic:.145}],416:[.789,.022,.767,{ic:.06}],417:[.536,.011,.511,{ic:.074}],431:[.789,.022,.743,{ic:.131}],432:[.536,.011,.537,{ic:.093}],461:[.855,0,.743],462:[.683,.011,.511,{ic:.054}],463:[.855,0,.386,{ic:.158}],464:[.683,.011,.307,{ic:.156}],465:[.855,.022,.767,{ic:.021}],466:[.683,.011,.511,{ic:.054}],467:[.855,.022,.743,{ic:.109}],468:[.683,.011,.537,{ic:.041}],471:[1.054,.022,.743,{ic:.109}],472:[.867,.011,.537,{ic:.077}],473:[1.003,.022,.743,{ic:.109}],474:[.816,.011,.537,{ic:.073}],475:[1.054,.022,.743,{ic:.109}],476:[.867,.011,.537,{ic:.024}],477:[.442,.011,.46,{ic:.039}],486:[.855,.022,.774,{ic:.038}],487:[.683,.205,.46,{ic:.093}],490:[.705,.211,.767,{ic:.021}],491:[.442,.211,.511],496:[.683,.205,.307,{ic:.156}],500:[.906,.022,.774,{ic:.038}],501:[.696,.205,.46,{ic:.067}],506:[1.071,0,.743],507:[.884,.011,.511,{ic:.062}],508:[.906,0,.883,{ic:.066}],509:[.696,.011,.716,{ic:.003}],510:[.906,.06,.767,{ic:.055}],511:[.696,.107,.511,{ic:.039}],512:[.936,0,.743],513:[.696,.011,.511,{ic:.014}],516:[.936,0,.678,{ic:.065}],517:[.696,.011,.46,{ic:.018}],520:[.936,0,.386,{ic:.115}],521:[.696,.011,.307,{ic:.095}],524:[.936,.022,.767,{ic:.021}],525:[.696,.011,.511],528:[.936,.022,.729],529:[.696,.011,.422,{ic:.067}],532:[.936,.022,.743,{ic:.109}],533:[.696,.011,.537,{ic:.024}],536:[.705,.29,.562,{ic:.07}],537:[.442,.29,.409,{ic:.01}],538:[.677,.29,.716,{ic:.09}],539:[.626,.29,.332,{ic:.041}],7692:[.683,.2,.755,{ic:.02}],7693:[.694,.2,.511,{ic:.049}],7694:[.683,.162,.755,{ic:.02}],7695:[.694,.162,.511,{ic:.049}],7716:[.683,.2,.743,{ic:.109}],7717:[.694,.2,.511,{ic:.024}],7718:[.832,0,.743,{ic:.109}],7719:[.843,.011,.511,{ic:.097}],7722:[.683,.232,.743,{ic:.109}],7723:[.694,.232,.511,{ic:.024}],7726:[1.054,0,.386,{ic:.199}],7727:[.867,.011,.307,{ic:.192}],7734:[.683,.2,.627],7735:[.694,.2,.256,{ic:.05}],7736:[.786,.2,.627],7737:[.797,.2,.256,{ic:.192}],7746:[.683,.2,.897,{ic:.105}],7747:[.442,.2,.818,{ic:.024}],7748:[.842,0,.743,{ic:.109}],7749:[.654,.011,.562,{ic:.024}],7750:[.683,.2,.743,{ic:.109}],7751:[.442,.2,.562,{ic:.024}],7768:[.864,.022,.729],7769:[.654,.011,.422,{ic:.067}],7770:[.683,.2,.729],7771:[.442,.2,.422,{ic:.067}],7772:[.786,.2,.729],7773:[.616,.2,.422,{ic:.105}],7778:[.705,.2,.562,{ic:.07}],7779:[.442,.2,.409,{ic:.01}],7788:[.677,.2,.716,{ic:.09}],7789:[.626,.2,.332,{ic:.041}],7790:[.677,.162,.716,{ic:.09}],7791:[.626,.162,.332,{ic:.041}],7808:[.906,.022,.999,{ic:.125}],7809:[.696,.011,.664,{ic:.033}],7810:[.906,.022,.999,{ic:.125}],7811:[.696,.011,.664,{ic:.033}],7812:[.832,.022,.999,{ic:.125}],7813:[.649,.011,.664,{ic:.033}],7826:[.683,.2,.613,{ic:.091}],7827:[.442,.2,.409,{ic:.055}],7831:[.793,.011,.332,{ic:.174}],7840:[.716,.2,.743],7841:[.442,.2,.511,{ic:.014}],7842:[.967,0,.743],7843:[.705,.011,.511,{ic:.014}],7844:[1.034,0,.743],7845:[.816,.011,.511,{ic:.043}],7846:[1.034,0,.743],7847:[.816,.011,.511,{ic:.017}],7848:[1.064,0,.743,{ic:.059}],7849:[.846,.011,.511,{ic:.121}],7850:[.996,0,.743,{ic:.026}],7851:[.778,.011,.511,{ic:.088}],7852:[.855,.2,.743],7853:[.683,.2,.511,{ic:.018}],7854:[1.095,0,.743],7855:[.865,.011,.511,{ic:.056}],7856:[1.095,0,.743],7857:[.865,.011,.511,{ic:.052}],7858:[1.125,0,.743],7859:[.895,.011,.511,{ic:.052}],7860:[1.057,0,.743,{ic:.042}],7861:[.827,.011,.511,{ic:.101}],7862:[.916,.2,.743],7863:[.686,.2,.511,{ic:.052}],7864:[.68,.2,.678,{ic:.065}],7865:[.442,.2,.46,{ic:.008}],7866:[.967,0,.678,{ic:.065}],7867:[.705,.011,.46,{ic:.021}],7868:[.837,0,.678,{ic:.065}],7869:[.651,.011,.46,{ic:.082}],7870:[1.034,0,.678,{ic:.065}],7871:[.816,.011,.46,{ic:.068}],7872:[1.034,0,.678,{ic:.065}],7873:[.816,.011,.46,{ic:.042}],7874:[1.064,0,.678,{ic:.092}],7875:[.846,.011,.46,{ic:.146}],7876:[.996,0,.678,{ic:.065}],7877:[.778,.011,.46,{ic:.113}],7878:[.855,.2,.678,{ic:.065}],7879:[.683,.2,.46,{ic:.043}],7880:[.967,0,.386,{ic:.124}],7881:[.705,.011,.307,{ic:.098}],7882:[.683,.2,.386,{ic:.115}],7883:[.654,.2,.307,{ic:.051}],7884:[.705,.2,.767,{ic:.021}],7885:[.442,.2,.511],7886:[.967,.022,.767,{ic:.021}],7887:[.705,.011,.511],7888:[1.034,.022,.767,{ic:.021}],7889:[.816,.011,.511,{ic:.043}],7890:[1.034,.022,.767,{ic:.021}],7891:[.816,.011,.511,{ic:.017}],7892:[1.064,.022,.767,{ic:.047}],7893:[.846,.011,.511,{ic:.121}],7894:[.996,.022,.767,{ic:.021}],7895:[.778,.011,.511,{ic:.088}],7896:[.855,.2,.767,{ic:.021}],7897:[.683,.2,.511,{ic:.018}],7898:[.906,.022,.767,{ic:.06}],7899:[.696,.011,.511,{ic:.074}],7900:[.906,.022,.767,{ic:.06}],7901:[.696,.011,.511,{ic:.074}],7902:[.967,.022,.767,{ic:.06}],7903:[.705,.011,.511,{ic:.074}],7904:[.837,.022,.767,{ic:.06}],7905:[.651,.011,.511,{ic:.074}],7906:[.789,.2,.767,{ic:.06}],7907:[.536,.2,.511,{ic:.074}],7908:[.683,.2,.743,{ic:.109}],7909:[.442,.2,.537,{ic:.024}],7910:[.967,.022,.743,{ic:.109}],7911:[.705,.011,.537,{ic:.024}],7912:[.906,.022,.743,{ic:.131}],7913:[.696,.011,.537,{ic:.093}],7914:[.906,.022,.743,{ic:.131}],7915:[.696,.011,.537,{ic:.093}],7916:[.967,.022,.743,{ic:.131}],7917:[.705,.011,.537,{ic:.093}],7918:[.837,.022,.743,{ic:.131}],7919:[.651,.011,.537,{ic:.093}],7920:[.789,.2,.743,{ic:.131}],7921:[.536,.2,.537,{ic:.093}],7922:[.906,0,.743,{ic:.131}],7923:[.696,.205,.486,{ic:.03}],7924:[.683,.2,.743,{ic:.131}],7925:[.442,.205,.486,{ic:.033}],7926:[.967,0,.743,{ic:.131}],7927:[.705,.205,.486,{ic:.03}],7928:[.837,0,.743,{ic:.131}],7929:[.651,.205,.486,{ic:.069}]}}},"MM"),{},["MJX-MM-LI"]);MathJax.loader&&MathJax.loader.checkVersion("[mathjax-modern]/chtml/dynamic/latin-i","4.0.0-beta.7","dynamic-font")})();