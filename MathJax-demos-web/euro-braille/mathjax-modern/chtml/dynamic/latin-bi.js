(()=>{"use strict";const c=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-modern"].chtml_ts.MathJaxModernFont.dynamicSetup("","latin-bi",c({LIB:{"bold-italic":{192:[.918,0,.866],193:[.918,0,.866],194:[.905,0,.866],195:[.86,0,.866],196:[.873,0,.866],197:[.879,0,.866],198:[.686,0,1.023,{ic:.049}],199:[.703,.2,.827,{ic:.082}],200:[.918,0,.757,{ic:.048}],201:[.918,0,.757,{ic:.048}],202:[.905,0,.757,{ic:.048}],203:[.873,0,.757,{ic:.048}],204:[.918,0,.472,{ic:.113}],205:[.918,0,.472,{ic:.137}],206:[.905,0,.472,{ic:.13}],207:[.873,0,.472,{ic:.179}],208:[.686,0,.876,{ic:.009}],209:[.86,0,.896,{ic:.107}],210:[.918,.017,.855,{ic:.01}],211:[.918,.017,.855,{ic:.01}],212:[.905,.017,.855,{ic:.01}],213:[.86,.017,.855,{ic:.01}],214:[.873,.017,.855,{ic:.01}],216:[.754,.068,.886,{ic:.009}],217:[.918,.017,.881,{ic:.107}],218:[.918,.017,.881,{ic:.107}],219:[.905,.017,.881,{ic:.107}],220:[.873,.017,.881,{ic:.107}],221:[.918,0,.866,{ic:.119}],222:[.686,0,.728,{ic:.009}],223:[.702,.202,.665,{ic:.026}],224:[.699,.008,.591,{ic:.024}],225:[.699,.008,.591,{ic:.024}],226:[.704,.008,.591,{ic:.024}],227:[.66,.008,.591,{ic:.042}],228:[.667,.008,.591,{ic:.045}],229:[.702,.008,.591,{ic:.024}],230:[.452,.008,.827],231:[.452,.2,.532,{ic:.004}],232:[.699,.008,.532,{ic:.004}],233:[.699,.008,.532,{ic:.035}],234:[.704,.008,.532,{ic:.042}],235:[.667,.008,.532,{ic:.075}],236:[.699,.008,.356,{ic:.034}],237:[.699,.008,.356,{ic:.122}],238:[.704,.008,.356,{ic:.129}],239:[.667,.008,.356,{ic:.162}],241:[.66,.008,.65,{ic:.034}],242:[.699,.008,.591,{ic:.004}],243:[.699,.008,.591,{ic:.005}],244:[.704,.008,.591,{ic:.012}],245:[.66,.008,.591,{ic:.042}],246:[.667,.008,.591,{ic:.045}],248:[.56,.115,.591,{ic:.034}],249:[.699,.008,.621,{ic:.034}],250:[.699,.008,.621,{ic:.034}],251:[.704,.008,.621,{ic:.034}],252:[.667,.008,.621,{ic:.034}],253:[.699,.202,.562,{ic:.033}],254:[.693,.194,.591,{ic:.005}],255:[.667,.202,.562,{ic:.059}],256:[.811,0,.866],257:[.626,.008,.591,{ic:.047}],258:[.93,0,.866],259:[.69,.008,.591,{ic:.037}],260:[.711,.204,.866],261:[.452,.204,.591,{ic:.024}],262:[.918,.017,.827,{ic:.082}],263:[.699,.008,.532,{ic:.061}],264:[.905,.017,.827,{ic:.082}],265:[.704,.008,.532,{ic:.068}],266:[.905,.017,.827,{ic:.082}],267:[.674,.008,.532,{ic:.004}],268:[.905,.017,.827,{ic:.082}],269:[.704,.008,.532,{ic:.108}],270:[.905,0,.876,{ic:.009}],271:[.694,.008,.591,{ic:.191}],272:[.686,0,.876,{ic:.009}],273:[.693,.008,.591,{ic:.052}],274:[.811,0,.757,{ic:.048}],275:[.626,.008,.532,{ic:.077}],276:[.93,0,.757,{ic:.048}],277:[.69,.008,.532,{ic:.067}],278:[.905,0,.757,{ic:.048}],279:[.674,.008,.532,{ic:.004}],280:[.68,.204,.757,{ic:.048}],281:[.452,.204,.532,{ic:.004}],282:[.905,0,.757,{ic:.048}],283:[.704,.008,.532,{ic:.082}],284:[.905,.017,.895,{ic:.013}],285:[.704,.202,.532,{ic:.057}],286:[.93,.017,.895,{ic:.013}],287:[.69,.202,.532,{ic:.082}],288:[.905,.017,.895,{ic:.013}],289:[.674,.202,.532,{ic:.031}],290:[.703,.307,.895,{ic:.013}],291:[.746,.202,.532,{ic:.031}],292:[.905,0,.896,{ic:.107}],293:[.913,.008,.591,{ic:.073}],294:[.686,0,.896,{ic:.107}],295:[.693,.008,.591,{ic:.034}],296:[.86,0,.472,{ic:.151}],297:[.66,.008,.356,{ic:.159}],298:[.811,0,.472,{ic:.153}],299:[.626,.008,.356,{ic:.164}],300:[.93,0,.472,{ic:.157}],301:[.69,.008,.356,{ic:.154}],302:[.686,.204,.472,{ic:.113}],303:[.694,.204,.356,{ic:.034}],304:[.905,0,.472,{ic:.113}],306:[.686,.017,1.035,{ic:.08}],307:[.674,.202,.676,{ic:.064}],308:[.905,.017,.611,{ic:.104}],309:[.704,.202,.356,{ic:.129}],310:[.686,.307,.895,{ic:.077}],311:[.693,.307,.532,{ic:.034}],313:[.918,0,.698],314:[.926,.008,.297,{ic:.226}],315:[.686,.307,.698],316:[.693,.307,.297,{ic:.044}],317:[.686,0,.698,{ic:.073}],318:[.694,.008,.297,{ic:.191}],319:[.686,0,.698,{ic:.071}],320:[.693,.008,.369,{ic:.102}],321:[.686,0,.698],322:[.693,.008,.356,{ic:.072}],323:[.918,0,.896,{ic:.107}],324:[.699,.008,.65,{ic:.034}],325:[.686,.307,.896,{ic:.107}],326:[.452,.307,.65,{ic:.034}],327:[.905,0,.896,{ic:.107}],328:[.704,.008,.65,{ic:.034}],330:[.703,.017,.896,{ic:.034}],331:[.452,.211,.585,{ic:.019}],332:[.811,.017,.855,{ic:.01}],333:[.626,.008,.591,{ic:.047}],334:[.93,.017,.855,{ic:.01}],335:[.69,.008,.591,{ic:.037}],336:[.947,.017,.855,{ic:.01}],337:[.699,.008,.591,{ic:.049}],338:[.703,.017,1.141,{ic:.049}],339:[.456,.012,.827],340:[.918,.017,.859],341:[.699,.008,.502,{ic:.06}],342:[.686,.307,.859],343:[.452,.307,.502,{ic:.06}],344:[.905,.017,.859],345:[.704,.008,.502,{ic:.096}],346:[.918,.017,.65,{ic:.053}],347:[.699,.008,.487,{ic:.057}],348:[.905,.017,.65,{ic:.053}],349:[.704,.008,.487,{ic:.064}],350:[.703,.2,.65,{ic:.053}],351:[.452,.2,.487],352:[.905,.017,.65,{ic:.07}],353:[.704,.008,.487,{ic:.104}],354:[.675,.2,.796,{ic:.076}],355:[.643,.2,.385,{ic:.034}],356:[.905,0,.796,{ic:.076}],357:[.74,.008,.385,{ic:.142}],360:[.86,.017,.881,{ic:.107}],361:[.66,.008,.621,{ic:.034}],362:[.811,.017,.881,{ic:.107}],363:[.626,.008,.621,{ic:.034}],364:[.93,.017,.881,{ic:.107}],365:[.69,.008,.621,{ic:.034}],366:[.879,.017,.881,{ic:.107}],367:[.702,.008,.621,{ic:.034}],368:[.947,.017,.881,{ic:.107}],369:[.699,.008,.621,{ic:.034}],370:[.686,.204,.881,{ic:.107}],371:[.452,.204,.621,{ic:.034}],372:[.905,.017,1.16,{ic:.114}],373:[.704,.008,.768,{ic:.023}],374:[.905,0,.866,{ic:.119}],375:[.704,.202,.562,{ic:.033}],376:[.873,0,.866,{ic:.119}],377:[.918,0,.709,{ic:.077}],378:[.699,.008,.491,{ic:.055}],379:[.905,0,.709,{ic:.077}],380:[.674,.008,.491,{ic:.049}],381:[.905,0,.709,{ic:.077}],382:[.704,.008,.491,{ic:.102}],383:[.702,.202,.4,{ic:.138}],398:[.68,0,.757,{ic:.106}],402:[.702,.202,.4,{ic:.138}],416:[.819,.022,.855,{ic:.07}],417:[.578,.011,.591,{ic:.096}],431:[.819,.022,.881,{ic:.138}],432:[.578,.011,.62,{ic:.117}],461:[.905,0,.866],462:[.704,.008,.591,{ic:.052}],463:[.905,0,.472,{ic:.159}],464:[.704,.008,.356,{ic:.169}],465:[.905,.017,.855,{ic:.01}],466:[.704,.008,.591,{ic:.052}],467:[.905,.017,.881,{ic:.107}],468:[.704,.008,.621,{ic:.037}],471:[1.118,.017,.881,{ic:.107}],472:[.899,.008,.621,{ic:.058}],473:[1.105,.017,.881,{ic:.107}],474:[.886,.008,.621,{ic:.08}],475:[1.118,.017,.881,{ic:.107}],476:[.899,.008,.621,{ic:.034}],477:[.452,.008,.532,{ic:.043}],486:[.905,.017,.895,{ic:.013}],487:[.704,.202,.532,{ic:.097}],490:[.703,.204,.855,{ic:.01}],491:[.452,.204,.591,{ic:.004}],496:[.704,.202,.356,{ic:.169}],500:[.918,.017,.895,{ic:.013}],501:[.699,.202,.532,{ic:.05}],506:[1.081,0,.866],507:[.904,.008,.591,{ic:.035}],508:[.918,0,1.023,{ic:.049}],509:[.699,.008,.827],510:[.918,.068,.886,{ic:.009}],511:[.699,.115,.591,{ic:.034}],512:[.947,0,.866],513:[.699,.008,.591,{ic:.024}],516:[.947,0,.757,{ic:.048}],517:[.699,.008,.532,{ic:.012}],520:[.947,0,.472,{ic:.113}],521:[.699,.008,.356,{ic:.099}],524:[.947,.017,.855,{ic:.01}],525:[.699,.008,.591,{ic:.004}],528:[.947,.017,.859],529:[.699,.008,.502,{ic:.06}],532:[.947,.017,.881,{ic:.107}],533:[.699,.008,.621,{ic:.034}],536:[.703,.307,.65,{ic:.053}],537:[.452,.307,.487],538:[.675,.307,.796,{ic:.076}],539:[.643,.307,.385,{ic:.034}],7692:[.686,.2,.876,{ic:.009}],7693:[.693,.2,.591,{ic:.044}],7694:[.686,.151,.876,{ic:.009}],7695:[.693,.151,.591,{ic:.044}],7716:[.686,.2,.896,{ic:.107}],7717:[.693,.2,.591,{ic:.034}],7718:[.873,0,.896,{ic:.107}],7719:[.881,.008,.591,{ic:.122}],7722:[.686,.215,.896,{ic:.107}],7723:[.693,.215,.591,{ic:.034}],7726:[1.118,0,.472,{ic:.187}],7727:[.899,.008,.356,{ic:.19}],7734:[.686,.2,.698],7735:[.693,.2,.297,{ic:.044}],7736:[.811,.2,.698],7737:[.819,.2,.297,{ic:.206}],7746:[.686,.2,1.073,{ic:.107}],7747:[.452,.2,.944,{ic:.034}],7748:[.888,0,.896,{ic:.107}],7749:[.674,.008,.65,{ic:.034}],7750:[.686,.2,.896,{ic:.107}],7751:[.452,.2,.65,{ic:.034}],7768:[.905,.017,.859],7769:[.674,.008,.502,{ic:.06}],7770:[.686,.2,.859],7771:[.452,.2,.502,{ic:.06}],7772:[.811,.2,.859],7773:[.626,.2,.502,{ic:.091}],7778:[.703,.2,.65,{ic:.053}],7779:[.452,.2,.487],7788:[.675,.2,.796,{ic:.076}],7789:[.643,.2,.385,{ic:.034}],7790:[.675,.151,.796,{ic:.076}],7791:[.643,.151,.385,{ic:.034}],7808:[.918,.017,1.16,{ic:.114}],7809:[.699,.008,.768,{ic:.023}],7810:[.918,.017,1.16,{ic:.114}],7811:[.699,.008,.768,{ic:.023}],7812:[.873,.017,1.16,{ic:.114}],7813:[.667,.008,.768,{ic:.023}],7826:[.686,.2,.709,{ic:.077}],7827:[.452,.2,.491,{ic:.049}],7831:[.831,.008,.385,{ic:.212}],7840:[.711,.2,.866],7841:[.452,.2,.591,{ic:.024}],7842:[.96,0,.866],7843:[.702,.008,.591,{ic:.024}],7844:[1.107,0,.866],7845:[.863,.008,.591,{ic:.025}],7846:[1.107,0,.866],7847:[.863,.008,.591,{ic:.024}],7848:[1.122,0,.866,{ic:.039}],7849:[.878,.008,.591,{ic:.116}],7850:[1.079,0,.866,{ic:.009}],7851:[.835,.008,.591,{ic:.086}],7852:[.905,.2,.866],7853:[.704,.2,.591,{ic:.024}],7854:[1.132,0,.866],7855:[.893,.008,.591,{ic:.038}],7856:[1.132,0,.866],7857:[.893,.008,.591,{ic:.038}],7858:[1.147,0,.866],7859:[.908,.008,.591,{ic:.038}],7860:[1.104,0,.866,{ic:.016}],7861:[.865,.008,.591,{ic:.094}],7862:[.93,.2,.866],7863:[.69,.2,.591,{ic:.037}],7864:[.68,.2,.757,{ic:.048}],7865:[.452,.2,.532,{ic:.004}],7866:[.96,0,.757,{ic:.048}],7867:[.702,.008,.532,{ic:.004}],7868:[.86,0,.757,{ic:.048}],7869:[.66,.008,.532,{ic:.072}],7870:[1.107,0,.757,{ic:.048}],7871:[.863,.008,.532,{ic:.055}],7872:[1.107,0,.757,{ic:.048}],7873:[.863,.008,.532,{ic:.04}],7874:[1.122,0,.757,{ic:.093}],7875:[.878,.008,.532,{ic:.146}],7876:[1.079,0,.757,{ic:.063}],7877:[.835,.008,.532,{ic:.116}],7878:[.905,.2,.757,{ic:.048}],7879:[.704,.2,.532,{ic:.042}],7880:[.96,0,.472,{ic:.113}],7881:[.702,.008,.356,{ic:.085}],7882:[.686,.2,.472,{ic:.113}],7883:[.674,.2,.356,{ic:.048}],7884:[.703,.2,.855,{ic:.01}],7885:[.452,.2,.591,{ic:.004}],7886:[.96,.017,.855,{ic:.01}],7887:[.702,.008,.591,{ic:.004}],7888:[1.107,.017,.855,{ic:.01}],7889:[.863,.008,.591,{ic:.025}],7890:[1.107,.017,.855,{ic:.01}],7891:[.863,.008,.591,{ic:.01}],7892:[1.122,.017,.855,{ic:.044}],7893:[.878,.008,.591,{ic:.116}],7894:[1.079,.017,.855,{ic:.014}],7895:[.835,.008,.591,{ic:.086}],7896:[.905,.2,.855,{ic:.01}],7897:[.704,.2,.591,{ic:.012}],7898:[.918,.022,.855,{ic:.07}],7899:[.699,.011,.591,{ic:.096}],7900:[.918,.022,.855,{ic:.07}],7901:[.699,.011,.591,{ic:.096}],7902:[.96,.022,.855,{ic:.07}],7903:[.702,.011,.591,{ic:.096}],7904:[.86,.022,.855,{ic:.07}],7905:[.66,.011,.591,{ic:.096}],7906:[.819,.178,.855,{ic:.07}],7907:[.578,.2,.591,{ic:.096}],7908:[.686,.2,.881,{ic:.107}],7909:[.452,.2,.621,{ic:.034}],7910:[.96,.017,.881,{ic:.107}],7911:[.702,.008,.621,{ic:.034}],7912:[.918,.022,.881,{ic:.138}],7913:[.699,.011,.62,{ic:.117}],7914:[.918,.022,.881,{ic:.138}],7915:[.699,.011,.62,{ic:.117}],7916:[.96,.022,.881,{ic:.138}],7917:[.702,.011,.62,{ic:.117}],7918:[.86,.022,.881,{ic:.138}],7919:[.66,.011,.62,{ic:.117}],7920:[.819,.178,.881,{ic:.138}],7921:[.578,.2,.62,{ic:.117}],7922:[.918,0,.866,{ic:.119}],7923:[.699,.202,.562,{ic:.033}],7924:[.686,.2,.866,{ic:.119}],7925:[.452,.202,.562,{ic:.097}],7926:[.96,0,.866,{ic:.119}],7927:[.702,.202,.562,{ic:.033}],7928:[.86,0,.866,{ic:.119}],7929:[.66,.202,.562,{ic:.056}]}}},"MM"),{},["MJX-MM-LIB"]);MathJax.loader&&MathJax.loader.checkVersion("[mathjax-modern]/chtml/dynamic/latin-bi","4.0.0-beta.7","dynamic-font")})();