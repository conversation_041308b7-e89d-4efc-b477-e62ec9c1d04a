(()=>{"use strict";const i=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-modern"].chtml_ts.MathJaxModernFont.dynamicSetup("","sans-serif-bi",i({SSLIB:{"sans-serif-bold-italic":{192:[.885,0,.733],193:[.885,0,.733,{ic:.027}],194:[.873,0,.733],195:[.877,0,.733,{ic:.001}],196:[.849,0,.733,{ic:.016}],197:[.852,0,.733],198:[.694,0,.947,{ic:.078}],199:[.716,.214,.703,{ic:.075}],200:[.885,0,.642,{ic:.077}],201:[.885,0,.642,{ic:.077}],202:[.873,0,.642,{ic:.077}],203:[.849,0,.642,{ic:.077}],204:[.885,0,.331,{ic:.071}],205:[.885,0,.331,{ic:.258}],206:[.873,0,.331,{ic:.171}],207:[.849,0,.331,{ic:.216}],208:[.694,0,.794,{ic:.024}],209:[.877,0,.794,{ic:.048}],210:[.885,.022,.794,{ic:.026}],211:[.885,.022,.794,{ic:.026}],212:[.873,.022,.794,{ic:.026}],213:[.877,.022,.794,{ic:.026}],214:[.849,.022,.794,{ic:.026}],216:[.78,.086,.856,{ic:.025}],217:[.885,.022,.764,{ic:.048}],218:[.885,.022,.764,{ic:.048}],219:[.873,.022,.764,{ic:.048}],220:[.849,.022,.764,{ic:.048}],221:[.885,0,.733,{ic:.119}],222:[.694,0,.642,{ic:.027}],223:[.705,.011,.565,{ic:.061}],224:[.694,.011,.525,{ic:.021}],225:[.694,.011,.525,{ic:.039}],226:[.697,.011,.525,{ic:.021}],227:[.681,.011,.525,{ic:.063}],228:[.667,.011,.525,{ic:.06}],229:[.684,.011,.525,{ic:.021}],230:[.475,.011,.794,{ic:.031}],231:[.475,.214,.489,{ic:.052}],232:[.694,.011,.511,{ic:.031}],233:[.694,.011,.511,{ic:.046}],234:[.697,.011,.511,{ic:.031}],235:[.667,.011,.511,{ic:.067}],236:[.694,0,.256,{ic:.055}],237:[.694,0,.256,{ic:.184}],238:[.697,0,.256,{ic:.153}],239:[.667,0,.256,{ic:.194}],241:[.681,0,.561,{ic:.045}],242:[.694,.011,.55,{ic:.025}],243:[.694,.011,.55,{ic:.026}],244:[.697,.011,.55,{ic:.025}],245:[.681,.011,.55,{ic:.05}],246:[.667,.011,.55,{ic:.047}],248:[.592,.133,.55,{ic:.063}],249:[.694,.011,.561,{ic:.029}],250:[.694,.011,.561,{ic:.029}],251:[.697,.011,.561,{ic:.029}],252:[.667,.011,.561,{ic:.042}],253:[.694,.205,.5,{ic:.066}],254:[.694,.194,.561,{ic:.022}],255:[.667,.205,.5,{ic:.072}],256:[.851,0,.733,{ic:.011}],257:[.658,.011,.525,{ic:.074}],258:[.898,0,.733,{ic:.007}],259:[.687,.011,.525,{ic:.066}],260:[.694,.213,.733],261:[.475,.213,.525,{ic:.021}],262:[.885,.011,.703,{ic:.077}],263:[.694,.011,.489,{ic:.081}],264:[.873,.011,.703,{ic:.071}],265:[.697,.011,.489,{ic:.061}],266:[.861,.011,.703,{ic:.071}],267:[.673,.011,.489,{ic:.052}],268:[.873,.011,.703,{ic:.071}],269:[.697,.011,.489,{ic:.09}],270:[.873,0,.794,{ic:.024}],271:[.694,.011,.561,{ic:.242}],272:[.694,0,.794,{ic:.024}],273:[.694,.011,.561,{ic:.155}],274:[.851,0,.642,{ic:.077}],275:[.658,.011,.511,{ic:.081}],276:[.898,0,.642,{ic:.077}],277:[.687,.011,.511,{ic:.073}],278:[.861,0,.642,{ic:.077}],279:[.673,.011,.511,{ic:.031}],280:[.691,.213,.642,{ic:.077}],281:[.475,.213,.511,{ic:.031}],282:[.873,0,.642,{ic:.077}],283:[.697,.011,.511,{ic:.055}],284:[.873,.011,.733,{ic:.053}],285:[.697,.206,.55,{ic:.071}],286:[.898,.011,.733,{ic:.053}],287:[.687,.206,.55,{ic:.071}],288:[.861,.011,.733,{ic:.053}],289:[.673,.206,.55,{ic:.071}],290:[.706,.292,.733,{ic:.053}],291:[.745,.206,.55,{ic:.071}],292:[.873,0,.794,{ic:.048}],293:[.873,0,.561,{ic:.057}],294:[.694,0,.794,{ic:.114}],295:[.694,0,.561,{ic:.014}],296:[.877,0,.331,{ic:.201}],297:[.681,0,.256,{ic:.197}],298:[.851,0,.331,{ic:.211}],299:[.658,0,.256,{ic:.208}],300:[.898,0,.331,{ic:.207}],301:[.687,0,.256,{ic:.2}],302:[.694,.213,.331,{ic:.048}],303:[.695,.213,.256,{ic:.085}],304:[.861,0,.331,{ic:.083}],306:[.694,.022,.817,{ic:.047}],307:[.673,.205,.516,{ic:.074}],308:[.873,.022,.519,{ic:.173}],309:[.697,.205,.286,{ic:.146}],310:[.694,.292,.764,{ic:.076}],311:[.694,.292,.531,{ic:.045}],313:[.885,0,.581,{ic:.007}],314:[.885,0,.256,{ic:.302}],315:[.694,.292,.581],316:[.694,.292,.256,{ic:.078}],317:[.694,0,.581],318:[.694,0,.256,{ic:.242}],319:[.694,0,.581,{ic:.03}],320:[.694,0,.425,{ic:.071}],321:[.694,0,.672],322:[.694,0,.381,{ic:.105}],323:[.885,0,.794,{ic:.048}],324:[.694,0,.561,{ic:.021}],325:[.694,.292,.794,{ic:.048}],326:[.469,.292,.561,{ic:.014}],327:[.873,0,.794,{ic:.048}],328:[.697,0,.561,{ic:.03}],330:[.716,.018,.794],331:[.469,.193,.561,{ic:.014}],332:[.851,.022,.794,{ic:.026}],333:[.658,.011,.55,{ic:.061}],334:[.898,.022,.794,{ic:.026}],335:[.687,.011,.55,{ic:.053}],336:[.913,.022,.794,{ic:.026}],337:[.694,.011,.55,{ic:.052}],338:[.716,.022,1.069,{ic:.078}],339:[.475,.011,.856,{ic:.031}],340:[.885,0,.703,{ic:.049}],341:[.694,0,.372,{ic:.081}],342:[.694,.292,.703,{ic:.049}],343:[.469,.292,.372,{ic:.081}],344:[.873,0,.703,{ic:.049}],345:[.697,0,.372,{ic:.124}],346:[.885,.022,.611,{ic:.088}],347:[.694,.011,.422,{ic:.09}],348:[.873,.022,.611,{ic:.041}],349:[.697,.011,.422,{ic:.07}],350:[.716,.214,.611,{ic:.041}],351:[.475,.214,.422,{ic:.047}],352:[.873,.022,.611,{ic:.054}],353:[.697,.011,.422,{ic:.099}],354:[.688,.192,.733,{ic:.098}],355:[.589,.192,.404,{ic:.042}],356:[.873,0,.733,{ic:.098}],357:[.712,.011,.404,{ic:.121}],360:[.877,.022,.764,{ic:.048}],361:[.681,.011,.561,{ic:.045}],362:[.851,.022,.764,{ic:.048}],363:[.658,.011,.561,{ic:.056}],364:[.898,.022,.764,{ic:.048}],365:[.687,.011,.561,{ic:.048}],366:[.852,.022,.764,{ic:.048}],367:[.684,.011,.561,{ic:.029}],368:[.913,.022,.764,{ic:.048}],369:[.694,.011,.561,{ic:.047}],370:[.694,.213,.764,{ic:.048}],371:[.458,.213,.561,{ic:.029}],372:[.873,0,1.039,{ic:.117}],373:[.697,0,.744,{ic:.066}],374:[.873,0,.733,{ic:.119}],375:[.697,.205,.5,{ic:.066}],376:[.849,0,.733,{ic:.119}],377:[.885,0,.672,{ic:.079}],378:[.694,0,.476,{ic:.063}],379:[.861,0,.672,{ic:.079}],380:[.673,0,.476,{ic:.052}],381:[.873,0,.672,{ic:.079}],382:[.697,0,.476,{ic:.072}],383:[.705,0,.311,{ic:.189}],398:[.691,0,.642,{ic:.093}],402:[.705,0,.336,{ic:.189}],416:[.825,.022,.794,{ic:.134}],417:[.589,.011,.55,{ic:.162}],431:[.825,.022,.825,{ic:.156}],432:[.589,.011,.622,{ic:.137}],461:[.873,0,.733],462:[.697,.011,.525,{ic:.048}],463:[.873,0,.331,{ic:.193}],464:[.697,0,.256,{ic:.182}],465:[.873,.022,.794,{ic:.026}],466:[.697,.011,.55,{ic:.035}],467:[.873,.022,.764,{ic:.048}],468:[.697,.011,.561,{ic:.03}],471:[1.04,.022,.764,{ic:.048}],472:[.858,.011,.561,{ic:.107}],473:[1.028,.022,.764,{ic:.048}],474:[.846,.011,.561,{ic:.073}],475:[1.04,.022,.764,{ic:.048}],476:[.858,.011,.561,{ic:.042}],477:[.475,.011,.511,{ic:.025}],486:[.873,.011,.733,{ic:.053}],487:[.697,.206,.55,{ic:.071}],490:[.716,.213,.794,{ic:.026}],491:[.475,.213,.55,{ic:.025}],496:[.697,.205,.286,{ic:.175}],500:[.885,.011,.733,{ic:.053}],501:[.694,.206,.55,{ic:.071}],506:[1.031,0,.733,{ic:.007}],507:[.863,.011,.525,{ic:.074}],508:[.885,0,.947,{ic:.078}],509:[.694,.011,.794,{ic:.031}],510:[.885,.086,.856,{ic:.025}],511:[.694,.133,.55,{ic:.063}],512:[.913,0,.733],513:[.694,.011,.525,{ic:.021}],516:[.913,0,.642,{ic:.077}],517:[.694,.011,.511,{ic:.031}],520:[.913,0,.331,{ic:.162}],521:[.694,0,.256,{ic:.154}],524:[.913,.022,.794,{ic:.026}],525:[.694,.011,.55,{ic:.025}],528:[.913,0,.703,{ic:.049}],529:[.694,0,.372,{ic:.096}],532:[.913,.022,.764,{ic:.048}],533:[.694,.011,.561,{ic:.029}],536:[.716,.292,.611,{ic:.041}],537:[.475,.292,.422,{ic:.047}],538:[.688,.292,.733,{ic:.098}],539:[.589,.292,.404,{ic:.042}],7692:[.694,.2,.794,{ic:.024}],7693:[.694,.2,.561,{ic:.078}],7694:[.694,.184,.794,{ic:.024}],7695:[.694,.184,.561,{ic:.078}],7716:[.694,.2,.794,{ic:.048}],7717:[.694,.2,.561,{ic:.014}],7718:[.849,0,.794,{ic:.048}],7719:[.849,0,.561,{ic:.102}],7722:[.694,.213,.794,{ic:.048}],7723:[.694,.213,.561,{ic:.014}],7726:[1.04,0,.331,{ic:.26}],7727:[.858,0,.256,{ic:.259}],7734:[.694,.2,.581],7735:[.694,.2,.256,{ic:.078}],7736:[.851,.2,.581],7737:[.851,.2,.256,{ic:.271}],7746:[.694,.2,.978,{ic:.048}],7747:[.469,.2,.867,{ic:.014}],7748:[.861,0,.794,{ic:.048}],7749:[.673,0,.561,{ic:.014}],7750:[.694,.2,.794,{ic:.048}],7751:[.469,.2,.561,{ic:.014}],7768:[.861,0,.703,{ic:.049}],7769:[.673,0,.372,{ic:.081}],7770:[.694,.2,.703,{ic:.049}],7771:[.469,.2,.372,{ic:.081}],7772:[.851,.2,.703,{ic:.049}],7773:[.658,.2,.372,{ic:.15}],7778:[.716,.2,.611,{ic:.041}],7779:[.475,.2,.422,{ic:.047}],7788:[.688,.2,.733,{ic:.098}],7789:[.589,.2,.404,{ic:.042}],7790:[.688,.184,.733,{ic:.098}],7791:[.589,.184,.404,{ic:.042}],7808:[.885,0,1.039,{ic:.117}],7809:[.694,0,.744,{ic:.066}],7810:[.885,0,1.039,{ic:.117}],7811:[.694,0,.744,{ic:.066}],7812:[.849,0,1.039,{ic:.117}],7813:[.667,0,.744,{ic:.066}],7826:[.694,.2,.672,{ic:.079}],7827:[.458,.2,.476,{ic:.052}],7831:[.744,.011,.404,{ic:.11}],7840:[.694,.2,.733],7841:[.475,.2,.525,{ic:.021}],7842:[.952,0,.733],7843:[.719,.011,.525,{ic:.021}],7844:[1.052,0,.733,{ic:.01}],7845:[.841,.011,.525,{ic:.069}],7846:[1.052,0,.733],7847:[.841,.011,.525,{ic:.047}],7848:[1.109,0,.733,{ic:.07}],7849:[.898,.011,.525,{ic:.129}],7850:[1.074,0,.733,{ic:.042}],7851:[.863,.011,.525,{ic:.101}],7852:[.873,.2,.733],7853:[.697,.2,.525,{ic:.021}],7854:[1.077,0,.733,{ic:.016}],7855:[.866,.011,.525,{ic:.076}],7856:[1.077,0,.733,{ic:.007}],7857:[.866,.011,.525,{ic:.066}],7858:[1.134,0,.733,{ic:.007}],7859:[.923,.011,.525,{ic:.066}],7860:[1.099,0,.733,{ic:.048}],7861:[.888,.011,.525,{ic:.107}],7862:[.898,.2,.733,{ic:.007}],7863:[.687,.2,.525,{ic:.066}],7864:[.691,.2,.642,{ic:.077}],7865:[.475,.2,.511,{ic:.031}],7866:[.952,0,.642,{ic:.077}],7867:[.719,.011,.511,{ic:.031}],7868:[.877,0,.642,{ic:.077}],7869:[.681,.011,.511,{ic:.07}],7870:[1.052,0,.642,{ic:.077}],7871:[.841,.011,.511,{ic:.076}],7872:[1.052,0,.642,{ic:.077}],7873:[.841,.011,.511,{ic:.054}],7874:[1.109,0,.642,{ic:.115}],7875:[.898,.011,.511,{ic:.136}],7876:[1.074,0,.642,{ic:.087}],7877:[.863,.011,.511,{ic:.108}],7878:[.873,.2,.642,{ic:.077}],7879:[.697,.2,.511,{ic:.031}],7880:[.952,0,.331,{ic:.129}],7881:[.719,0,.256,{ic:.118}],7882:[.694,.2,.331,{ic:.048}],7883:[.673,.2,.256,{ic:.081}],7884:[.716,.2,.794,{ic:.026}],7885:[.475,.2,.55,{ic:.025}],7886:[.952,.022,.794,{ic:.026}],7887:[.719,.011,.55,{ic:.025}],7888:[1.052,.022,.794,{ic:.026}],7889:[.841,.011,.55,{ic:.056}],7890:[1.052,.022,.794,{ic:.026}],7891:[.841,.011,.55,{ic:.034}],7892:[1.109,.022,.794,{ic:.039}],7893:[.898,.011,.55,{ic:.116}],7894:[1.074,.022,.794,{ic:.026}],7895:[.863,.011,.55,{ic:.088}],7896:[.873,.2,.794,{ic:.026}],7897:[.697,.2,.55,{ic:.025}],7898:[.885,.022,.794,{ic:.134}],7899:[.694,.011,.55,{ic:.162}],7900:[.885,.022,.794,{ic:.134}],7901:[.694,.011,.55,{ic:.162}],7902:[.952,.022,.794,{ic:.134}],7903:[.719,.011,.55,{ic:.162}],7904:[.877,.022,.794,{ic:.134}],7905:[.681,.011,.55,{ic:.162}],7906:[.825,.2,.794,{ic:.134}],7907:[.589,.2,.55,{ic:.162}],7908:[.694,.2,.764,{ic:.048}],7909:[.458,.2,.561,{ic:.029}],7910:[.952,.022,.764,{ic:.048}],7911:[.719,.011,.561,{ic:.029}],7912:[.885,.022,.825,{ic:.156}],7913:[.694,.011,.622,{ic:.137}],7914:[.885,.022,.825,{ic:.156}],7915:[.694,.011,.622,{ic:.137}],7916:[.952,.022,.825,{ic:.156}],7917:[.719,.011,.622,{ic:.137}],7918:[.877,.022,.825,{ic:.156}],7919:[.681,.011,.622,{ic:.137}],7920:[.825,.2,.825,{ic:.156}],7921:[.589,.2,.622,{ic:.137}],7922:[.885,0,.733,{ic:.119}],7923:[.694,.205,.5,{ic:.066}],7924:[.694,.2,.733,{ic:.119}],7925:[.458,.205,.5,{ic:.066}],7926:[.952,0,.733,{ic:.119}],7927:[.719,.205,.5,{ic:.066}],7928:[.877,0,.733,{ic:.119}],7929:[.681,.205,.5,{ic:.075}]}}},"MM"),{},["MJX-MM-SSLIB"]);MathJax.loader&&MathJax.loader.checkVersion("[mathjax-modern]/chtml/dynamic/sans-serif-bi","4.0.0-beta.7","dynamic-font")})();