package com.logictrue.word.service;

import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.util.List;

/**
 * 简单的Word导出服务
 * 专门用于测试，不依赖复杂的框架和配置
 */
public class SimpleWordService {

    /**
     * 导出Word文档
     */
    public byte[] exportWord(SimpleTableConfig config) throws IOException {
        try (XWPFDocument document = new XWPFDocument();
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {

            // 设置页面方向
            setPageOrientation(document, config.isLandscape());

            // 添加标题
            if (config.getTitle() != null && !config.getTitle().trim().isEmpty()) {
                addTitle(document, config.getTitle());
            }

            // 创建表格
            createTable(document, config);

            document.write(out);
            return out.toByteArray();
        }
    }

    /**
     * 设置页面方向 (POI 5.2.3兼容)
     */
    private void setPageOrientation(XWPFDocument document, boolean landscape) {
        try {
            CTSectPr sectPr = document.getDocument().getBody().getSectPr();
            if (sectPr == null) {
                sectPr = document.getDocument().getBody().addNewSectPr();
            }

            CTPageSz pageSize = sectPr.getPgSz();
            if (pageSize == null) {
                pageSize = sectPr.addNewPgSz();
            }

            if (landscape) {
                // 横向：宽度 > 高度 (A4: 297mm × 210mm)
                pageSize.setW(BigInteger.valueOf(16838)); // 297mm in twips
                pageSize.setH(BigInteger.valueOf(11906)); // 210mm in twips
                pageSize.setOrient(STPageOrientation.LANDSCAPE);
            } else {
                // 纵向：高度 > 宽度 (A4: 210mm × 297mm)
                pageSize.setW(BigInteger.valueOf(11906)); // 210mm in twips
                pageSize.setH(BigInteger.valueOf(16838)); // 297mm in twips
                pageSize.setOrient(STPageOrientation.PORTRAIT);
            }
        } catch (Exception e) {
            System.out.println("警告: 页面方向设置失败: " + e.getMessage());
        }
    }

    /**
     * 添加标题
     */
    private void addTitle(XWPFDocument document, String title) {
        XWPFParagraph titleParagraph = document.createParagraph();
        titleParagraph.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun titleRun = titleParagraph.createRun();
        titleRun.setText(title);
        titleRun.setBold(true);
        titleRun.setFontSize(16);
        titleRun.setFontFamily("宋体");
    }

    /**
     * 创建表格
     */
    private void createTable(XWPFDocument document, SimpleTableConfig config) {
        int totalRows = config.getHeaderRows().size() + config.getDataRows().size();
        int totalCols = config.getColumnWidths().size();

        // 创建表格
        XWPFTable table = document.createTable(totalRows, totalCols);

        // 设置表格样式
        setTableStyle(table, config);

        int currentRow = 0;

        // 处理表头
        for (int i = 0; i < config.getHeaderRows().size(); i++) {
            XWPFTableRow row = table.getRow(currentRow);
            List<String> headerRow = config.getHeaderRows().get(i);
            processHeaderRow(row, headerRow, config, i);
            currentRow++;
        }

        // 处理数据行
        for (List<String> dataRow : config.getDataRows()) {
            XWPFTableRow row = table.getRow(currentRow);
            processDataRow(row, dataRow, config);
            currentRow++;
        }

        // 应用合并
        applyMerges(table, config);

        // 设置边框
        setTableBorders(table);
    }

    /**
     * 设置表格样式
     */
    private void setTableStyle(XWPFTable table, SimpleTableConfig config) {
        // 计算总宽度
        int totalWidth = config.getColumnWidths().stream().mapToInt(Integer::intValue).sum();
        int totalWidthTwips = totalWidth * 15; // 像素转Twips

        // 设置表格宽度
        CTTblPr tblPr = table.getCTTbl().getTblPr();
        if (tblPr == null) {
            tblPr = table.getCTTbl().addNewTblPr();
        }

        CTTblWidth tblWidth = tblPr.getTblW();
        if (tblWidth == null) {
            tblWidth = tblPr.addNewTblW();
        }
        tblWidth.setType(STTblWidth.DXA);
        tblWidth.setW(BigInteger.valueOf(totalWidthTwips));

        CTJcTable jcTable = tblPr.getJc();
        if (jcTable == null) {
            jcTable = tblPr.addNewJc();
        }
        jcTable.setVal(STJcTable.CENTER);

    }

    /**
     * 处理表头行
     */
    private void processHeaderRow(XWPFTableRow row, List<String> headerRow, SimpleTableConfig config, int rowIndex) {
        for (int i = 0; i < headerRow.size() && i < row.getTableCells().size(); i++) {
            XWPFTableCell cell = row.getCell(i);
            String content = headerRow.get(i);

            // 设置内容
            if (content != null && !content.trim().isEmpty()) {
                setCellContent(cell, content, true);
            }

            // 设置列宽
            if (i < config.getColumnWidths().size()) {
                setCellWidth(cell, config.getColumnWidths().get(i));
            }

            // 设置行高
            if (rowIndex < config.getHeaderHeights().size()) {
                setCellHeight(row, config.getHeaderHeights().get(rowIndex));
            }
        }
    }

    /**
     * 处理数据行
     */
    private void processDataRow(XWPFTableRow row, List<String> dataRow, SimpleTableConfig config) {
        for (int i = 0; i < dataRow.size() && i < row.getTableCells().size(); i++) {
            XWPFTableCell cell = row.getCell(i);
            String content = dataRow.get(i);

            // 设置内容
            if (content != null && !content.trim().isEmpty()) {
                setCellContent(cell, content, false);
            }

            // 设置列宽
            if (i < config.getColumnWidths().size()) {
                setCellWidth(cell, config.getColumnWidths().get(i));
            }
        }
    }

    /**
     * 设置单元格内容
     */
    private void setCellContent(XWPFTableCell cell, String content, boolean isHeader) {
        // 清除默认段落
        cell.removeParagraph(0);

        XWPFParagraph paragraph = cell.addParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);

        XWPFRun run = paragraph.createRun();
        run.setText(content);
        run.setFontFamily("宋体");
        run.setFontSize(isHeader ? 12 : 11);

        if (isHeader) {
            run.setBold(true);
        }
    }

    /**
     * 设置单元格宽度
     */
    private void setCellWidth(XWPFTableCell cell, int widthPx) {
        int widthTwips = widthPx * 15;

        CTTcPr tcPr = cell.getCTTc().getTcPr();
        if (tcPr == null) {
            tcPr = cell.getCTTc().addNewTcPr();
        }

        CTTblWidth cellWidth = tcPr.getTcW();
        if (cellWidth == null) {
            cellWidth = tcPr.addNewTcW();
        }

        cellWidth.setType(STTblWidth.DXA);
        cellWidth.setW(BigInteger.valueOf(widthTwips));
    }

    /**
     * 设置行高
     */
    private void setCellHeight(XWPFTableRow row, int heightPx) {
        int heightTwips = heightPx * 15;

        CTTrPr trPr = row.getCtRow().getTrPr();
        if (trPr == null) {
            trPr = row.getCtRow().addNewTrPr();
        }

        CTHeight rowHeight = trPr.getTrHeightList().isEmpty() ?
                trPr.addNewTrHeight() : trPr.getTrHeightList().get(0);

        rowHeight.setVal(BigInteger.valueOf(heightTwips));
        rowHeight.setHRule(STHeightRule.AT_LEAST);
    }

    /**
     * 应用合并
     */
    private void applyMerges(XWPFTable table, SimpleTableConfig config) {
        for (SimpleMergeInfo merge : config.getMerges()) {
            applyMerge(table, merge);
        }
    }

    /**
     * 应用单个合并
     */
    private void applyMerge(XWPFTable table, SimpleMergeInfo merge) {
        // 验证合并信息的有效性
        if (!isValidMerge(table, merge)) {
            System.out.println("警告: 跳过无效的合并信息: " + merge);
            return;
        }

        // 获取主单元格
        XWPFTableCell mainCell = table.getRow(merge.getStartRow()).getCell(merge.getStartCol());

        // 设置主单元格内容
        if (merge.getContent() != null && !merge.getContent().trim().isEmpty()) {
            setCellContent(mainCell, merge.getContent(), merge.isHeader());
        }

        // 水平合并
        if (merge.getColspan() > 1) {
            // 设置主单元格的gridSpan
            CTTcPr tcPr = mainCell.getCTTc().getTcPr();
            if (tcPr == null) {
                tcPr = mainCell.getCTTc().addNewTcPr();
            }

            CTDecimalNumber gridSpan = tcPr.getGridSpan();
            if (gridSpan == null) {
                gridSpan = tcPr.addNewGridSpan();
            }
            gridSpan.setVal(BigInteger.valueOf(merge.getColspan()));

            // 设置被合并的单元格为隐藏或合并继续
            XWPFTableRow row = table.getRow(merge.getStartRow());
            if (row == null) {
                System.out.println("警告: 无法获取行 " + merge.getStartRow() + "，跳过水平合并");
                return;
            }

            for (int col = merge.getStartCol() + 1; col <= merge.getEndCol(); col++) {
                XWPFTableCell cell = row.getCell(col);
                if (cell == null) {
                    System.out.println("警告: 无法获取单元格 [" + merge.getStartRow() + "," + col + "]，跳过此列的水平合并");
                    continue;
                }

                CTTcPr cellTcPr = cell.getCTTc().getTcPr();
                if (cellTcPr == null) {
                    cellTcPr = cell.getCTTc().addNewTcPr();
                }

                // 设置水平合并继续
                CTHMerge hMerge = cellTcPr.getHMerge();
                if (hMerge == null) {
                    hMerge = cellTcPr.addNewHMerge();
                }
                hMerge.setVal(STMerge.CONTINUE);
            }

            // 设置主单元格的水平合并开始
            CTHMerge mainHMerge = tcPr.getHMerge();
            if (mainHMerge == null) {
                mainHMerge = tcPr.addNewHMerge();
            }
            mainHMerge.setVal(STMerge.RESTART);
        }

        // 垂直合并
        if (merge.getRowspan() > 1) {
            // 设置主单元格为合并开始
            CTTcPr mainTcPr = mainCell.getCTTc().getTcPr();
            if (mainTcPr == null) {
                mainTcPr = mainCell.getCTTc().addNewTcPr();
            }
            CTVMerge vMerge = mainTcPr.getVMerge();
            if (vMerge == null) {
                vMerge = mainTcPr.addNewVMerge();
            }
            vMerge.setVal(STMerge.RESTART);

            // 设置其他单元格为合并继续
            for (int row = merge.getStartRow() + 1; row <= merge.getEndRow(); row++) {
                XWPFTableRow tableRow = table.getRow(row);
                if (tableRow == null) {
                    System.out.println("警告: 行索引 " + row + " 超出表格范围，跳过垂直合并");
                    continue;
                }

                XWPFTableCell cell = tableRow.getCell(merge.getStartCol());
                if (cell == null) {
                    System.out.println("警告: 列索引 " + merge.getStartCol() + " 超出表格范围，跳过垂直合并");
                    continue;
                }

                CTTcPr tcPr = cell.getCTTc().getTcPr();
                if (tcPr == null) {
                    tcPr = cell.getCTTc().addNewTcPr();
                }
                CTVMerge cellVMerge = tcPr.getVMerge();
                if (cellVMerge == null) {
                    cellVMerge = tcPr.addNewVMerge();
                }
                cellVMerge.setVal(STMerge.CONTINUE);
            }
        }
    }

    /**
     * 设置表格边框 (POI 5.2.3兼容)
     */
    private void setTableBorders(XWPFTable table) {
        try {
            CTTblPr tblPr = table.getCTTbl().getTblPr();
            if (tblPr == null) {
                tblPr = table.getCTTbl().addNewTblPr();
            }

            CTTblBorders borders = tblPr.getTblBorders();
            if (borders == null) {
                borders = tblPr.addNewTblBorders();
            }

            // 创建边框样式 (POI 5.x兼容写法)
            CTBorder border = CTBorder.Factory.newInstance();
            border.setVal(STBorder.SINGLE);
            border.setSz(BigInteger.valueOf(4));
            border.setColor("000000");

            // 设置所有边框
            if (borders.getTop() == null) borders.addNewTop().set(border);
            if (borders.getBottom() == null) borders.addNewBottom().set(border);
            if (borders.getLeft() == null) borders.addNewLeft().set(border);
            if (borders.getRight() == null) borders.addNewRight().set(border);
            if (borders.getInsideH() == null) borders.addNewInsideH().set(border);
            if (borders.getInsideV() == null) borders.addNewInsideV().set(border);

        } catch (Exception e) {
            System.out.println("警告: 表格边框设置失败: " + e.getMessage());
            // 使用简单的边框设置作为备选方案
            setSimpleBorders(table);
        }
    }

    /**
     * 简单边框设置 (备选方案)
     */
    private void setSimpleBorders(XWPFTable table) {
        try {
            // 使用POI的高级API设置边框
            table.setTableAlignment(TableRowAlign.CENTER);
        } catch (Exception e) {
            System.out.println("警告: 简单边框设置也失败，使用默认样式");
        }
    }

    /**
     * 验证合并信息是否有效
     */
    private boolean isValidMerge(XWPFTable table, SimpleMergeInfo merge) {
        if (merge == null) {
            System.out.println("警告: 合并信息为null");
            return false;
        }

        int tableRows = table.getRows().size();
        int tableCols = table.getRow(0).getTableCells().size();

        // 检查行索引
        if (merge.getStartRow() < 0 || merge.getStartRow() >= tableRows) {
            System.out.println("警告: 起始行索引超出范围: " + merge.getStartRow() + " (表格行数: " + tableRows + ")");
            return false;
        }
        if (merge.getEndRow() < 0 || merge.getEndRow() >= tableRows) {
            System.out.println("警告: 结束行索引超出范围: " + merge.getEndRow() + " (表格行数: " + tableRows + ")");
            return false;
        }

        // 检查列索引
        if (merge.getStartCol() < 0 || merge.getStartCol() >= tableCols) {
            System.out.println("警告: 起始列索引超出范围: " + merge.getStartCol() + " (表格列数: " + tableCols + ")");
            return false;
        }
        if (merge.getEndCol() < 0 || merge.getEndCol() >= tableCols) {
            System.out.println("警告: 结束列索引超出范围: " + merge.getEndCol() + " (表格列数: " + tableCols + ")");
            return false;
        }

        // 检查合并范围的逻辑性
        if (merge.getStartRow() > merge.getEndRow()) {
            System.out.println("警告: 起始行大于结束行: " + merge.getStartRow() + " > " + merge.getEndRow());
            return false;
        }
        if (merge.getStartCol() > merge.getEndCol()) {
            System.out.println("警告: 起始列大于结束列: " + merge.getStartCol() + " > " + merge.getEndCol());
            return false;
        }

        return true;
    }
}
