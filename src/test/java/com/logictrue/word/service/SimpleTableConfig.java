package com.logictrue.word.service;

import java.util.ArrayList;
import java.util.List;

/**
 * 简单的表格配置类
 */
public class SimpleTableConfig {
    
    private String title;                           // 表格标题
    private boolean landscape = true;               // 是否横向（默认横向）
    private List<List<String>> headerRows;          // 表头行数据
    private List<List<String>> dataRows;            // 数据行数据
    private List<Integer> columnWidths;             // 列宽配置（像素）
    private List<Integer> headerHeights;            // 表头行高配置（像素）
    private List<SimpleMergeInfo> merges;           // 合并单元格信息

    public SimpleTableConfig() {
        this.headerRows = new ArrayList<>();
        this.dataRows = new ArrayList<>();
        this.columnWidths = new ArrayList<>();
        this.headerHeights = new ArrayList<>();
        this.merges = new ArrayList<>();
    }

    // Getters and Setters
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public boolean isLandscape() {
        return landscape;
    }

    public void setLandscape(boolean landscape) {
        this.landscape = landscape;
    }

    public List<List<String>> getHeaderRows() {
        return headerRows;
    }

    public void setHeaderRows(List<List<String>> headerRows) {
        this.headerRows = headerRows;
    }

    public List<List<String>> getDataRows() {
        return dataRows;
    }

    public void setDataRows(List<List<String>> dataRows) {
        this.dataRows = dataRows;
    }

    public List<Integer> getColumnWidths() {
        return columnWidths;
    }

    public void setColumnWidths(List<Integer> columnWidths) {
        this.columnWidths = columnWidths;
    }

    public List<Integer> getHeaderHeights() {
        return headerHeights;
    }

    public void setHeaderHeights(List<Integer> headerHeights) {
        this.headerHeights = headerHeights;
    }

    public List<SimpleMergeInfo> getMerges() {
        return merges;
    }

    public void setMerges(List<SimpleMergeInfo> merges) {
        this.merges = merges;
    }

    // 便捷方法
    public void addHeaderRow(List<String> headerRow) {
        this.headerRows.add(headerRow);
    }

    public void addDataRow(List<String> dataRow) {
        this.dataRows.add(dataRow);
    }

    public void addColumnWidth(int width) {
        this.columnWidths.add(width);
    }

    public void addHeaderHeight(int height) {
        this.headerHeights.add(height);
    }

    public void addMerge(SimpleMergeInfo merge) {
        this.merges.add(merge);
    }

    public void addMerge(int startRow, int startCol, int endRow, int endCol, String content, boolean isHeader) {
        SimpleMergeInfo merge = new SimpleMergeInfo();
        merge.setStartRow(startRow);
        merge.setStartCol(startCol);
        merge.setEndRow(endRow);
        merge.setEndCol(endCol);
        merge.setContent(content);
        merge.setHeader(isHeader);
        this.merges.add(merge);
    }
}

/**
 * 简单的合并信息类
 */
class SimpleMergeInfo {
    private int startRow;       // 起始行
    private int startCol;       // 起始列
    private int endRow;         // 结束行
    private int endCol;         // 结束列
    private String content;     // 合并后的内容
    private boolean isHeader;   // 是否为表头合并

    // 计算跨度
    public int getRowspan() {
        return endRow - startRow + 1;
    }

    public int getColspan() {
        return endCol - startCol + 1;
    }

    // Getters and Setters
    public int getStartRow() {
        return startRow;
    }

    public void setStartRow(int startRow) {
        this.startRow = startRow;
    }

    public int getStartCol() {
        return startCol;
    }

    public void setStartCol(int startCol) {
        this.startCol = startCol;
    }

    public int getEndRow() {
        return endRow;
    }

    public void setEndRow(int endRow) {
        this.endRow = endRow;
    }

    public int getEndCol() {
        return endCol;
    }

    public void setEndCol(int endCol) {
        this.endCol = endCol;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public boolean isHeader() {
        return isHeader;
    }

    public void setHeader(boolean header) {
        isHeader = header;
    }

    @Override
    public String toString() {
        return String.format("Merge[(%d,%d)->(%d,%d), %dx%d, %s, %s]", 
                           startRow, startCol, endRow, endCol, 
                           getColspan(), getRowspan(), 
                           content, isHeader ? "Header" : "Data");
    }
}
