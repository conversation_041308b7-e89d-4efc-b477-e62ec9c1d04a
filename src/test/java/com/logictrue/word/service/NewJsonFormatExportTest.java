package com.logictrue.word.service;

import com.logictrue.word.dto.JsonTableExportRequest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 新JSON格式导出测试
 * 测试参考SimpleWordService重构后的WordExportService中的新JSON格式导出功能
 */
public class NewJsonFormatExportTest {

    public static void main(String[] args) {
        System.out.println("🚀 开始测试新JSON格式导出功能...");

        // 创建Spring应用上下文
        ApplicationContext context = new AnnotationConfigApplicationContext(
            com.logictrue.LogicTrueWordApplication.class
        );

        try {
            // 获取WordExportService
            WordExportService wordExportService = context.getBean(WordExportService.class);

            // 创建测试请求
            JsonTableExportRequest request = createTestJsonRequest();

            // 导出Word文档
            System.out.println("📝 开始导出Word文档...");
            byte[] wordBytes = wordExportService.exportNewJsonFormatToWord(request);

            // 保存到文件
            String fileName = "test_new_json_format_export.docx";
            try (FileOutputStream fos = new FileOutputStream(fileName)) {
                fos.write(wordBytes);
                System.out.println("✅ 导出成功！文件已保存为: " + fileName);
                System.out.println("📊 文件大小: " + wordBytes.length + " bytes");
            }

        } catch (Exception e) {
            System.err.println("❌ 导出失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 关闭应用上下文
            if (context instanceof AnnotationConfigApplicationContext) {
                ((AnnotationConfigApplicationContext) context).close();
            }
        }
    }

    /**
     * 创建测试用的JSON请求数据
     * 基于示例JSON数据格式
     */
    private static JsonTableExportRequest createTestJsonRequest() {
        JsonTableExportRequest request = new JsonTableExportRequest();

        // 设置标题
        request.setTitle("检验记录表");

        // 设置表头（两行）
        List<List<String>> headers = new ArrayList<>();
        headers.add(Arrays.asList("产品名称", "生产信息", "", "责任人员", "", ""));
        headers.add(Arrays.asList("", "批次号", "日期", "检验员", "审核员", "负责人"));
        request.setHeaders(headers);

        // 设置数据行
        List<List<String>> rows = new ArrayList<>();
        rows.add(Arrays.asList("智能手机\n（多功能检测）", "A001", "2024-01-15", "张三", "李四", "王五"));
        request.setRows(rows);

        // 设置表头合并
        List<JsonTableExportRequest.MergeConfig> headerMerges = new ArrayList<>();

        // 产品名称列合并（跨两行）
        JsonTableExportRequest.MergeConfig productNameMerge = new JsonTableExportRequest.MergeConfig();
        productNameMerge.setStartRow(0);
        productNameMerge.setStartCol(0);
        productNameMerge.setEndRow(1);
        productNameMerge.setEndCol(0);
        productNameMerge.setContent("产品名称");
        headerMerges.add(productNameMerge);

        // 生产信息合并（跨两列）
        JsonTableExportRequest.MergeConfig productionMerge = new JsonTableExportRequest.MergeConfig();
        productionMerge.setStartRow(0);
        productionMerge.setStartCol(1);
        productionMerge.setEndRow(0);
        productionMerge.setEndCol(2);
        productionMerge.setContent("生产信息");
        headerMerges.add(productionMerge);

        // 责任人员合并（跨三列）
        JsonTableExportRequest.MergeConfig responsibleMerge = new JsonTableExportRequest.MergeConfig();
        responsibleMerge.setStartRow(0);
        responsibleMerge.setStartCol(3);
        responsibleMerge.setEndRow(0);
        responsibleMerge.setEndCol(5);
        responsibleMerge.setContent("责任人员");
        headerMerges.add(responsibleMerge);

        request.setHeaderMerges(headerMerges);

        // 设置数据行合并
        List<JsonTableExportRequest.MergeConfig> merges = new ArrayList<>();

        // 产品名称数据合并（示例中的智能手机合并）
        JsonTableExportRequest.MergeConfig dataMerge = new JsonTableExportRequest.MergeConfig();
        dataMerge.setStartRow(0); // 数据行的第一行
        dataMerge.setStartCol(0);
        dataMerge.setEndRow(0);   // 只合并一行（如果需要跨行可以设置为1）
        dataMerge.setEndCol(0);
        dataMerge.setContent("智能手机\n（多功能检测）");
        merges.add(dataMerge);

        request.setMerges(merges);

        // 设置表头宽度配置
        JsonTableExportRequest.HeaderWidthConfig headerWidthConfig = new JsonTableExportRequest.HeaderWidthConfig();
        headerWidthConfig.setColumnWidths(Arrays.asList(280, 100, 100, 80, 80, 80));
        headerWidthConfig.setHeaderHeights(Arrays.asList(60, 40));
        request.setHeaderWidthConfig(headerWidthConfig);

        // 设置纵向文字配置（假设后三列需要纵向显示）
        request.setVerticalHeadersConfig(Arrays.asList(false, false, false, true, true, true));

        // 设置元数据
        JsonTableExportRequest.MetadataInfo metadata = new JsonTableExportRequest.MetadataInfo();
        metadata.setTitle("检验记录表");
        metadata.setTotalRows(2);
        metadata.setTotalColumns(6);
        metadata.setHeaderRows(2);
        metadata.setExportTime("2025-08-21T05:24:24.642Z");
        metadata.setHasMergedCells(true);
        metadata.setHasHeaderMerges(true);
        request.setMetadata(metadata);

        return request;
    }
}
