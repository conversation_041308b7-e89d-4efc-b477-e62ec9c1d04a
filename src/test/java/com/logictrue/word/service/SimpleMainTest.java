package com.logictrue.word.service;

import com.logictrue.word.dto.TableExportRequest;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 简单的main方法测试
 * 不依赖Spring Boot，直接创建WordExportService实例
 */
public class SimpleMainTest {

    public static void main(String[] args) {
        try {
            // 直接创建WordExportService实例
            WordExportService wordExportService = new WordExportService();
            
            // 创建测试请求
            TableExportRequest request = createTestRequest();
            
            // 导出Word文档
            System.out.println("开始导出Word文档...");
            byte[] wordBytes = wordExportService.exportTableToWordWithJson(request);
            
            // 保存到文件
            String fileName = "test_export_" + System.currentTimeMillis() + ".docx";
            try (FileOutputStream fos = new FileOutputStream(fileName)) {
                fos.write(wordBytes);
            }
            
            System.out.println("✅ Word文档导出成功！");
            System.out.println("📁 文件名: " + fileName);
            System.out.println("📊 文件大小: " + wordBytes.length + " bytes");
            System.out.println("🎯 表格配置已应用");
            
        } catch (Exception e) {
            System.err.println("❌ 导出失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建测试请求
     * 横向A4纸张，两行八列表头，一行包含合并单元格的数据
     */
    private static TableExportRequest createTestRequest() {
        System.out.println("🔧 创建测试配置...");
        
        TableExportRequest request = new TableExportRequest();
        request.setTitle("产品检测记录表");
        request.setPageOrientation("LANDSCAPE"); // 横向A4纸张

        // 创建表格数据
        TableExportRequest.TableData tableData = new TableExportRequest.TableData();
        
        // === 创建两行八列表头 ===
        List<List<TableExportRequest.HeaderCell>> headers = new ArrayList<>();
        
        // 第一行表头
        List<TableExportRequest.HeaderCell> headerRow1 = new ArrayList<>();
        headerRow1.add(createHeaderCell("产品信息"));      // 列1-2合并
        headerRow1.add(createHeaderCell(""));
        headerRow1.add(createHeaderCell("检测项目"));      // 列3-4合并
        headerRow1.add(createHeaderCell(""));
        headerRow1.add(createHeaderCell("检测结果"));      // 列5-6合并
        headerRow1.add(createHeaderCell(""));
        headerRow1.add(createHeaderCell("责任人"));        // 行1-2合并
        headerRow1.add(createHeaderCell("备注"));          // 行1-2合并
        headers.add(headerRow1);
        
        // 第二行表头
        List<TableExportRequest.HeaderCell> headerRow2 = new ArrayList<>();
        headerRow2.add(createHeaderCell("产品名称"));
        headerRow2.add(createHeaderCell("产品型号"));
        headerRow2.add(createHeaderCell("外观检测"));
        headerRow2.add(createHeaderCell("功能测试"));
        headerRow2.add(createHeaderCell("合格"));
        headerRow2.add(createHeaderCell("不合格"));
        headerRow2.add(createHeaderCell(""));             // 被上面合并
        headerRow2.add(createHeaderCell(""));             // 被上面合并
        headers.add(headerRow2);
        
        tableData.setHeaders(headers);

        // === 创建一行包含合并单元格的数据 ===
        List<List<TableExportRequest.DataCell>> dataRows = new ArrayList<>();
        List<TableExportRequest.DataCell> dataRow1 = new ArrayList<>();
        dataRow1.add(createDataCell("智能手机"));
        dataRow1.add(createDataCell("iPhone 15 Pro"));
        dataRow1.add(createDataCell("外观完好"));
        dataRow1.add(createDataCell("功能正常"));
        dataRow1.add(createDataCell("✓ 合格"));           // 列5-6合并
        dataRow1.add(createDataCell(""));                 // 被合并
        dataRow1.add(createDataCell("张三"));
        dataRow1.add(createDataCell("检测完成，符合标准"));
        dataRows.add(dataRow1);
        
        tableData.setDataRows(dataRows);
        request.setTableData(tableData);

        // === 设置表头宽度和高度配置 ===
        TableExportRequest.HeaderWidthConfig headerWidthConfig = new TableExportRequest.HeaderWidthConfig();
        
        // 配置八列的宽度（像素）
        List<Integer> columnWidths = new ArrayList<>();
        columnWidths.add(100);  // 产品名称
        columnWidths.add(120);  // 产品型号
        columnWidths.add(100);  // 外观检测
        columnWidths.add(100);  // 功能测试
        columnWidths.add(80);   // 合格
        columnWidths.add(80);   // 不合格
        columnWidths.add(100);  // 责任人
        columnWidths.add(160);  // 备注
        headerWidthConfig.setColumnWidths(columnWidths);
        
        // 配置两行表头的高度（像素）
        List<Integer> headerHeights = new ArrayList<>();
        headerHeights.add(60);  // 第一行表头高度
        headerHeights.add(45);  // 第二行表头高度
        headerWidthConfig.setHeaderHeights(headerHeights);
        
        request.setHeaderWidthConfig(headerWidthConfig);

        // === 创建表头合并信息 ===
        List<TableExportRequest.MergeCell> headerMerges = new ArrayList<>();
        
        // 产品信息 (列1-2合并)
        headerMerges.add(createMergeCell(0, 0, 0, 1, 2, 1, 220, 60, "产品信息"));
        
        // 检测项目 (列3-4合并)
        headerMerges.add(createMergeCell(0, 2, 0, 3, 2, 1, 200, 60, "检测项目"));
        
        // 检测结果 (列5-6合并)
        headerMerges.add(createMergeCell(0, 4, 0, 5, 2, 1, 160, 60, "检测结果"));
        
        // 责任人 (行1-2合并)
        headerMerges.add(createMergeCell(0, 6, 1, 6, 1, 2, 100, 105, "责任人"));
        
        // 备注 (行1-2合并)
        headerMerges.add(createMergeCell(0, 7, 1, 7, 1, 2, 160, 105, "备注"));
        
        request.setHeaderMerges(headerMerges);

        // === 创建数据行合并信息 ===
        List<TableExportRequest.MergeCell> dataMerges = new ArrayList<>();
        
        // 检测结果合并 (列5-6合并)
        dataMerges.add(createMergeCell(0, 4, 0, 5, 2, 1, 160, 45, "✓ 合格"));
        
        request.setMerges(dataMerges);

        // 打印配置信息
        printConfiguration(columnWidths, headerHeights, headerMerges.size(), dataMerges.size());

        return request;
    }

    /**
     * 创建表头单元格
     */
    private static TableExportRequest.HeaderCell createHeaderCell(String content) {
        TableExportRequest.HeaderCell cell = new TableExportRequest.HeaderCell();
        cell.setContent(content);
        return cell;
    }

    /**
     * 创建数据单元格
     */
    private static TableExportRequest.DataCell createDataCell(String content) {
        TableExportRequest.DataCell cell = new TableExportRequest.DataCell();
        cell.setContent(content);
        cell.setHasMath(false);
        return cell;
    }

    /**
     * 创建合并单元格信息
     */
    private static TableExportRequest.MergeCell createMergeCell(int startRow, int startCol, int endRow, int endCol,
                                                               int colspan, int rowspan, int width, int height, String content) {
        TableExportRequest.MergeCell merge = new TableExportRequest.MergeCell();
        merge.setStartRow(startRow);
        merge.setStartCol(startCol);
        merge.setEndRow(endRow);
        merge.setEndCol(endCol);
        merge.setColspan(colspan);
        merge.setRowspan(rowspan);
        merge.setWidth(width);
        merge.setHeight(height);
        merge.setContent(content);
        return merge;
    }

    /**
     * 打印配置信息
     */
    private static void printConfiguration(List<Integer> columnWidths, List<Integer> headerHeights, 
                                         int headerMergeCount, int dataMergeCount) {
        System.out.println("\n📋 === 表格配置信息 ===");
        System.out.println("📄 纸张方向: 横向A4");
        System.out.println("📊 表头规格: 2行 × 8列");
        System.out.println("📈 数据行数: 1行");
        System.out.println("📏 列宽配置: " + columnWidths + " (像素)");
        System.out.println("📐 行高配置: " + headerHeights + " (像素)");
        System.out.println("🔗 表头合并: " + headerMergeCount + " 个");
        System.out.println("🔗 数据合并: " + dataMergeCount + " 个");
        
        int totalWidth = columnWidths.stream().mapToInt(Integer::intValue).sum();
        System.out.println("📊 总宽度: " + totalWidth + "px (" + (totalWidth * 15) + " twips)");
        System.out.println("========================\n");
    }
}
