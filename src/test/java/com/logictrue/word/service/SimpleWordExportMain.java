package com.logictrue.word.service;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Arrays;

/**
 * 简单的Word导出测试主类
 * 横向A4纸张，两行八列的表头，一行包含合并单元格的数据
 */
public class SimpleWordExportMain {

    public static void main(String[] args) {
        try {
            System.out.println("🚀 开始Word导出测试...");

            // 创建简单的Word服务
            SimpleWordService wordService = new SimpleWordService();

            // 创建表格配置
            SimpleTableConfig config = createTableConfig();

            // 导出Word文档
            byte[] wordBytes = wordService.exportWord(config);

            // 保存到文件
            String fileName = "simple_word_export_" + System.currentTimeMillis() + ".docx";
            try (FileOutputStream fos = new FileOutputStream(fileName)) {
                fos.write(wordBytes);
            }

            System.out.println("✅ Word文档导出成功！");
            System.out.println("📁 文件名: " + fileName);
            System.out.println("📊 文件大小: " + wordBytes.length + " bytes");

        } catch (Exception e) {
            System.err.println("❌ 导出失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建表格配置
     * 横向A4纸张，两行八列表头，一行包含合并单元格的数据
     */
    private static SimpleTableConfig createTableConfig() {
        System.out.println("🔧 创建表格配置...");

        SimpleTableConfig config = new SimpleTableConfig();

        // 基本设置
        config.setTitle("产品质量检测记录表");
        config.setLandscape(true); // 横向A4纸张

        // === 配置列宽（8列，像素） ===
        config.setColumnWidths(Arrays.asList(
            50,  // 第1列：产品名称
            100,  // 第2列：产品型号
            90,   // 第3列：外观检测
            90,   // 第4列：功能测试
            70,   // 第5列：合格
            70,   // 第6列：不合格
            20,  // 第7列：检验员
            30   // 第8列：备注
        ));

        // === 配置表头行高（2行，像素） ===
        config.setHeaderHeights(Arrays.asList(
            55,   // 第1行表头高度
            45    // 第2行表头高度
        ));

        // === 创建两行八列表头（横向合并，主单元格有内容，被合并单元格留空） ===

        // 第一行表头
        config.addHeaderRow(Arrays.asList(
            "产品信息",    // 列1-2合并的主单元格
            "",           // 被合并的单元格，留空
            "检测项目",    // 列3-4合并的主单元格
            "",           // 被合并的单元格，留空
            "检测结果",    // 列5-6合并的主单元格
            "",           // 被合并的单元格，留空
            "检验人员",    // 行1-2合并显示
            "备注说明"     // 行1-2合并显示
        ));

        // 第二行表头
        config.addHeaderRow(Arrays.asList(
            "产品名称",
            "产品型号",
            "外观检测",
            "功能测试",
            "合格",
            "不合格",
            "",           // 被纵向合并的单元格，留空
            ""            // 被纵向合并的单元格，留空
        ));

        // === 创建一行包含合并单元格的数据 ===
        config.addDataRow(Arrays.asList(
            "智能手机",
            "iPhone 15 Pro Max",
            "外观完好",
            "功能正常",
            "✓ 合格",     // 列5-6合并的主单元格
            "",           // 被合并的单元格，留空
            "张三",
            "产品质量符合标准要求"
        ));

        // === 配置合并单元格 ===

        // 表头合并（第1行的横向合并）
        config.addMerge(0, 0, 0, 1, "产品信息", true);    // 第1行，列1-2合并
        config.addMerge(0, 2, 0, 3, "检测项目", true);    // 第1行，列3-4合并
        config.addMerge(0, 4, 0, 5, "检测结果", true);    // 第1行，列5-6合并

        // 表头合并（列7-8的纵向合并）
        config.addMerge(0, 6, 1, 6, "检验人员", true);    // 列7，行1-2合并
        config.addMerge(0, 7, 1, 7, "备注说明", true);    // 列8，行1-2合并

        // 数据行合并（注意：表头占用0-1行，数据行从第2行开始）
        config.addMerge(2, 4, 2, 5, "✓ 合格", false);    // 数据行第1行（表格第3行），列5-6合并

        // 打印配置信息
        printConfigInfo(config);

        return config;
    }

    /**
     * 打印配置信息
     */
    private static void printConfigInfo(SimpleTableConfig config) {
        System.out.println("\n📋 === 表格配置详情 ===");
        System.out.println("📄 标题: " + config.getTitle());
        System.out.println("📐 纸张: " + (config.isLandscape() ? "横向A4" : "纵向A4"));
        System.out.println("📊 规格: " + config.getHeaderRows().size() + "行表头 × " +
                          config.getColumnWidths().size() + "列");
        System.out.println("📈 数据: " + config.getDataRows().size() + "行");

        System.out.println("📏 列宽: " + config.getColumnWidths() + " (像素)");
        System.out.println("📐 行高: " + config.getHeaderHeights() + " (像素)");

        int totalWidth = config.getColumnWidths().stream().mapToInt(Integer::intValue).sum();
        System.out.println("📊 总宽: " + totalWidth + "px (" + (totalWidth * 15) + " twips)");

        System.out.println("🔗 合并: " + config.getMerges().size() + " 个");
        for (int i = 0; i < config.getMerges().size(); i++) {
            SimpleMergeInfo merge = config.getMerges().get(i);
            System.out.println("  " + (i + 1) + ". " + merge);
        }

        System.out.println("📝 表格结构:");
        System.out.println("  表头第1行(索引0): [产品信息----] | [检测项目----] | [检测结果----] | 检验人员 | 备注说明");
        System.out.println("                  (跨2列合并)    | (跨2列合并)    | (跨2列合并)    | (跨2行)  | (跨2行)");
        System.out.println("  表头第2行(索引1): 产品名称 | 产品型号 | 外观检测 | 功能测试 | 合格 | 不合格 | (合并) | (合并)");
        System.out.println("  数据第1行(索引2): 智能手机 | iPhone 15 Pro Max | 外观完好 | 功能正常 | [✓ 合格--] | 张三 | 符合标准");
        System.out.println("                                                                      (跨2列合并)");

        System.out.println("========================\n");
    }
}
