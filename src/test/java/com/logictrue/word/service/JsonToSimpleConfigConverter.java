package com.logictrue.word.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * JSON数据转换器
 * 将前端传递的JSON数据转换为SimpleWordService兼容的配置格式
 */
public class JsonToSimpleConfigConverter {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 将JSON字符串转换为SimpleTableConfig
     */
    public static SimpleTableConfig convertFromJson(String jsonString) throws IOException {
        JsonNode rootNode = objectMapper.readTree(jsonString);
        return convertFromJsonNode(rootNode);
    }

    /**
     * 将JsonNode转换为SimpleTableConfig
     */
    public static SimpleTableConfig convertFromJsonNode(JsonNode rootNode) {
        SimpleTableConfig config = new SimpleTableConfig();

        // 设置基本信息
        config.setTitle(getStringValue(rootNode, "title", "检验记录表"));
        config.setLandscape(true); // 默认横向

        // 转换表头数据
        convertHeaders(rootNode, config);

        // 转换数据行
        convertDataRows(rootNode, config);

        // 转换列宽和行高配置
        convertSizeConfig(rootNode, config);

        // 转换合并信息
        convertMerges(rootNode, config);

        return config;
    }

    /**
     * 转换表头数据
     */
    private static void convertHeaders(JsonNode rootNode, SimpleTableConfig config) {
        JsonNode headersNode = rootNode.get("headers");
        if (headersNode != null && headersNode.isArray()) {
            for (JsonNode headerRowNode : headersNode) {
                List<String> headerRow = new ArrayList<>();
                if (headerRowNode.isArray()) {
                    for (JsonNode cellNode : headerRowNode) {
                        String cellContent = cellNode.asText("");
                        headerRow.add(cellContent);
                    }
                }
                config.addHeaderRow(headerRow);
            }
        }
    }

    /**
     * 转换数据行
     */
    private static void convertDataRows(JsonNode rootNode, SimpleTableConfig config) {
        // 优先使用 cellRows 中的内容
        JsonNode cellRowsNode = rootNode.get("cellRows");
        if (cellRowsNode != null && cellRowsNode.isArray()) {
            for (JsonNode rowNode : cellRowsNode) {
                List<String> dataRow = new ArrayList<>();
                if (rowNode.isArray()) {
                    for (JsonNode cellNode : rowNode) {
                        String content = getStringValue(cellNode, "content", "");
                        dataRow.add(content);
                    }
                }
                config.addDataRow(dataRow);
            }
        } else {
            // 备选：使用 rows 数据
            JsonNode rowsNode = rootNode.get("rows");
            if (rowsNode != null && rowsNode.isArray()) {
                for (JsonNode rowNode : rowsNode) {
                    List<String> dataRow = new ArrayList<>();
                    if (rowNode.isArray()) {
                        for (JsonNode cellNode : rowNode) {
                            dataRow.add(cellNode.asText(""));
                        }
                    }
                    config.addDataRow(dataRow);
                }
            }
        }
    }

    /**
     * 转换尺寸配置
     */
    private static void convertSizeConfig(JsonNode rootNode, SimpleTableConfig config) {
        JsonNode headerWidthConfigNode = rootNode.get("headerWidthConfig");
        if (headerWidthConfigNode != null) {
            // 转换列宽
            JsonNode columnWidthsNode = headerWidthConfigNode.get("columnWidths");
            if (columnWidthsNode != null && columnWidthsNode.isArray()) {
                List<Integer> columnWidths = new ArrayList<>();
                for (JsonNode widthNode : columnWidthsNode) {
                    columnWidths.add(widthNode.asInt(100));
                }
                config.setColumnWidths(columnWidths);
            }

            // 转换行高
            JsonNode headerHeightsNode = headerWidthConfigNode.get("headerHeights");
            if (headerHeightsNode != null && headerHeightsNode.isArray()) {
                List<Integer> headerHeights = new ArrayList<>();
                for (JsonNode heightNode : headerHeightsNode) {
                    headerHeights.add(heightNode.asInt(50));
                }
                config.setHeaderHeights(headerHeights);
            }
        }
    }

    /**
     * 转换合并信息
     */
    private static void convertMerges(JsonNode rootNode, SimpleTableConfig config) {
        // 转换表头合并
        JsonNode headerMergesNode = rootNode.get("headerMerges");
        if (headerMergesNode != null && headerMergesNode.isArray()) {
            for (JsonNode mergeNode : headerMergesNode) {
                SimpleMergeInfo merge = createMergeFromNode(mergeNode, true);
                if (merge != null) {
                    config.addMerge(merge);
                }
            }
        }

        // 转换数据行合并（需要调整行索引）
        JsonNode mergesNode = rootNode.get("merges");
        if (mergesNode != null && mergesNode.isArray()) {
            int headerRowCount = config.getHeaderRows().size();
            for (JsonNode mergeNode : mergesNode) {
                SimpleMergeInfo merge = createMergeFromNode(mergeNode, false);
                if (merge != null) {
                    // 调整数据行的行索引（加上表头行数）
                    merge.setStartRow(merge.getStartRow() + headerRowCount);
                    merge.setEndRow(merge.getEndRow() + headerRowCount);
                    config.addMerge(merge);
                }
            }
        }
    }

    /**
     * 从JsonNode创建合并信息
     */
    private static SimpleMergeInfo createMergeFromNode(JsonNode mergeNode, boolean isHeader) {
        if (mergeNode == null) return null;

        SimpleMergeInfo merge = new SimpleMergeInfo();
        merge.setStartRow(mergeNode.get("startRow").asInt(0));
        merge.setStartCol(mergeNode.get("startCol").asInt(0));
        merge.setEndRow(mergeNode.get("endRow").asInt(0));
        merge.setEndCol(mergeNode.get("endCol").asInt(0));
        merge.setContent(getStringValue(mergeNode, "content", ""));
        merge.setHeader(isHeader);

        return merge;
    }

    /**
     * 安全获取字符串值
     */
    private static String getStringValue(JsonNode node, String fieldName, String defaultValue) {
        if (node == null) return defaultValue;
        JsonNode fieldNode = node.get(fieldName);
        return fieldNode != null ? fieldNode.asText(defaultValue) : defaultValue;
    }

    /**
     * 打印转换结果
     */
    public static void printConversionResult(SimpleTableConfig config) {
        System.out.println("=== JSON转换结果 ===");
        System.out.println("标题: " + config.getTitle());
        System.out.println("纸张: " + (config.isLandscape() ? "横向" : "纵向"));
        System.out.println("表头行数: " + config.getHeaderRows().size());
        System.out.println("数据行数: " + config.getDataRows().size());
        System.out.println("列宽: " + config.getColumnWidths());
        System.out.println("行高: " + config.getHeaderHeights());
        System.out.println("合并数: " + config.getMerges().size());

        System.out.println("\n表头内容:");
        for (int i = 0; i < config.getHeaderRows().size(); i++) {
            System.out.println("  第" + (i + 1) + "行: " + config.getHeaderRows().get(i));
        }

        System.out.println("\n数据内容:");
        for (int i = 0; i < config.getDataRows().size(); i++) {
            System.out.println("  第" + (i + 1) + "行: " + config.getDataRows().get(i));
        }

        System.out.println("\n合并信息:");
        for (int i = 0; i < config.getMerges().size(); i++) {
            System.out.println("  " + (i + 1) + ". " + config.getMerges().get(i));
        }
        System.out.println("==================");
    }
}
