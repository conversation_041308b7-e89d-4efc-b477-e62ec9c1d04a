package com.logictrue.word.service;

import java.io.FileOutputStream;
import java.io.IOException;

/**
 * JSON转换测试类
 * 测试将前端JSON数据转换为SimpleWordService兼容格式并导出Word
 */
public class JsonConversionTest {

    public static void main(String[] args) {
        try {
            System.out.println("🔄 开始JSON转换测试...");

            // 前端传递的JSON数据
            String jsonData = createTestJsonData();

            // 转换为SimpleTableConfig
            SimpleTableConfig config = JsonToSimpleConfigConverter.convertFromJson(jsonData);

            // 打印转换结果
            JsonToSimpleConfigConverter.printConversionResult(config);

            // 导出Word文档
            SimpleWordService wordService = new SimpleWordService();
            byte[] wordBytes = wordService.exportWord(config);

            // 保存文件
            String fileName = "json_conversion_test_" + System.currentTimeMillis() + ".docx";
            try (FileOutputStream fos = new FileOutputStream(fileName)) {
                fos.write(wordBytes);
            }

            System.out.println("\n✅ JSON转换测试成功！");
            System.out.println("📁 文件名: " + fileName);
            System.out.println("📊 文件大小: " + wordBytes.length + " bytes");

        } catch (Exception e) {
            System.err.println("❌ 转换测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建测试用的JSON数据
     * 这是您提供的实际JSON数据
     */
    private static String createTestJsonData() {
        return "{\n" +
                "  \"title\": \"检验记录表\",\n" +
                "  \"headers\": [\n" +
                "    [\"产品名称\", \"生产信息\", \"\", \"责任人员\", \"\", \"\"],\n" +
                "    [\"\", \"批次号\", \"日期\", \"检验员\", \"审核员\", \"负责人\"]\n" +
                "  ],\n" +
                "  \"cellRows\": [\n" +
                "    [\n" +
                "      {\n" +
                "        \"content\": \"智能手机\\n（多功能检测）\",\n" +
                "        \"hasMath\": false,\n" +
                "        \"mathML\": null,\n" +
                "        \"hasMultipleContent\": false,\n" +
                "        \"mathMLMap\": null,\n" +
                "        \"width\": 280,\n" +
                "        \"height\": 50\n" +
                "      },\n" +
                "      {\n" +
                "        \"content\": \"\",\n" +
                "        \"hasMath\": false,\n" +
                "        \"mathML\": null,\n" +
                "        \"hasMultipleContent\": false,\n" +
                "        \"mathMLMap\": null,\n" +
                "        \"width\": 100,\n" +
                "        \"height\": 50\n" +
                "      },\n" +
                "      {\n" +
                "        \"content\": \"\",\n" +
                "        \"hasMath\": false,\n" +
                "        \"mathML\": null,\n" +
                "        \"hasMultipleContent\": false,\n" +
                "        \"mathMLMap\": null,\n" +
                "        \"width\": 100,\n" +
                "        \"height\": 50\n" +
                "      },\n" +
                "      {\n" +
                "        \"content\": \"\",\n" +
                "        \"hasMath\": false,\n" +
                "        \"mathML\": null,\n" +
                "        \"hasMultipleContent\": false,\n" +
                "        \"mathMLMap\": null,\n" +
                "        \"width\": 50,\n" +
                "        \"height\": 50\n" +
                "      },\n" +
                "      {\n" +
                "        \"content\": \"\",\n" +
                "        \"hasMath\": false,\n" +
                "        \"mathML\": null,\n" +
                "        \"hasMultipleContent\": false,\n" +
                "        \"mathMLMap\": null,\n" +
                "        \"width\": 50,\n" +
                "        \"height\": 50\n" +
                "      },\n" +
                "      {\n" +
                "        \"content\": \"\",\n" +
                "        \"hasMath\": false,\n" +
                "        \"mathML\": null,\n" +
                "        \"hasMultipleContent\": false,\n" +
                "        \"mathMLMap\": null,\n" +
                "        \"width\": 50,\n" +
                "        \"height\": 50\n" +
                "      }\n" +
                "    ]\n" +
                "  ],\n" +
                "  \"rows\": [\n" +
                "    [\"智能手机\\n（多功能检测）\", \"\", \"\", \"\", \"\", \"\"]\n" +
                "  ],\n" +
                "  \"merges\": [\n" +
                "    {\n" +
                "      \"startRow\": 0,\n" +
                "      \"startCol\": 0,\n" +
                "      \"endRow\": 1,\n" +
                "      \"endCol\": 0,\n" +
                "      \"rowspan\": 2,\n" +
                "      \"colspan\": 1,\n" +
                "      \"width\": 280,\n" +
                "      \"height\": 130,\n" +
                "      \"content\": \"智能手机\\n（多功能检测）\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"headerMerges\": [\n" +
                "    {\n" +
                "      \"startRow\": 0,\n" +
                "      \"startCol\": 0,\n" +
                "      \"endRow\": 1,\n" +
                "      \"endCol\": 0,\n" +
                "      \"rowspan\": 2,\n" +
                "      \"colspan\": 1,\n" +
                "      \"width\": 280,\n" +
                "      \"height\": 130,\n" +
                "      \"content\": \"产品名称\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"startRow\": 0,\n" +
                "      \"startCol\": 1,\n" +
                "      \"endRow\": 0,\n" +
                "      \"endCol\": 2,\n" +
                "      \"rowspan\": 1,\n" +
                "      \"colspan\": 2,\n" +
                "      \"width\": 200,\n" +
                "      \"height\": 50,\n" +
                "      \"content\": \"生产信息\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"startRow\": 0,\n" +
                "      \"startCol\": 3,\n" +
                "      \"endRow\": 0,\n" +
                "      \"endCol\": 5,\n" +
                "      \"rowspan\": 1,\n" +
                "      \"colspan\": 3,\n" +
                "      \"width\": 150,\n" +
                "      \"height\": 50,\n" +
                "      \"content\": \"责任人员\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"headerWidthConfig\": {\n" +
                "    \"columnWidths\": [280, 100, 100, 50, 50, 50],\n" +
                "    \"headerHeights\": [50, 80],\n" +
                "    \"verticalHeaders\": [false, false, false, true, true, true]\n" +
                "  },\n" +
                "  \"verticalHeadersConfig\": [false, false, false, true, true, true],\n" +
                "  \"metadata\": {\n" +
                "    \"exportTime\": \"2025-08-21T04:49:44.684Z\",\n" +
                "    \"totalRows\": 1,\n" +
                "    \"totalColumns\": 6,\n" +
                "    \"headerRows\": 2,\n" +
                "    \"hasMergedCells\": true,\n" +
                "    \"hasHeaderMerges\": true,\n" +
                "    \"hasLatexProcessing\": true,\n" +
                "    \"useDynamicHeader\": true,\n" +
                "    \"hasCustomWidth\": [280, 100, 100, 50, 50, 50],\n" +
                "    \"hasVerticalHeaders\": true\n" +
                "  }\n" +
                "}";
    }
}
