package com.logictrue.word.service;

import java.io.FileOutputStream;

/**
 * 调试版JSON转换测试
 * 添加详细的调试信息，帮助定位NullPointerException问题
 */
public class DebugJsonTest {

    public static void main(String[] args) {
        System.out.println("🔍 调试版JSON转换测试开始...");

        try {
            // 您提供的JSON数据
            String jsonData = "{\"title\":\"检验记录表\",\"headers\":[[\"产品名称\",\"生产信息\",\"\",\"责任人员\",\"\",\"\"],[\"\"," +
                    "\"批次号\",\"日期\",\"检验员\",\"审核员\",\"负责人\"]],\"cellRows\":[[{\"content\":\"智能手机\\n（多功能检测）\"," +
                    "\"hasMath\":false,\"mathML\":null,\"hasMultipleContent\":false,\"mathMLMap\":null,\"width\":280,\"height\":50}," +
                    "{\"content\":\"\",\"hasMath\":false,\"mathML\":null,\"hasMultipleContent\":false,\"mathMLMap\":null,\"width\":100," +
                    "\"height\":50},{\"content\":\"\",\"hasMath\":false,\"mathML\":null,\"hasMultipleContent\":false,\"mathMLMap\":null," +
                    "\"width\":100,\"height\":50},{\"content\":\"\",\"hasMath\":false,\"mathML\":null,\"hasMultipleContent\":false," +
                    "\"mathMLMap\":null,\"width\":50,\"height\":50},{\"content\":\"\",\"hasMath\":false,\"mathML\":null," +
                    "\"hasMultipleContent\":false,\"mathMLMap\":null,\"width\":50,\"height\":50},{\"content\":\"\",\"hasMath\":false," +
                    "\"mathML\":null,\"hasMultipleContent\":false,\"mathMLMap\":null,\"width\":50,\"height\":50}]],\"rows\":" +
                    "[[\"智能手机\\n（多功能检测）\",\"\",\"\",\"\",\"\",\"\"]],\"merges\":[{\"startRow\":0,\"startCol\":0,\"endRow\":1," +
                    "\"endCol\":0,\"rowspan\":2,\"colspan\":1,\"width\":280,\"height\":130,\"content\":\"智能手机\\n（多功能检测）\"}]," +
                    "\"headerMerges\":[{\"startRow\":0,\"startCol\":0,\"endRow\":1,\"endCol\":0,\"rowspan\":2,\"colspan\":1,\"width\":280," +
                    "\"height\":130,\"content\":\"产品名称\"},{\"startRow\":0,\"startCol\":1,\"endRow\":0,\"endCol\":2,\"rowspan\":1," +
                    "\"colspan\":2,\"width\":200,\"height\":50,\"content\":\"生产信息\"},{\"startRow\":0,\"startCol\":3,\"endRow\":0," +
                    "\"endCol\":5,\"rowspan\":1,\"colspan\":3,\"width\":150,\"height\":50,\"content\":\"责任人员\"}],\"headerWidthConfig\":" +
                    "{\"columnWidths\":[280,100,100,50,50,50],\"headerHeights\":[50,80],\"verticalHeaders\":[false,false,false,true," +
                    "true,true]},\"verticalHeadersConfig\":[false,false,false,true,true,true],\"metadata\":{\"exportTime\":" +
                    "\"2025-08-21T04:49:44.684Z\",\"totalRows\":1,\"totalColumns\":6,\"headerRows\":2,\"hasMergedCells\":true," +
                    "\"hasHeaderMerges\":true,\"hasLatexProcessing\":true,\"useDynamicHeader\":true,\"hasCustomWidth\":[280,100,100,50," +
                    "50,50],\"hasVerticalHeaders\":true}}";

            // 转换JSON数据
            System.out.println("🔄 转换JSON数据...");
            SimpleTableConfig config = JsonToSimpleConfigConverter.convertFromJson(jsonData);

            // 详细调试信息
            printDetailedDebugInfo(config);

            // 导出Word文档
            System.out.println("📝 开始导出Word文档...");
            SimpleWordService wordService = new SimpleWordService();
            byte[] wordBytes = wordService.exportWord(config);

            // 保存文件
            String fileName = "debug_json_test.docx";
            try (FileOutputStream fos = new FileOutputStream(fileName)) {
                fos.write(wordBytes);
            }

            System.out.println("✅ 调试测试成功完成！");
            System.out.println("📁 生成文件: " + fileName);
            System.out.println("📊 文件大小: " + wordBytes.length + " bytes");

        } catch (Exception e) {
            System.err.println("❌ 调试测试失败: " + e.getMessage());
            e.printStackTrace();
            
            // 打印详细的堆栈跟踪
            System.err.println("\n🔍 详细错误信息:");
            StackTraceElement[] stackTrace = e.getStackTrace();
            for (int i = 0; i < Math.min(10, stackTrace.length); i++) {
                System.err.println("  " + stackTrace[i]);
            }
        }
    }

    /**
     * 打印详细的调试信息
     */
    private static void printDetailedDebugInfo(SimpleTableConfig config) {
        System.out.println("\n🔍 === 详细调试信息 ===");
        
        // 基本信息
        System.out.println("📋 基本信息:");
        System.out.println("  标题: " + config.getTitle());
        System.out.println("  横向: " + config.isLandscape());
        
        // 表头信息
        System.out.println("\n📊 表头信息:");
        System.out.println("  表头行数: " + config.getHeaderRows().size());
        for (int i = 0; i < config.getHeaderRows().size(); i++) {
            System.out.println("  表头第" + (i + 1) + "行: " + config.getHeaderRows().get(i));
            System.out.println("    长度: " + config.getHeaderRows().get(i).size());
        }
        
        // 数据信息
        System.out.println("\n📈 数据信息:");
        System.out.println("  数据行数: " + config.getDataRows().size());
        for (int i = 0; i < config.getDataRows().size(); i++) {
            System.out.println("  数据第" + (i + 1) + "行: " + config.getDataRows().get(i));
            System.out.println("    长度: " + config.getDataRows().get(i).size());
        }
        
        // 尺寸配置
        System.out.println("\n📏 尺寸配置:");
        System.out.println("  列宽: " + config.getColumnWidths());
        System.out.println("  行高: " + config.getHeaderHeights());
        
        // 合并信息详细分析
        System.out.println("\n🔗 合并信息详细分析:");
        System.out.println("  合并总数: " + config.getMerges().size());
        
        int headerMergeCount = 0;
        int dataMergeCount = 0;
        
        for (int i = 0; i < config.getMerges().size(); i++) {
            SimpleMergeInfo merge = config.getMerges().get(i);
            String type = merge.isHeader() ? "表头" : "数据";
            
            if (merge.isHeader()) {
                headerMergeCount++;
            } else {
                dataMergeCount++;
            }
            
            System.out.println("  合并" + (i + 1) + " [" + type + "]: ");
            System.out.println("    位置: (" + merge.getStartRow() + "," + merge.getStartCol() + 
                             ") -> (" + merge.getEndRow() + "," + merge.getEndCol() + ")");
            System.out.println("    跨度: " + merge.getColspan() + "列 × " + merge.getRowspan() + "行");
            System.out.println("    内容: \"" + merge.getContent() + "\"");
        }
        
        System.out.println("  表头合并: " + headerMergeCount + " 个");
        System.out.println("  数据合并: " + dataMergeCount + " 个");
        
        // 预期表格结构
        int totalRows = config.getHeaderRows().size() + config.getDataRows().size();
        int totalCols = config.getColumnWidths().size();
        System.out.println("\n📐 预期表格结构:");
        System.out.println("  总行数: " + totalRows + " (表头: " + config.getHeaderRows().size() + 
                          " + 数据: " + config.getDataRows().size() + ")");
        System.out.println("  总列数: " + totalCols);
        
        // 验证合并索引
        System.out.println("\n✅ 合并索引验证:");
        for (SimpleMergeInfo merge : config.getMerges()) {
            boolean valid = true;
            String issues = "";
            
            if (merge.getStartRow() < 0 || merge.getStartRow() >= totalRows) {
                valid = false;
                issues += "起始行超范围(" + merge.getStartRow() + ") ";
            }
            if (merge.getEndRow() < 0 || merge.getEndRow() >= totalRows) {
                valid = false;
                issues += "结束行超范围(" + merge.getEndRow() + ") ";
            }
            if (merge.getStartCol() < 0 || merge.getStartCol() >= totalCols) {
                valid = false;
                issues += "起始列超范围(" + merge.getStartCol() + ") ";
            }
            if (merge.getEndCol() < 0 || merge.getEndCol() >= totalCols) {
                valid = false;
                issues += "结束列超范围(" + merge.getEndCol() + ") ";
            }
            
            String status = valid ? "✅ 有效" : "❌ 无效";
            System.out.println("  " + merge.getContent() + ": " + status + 
                             (valid ? "" : " - " + issues));
        }
        
        System.out.println("========================\n");
    }
}
