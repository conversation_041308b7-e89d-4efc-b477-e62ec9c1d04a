package com.logictrue.word.service;

import com.logictrue.word.dto.TableExportRequest;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 简单的Word导出测试
 * 横向A4纸张，两行八列表头，一行包含合并单元格的数据
 */
@SpringBootApplication
public class SimpleWordExportTest {

    public static void main(String[] args) {
        // 启动Spring Boot应用上下文
        ConfigurableApplicationContext context = SpringApplication.run(SimpleWordExportTest.class, args);
        
        try {
            // 获取WordExportService
            WordExportService wordExportService = context.getBean(WordExportService.class);
            
            // 创建测试请求
            TableExportRequest request = createSimpleTestRequest();
            
            // 导出Word文档
            System.out.println("开始导出Word文档...");
            byte[] wordBytes = wordExportService.exportTableToWordWithJson(request);
            
            // 保存到文件
            String fileName = "simple_test_export.docx";
            try (FileOutputStream fos = new FileOutputStream(fileName)) {
                fos.write(wordBytes);
            }
            
            System.out.println("Word文档导出成功！");
            System.out.println("文件名: " + fileName);
            System.out.println("文件大小: " + wordBytes.length + " bytes");
            
        } catch (Exception e) {
            System.err.println("导出失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 关闭应用上下文
            context.close();
        }
    }

    /**
     * 创建简单的测试请求
     * 横向A4纸张，两行八列表头，一行包含合并单元格的数据
     */
    private static TableExportRequest createSimpleTestRequest() {
        TableExportRequest request = new TableExportRequest();
        request.setTitle("简单测试表格");
        request.setPageOrientation("LANDSCAPE"); // 横向A4纸张

        // 创建表格数据
        TableExportRequest.TableData tableData = new TableExportRequest.TableData();
        
        // 创建两行八列表头
        List<List<TableExportRequest.HeaderCell>> headers = new ArrayList<>();
        
        // 第一行表头
        List<TableExportRequest.HeaderCell> headerRow1 = new ArrayList<>();
        headerRow1.add(createHeaderCell("产品信息", null, null));
        headerRow1.add(createHeaderCell("", null, null));
        headerRow1.add(createHeaderCell("检测项目", null, null));
        headerRow1.add(createHeaderCell("", null, null));
        headerRow1.add(createHeaderCell("检测结果", null, null));
        headerRow1.add(createHeaderCell("", null, null));
        headerRow1.add(createHeaderCell("责任人", null, null));
        headerRow1.add(createHeaderCell("备注", null, null));
        headers.add(headerRow1);
        
        // 第二行表头
        List<TableExportRequest.HeaderCell> headerRow2 = new ArrayList<>();
        headerRow2.add(createHeaderCell("名称", null, null));
        headerRow2.add(createHeaderCell("型号", null, null));
        headerRow2.add(createHeaderCell("项目1", null, null));
        headerRow2.add(createHeaderCell("项目2", null, null));
        headerRow2.add(createHeaderCell("合格", null, null));
        headerRow2.add(createHeaderCell("不合格", null, null));
        headerRow2.add(createHeaderCell("检验员", null, null));
        headerRow2.add(createHeaderCell("说明", null, null));
        headers.add(headerRow2);
        
        tableData.setHeaders(headers);

        // 创建一行包含合并单元格的数据
        List<List<TableExportRequest.DataCell>> dataRows = new ArrayList<>();
        List<TableExportRequest.DataCell> dataRow1 = new ArrayList<>();
        dataRow1.add(createDataCell("智能手机", null, null));
        dataRow1.add(createDataCell("iPhone 15", null, null));
        dataRow1.add(createDataCell("外观检测", null, null));
        dataRow1.add(createDataCell("功能测试", null, null));
        dataRow1.add(createDataCell("✓", null, null));
        dataRow1.add(createDataCell("", null, null));
        dataRow1.add(createDataCell("张三", null, null));
        dataRow1.add(createDataCell("测试通过", null, null));
        dataRows.add(dataRow1);
        
        tableData.setDataRows(dataRows);
        request.setTableData(tableData);

        // 设置表头宽度和高度配置
        TableExportRequest.HeaderWidthConfig headerWidthConfig = new TableExportRequest.HeaderWidthConfig();
        
        // 配置八列的宽度（像素）
        List<Integer> columnWidths = new ArrayList<>();
        columnWidths.add(120);  // 产品名称列
        columnWidths.add(120);  // 产品型号列
        columnWidths.add(100);  // 检测项目1列
        columnWidths.add(100);  // 检测项目2列
        columnWidths.add(80);   // 合格列
        columnWidths.add(80);   // 不合格列
        columnWidths.add(100);  // 责任人列
        columnWidths.add(150);  // 备注列
        headerWidthConfig.setColumnWidths(columnWidths);
        
        // 配置两行表头的高度（像素）
        List<Integer> headerHeights = new ArrayList<>();
        headerHeights.add(50);  // 第一行表头高度
        headerHeights.add(40);  // 第二行表头高度
        headerWidthConfig.setHeaderHeights(headerHeights);
        
        request.setHeaderWidthConfig(headerWidthConfig);

        // 创建表头合并信息
        List<TableExportRequest.MergeCell> headerMerges = new ArrayList<>();
        
        // 产品信息 - 跨1行2列
        TableExportRequest.MergeCell headerMerge1 = new TableExportRequest.MergeCell();
        headerMerge1.setStartRow(0);
        headerMerge1.setStartCol(0);
        headerMerge1.setEndRow(0);
        headerMerge1.setEndCol(1);
        headerMerge1.setRowspan(1);
        headerMerge1.setColspan(2);
        headerMerge1.setWidth(240);  // 120 + 120 = 240
        headerMerge1.setHeight(50);  // 第一行高度
        headerMerge1.setContent("产品信息");
        headerMerges.add(headerMerge1);
        
        // 检测项目 - 跨1行2列
        TableExportRequest.MergeCell headerMerge2 = new TableExportRequest.MergeCell();
        headerMerge2.setStartRow(0);
        headerMerge2.setStartCol(2);
        headerMerge2.setEndRow(0);
        headerMerge2.setEndCol(3);
        headerMerge2.setRowspan(1);
        headerMerge2.setColspan(2);
        headerMerge2.setWidth(200);  // 100 + 100 = 200
        headerMerge2.setHeight(50);  // 第一行高度
        headerMerge2.setContent("检测项目");
        headerMerges.add(headerMerge2);
        
        // 检测结果 - 跨1行2列
        TableExportRequest.MergeCell headerMerge3 = new TableExportRequest.MergeCell();
        headerMerge3.setStartRow(0);
        headerMerge3.setStartCol(4);
        headerMerge3.setEndRow(0);
        headerMerge3.setEndCol(5);
        headerMerge3.setRowspan(1);
        headerMerge3.setColspan(2);
        headerMerge3.setWidth(160);  // 80 + 80 = 160
        headerMerge3.setHeight(50);  // 第一行高度
        headerMerge3.setContent("检测结果");
        headerMerges.add(headerMerge3);
        
        // 责任人 - 跨2行1列
        TableExportRequest.MergeCell headerMerge4 = new TableExportRequest.MergeCell();
        headerMerge4.setStartRow(0);
        headerMerge4.setStartCol(6);
        headerMerge4.setEndRow(1);
        headerMerge4.setEndCol(6);
        headerMerge4.setRowspan(2);
        headerMerge4.setColspan(1);
        headerMerge4.setWidth(100);  // 100
        headerMerge4.setHeight(90);  // 50 + 40 = 90
        headerMerge4.setContent("责任人");
        headerMerges.add(headerMerge4);
        
        // 备注 - 跨2行1列
        TableExportRequest.MergeCell headerMerge5 = new TableExportRequest.MergeCell();
        headerMerge5.setStartRow(0);
        headerMerge5.setStartCol(7);
        headerMerge5.setEndRow(1);
        headerMerge5.setEndCol(7);
        headerMerge5.setRowspan(2);
        headerMerge5.setColspan(1);
        headerMerge5.setWidth(150);  // 150
        headerMerge5.setHeight(90);  // 50 + 40 = 90
        headerMerge5.setContent("备注");
        headerMerges.add(headerMerge5);
        
        request.setHeaderMerges(headerMerges);

        // 创建数据行合并信息
        List<TableExportRequest.MergeCell> dataMerges = new ArrayList<>();
        
        // 检测结果合并 - 合格和不合格列合并
        TableExportRequest.MergeCell dataMerge1 = new TableExportRequest.MergeCell();
        dataMerge1.setStartRow(0);
        dataMerge1.setStartCol(4);
        dataMerge1.setEndRow(0);
        dataMerge1.setEndCol(5);
        dataMerge1.setRowspan(1);
        dataMerge1.setColspan(2);
        dataMerge1.setWidth(160);  // 80 + 80 = 160
        dataMerge1.setHeight(40);  // 数据行高度
        dataMerge1.setContent("合格");
        dataMerges.add(dataMerge1);
        
        request.setMerges(dataMerges);

        // 打印配置信息
        System.out.println("=== 表格配置信息 ===");
        System.out.println("纸张方向: 横向A4");
        System.out.println("表头行数: 2行");
        System.out.println("表头列数: 8列");
        System.out.println("数据行数: 1行");
        System.out.println("列宽配置: " + columnWidths);
        System.out.println("行高配置: " + headerHeights);
        System.out.println("表头合并数: " + headerMerges.size());
        System.out.println("数据合并数: " + dataMerges.size());
        
        int totalWidth = columnWidths.stream().mapToInt(Integer::intValue).sum();
        System.out.println("总宽度: " + totalWidth + "px");

        return request;
    }

    private static TableExportRequest.HeaderCell createHeaderCell(String content, Integer width, Integer height) {
        TableExportRequest.HeaderCell cell = new TableExportRequest.HeaderCell();
        cell.setContent(content);
        cell.setWidth(width);
        cell.setHeight(height);
        return cell;
    }

    private static TableExportRequest.DataCell createDataCell(String content, Integer width, Integer height) {
        TableExportRequest.DataCell cell = new TableExportRequest.DataCell();
        cell.setContent(content);
        cell.setWidth(width);
        cell.setHeight(height);
        cell.setHasMath(false);
        return cell;
    }
}
