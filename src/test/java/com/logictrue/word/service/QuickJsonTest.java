package com.logictrue.word.service;

import java.io.FileOutputStream;

/**
 * 快速JSON转换测试
 * 直接使用您提供的JSON数据进行转换和导出测试
 */
public class QuickJsonTest {

    public static void main(String[] args) {
        System.out.println("🚀 快速JSON转换测试开始...");

        try {
            // 您提供的JSON数据
            String jsonData = "{\"title\":\"检验记录表\",\"headers\":[[\"产品名称\",\"生产信息\",\"\",\"责任人员\",\"\",\"\"],[\"\"," +
                    "\"批次号\",\"日期\",\"检验员\",\"审核员\",\"负责人\"]],\"cellRows\":[[{\"content\":\"智能手机\\n（多功能检测）\"," +
                    "\"hasMath\":false,\"mathML\":null,\"hasMultipleContent\":false,\"mathMLMap\":null,\"width\":280,\"height\":50}," +
                    "{\"content\":\"\",\"hasMath\":false,\"mathML\":null,\"hasMultipleContent\":false,\"mathMLMap\":null,\"width\":100," +
                    "\"height\":50},{\"content\":\"\",\"hasMath\":false,\"mathML\":null,\"hasMultipleContent\":false,\"mathMLMap\":null," +
                    "\"width\":100,\"height\":50},{\"content\":\"\",\"hasMath\":false,\"mathML\":null,\"hasMultipleContent\":false," +
                    "\"mathMLMap\":null,\"width\":50,\"height\":50},{\"content\":\"\",\"hasMath\":false,\"mathML\":null," +
                    "\"hasMultipleContent\":false,\"mathMLMap\":null,\"width\":50,\"height\":50},{\"content\":\"\",\"hasMath\":false," +
                    "\"mathML\":null,\"hasMultipleContent\":false,\"mathMLMap\":null,\"width\":50,\"height\":50}]],\"rows\":" +
                    "[[\"智能手机\\n（多功能检测）\",\"\",\"\",\"\",\"\",\"\"]],\"merges\":[{\"startRow\":0,\"startCol\":0,\"endRow\":1," +
                    "\"endCol\":0,\"rowspan\":2,\"colspan\":1,\"width\":280,\"height\":130,\"content\":\"智能手机\\n（多功能检测）\"}]," +
                    "\"headerMerges\":[{\"startRow\":0,\"startCol\":0,\"endRow\":1,\"endCol\":0,\"rowspan\":2,\"colspan\":1,\"width\":280," +
                    "\"height\":130,\"content\":\"产品名称\"},{\"startRow\":0,\"startCol\":1,\"endRow\":0,\"endCol\":2,\"rowspan\":1," +
                    "\"colspan\":2,\"width\":200,\"height\":50,\"content\":\"生产信息\"},{\"startRow\":0,\"startCol\":3,\"endRow\":0," +
                    "\"endCol\":5,\"rowspan\":1,\"colspan\":3,\"width\":150,\"height\":50,\"content\":\"责任人员\"}],\"headerWidthConfig\":" +
                    "{\"columnWidths\":[280,100,100,50,50,50],\"headerHeights\":[50,80],\"verticalHeaders\":[false,false,false,true," +
                    "true,true]},\"verticalHeadersConfig\":[false,false,false,true,true,true],\"metadata\":{\"exportTime\":" +
                    "\"2025-08-21T04:49:44.684Z\",\"totalRows\":1,\"totalColumns\":6,\"headerRows\":2,\"hasMergedCells\":true," +
                    "\"hasHeaderMerges\":true,\"hasLatexProcessing\":true,\"useDynamicHeader\":true,\"hasCustomWidth\":[280,100,100,50," +
                    "50,50],\"hasVerticalHeaders\":true}}";

            // 转换JSON数据
            System.out.println("🔄 转换JSON数据...");
            SimpleTableConfig config = JsonToSimpleConfigConverter.convertFromJson(jsonData);

            // 打印转换结果
            printSimpleResult(config);

            // 导出Word文档
            System.out.println("📝 导出Word文档...");
            SimpleWordService wordService = new SimpleWordService();
            byte[] wordBytes = wordService.exportWord(config);

            // 保存文件
            String fileName = "quick_json_test.docx";
            try (FileOutputStream fos = new FileOutputStream(fileName)) {
                fos.write(wordBytes);
            }

            System.out.println("✅ 测试成功完成！");
            System.out.println("📁 生成文件: " + fileName);
            System.out.println("📊 文件大小: " + wordBytes.length + " bytes");

        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 打印简化的转换结果
     */
    private static void printSimpleResult(SimpleTableConfig config) {
        System.out.println("\n📋 转换结果:");
        System.out.println("标题: " + config.getTitle());
        System.out.println("表头: " + config.getHeaderRows().size() + "行 × " + config.getColumnWidths().size() + "列");
        System.out.println("数据: " + config.getDataRows().size() + "行");
        System.out.println("列宽: " + config.getColumnWidths());
        System.out.println("行高: " + config.getHeaderHeights());
        System.out.println("合并: " + config.getMerges().size() + "个");

        System.out.println("\n📝 表头预览:");
        for (int i = 0; i < config.getHeaderRows().size(); i++) {
            System.out.println("  行" + (i + 1) + ": " + config.getHeaderRows().get(i));
        }

        System.out.println("\n📊 数据预览:");
        for (int i = 0; i < config.getDataRows().size(); i++) {
            System.out.println("  行" + (i + 1) + ": " + config.getDataRows().get(i));
        }

        System.out.println("\n🔗 合并预览:");
        for (SimpleMergeInfo merge : config.getMerges()) {
            String type = merge.isHeader() ? "表头" : "数据";
            System.out.println("  " + type + "合并: (" + merge.getStartRow() + "," + merge.getStartCol() + 
                             ") -> (" + merge.getEndRow() + "," + merge.getEndCol() + ") 内容: " + merge.getContent());
        }
        System.out.println();
    }
}
