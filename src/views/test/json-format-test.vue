<template>
  <div class="json-format-test">
    <h2>JSON格式测试</h2>
    
    <div class="test-section">
      <h3>表格组件</h3>
      <TableContainer
        ref="tableContainer"
        :use-dynamic-header="true"
        :header-config="testHeaderConfig"
        :header-width-config="testHeaderWidthConfig"
        :initial-data="testData"
        style="margin-bottom: 20px;"
      />
    </div>

    <div class="test-section">
      <h3>操作按钮</h3>
      <el-button type="primary" @click="exportJSON">导出JSON</el-button>
      <el-button type="success" @click="importJSON">导入JSON</el-button>
      <el-button type="info" @click="compareFormats">对比格式</el-button>
    </div>

    <div class="test-section" v-if="exportedJSON">
      <h3>导出的JSON格式</h3>
      <pre class="json-display">{{ JSON.stringify(exportedJSON, null, 2) }}</pre>
    </div>

    <div class="test-section" v-if="importJSON">
      <h3>导入的JSON格式</h3>
      <pre class="json-display">{{ JSON.stringify(importFormatJSON, null, 2) }}</pre>
    </div>

    <div class="test-section" v-if="comparisonResult">
      <h3>格式对比结果</h3>
      <div class="comparison-result">
        <p><strong>格式匹配度:</strong> {{ comparisonResult.matchPercentage }}%</p>
        <div v-if="comparisonResult.differences.length > 0">
          <h4>差异项:</h4>
          <ul>
            <li v-for="diff in comparisonResult.differences" :key="diff">{{ diff }}</li>
          </ul>
        </div>
        <div v-else>
          <p style="color: green;">✅ 格式完全匹配！</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TableContainer from '@/components/TableContainer.vue'

export default {
  name: 'JsonFormatTest',
  components: {
    TableContainer
  },
  data() {
    return {
      exportedJSON: null,
      comparisonResult: null,
      
      // 测试用的表头配置
      testHeaderConfig: {
        headers: [
          ['产品名称', '生产信息', '', '责任人员', '', ''],
          ['', '批次号', '日期', '检验员', '审核员', '负责人']
        ],
        merges: [
          {
            startRow: 0,
            startCol: 0,
            endRow: 1,
            endCol: 0,
            content: '产品名称'
          },
          {
            startRow: 0,
            startCol: 1,
            endRow: 0,
            endCol: 2,
            content: '生产信息'
          },
          {
            startRow: 0,
            startCol: 3,
            endRow: 0,
            endCol: 5,
            content: '责任人员'
          }
        ]
      },

      // 测试用的表头宽度配置
      testHeaderWidthConfig: {
        columnWidths: [180, 100, 100, 80, 80, 80],
        headerHeights: [60, 40],
        verticalHeaders: [false, false, false, true, true, true]
      },

      // 测试用的数据
      testData: [
        [
          { content: '智能手机', merged: { startRow: 0, endRow: 1, startCol: 0, endCol: 0 } },
          { content: 'A001' },
          { content: '2024-01-15' },
          { content: '张三' },
          { content: '李四' },
          { content: '王五' }
        ],
        [
          { content: '（多功能检测）', merged: { startRow: 0, endRow: 1, startCol: 0, endCol: 0 } },
          { content: 'B002' },
          { content: '2024-01-16' },
          { content: '赵六' },
          { content: '钱七' },
          { content: '孙八' }
        ]
      ],

      // 期望的导入格式
      importFormatJSON: {
        "headerConfig": {
          "headers": [
            ["产品名称", "生产信息", "", "责任人员", "", ""],
            ["", "批次号", "日期", "检验员", "审核员", "负责人"]
          ],
          "merges": [
            {
              "startRow": 0,
              "startCol": 0,
              "endRow": 1,
              "endCol": 0,
              "content": "产品名称"
            },
            {
              "startRow": 0,
              "startCol": 1,
              "endRow": 0,
              "endCol": 2,
              "content": "生产信息"
            },
            {
              "startRow": 0,
              "startCol": 3,
              "endRow": 0,
              "endCol": 5,
              "content": "责任人员"
            }
          ]
        },
        "headerWidthConfig": {
          "columnWidths": [180, 100, 100, 80, 80, 80],
          "headerHeights": [60, 40],
          "verticalHeaders": [false, false, false, true, true, true]
        },
        "cellRows": [
          [
            {
              "content": "智能手机",
              "width": 180,
              "height": 50
            },
            {
              "content": "A001",
              "width": 100,
              "height": 50
            },
            {
              "content": "2024-01-15",
              "width": 100,
              "height": 50
            },
            {
              "content": "张三",
              "width": 90,
              "height": 50
            },
            {
              "content": "李四",
              "width": 90,
              "height": 50
            },
            {
              "content": "王五",
              "width": 90,
              "height": 50
            }
          ]
        ],
        "merges": [
          {
            "startRow": 2,
            "endRow": 3,
            "startCol": 0,
            "endCol": 0,
            "content": "智能手机\n（多功能检测）"
          }
        ],
        "metadata": {
          "title": "检验记录表",
          "useDynamicHeader": true,
          "hasCustomWidth": true
        }
      }
    }
  },
  methods: {
    exportJSON() {
      if (this.$refs.tableContainer) {
        this.exportedJSON = this.$refs.tableContainer.getDataAsJSON({
          includeEmpty: false,
          includeMergeInfo: true
        })
        console.log('导出的JSON:', this.exportedJSON)
      }
    },

    importJSON() {
      // 这里可以实现导入JSON的逻辑
      console.log('导入JSON格式:', this.importFormatJSON)
    },

    compareFormats() {
      if (!this.exportedJSON) {
        this.$message.warning('请先导出JSON')
        return
      }

      const differences = []
      let totalChecks = 0
      let matchedChecks = 0

      // 检查主要结构
      const checkStructure = (exported, expected, path = '') => {
        if (typeof exported !== typeof expected) {
          differences.push(`${path}: 类型不匹配 (导出: ${typeof exported}, 期望: ${typeof expected})`)
          totalChecks++
          return
        }

        if (Array.isArray(expected)) {
          totalChecks++
          if (Array.isArray(exported) && exported.length === expected.length) {
            matchedChecks++
          } else {
            differences.push(`${path}: 数组长度不匹配 (导出: ${exported?.length || 0}, 期望: ${expected.length})`)
          }
        } else if (typeof expected === 'object' && expected !== null) {
          Object.keys(expected).forEach(key => {
            totalChecks++
            if (exported && exported.hasOwnProperty(key)) {
              matchedChecks++
              checkStructure(exported[key], expected[key], `${path}.${key}`)
            } else {
              differences.push(`${path}.${key}: 缺少属性`)
            }
          })
        } else {
          totalChecks++
          if (exported === expected) {
            matchedChecks++
          } else {
            differences.push(`${path}: 值不匹配 (导出: ${exported}, 期望: ${expected})`)
          }
        }
      }

      checkStructure(this.exportedJSON, this.importFormatJSON)

      this.comparisonResult = {
        matchPercentage: totalChecks > 0 ? Math.round((matchedChecks / totalChecks) * 100) : 0,
        differences: differences.slice(0, 10) // 只显示前10个差异
      }

      console.log('格式对比结果:', this.comparisonResult)
    }
  }
}
</script>

<style scoped>
.json-format-test {
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.json-display {
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 400px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.comparison-result {
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.comparison-result ul {
  margin: 10px 0;
  padding-left: 20px;
}

.comparison-result li {
  margin: 5px 0;
  color: #e6a23c;
}
</style>
