<template>
  <div class="table-page">
    <button class="download-button" @click="downloadStaticTable">下载静态表格</button>

    <div style="position: relative;">
      <div class="add-button top-left-add">+</div>

      <div class="table-container" ref="tableContainer">
        <table id="resizableTable" ref="resizableTable">
          <tr>
            <td class="resizable-cell" rowspan="2">
              检查工序名称
              <div class="resize-handle resize-handle-left"></div>
              <div class="resize-handle resize-handle-right"></div>
              <div class="resize-handle resize-handle-bottom"></div>
              <div class="resize-handle resize-handle-corner"></div>
            </td>
            <td class="resizable-cell" rowspan="2">
              检查项目及技术条件
              <div class="resize-handle resize-handle-left"></div>
              <div class="resize-handle resize-handle-right"></div>
              <div class="resize-handle resize-handle-bottom"></div>
              <div class="resize-handle resize-handle-corner"></div>
            </td>
            <td class="resizable-cell" rowspan="2">
              实际检查结果
              <div class="resize-handle resize-handle-left"></div>
              <div class="resize-handle resize-handle-right"></div>
              <div class="resize-handle resize-handle-bottom"></div>
              <div class="resize-handle resize-handle-corner"></div>
            </td>
            <td class="resizable-cell" colspan="2">
              完工
              <div class="resize-handle resize-handle-left"></div>
              <div class="resize-handle resize-handle-right"></div>
              <div class="resize-handle resize-handle-bottom"></div>
              <div class="resize-handle resize-handle-corner"></div>
            </td>
            <td class="resizable-cell" rowspan="2">
              操作员
              <div class="resize-handle resize-handle-left"></div>
              <div class="resize-handle resize-handle-right"></div>
              <div class="resize-handle resize-handle-bottom"></div>
              <div class="resize-handle resize-handle-corner"></div>
            </td>
            <td class="resizable-cell" rowspan="2">
              班组长
              <div class="resize-handle resize-handle-left"></div>
              <div class="resize-handle resize-handle-right"></div>
              <div class="resize-handle resize-handle-bottom"></div>
              <div class="resize-handle resize-handle-corner"></div>
            </td>
            <td class="resizable-cell" rowspan="2">
              检验员
              <div class="resize-handle resize-handle-left"></div>
              <div class="resize-handle resize-handle-right"></div>
              <div class="resize-handle resize-handle-bottom"></div>
              <div class="resize-handle resize-handle-corner"></div>
            </td>
          </tr>
          <tr>
            <td class="resizable-cell">
              月
              <div class="resize-handle resize-handle-left"></div>
              <div class="resize-handle resize-handle-right"></div>
              <div class="resize-handle resize-handle-bottom"></div>
              <div class="resize-handle resize-handle-corner"></div>
            </td>
            <td class="resizable-cell">
              日
              <div class="resize-handle resize-handle-left"></div>
              <div class="resize-handle resize-handle-right"></div>
              <div class="resize-handle resize-handle-bottom"></div>
              <div class="resize-handle resize-handle-corner"></div>
            </td>
          </tr>
          <tr>
            <td class="date-columns resizable-cell" v-for="n in 8" :key="n">
              <div class="resize-handle resize-handle-left"></div>
              <div class="resize-handle resize-handle-right"></div>
              <div class="resize-handle resize-handle-bottom"></div>
              <div class="resize-handle resize-handle-corner"></div>
            </td>
          </tr>
        </table>
      </div>

      <div class="add-button bottom-add">+</div>
      <div class="add-button right-add">+</div>
    </div>

    <!-- Context Menu -->
    <div
      id="contextMenu"
      class="context-menu"
      ref="contextMenu"
      :style="{ display: contextMenuVisible ? 'block' : 'none', left: contextMenuX + 'px', top: contextMenuY + 'px' }"
    >
      <div class="context-menu-info">
        <div>当前宽度: <span>{{ currentWidth }}</span>px</div>
        <div>当前高度: <span>{{ currentHeight }}</span>px</div>
      </div>
      <div class="context-menu-item" @click="adjustCellSize">调整尺寸</div>
      <div class="context-menu-item" @click="resetCellSize">重置尺寸</div>
    </div>

    <!-- Input Dialog -->
    <div v-if="dialogVisible" class="overlay" @click="closeDialog"></div>
    <div v-if="dialogVisible" class="input-dialog">
      <h3>调整单元格尺寸</h3>
      <div class="input-group">
        <label for="widthInput">宽度 (px):</label>
        <input
          type="number"
          v-model="widthInput"
          min="80"
          step="1"
          ref="widthInput"
          @keydown.enter="applyCellSize"
          @keydown.esc="closeDialog"
        >
      </div>
      <div class="input-group">
        <label for="heightInput">高度 (px):</label>
        <input
          type="number"
          v-model="heightInput"
          min="40"
          step="1"
          @keydown.enter="applyCellSize"
          @keydown.esc="closeDialog"
        >
      </div>
      <div class="dialog-buttons">
        <button class="dialog-button secondary" @click="closeDialog">取消</button>
        <button class="dialog-button primary" @click="applyCellSize">确定</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TableView',
  data() {
    return {
      // Context menu state
      contextMenuVisible: false,
      contextMenuX: 0,
      contextMenuY: 0,
      currentContextCell: null,
      currentWidth: 0,
      currentHeight: 0,

      // Dialog state
      dialogVisible: false,
      widthInput: 0,
      heightInput: 0,

      // Resize state
      isResizing: false,
      currentHandle: null,
      currentCell: null,
      adjacentCell: null,
      startX: 0,
      startY: 0,
      startWidth: 0,
      startHeight: 0,
      adjacentStartWidth: 0,
      adjacentStartHeight: 0,
      resizePreview: null,

      // Constants
      MIN_CELL_WIDTH: 80,
      MIN_CELL_HEIGHT: 40,
      MAX_TABLE_WIDTH: 1800
    }
  },
  mounted() {
    this.initializeResizeHandlers()
    this.initializeContextMenu()
    this.initializeKeyboardHandlers()
  },
  beforeDestroy() {
    this.removeEventListeners()
  },
  methods: {
    // Initialize resize functionality
    initializeResizeHandlers() {
      this.$nextTick(() => {
        const handles = this.$el.querySelectorAll('.resize-handle')
        handles.forEach(handle => {
          handle.addEventListener('mousedown', this.handleResizeStart)
        })
      })
    },

    // Initialize context menu
    initializeContextMenu() {
      document.addEventListener('contextmenu', this.handleContextMenu)
      document.addEventListener('click', this.hideContextMenu)
    },

    // Initialize keyboard handlers
    initializeKeyboardHandlers() {
      document.addEventListener('keydown', this.handleKeyDown)
    },

    // Remove event listeners
    removeEventListeners() {
      document.removeEventListener('contextmenu', this.handleContextMenu)
      document.removeEventListener('click', this.hideContextMenu)
      document.removeEventListener('keydown', this.handleKeyDown)
      document.removeEventListener('mousemove', this.handleMouseMove)
      document.removeEventListener('mouseup', this.handleMouseUp)
    },

    // Handle context menu
    handleContextMenu(e) {
      const cell = e.target.closest('td, th')
      if (cell && cell.classList.contains('resizable-cell')) {
        e.preventDefault()
        this.currentContextCell = cell

        const rect = cell.getBoundingClientRect()

        // Update current dimensions display
        this.currentWidth = Math.round(rect.width)
        this.currentHeight = Math.round(rect.height)

        // Position context menu
        this.contextMenuX = e.pageX
        this.contextMenuY = e.pageY
        this.contextMenuVisible = true

        // Adjust position if menu goes off screen
        this.$nextTick(() => {
          const contextMenu = this.$refs.contextMenu
          if (contextMenu) {
            const menuRect = contextMenu.getBoundingClientRect()
            if (menuRect.right > window.innerWidth) {
              this.contextMenuX = e.pageX - menuRect.width
            }
            if (menuRect.bottom > window.innerHeight) {
              this.contextMenuY = e.pageY - menuRect.height
            }
          }
        })
      }
    },

    // Hide context menu
    hideContextMenu(e) {
      if (!this.$refs.contextMenu || !this.$refs.contextMenu.contains(e.target)) {
        this.contextMenuVisible = false
      }
    },

    // Handle keyboard events
    handleKeyDown(e) {
      if (e.key === 'Escape') {
        this.closeDialog()
        this.contextMenuVisible = false
      }
    },

    // Adjust cell size
    adjustCellSize() {
      if (!this.currentContextCell) return

      const rect = this.currentContextCell.getBoundingClientRect()
      this.widthInput = Math.round(rect.width)
      this.heightInput = Math.round(rect.height)

      this.dialogVisible = true
      this.contextMenuVisible = false

      // Focus on width input
      this.$nextTick(() => {
        if (this.$refs.widthInput) {
          this.$refs.widthInput.focus()
          this.$refs.widthInput.select()
        }
      })
    },

    // Reset cell size
    resetCellSize() {
      if (!this.currentContextCell) return

      // Remove inline styles to reset to CSS defaults
      this.currentContextCell.style.width = ''
      this.currentContextCell.style.height = ''

      this.contextMenuVisible = false
    },

    // Apply cell size
    applyCellSize() {
      if (!this.currentContextCell) return

      const width = parseInt(this.widthInput)
      const height = parseInt(this.heightInput)

      if (width >= this.MIN_CELL_WIDTH) {
        const currentRect = this.currentContextCell.getBoundingClientRect()
        const widthDelta = width - currentRect.width

        // Set new width for current cell
        this.currentContextCell.style.width = width + 'px'

        // Get all cells to the right of current cell
        const cells = Array.from(this.currentContextCell.parentElement.cells)
        const cellIndex = cells.indexOf(this.currentContextCell)
        const rightCells = cells.slice(cellIndex + 1)

        // Check if this is the rightmost cell
        if (rightCells.length === 0) {
          // This is the rightmost cell - update table container size based on cells
          this.updateTableContainerSize()
        } else {
          // This is not the rightmost cell - maintain table width by adjusting right cells
          const widthChangePerCell = -widthDelta / rightCells.length

          rightCells.forEach((rightCell) => {
            const rightRect = rightCell.getBoundingClientRect()
            const newRightWidth = Math.max(this.MIN_CELL_WIDTH, rightRect.width + widthChangePerCell)
            rightCell.style.width = newRightWidth + 'px'
          })
        }
      }

      if (height >= this.MIN_CELL_HEIGHT) {
        this.currentContextCell.style.height = height + 'px'
      }

      this.closeDialog()
    },

    // Close dialog
    closeDialog() {
      this.dialogVisible = false
    },

    // Update table container size
    updateTableContainerSize() {
      this.updateTableContainerWidth()
      this.updateTableContainerHeight()
    },

    // Update table container width
    updateTableContainerWidth() {
      const table = this.$refs.resizableTable
      if (!table) return

      // Calculate total width of all cells in the first row
      const firstRow = table.rows[0]
      let totalWidth = 0

      for (let i = 0; i < firstRow.cells.length; i++) {
        const cell = firstRow.cells[i]
        const cellWidth = cell.getBoundingClientRect().width
        totalWidth += cellWidth
      }

      // Set table width to total width
      table.style.width = totalWidth + 'px'

      // Also update table container width
      const tableContainer = this.$refs.tableContainer
      if (tableContainer) {
        tableContainer.style.width = totalWidth + 'px'
      }
    },

    // Update table container height
    updateTableContainerHeight() {
      const table = this.$refs.resizableTable
      if (!table) return

      // Calculate total height of all rows
      let totalHeight = 0
      for (let i = 0; i < table.rows.length; i++) {
        const row = table.rows[i]
        const rowHeight = row.getBoundingClientRect().height
        totalHeight += rowHeight
      }

      table.style.height = totalHeight + 'px'
    },

    // Handle resize start
    handleResizeStart(e) {
      e.preventDefault()
      this.isResizing = true
      this.currentHandle = e.target
      this.currentCell = e.target.parentElement

      // Add visual feedback
      this.currentCell.classList.add('resizing')

      this.startX = e.clientX
      this.startY = e.clientY

      const rect = this.currentCell.getBoundingClientRect()
      this.startWidth = rect.width
      this.startHeight = rect.height

      // Create resize preview
      this.createResizePreview()

      // Find adjacent cell for width/height adjustment
      if (this.currentHandle.classList.contains('resize-handle-right') ||
          this.currentHandle.classList.contains('resize-handle-corner')) {
        this.adjacentCell = this.getAdjacentCell(this.currentCell, 'right')
        if (this.adjacentCell) {
          const adjacentRect = this.adjacentCell.getBoundingClientRect()
          this.adjacentStartWidth = adjacentRect.width
          this.adjacentCell.classList.add('resizing')
        }
      }

      if (this.currentHandle.classList.contains('resize-handle-left')) {
        this.adjacentCell = this.getAdjacentCell(this.currentCell, 'left')
        if (this.adjacentCell) {
          const adjacentRect = this.adjacentCell.getBoundingClientRect()
          this.adjacentStartWidth = adjacentRect.width
          this.adjacentCell.classList.add('resizing')
        }
      }

      if (this.currentHandle.classList.contains('resize-handle-bottom') ||
          this.currentHandle.classList.contains('resize-handle-corner')) {
        const bottomAdjacentCell = this.getAdjacentCell(this.currentCell, 'bottom')
        if (bottomAdjacentCell) {
          const adjacentRect = bottomAdjacentCell.getBoundingClientRect()
          this.adjacentStartHeight = adjacentRect.height
          bottomAdjacentCell.classList.add('resizing')
        }
      }

      document.addEventListener('mousemove', this.handleMouseMove)
      document.addEventListener('mouseup', this.handleMouseUp)

      document.body.style.cursor = e.target.style.cursor
      document.body.style.userSelect = 'none'
    },

    // Get adjacent cell
    getAdjacentCell(cell, direction) {
      const table = this.$refs.resizableTable
      if (!table) return null

      const rows = Array.from(table.rows)
      const cells = Array.from(cell.parentElement.cells)
      const cellIndex = cells.indexOf(cell)
      const rowIndex = rows.indexOf(cell.parentElement)

      if (direction === 'right') {
        // Find next cell in the same row
        if (cellIndex < cells.length - 1) {
          return cells[cellIndex + 1]
        }
      } else if (direction === 'left') {
        // Find previous cell in the same row
        if (cellIndex > 0) {
          return cells[cellIndex - 1]
        }
      } else if (direction === 'bottom') {
        // Find cell in the same column of next row
        if (rowIndex < rows.length - 1) {
          const nextRow = rows[rowIndex + 1]
          if (nextRow.cells[cellIndex]) {
            return nextRow.cells[cellIndex]
          }
        }
      }
      return null
    },

    // Create resize preview
    createResizePreview() {
      if (this.resizePreview) {
        document.body.removeChild(this.resizePreview)
      }

      this.resizePreview = document.createElement('div')
      this.resizePreview.className = 'resize-preview'
      document.body.appendChild(this.resizePreview)

      return this.resizePreview
    },

    // Update resize preview
    updateResizePreview(cell, width, height) {
      if (!this.resizePreview) return

      const rect = cell.getBoundingClientRect()
      this.resizePreview.style.left = rect.left + 'px'
      this.resizePreview.style.top = rect.top + 'px'
      this.resizePreview.style.width = width + 'px'
      this.resizePreview.style.height = height + 'px'
    },

    // Handle mouse move during resize
    handleMouseMove(e) {
      if (!this.isResizing) return

      const deltaX = e.clientX - this.startX
      const deltaY = e.clientY - this.startY

      if (this.currentHandle.classList.contains('resize-handle-right')) {
        this.handleRightResize(deltaX)
      }

      if (this.currentHandle.classList.contains('resize-handle-left')) {
        this.handleLeftResize(deltaX)
      }

      if (this.currentHandle.classList.contains('resize-handle-corner')) {
        this.handleCornerResize(deltaX, deltaY)
      }

      if (this.currentHandle.classList.contains('resize-handle-bottom')) {
        this.handleBottomResize(deltaY)
      }
    },

    // Handle right resize
    handleRightResize(deltaX) {
      const rightCell = this.getAdjacentCell(this.currentCell, 'right')

      if (rightCell) {
        // When dragging right border, only affect current cell and right neighbor
        const newCurrentWidth = Math.max(this.MIN_CELL_WIDTH, this.startWidth + deltaX)
        const newRightWidth = Math.max(this.MIN_CELL_WIDTH, this.adjacentStartWidth - deltaX)

        // Update preview
        this.updateResizePreview(this.currentCell, newCurrentWidth, this.currentCell.getBoundingClientRect().height)

        // Only apply changes if both cells can be resized
        if (newCurrentWidth >= this.MIN_CELL_WIDTH && newRightWidth >= this.MIN_CELL_WIDTH) {
          this.currentCell.style.width = newCurrentWidth + 'px'
          rightCell.style.width = newRightWidth + 'px'
        }
      } else {
        // This is the rightmost cell - only increase current cell width and table width
        const newWidth = Math.max(this.MIN_CELL_WIDTH, this.startWidth + deltaX)

        // Update preview
        this.updateResizePreview(this.currentCell, newWidth, this.currentCell.getBoundingClientRect().height)

        // Set the new width for the current cell
        this.currentCell.style.width = newWidth + 'px'

        // Update table width directly to prevent compression of other cells
        const table = this.$refs.resizableTable
        const currentTableWidth = table.getBoundingClientRect().width
        let newTableWidth = currentTableWidth + deltaX

        // Ensure table width doesn't exceed maximum
        newTableWidth = Math.min(newTableWidth, this.MAX_TABLE_WIDTH)

        table.style.width = newTableWidth + 'px'

        // Also update table container width
        const tableContainer = this.$refs.tableContainer
        if (tableContainer) {
          tableContainer.style.width = newTableWidth + 'px'
        }
      }
    },

    // Handle left resize
    handleLeftResize(deltaX) {
      const leftCell = this.getAdjacentCell(this.currentCell, 'left')

      if (leftCell) {
        // When dragging left border, only affect current cell and left neighbor
        const newLeftWidth = Math.max(this.MIN_CELL_WIDTH, this.adjacentStartWidth + deltaX)
        const newCurrentWidth = Math.max(this.MIN_CELL_WIDTH, this.startWidth - deltaX)

        // Update preview
        this.updateResizePreview(this.currentCell, newCurrentWidth, this.currentCell.getBoundingClientRect().height)

        // Only apply changes if both cells can be resized
        if (newLeftWidth >= this.MIN_CELL_WIDTH && newCurrentWidth >= this.MIN_CELL_WIDTH) {
          leftCell.style.width = newLeftWidth + 'px'
          this.currentCell.style.width = newCurrentWidth + 'px'
        }
      } else {
        // This is the leftmost cell - only adjust current cell
        const newWidth = Math.max(this.MIN_CELL_WIDTH, this.startWidth - deltaX)

        // Update preview
        this.updateResizePreview(this.currentCell, newWidth, this.currentCell.getBoundingClientRect().height)

        this.currentCell.style.width = newWidth + 'px'
      }
    },

    // Handle corner resize
    handleCornerResize(deltaX, deltaY) {
      // Handle width adjustment (same as right border)
      this.handleRightResize(deltaX)

      // Handle height adjustment
      const newHeight = Math.max(this.MIN_CELL_HEIGHT, this.startHeight + deltaY)

      // Update preview
      this.updateResizePreview(this.currentCell, this.currentCell.getBoundingClientRect().width, newHeight)

      this.currentCell.style.height = newHeight + 'px'

      // Adjust adjacent cell height in opposite direction
      const bottomAdjacentCell = this.getAdjacentCell(this.currentCell, 'bottom')
      if (bottomAdjacentCell) {
        const adjacentNewHeight = Math.max(this.MIN_CELL_HEIGHT, this.adjacentStartHeight - deltaY)
        bottomAdjacentCell.style.height = adjacentNewHeight + 'px'
      }
    },

    // Handle bottom resize
    handleBottomResize(deltaY) {
      const newHeight = Math.max(this.MIN_CELL_HEIGHT, this.startHeight + deltaY)

      // Update preview
      this.updateResizePreview(this.currentCell, this.currentCell.getBoundingClientRect().width, newHeight)

      this.currentCell.style.height = newHeight + 'px'

      // Adjust adjacent cell height in opposite direction
      const bottomAdjacentCell = this.getAdjacentCell(this.currentCell, 'bottom')
      if (bottomAdjacentCell) {
        const adjacentNewHeight = Math.max(this.MIN_CELL_HEIGHT, this.adjacentStartHeight - deltaY)
        bottomAdjacentCell.style.height = adjacentNewHeight + 'px'
      } else {
        // This is the bottommost cell - update table container height
        this.updateTableContainerSize()
      }
    },

    // Handle mouse up (end resize)
    handleMouseUp() {
      if (!this.isResizing) return

      this.isResizing = false

      // Remove visual feedback
      if (this.currentCell) {
        this.currentCell.classList.remove('resizing')
      }
      if (this.adjacentCell) {
        this.adjacentCell.classList.remove('resizing')
      }

      // Remove resize preview
      if (this.resizePreview) {
        document.body.removeChild(this.resizePreview)
        this.resizePreview = null
      }

      this.currentHandle = null
      this.currentCell = null
      this.adjacentCell = null

      document.removeEventListener('mousemove', this.handleMouseMove)
      document.removeEventListener('mouseup', this.handleMouseUp)

      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    },

    // Download static table
    downloadStaticTable() {
      // Get the current table
      const table = this.$refs.resizableTable
      const tableContainer = this.$refs.tableContainer

      if (!table || !tableContainer) return

      // Create a clone of the table
      const clonedTable = table.cloneNode(true)
      const clonedContainer = document.createElement('div')
      clonedContainer.className = 'table-container'

      // Remove all resize handles and interactive elements from the clone
      const resizeHandles = clonedTable.querySelectorAll('.resize-handle')
      resizeHandles.forEach(handle => handle.remove())

      // Get current computed styles for all cells and apply them as inline styles
      const originalCells = table.querySelectorAll('td, th')
      const clonedCells = clonedTable.querySelectorAll('td, th')

      originalCells.forEach((cell, index) => {
        const computedStyle = window.getComputedStyle(cell)
        const clonedCell = clonedCells[index]

        // Apply current dimensions as fixed inline styles
        clonedCell.style.width = computedStyle.width
        clonedCell.style.height = computedStyle.height
        clonedCell.style.minWidth = computedStyle.width
        clonedCell.style.minHeight = computedStyle.height
        clonedCell.style.maxWidth = computedStyle.width
        clonedCell.style.maxHeight = computedStyle.height

        // Remove resizable class and resize property
        clonedCell.classList.remove('resizable-cell')
        clonedCell.style.resize = 'none'
        clonedCell.style.position = 'static'
      })

      // Apply table container dimensions
      const containerStyle = window.getComputedStyle(tableContainer)
      clonedContainer.style.width = containerStyle.width
      clonedContainer.style.height = containerStyle.height

      // Append cloned table to container
      clonedContainer.appendChild(clonedTable)

      // Create static HTML content
      const staticHTML = this.generateStaticHTML(clonedContainer)

      // Create and download file
      const blob = new Blob([staticHTML], { type: 'text/html;charset=utf-8' })
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = '检查表格_静态版本.html'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(link.href)
    },

    // Generate static HTML
    generateStaticHTML(tableContainer) {
      const currentDate = new Date().toLocaleString('zh-CN')

      return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>静态检查表格</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
            color: #333;
        }

        .table-container {
            background-color: white;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            display: inline-block;
            position: relative;
        }

        table {
            border-collapse: collapse;
            width: 100%;
            table-layout: fixed;
        }

        th, td {
            border: 1px solid #dee2e6;
            padding: 12px 15px;
            text-align: center;
            vertical-align: middle;
            font-size: 14px;
            overflow: hidden;
            word-wrap: break-word;
            background-color: white;
        }

        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        @media print {
            body {
                margin: 0;
                background-color: white;
            }

            .table-container {
                border: 2px solid #000;
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <div style="text-align: center; margin-bottom: 20px;">
        <h2>检查表格 - 静态版本</h2>
        <p>生成时间: ${currentDate}</p>
    </div>
    ${tableContainer.outerHTML}
</body>
</html>`
    }
  }
}
</script>

<style scoped>
.table-page {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  margin: 20px;
  background-color: #f8f9fa;
  color: #333;
}

.table-container {
  background-color: white;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  min-width: 800px;
  max-width: 1800px;
  width: auto;
  position: relative;
}

table {
  border-collapse: collapse;
  width: auto;
  min-width: 600px;
  max-width: 1800px;
  min-height: 300px;
  table-layout: fixed;
}

th, td {
  border: 1px solid #dee2e6;
  padding: 12px 15px;
  text-align: center;
  vertical-align: middle;
  font-size: 14px;
  position: relative;
  overflow: hidden;
  transition: background-color 0.2s ease;
  min-width: 80px;
  min-height: 40px;
  background-color: white;
}

th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

tr:hover td {
  background-color: #f8f9fa;
}

/* Resizable handles */
.resize-handle {
  position: absolute;
  background-color: transparent;
  z-index: 10;
  transition: background-color 0.2s ease;
}

.resize-handle:hover {
  background-color: rgba(0, 123, 255, 0.3);
}

.resize-handle-right {
  top: 0;
  right: -2px;
  width: 4px;
  height: 100%;
  cursor: col-resize;
}

.resize-handle-left {
  top: 0;
  left: -2px;
  width: 4px;
  height: 100%;
  cursor: col-resize;
}

.resize-handle-bottom {
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 4px;
  cursor: row-resize;
}

.resize-handle-corner {
  bottom: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  cursor: nwse-resize;
  border-radius: 50%;
  background-color: rgba(0, 123, 255, 0.2);
}

.resize-handle-corner:hover {
  background-color: rgba(0, 123, 255, 0.5);
}

.add-button {
  position: absolute;
  background: #007bff;
  border: none;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
  color: white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
  z-index: 20;
}

.add-button:hover {
  background-color: #0056b3;
  transform: scale(1.1);
}

.top-left-add {
  top: -14px;
  left: -14px;
}

.bottom-add {
  bottom: -14px;
  left: 50%;
  transform: translateX(-50%);
}

.right-add {
  top: 50%;
  right: -14px;
  transform: translateY(-50%);
}

/* Make cells resizable */
.resizable-cell {
  position: relative;
}

.resizable-cell:hover .resize-handle {
  background-color: rgba(0, 123, 255, 0.2);
}

.download-button {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  border: none;
  padding: 12px 24px;
  margin-bottom: 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 5px rgba(0, 123, 255, 0.3);
  transition: all 0.2s ease;
  z-index: 1000;
}

.download-button:hover {
  background: linear-gradient(135deg, #0056b3, #004085);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
}

/* Context Menu Styles */
.context-menu {
  position: fixed;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 8px 0;
  z-index: 1000;
  min-width: 200px;
  font-size: 14px;
  backdrop-filter: blur(5px);
}

.context-menu-item {
  padding: 10px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.context-menu-item:hover {
  background-color: #f8f9fa;
  color: #007bff;
}

.context-menu-info {
  padding: 10px 16px;
  color: #6c757d;
  font-size: 12px;
  border-bottom: 1px solid #f1f3f5;
  margin-bottom: 5px;
}

.input-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  padding: 24px;
  z-index: 1001;
  min-width: 320px;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translate(-50%, -50%) scale(0.9); }
  to { opacity: 1; transform: translate(-50%, -50%) scale(1); }
}

.input-dialog h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-weight: 600;
}

.input-group {
  margin-bottom: 16px;
}

.input-group label {
  display: block;
  margin-bottom: 8px;
  color: #495057;
  font-weight: 500;
}

.input-group input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.input-group input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.dialog-buttons {
  text-align: right;
  margin-top: 24px;
}

.dialog-button {
  padding: 10px 16px;
  margin-left: 8px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.dialog-button.primary {
  background: #007bff;
  color: white;
}

.dialog-button.primary:hover {
  background: #0056b3;
}

.dialog-button.secondary {
  background: #6c757d;
  color: white;
}

.dialog-button.secondary:hover {
  background: #545b62;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  backdrop-filter: blur(2px);
}

/* Visual feedback for resizing */
.resizing {
  opacity: 0.7;
}

.resize-preview {
  position: absolute;
  background-color: rgba(0, 123, 255, 0.1);
  border: 1px dashed #007bff;
  pointer-events: none;
  z-index: 5;
}
</style>
