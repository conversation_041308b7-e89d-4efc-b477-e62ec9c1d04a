<template>
  <div class="index-container d-flex flex-column">
    <img src="@/assets/images/首页顶部中间.png" class="light" alt="" />
    <header class="d-flex a-center j-sb pd-r30 pd-l30">
      <div class="d-flex j-center a-center logo" style="height: 100%">
        <img
          v-if="user.avatar"
          src="@/assets/favicon.png"
          @click="changeScreen"
          style="width: 70px; height: 60px; cursor: pointer"
        />
        <h3 class="logo-title">{{ title }}</h3>
        <img src="@/assets/images/logo装饰.png" class="zs" alt="" />
      </div>
      <el-dropdown trigger="click">
        <div class="dropdown">
          <span style="padding-left: 5px; padding-right: 8px">{{
            user.nickName
          }}</span>
          <i class="el-icon-arrow-down" />
        </div>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item @click.native="logout">
            <span>退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </header>
    <main class="flex-1 main d-flex" style="overflow: hidden">
      <div class="flex-1">
        <div class="search">
          <div style="display: flex; height: 50px">
            <el-select
              @change="change"
              v-model="route"
              filterable
              remote
              clearable
              reserve-keyword
              style="width: 550px"
              placeholder="请输入菜单名称"
              :remote-method="remoteMethod"
              :loading="loading"
            >
              <el-option
                v-for="(item, index) in options"
                :key="index"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
            <img src="@/assets/images/搜索框图标.png" alt="" />
          </div>
        </div>
        <el-scrollbar style="height: 680px">
          <div class="list">
            <div
              v-for="(item, index) in filterRouters"
              :key="index"
              class="item pointer"
              @click="jump(item)"
            >
              <img
                v-if="parseInt(item.meta.icon) > 0"
                :src="getIconImage(item.meta.icon)"
                class="img"
              />
              <svg-icon
                v-else
                :icon-class="item.meta.icon"
                style="width: 80px; height: 80px"
                class="img"
              />
              <div class="pd-t5 text">
                {{ item.meta.title }}
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </main>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import systemRoute from '@/mixin/systemRoute.js';
import { getFile } from '@/api/file/file.js';

import { getIconImage } from '@/utils/index.js';
import { getPartiesLoginInfo } from '@/api/interfaces/interfaces';
//import updatePassword from './components/updatePassword.vue';

export default {
  name: 'Index',
  mixins: [systemRoute],
  components: {
    /*updatePassword*/
  },
  data() {
    return {
      getIconImage,
      carouselList: [],
      route: null,
      filterRouters: [],
      options: [],
      company: '',
      search: '',
      loading: false,
      isFull: false,
    };
  },
  computed: {
    title() {
      return (
        sessionStorage.getItem('docTitle') || this.$store.state.app.appTitle
      );
    },
    showRight() {
      return sessionStorage.getItem('loginType') == '1';
    },
    ...mapGetters(['user']),
  },

  created() {
    this.getConfigKey('sys.index.company').then((res) => {
      this.company = res.msg;
    });

    getFile({ fileId: 'f6935436d36e4be98a1b3c13fc2dba7f' }).then((res) => {
      let arr = [];
      res.data.forEach((item) => {
        arr.push(this.$store.state.app.uploadUrl + item.pathName);
      });

      this.carouselList = arr;
    });
    this.filterRouters = [...this.routers];
    if (this.$store.state.permission.indexRouters.length == 0) {
      this.getRoutes();
    }
  },

  methods: {
    changeScreen() {
      const element = document.documentElement;
      if (this.isFull) {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.webkitCancelFullScreen) {
          document.webkitCancelFullScreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
      } else {
        if (element.requestFullscreen) {
          element.requestFullscreen();
        } else if (element.webkitRequestFullScreen) {
          element.webkitRequestFullScreen();
        } else if (element.mozRequestFullScreen) {
          element.mozRequestFullScreen();
        } else if (element.msRequestFullscreen) {
          // IE11
          element.msRequestFullscreen();
        }
      }
      this.isFull = !this.isFull;
    },
    change(e) {
      if (e) {
        this.$router.push(e);
      }
    },
    getRoutes() {
      let res = [];
      function fun(arr, titleArr, pathArr) {
        arr.forEach((e) => {
          if (e.children && e.children.length > 0) {
            fun(e.children, [...titleArr, e.meta.title], [...pathArr, e.path]);
          } else {
            res.push({
              label: [...titleArr, e.meta.title].join(` > `),
              value: [...pathArr, e.path].join('/'),
            });
          }
        });
      }
      fun(this.routers, [], []);
      this.$store.commit('SET_INDEX_ROUTERS', res);
    },
    remoteMethod(query) {
      if (query !== '') {
        this.loading = true;
        setTimeout(() => {
          this.loading = false;
          this.options = this.$store.state.permission.indexRouters.filter(
            (item) => item.label.includes(query),
          );
        }, 200);
      } else {
        this.options = [];
      }
    },
    jump(item) {
      if (item.meta.link) {
        item.meta.link = this.$string.handlerLink(item.meta.link);
        if (item.isSso === '1') {
          getPartiesLoginInfo(item.ssoName).then((res) => {
            console.log(
              `${item.meta.link}?username=${res.data.user_id}&password=${res.data.password}`,
            );
            window.open(
              res.data
                ? `${item.meta.link}?username=${res.data.user_id}&password=${res.data.password}`
                : item.meta.link,
              'href',
            );
          });
          return;
        }
        window.open(item.meta.link, 'href');
      } else {
        let arr = ['/workStation', '/schedule','workshop'];
        let { path } = arr.includes(item.path)
          ? item
          : this.getSystemRoute(item);
        this.$router.push(path);
      }
    },
    async logout() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.$store.dispatch('LogOut').then(() => {
            location.href = '/index';
          });
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped lang="scss">
.box {
  border-radius: 10px;
  width: 100%;
  padding: 10px 15px;
  background-color: #fff;
  box-shadow: 0px 8px 8px rgba(0, 0, 0, 0.13);
  display: flex;
  flex-direction: column;
  .item {
    margin: 12px 0;
    font-size: 18px;
    color: #444;
    .label {
      font-size: 20px;
      font-weight: bold;
    }
  }
}

.index-container {
  height: 100%;
  background: url('../assets/images/首页背景.png') 100% 100% no-repeat;
  background-size: cover;
  position: relative;
  .light {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%);
    width: 340px;
    height: 140px;
    z-index: 100;
  }
  header {
    background: linear-gradient(90deg, #0d438d 0%, #144d9c 100%);
    height: 68px;
    z-index: 10;
    .logo {
      position: relative;
      .zs {
        position: absolute;
        width: 330px;
        height: 55px;
        left: 50%;
        bottom: 10px;
      }
    }
    .logo-title {
      font-size: 24px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      color: #fefefe;
      line-height: 60px;

      background: linear-gradient(0deg, #aed8ff 0%, #ffffff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .dropdown {
      color: #fff;
      cursor: pointer;
      padding: 5px 10px;
      font-size: 16px;
      background: linear-gradient(0deg, #0096ff, #043475);
      border: 1px solid #548ddb;
      border-radius: 4px;
    }
  }
  .main {
    padding: 10px;
    box-sizing: border-box;
    height: calc(100vh - 68px);
    .search {
      display: flex;
      margin: 50px 0;
      justify-content: center;

      ::v-deep .el-input__inner {
        height: 50px;

        background: linear-gradient(0deg, #0d3a8d, #03256b);
        border: 1px solid #548ddb;
        // border-image: radial-gradient(circle, #bde4ff, #548ddb) 1 1;
        border-radius: 4px;
        font-size: 18px;
      }

      img {
        width: 50px;
        height: 50px;
        transform: translate(-10px);
      }
    }
    .list {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      padding: 0 130px;
      justify-content: center;
      .item {
        width: 275px;
        margin: 15px 10px;
        height: 310px;
        position: relative;
        background: url('../assets/images/菜单项背景.png') no-repeat;
        background-size: 100% 100%;

        img {
          width: 80px;
          height: 80px;
        }
        .img {
          position: absolute;
          left: 50%;
          transform: translate(-50%);
          top: 27%;
        }
        .text {
          position: absolute;
          left: 50%;
          transform: translate(-50%);
          bottom: 9.5%;

          font-size: 22px;
          font-family: Microsoft YaHei;
          font-weight: bold;
          color: #ffffff;
          line-height: 26px;

          background: linear-gradient(0deg, #cff1ff 0%, #ffffff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
  }
  main {
    position: relative;

    .systemList {
      position: absolute;
      left: 50%;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.2);
      padding: 20px 40px;
      border-radius: 20px;
      z-index: 9;
      transform: translate(-50%, 50%);
      .systemItem {
        width: 90px;
        height: 90px;
        border-radius: 20px;
        background-color: #fff;
        transition: 0.3s;
        &:not(:last-child) {
          margin-right: 20px;
        }
        &:hover {
          transform: scale(1.1);
        }
        img {
          width: 50px;
          height: 50px;
          border-radius: 10px;
        }
      }
    }
  }

  footer {
    background-color: #fafafb;
    color: #ccc;
  }
}

::v-deep .el-input-group__append {
  background-color: dodgerblue;
  i {
    color: #fff;
  }
}
</style>
