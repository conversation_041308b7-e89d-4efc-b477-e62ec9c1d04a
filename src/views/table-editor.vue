<template>
  <div class="table-editor-page">
    <!-- 控制面板 -->
    <div class="control-panel">
      <!-- 表格尺寸和实时信息并排显示 -->
      <div class="control-section">
        <div class="control-row-split">
          <!-- 左侧：表格整体尺寸 -->
          <div class="control-column">
            <h4>表格整体尺寸</h4>
            <div class="control-row-compact">
              <div class="control-item-compact">
                <label>宽度:</label>
                <div class="input-with-icon">
                  <input
                    type="text"
                    v-model="tableWidthInput"
                    @input="debounceUpdateTableWidth"
                    placeholder="100vw"
                    class="dimension-input width-input"
                  >
                  <span class="input-icon">📏</span>
                </div>
              </div>
              <div class="control-item-compact">
                <label>高度:</label>
                <div class="input-with-icon">
                  <input
                    type="text"
                    v-model="tableHeightInput"
                    @input="debounceUpdateTableHeight"
                    placeholder="auto"
                    class="dimension-input height-input"
                  >
                  <span class="input-icon">📐</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧：实时信息 -->
          <div class="control-column">
            <h4>实时信息</h4>
            <div class="info-display-compact">
              <div>宽度: {{ currentTableWidth }}px</div>
              <div>高度: {{ currentTableHeight }}px</div>
              <div>单元格: {{ totalCells }}个</div>
            </div>
          </div>
        </div>
      </div>

      <div class="control-section">
        <div class="control-buttons">
          <button @click="showUniformSizeDialog" class="uniform-size-button">
            <span class="button-icon">📐</span>
            统一单元格尺寸
          </button>
          <button @click="addBlankRow" class="add-row-button">
            <span class="button-icon">➕</span>
            添加空白行
          </button>
          <button @click="showMathHelpDialog" class="math-help-button">
            <span class="button-icon">∑</span>
            数学公式帮助
          </button>
          <button @click="resetTable" class="reset-button">重置表格</button>
          <button @click="downloadTable" class="download-button">下载HTML</button>
          <button @click="exportToWord" class="export-word-button">导出Word</button>
          <button @click="addTestRows" class="test-button">添加测试行</button>
        </div>
      </div>
    </div>

    <!-- 表格容器组件 -->
    <TableContainer
      ref="tableContainer"
      :table-width="tableWidthInput"
      :table-height="tableHeightInput"
      :data-rows="dataRows"
      :min-cell-width="minCellWidth"
      :min-cell-height="minCellHeight"
      @delete-row="handleDeleteRow"
      @table-updated="updateTableInfo"
      @table-width-changed="handleTableWidthChanged"
      @ensure-row="ensureRow"
      @start-edit="handleStartEdit"
      @finish-edit="handleFinishEdit"
      @cancel-edit="handleCancelEdit"
    />

    <!-- 统一尺寸设置对话框 -->
    <div v-if="uniformSizeDialogVisible" class="dialog-overlay" @click="closeUniformSizeDialog">
      <div class="dialog uniform-size-dialog" @click.stop>
        <h3>统一单元格尺寸</h3>
        <div class="dialog-content">
          <div class="uniform-size-section">
            <h4>当前设置</h4>
            <div class="current-settings">
              <div class="setting-item">
                <span class="setting-label">统一宽度:</span>
                <span class="setting-value">{{ uniformCellWidth }}px</span>
              </div>
              <div class="setting-item">
                <span class="setting-label">统一高度:</span>
                <span class="setting-value">{{ uniformCellHeight }}px</span>
              </div>
            </div>
          </div>

          <div class="uniform-size-section">
            <h4>新的设置</h4>
            <div class="uniform-inputs">
              <div class="uniform-input-group">
                <label>统一宽度:</label>
                <div class="input-with-icon">
                  <input
                    type="number"
                    v-model="newUniformWidth"
                    :min="minCellWidth"
                    placeholder="120"
                    class="dimension-input uniform-dialog-input"
                    @keydown.enter="handleApplyUniformSize"
                    @keydown.esc="closeUniformSizeDialog"
                    ref="uniformWidthInput"
                  >
                  <span class="input-unit">px</span>
                </div>
              </div>

              <div class="uniform-input-group">
                <label>统一高度:</label>
                <div class="input-with-icon">
                  <input
                    type="number"
                    v-model="newUniformHeight"
                    :min="minCellHeight"
                    placeholder="50"
                    class="dimension-input uniform-dialog-input"
                    @keydown.enter="handleApplyUniformSize"
                    @keydown.esc="closeUniformSizeDialog"
                  >
                  <span class="input-unit">px</span>
                </div>
              </div>
            </div>
          </div>

          <div class="uniform-size-section">
            <div class="quick-presets">
              <h4>快速预设</h4>
              <div class="preset-buttons">
                <button @click="applyPreset('small')" class="preset-btn small-preset">
                  小尺寸 (80×30)
                </button>
                <button @click="applyPreset('medium')" class="preset-btn medium-preset">
                  中尺寸 (120×50)
                </button>
                <button @click="applyPreset('large')" class="preset-btn large-preset">
                  大尺寸 (160×70)
                </button>
              </div>
            </div>
          </div>
        </div>

        <div class="dialog-buttons">
          <button @click="closeUniformSizeDialog" class="btn-cancel">取消</button>
          <button @click="handleApplyUniformSize" class="btn-confirm">应用设置</button>
        </div>
      </div>
    </div>

    <!-- 数学公式帮助对话框 -->
    <div v-if="mathHelpDialogVisible" class="dialog-overlay" @click="closeMathHelpDialog">
      <div class="dialog math-help-dialog" @click.stop>
        <h3>数学公式输入帮助</h3>
        <div class="dialog-content">
          <div class="math-help-section">
            <h4>基本语法</h4>
            <div class="math-examples">
              <div class="math-example">
                <div class="example-label">行内公式：</div>
                <div class="example-input">$E = mc^2$</div>
              </div>
              <div class="math-example">
                <div class="example-label">显示公式：</div>
                <div class="example-input">$$\\int_0^\\infty e^{-x^2} dx$$</div>
              </div>
            </div>
          </div>

          <div class="math-help-section">
            <h4>常用符号</h4>
            <div class="symbol-grid">
              <div class="symbol-item">
                <span class="symbol-display">α β γ</span>
                <span class="symbol-latex">\\alpha \\beta \\gamma</span>
              </div>
              <div class="symbol-item">
                <span class="symbol-display">∑ ∏ ∫</span>
                <span class="symbol-latex">\\sum \\prod \\int</span>
              </div>
              <div class="symbol-item">
                <span class="symbol-display">√ ∞ ±</span>
                <span class="symbol-latex">\\sqrt{} \\infty \\pm</span>
              </div>
            </div>
          </div>

          <div class="math-help-section">
            <h4>快速示例</h4>
            <div class="quick-examples">
              <button @click="insertQuickExample('$x^2 + y^2 = r^2$')" class="example-btn">
                平方和
              </button>
              <button @click="insertQuickExample('$\\frac{a}{b}$')" class="example-btn">
                分数
              </button>
              <button @click="insertQuickExample('$\\sqrt{x}$')" class="example-btn">
                平方根
              </button>
              <button @click="insertQuickExample('$\\sum_{i=1}^n x_i$')" class="example-btn">
                求和
              </button>
            </div>
          </div>
        </div>

        <div class="dialog-buttons">
          <button @click="closeMathHelpDialog" class="btn-confirm">关闭</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TableContainer from '@/components/TableContainer.vue'

export default {
  name: 'TableEditor',
  components: {
    TableContainer
  },
  data() {
    return {
      // 表格尺寸控制
      tableWidthInput: '1600px',
      tableHeightInput: '650px',
      uniformCellWidth: 120,
      uniformCellHeight: 50,

      // 实时信息
      currentTableWidth: 0,
      currentTableHeight: 0,
      totalCells: 0,

      // 统一尺寸设置对话框
      uniformSizeDialogVisible: false,
      newUniformWidth: 120,
      newUniformHeight: 50,

      // 最小尺寸限制
      minCellWidth: 20,
      minCellHeight: 20,

      // 表格数据行（不包括表头）
      dataRows: [
        Array(8).fill(null).map(() => ({
          content: '',
          isEditing: false,
          originalContent: '',
          hasMath: false
        }))
      ],

      // 数学公式帮助对话框
      mathHelpDialogVisible: false,

      // 防抖定时器
      debounceTimers: {},

      // 初始状态保存
      initialTableState: null
    }
  },
  computed: {
    // 可以在这里添加其他计算属性
  },
  mounted() {
    this.initializeTable()
    this.saveInitialState()
    this.updateTableInfo()
    this.setupEventListeners()
  },
  beforeDestroy() {
    this.removeEventListeners()
    this.clearAllDebounceTimers()
  },
  methods: {
    // 初始化表格
    initializeTable() {
      this.$nextTick(() => {
        this.calculateTotalCells()
        this.updateTableInfo()
      })
    },

    // 保存初始状态
    saveInitialState() {
      this.$nextTick(() => {
        const tableContainer = this.$refs.tableContainer
        if (tableContainer) {
          const table = tableContainer.getTableRef()
          if (table) {
            this.initialTableState = {
              width: this.tableWidthInput,
              height: this.tableHeightInput,
              cellStyles: this.getCellStyles(table)
            }
          }
        }
      })
    },

    // 获取所有单元格样式
    getCellStyles(table) {
      const cells = table.querySelectorAll('td')
      const styles = []
      cells.forEach(cell => {
        const rect = cell.getBoundingClientRect()
        styles.push({
          width: rect.width,
          height: rect.height,
          minWidth: cell.style.minWidth || '',
          minHeight: cell.style.minHeight || ''
        })
      })
      return styles
    },

    // 计算单元格总数
    calculateTotalCells() {
      const headerCells = 10
      const dataCells = this.dataRows.length * 8
      this.totalCells = headerCells + dataCells
    },

    // 更新表格信息
    updateTableInfo() {
      this.$nextTick(() => {
        const tableContainer = this.$refs.tableContainer
        if (tableContainer) {
          const container = tableContainer.getTableContainerRef()
          const table = tableContainer.getTableRef()

          if (container && table) {
            const containerRect = container.getBoundingClientRect()
            this.currentTableWidth = Math.round(containerRect.width)
            this.currentTableHeight = Math.round(containerRect.height)
          }
        }
      })
    },

    // 设置事件监听器
    setupEventListeners() {
      window.addEventListener('resize', this.handleWindowResize)
    },

    // 移除事件监听器
    removeEventListeners() {
      window.removeEventListener('resize', this.handleWindowResize)
    },

    // 窗口大小改变处理
    handleWindowResize() {
      this.debounce('windowResize', () => {
        this.updateTableInfo()
      }, 300)
    },

    // 防抖函数
    debounce(key, func, delay) {
      if (this.debounceTimers[key]) {
        clearTimeout(this.debounceTimers[key])
      }
      this.debounceTimers[key] = setTimeout(() => {
        func()
        delete this.debounceTimers[key]
      }, delay)
    },

    // 清除所有防抖定时器
    clearAllDebounceTimers() {
      Object.values(this.debounceTimers).forEach(timer => clearTimeout(timer))
      this.debounceTimers = {}
    },

    // 防抖更新表格宽度
    debounceUpdateTableWidth() {
      this.debounce('tableWidth', () => {
        this.updateTableInfo()
      }, 300)
    },

    // 防抖更新表格高度
    debounceUpdateTableHeight() {
      this.debounce('tableHeight', () => {
        this.updateTableInfo()
      }, 300)
    },

    // 显示统一尺寸设置对话框
    showUniformSizeDialog() {
      this.newUniformWidth = this.uniformCellWidth
      this.newUniformHeight = this.uniformCellHeight
      this.uniformSizeDialogVisible = true

      this.$nextTick(() => {
        if (this.$refs.uniformWidthInput) {
          this.$refs.uniformWidthInput.focus()
          this.$refs.uniformWidthInput.select()
        }
      })
    },

    // 关闭统一尺寸设置对话框
    closeUniformSizeDialog() {
      this.uniformSizeDialogVisible = false
    },

    // 处理应用统一尺寸按钮点击
    handleApplyUniformSize() {
      this.applyUniformSize()
    },

    // 应用统一尺寸设置
    applyUniformSize() {
      const width = Math.max(this.minCellWidth, parseInt(this.newUniformWidth) || this.minCellWidth)
      const height = Math.max(this.minCellHeight, parseInt(this.newUniformHeight) || this.minCellHeight)

      this.uniformCellWidth = width
      this.uniformCellHeight = height

      const tableContainer = this.$refs.tableContainer
      if (tableContainer) {
        tableContainer.applyUniformSize(width, height)
      }

      this.closeUniformSizeDialog()
    },

    // 应用预设尺寸
    applyPreset(size) {
      const presets = {
        small: { width: 80, height: 30 },
        medium: { width: 120, height: 50 },
        large: { width: 160, height: 70 }
      }

      const preset = presets[size]
      if (preset) {
        this.newUniformWidth = preset.width
        this.newUniformHeight = preset.height
      }
    },

    // 处理删除行事件
    handleDeleteRow(dataRowIndex) {
      this.dataRows.splice(dataRowIndex, 1)
      this.calculateTotalCells()
      this.updateTableInfo()
    },

    // 处理表格宽度变化事件
    handleTableWidthChanged(newWidth) {
      this.tableWidthInput = newWidth
    },

    // 确保行存在
    ensureRow(rowIndex) {
      if (!this.dataRows[rowIndex]) {
        this.$set(this.dataRows, rowIndex, Array(8).fill(null).map(() => ({
          content: '',
          isEditing: false,
          originalContent: '',
          hasMath: false
        })))
      }
    },

    // 处理开始编辑事件
    handleStartEdit({ rowIndex, cellIndex, cell }) {
      cell.isEditing = true
    },

    // 处理完成编辑事件
    handleFinishEdit({ rowIndex, cellIndex, cell, newContent }) {
      cell.isEditing = false
    },

    // 处理取消编辑事件
    handleCancelEdit({ rowIndex, cellIndex, cell }) {
      cell.isEditing = false
    },

    // 添加空白行
    addBlankRow() {
      const newRow = Array(8).fill(null).map(() => ({
        content: '',
        isEditing: false,
        originalContent: '',
        hasMath: false
      }))

      this.dataRows.push(newRow)
      this.calculateTotalCells()
      this.updateTableInfo()

      this.$nextTick(() => {
        this.scrollToNewRow()
      })
    },

    // 滚动到新添加的行
    scrollToNewRow() {
      const scrollContainer = document.querySelector('.table-scroll-container')
      const tableContainer = this.$refs.tableContainer

      if (scrollContainer && tableContainer) {
        const table = tableContainer.getTableRef()
        if (table) {
          const lastRowIndex = this.dataRows.length - 1 + 2
          const lastRow = table.rows[lastRowIndex]

          if (lastRow) {
            lastRow.scrollIntoView({
              behavior: 'smooth',
              block: 'center'
            })
          }
        }
      }
    },

    // 添加测试行
    addTestRows() {
      const mathExamples = [
        '$E = mc^2$',
        '$\\int_0^\\infty e^{-x^2} dx = \\frac{\\sqrt{\\pi}}{2}$',
        '$\\sum_{n=1}^\\infty \\frac{1}{n^2} = \\frac{\\pi^2}{6}$',
        '$\\lim_{x \\to 0} \\frac{\\sin x}{x} = 1$',
        '$\\sqrt{a^2 + b^2}$'
      ]

      for (let i = 0; i < 5; i++) {
        const newRow = Array(8).fill(null).map((_, j) => {
          let content = `测试数据 ${this.dataRows.length + i + 1}-${j + 1}`

          if (j === 2 && i < mathExamples.length) {
            content = mathExamples[i]
          }

          return {
            content: content,
            isEditing: false,
            originalContent: '',
            hasMath: this.containsMath(content)
          }
        })
        this.dataRows.push(newRow)
      }

      this.calculateTotalCells()
      this.updateTableInfo()
    },

    // 检测内容是否包含数学公式
    containsMath(content) {
      if (!content) return false

      const mathPatterns = [
        /\$.*?\$/,
        /\$\$.*?\$\$/,
        /\\\(.*?\\\)/,
        /\\\[.*?\\\]/,
        /\\begin\{.*?\}.*?\\end\{.*?\}/,
        /\\[a-zA-Z]+/
      ]

      return mathPatterns.some(pattern => pattern.test(content))
    },

    // 显示数学公式帮助对话框
    showMathHelpDialog() {
      this.mathHelpDialogVisible = true
    },

    // 关闭数学公式帮助对话框
    closeMathHelpDialog() {
      this.mathHelpDialogVisible = false
    },

    // 插入快速示例
    insertQuickExample(latex) {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(latex).then(() => {
          alert(`公式 "${latex}" 已复制到剪贴板，您可以粘贴到单元格中`)
        }).catch(() => {
          prompt('请复制以下公式:', latex)
        })
      } else {
        prompt('请复制以下公式:', latex)
      }
    },

    // 重置表格
    resetTable() {
      if (!this.initialTableState) return

      this.tableWidthInput = this.initialTableState.width
      this.tableHeightInput = this.initialTableState.height

      const tableContainer = this.$refs.tableContainer
      if (tableContainer) {
        const table = tableContainer.getTableRef()
        if (table) {
          const cells = table.querySelectorAll('td')
          cells.forEach((cell, index) => {
            if (this.initialTableState.cellStyles[index]) {
              const style = this.initialTableState.cellStyles[index]
              cell.style.width = style.minWidth || ''
              cell.style.height = style.minHeight || ''
              cell.style.minWidth = style.minWidth || ''
              cell.style.minHeight = style.minHeight || ''
            }
          })
        }
      }

      this.updateTableInfo()
    },

    // 下载表格
    downloadTable() {
      const tableContainer = this.$refs.tableContainer
      if (!tableContainer) return

      const table = tableContainer.getTableRef()
      if (!table) return

      // 创建表格副本
      const clonedTable = table.cloneNode(true)

      // 生成HTML
      const html = this.generateDownloadHTML(clonedTable)

      // 创建下载链接
      const blob = new Blob([html], { type: 'text/html;charset=utf-8' })
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = '表格重构版本.html'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(link.href)
    },

    // 导出到Word
    async exportToWord() {
      // 检查是否有数据
      if (!this.dataRows || this.dataRows.length === 0) {
        this.$message.warning('表格中没有数据，请先添加一些内容')
        return
      }

      // 显示加载提示
      const loading = this.$loading({
        lock: true,
        text: '正在生成Word文档，请稍候...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      try {
        console.log('开始导出Word文档...')

        // 准备导出数据（异步处理数学公式）
        const exportData = await this.prepareExportData()
        console.log('导出数据:', exportData)

        // 动态导入API
        const { exportSimpleTableToWord } = await import('@/api/word/export')

        // 调用导出API
        const response = await exportSimpleTableToWord(exportData)

        console.log('API响应:', response)
        console.log('响应状态:', response.status)
        console.log('响应头:', response.headers)
        console.log('响应数据类型:', typeof response.data)
        console.log('响应数据大小:', response.data ? (response.data.size || response.data.byteLength || response.data.length) : 'unknown')

        // 检查响应
        if (!response || !response.data) {
          throw new Error('服务器返回数据为空')
        }

        if (response.status !== 200) {
          throw new Error(`服务器响应错误: ${response.status}`)
        }

        const dataSize = response.data.size || response.data.byteLength || response.data.length
        if (!dataSize || dataSize === 0) {
          throw new Error('返回的文件数据为空')
        }

        // 处理文件下载
        this.downloadWordFile(response, exportData.title)

        loading.close()
        this.$message.success('Word文档导出成功！')

      } catch (error) {
        // 关闭加载提示
        loading.close()

        console.error('导出Word文档失败:', error)

        // 根据错误类型显示不同的提示
        let errorMessage = '导出失败'
        if (error.response) {
          // 服务器响应错误
          if (error.response.status === 404) {
            errorMessage = '导出服务不可用，请检查后端服务'
          } else if (error.response.status === 500) {
            errorMessage = '服务器内部错误，请稍后重试'
          } else {
            errorMessage = `服务器错误: ${error.response.status}`
          }
        } else if (error.message) {
          // 其他错误
          errorMessage = `导出失败: ${error.message}`
        } else {
          errorMessage = '导出失败: 未知错误'
        }

        this.$message.error(errorMessage)
      }
    },

    // 准备导出数据
    async prepareExportData() {
      const tableContainer = this.$refs.tableContainer
      if (!tableContainer) {
        throw new Error('未找到表格容器组件')
      }

      const table = tableContainer.getTableRef()
      if (!table) {
        throw new Error('未找到表格元素')
      }

      // 获取表格尺寸
      const tableRect = table.getBoundingClientRect()

      // 获取实际的列宽配置和纵向显示配置
      const columnWidthsInfo = tableContainer.getColumnWidthsInfo ? tableContainer.getColumnWidthsInfo() : null
      const actualColumnWidths = columnWidthsInfo ? columnWidthsInfo.columnWidths : [150, 200, 150, 80, 80, 120, 120, 120]
      const headerHeight = columnWidthsInfo ? columnWidthsInfo.headerCellHeight : 50
      const verticalHeaders = columnWidthsInfo ? columnWidthsInfo.verticalHeaders : [false, false, false, false, false, true, true, true]

      // 准备表头数据 - 使用实际的列宽配置和纵向显示配置
      const headers = [
        [
          { content: '检查工序<br>名称', rowspan: 2, colspan: 1, width: actualColumnWidths[0], height: headerHeight, isVertical: verticalHeaders[0] },
          { content: '检查项目及技术条件', rowspan: 2, colspan: 1, width: actualColumnWidths[1], height: headerHeight, isVertical: verticalHeaders[1] },
          { content: '实际检查结果', rowspan: 2, colspan: 1, width: actualColumnWidths[2], height: headerHeight, isVertical: verticalHeaders[2] },
          { content: '完工', rowspan: 1, colspan: 2, width: actualColumnWidths[3] + actualColumnWidths[4], height: headerHeight / 2, isVertical: false },
          { content: '', rowspan: 1, colspan: 1, width: 0, height: headerHeight / 2, isVertical: false }, // 占位符
          { content: '操作员', rowspan: 2, colspan: 1, width: actualColumnWidths[5], height: headerHeight, isVertical: verticalHeaders[5] },
          { content: '班组长', rowspan: 2, colspan: 1, width: actualColumnWidths[6], height: headerHeight, isVertical: verticalHeaders[6] },
          { content: '检验员', rowspan: 2, colspan: 1, width: actualColumnWidths[7], height: headerHeight, isVertical: verticalHeaders[7] }
        ],
        [
          { content: '', rowspan: 1, colspan: 1, width: 0, height: headerHeight / 2, isVertical: false }, // 占位符
          { content: '', rowspan: 1, colspan: 1, width: 0, height: headerHeight / 2, isVertical: false }, // 占位符
          { content: '', rowspan: 1, colspan: 1, width: 0, height: headerHeight / 2, isVertical: false }, // 占位符
          { content: '月', rowspan: 1, colspan: 1, width: actualColumnWidths[3], height: headerHeight / 2, isVertical: verticalHeaders[3] },
          { content: '日', rowspan: 1, colspan: 1, width: actualColumnWidths[4], height: headerHeight / 2, isVertical: verticalHeaders[4] },
          { content: '', rowspan: 1, colspan: 1, width: 0, height: headerHeight / 2, isVertical: false }, // 占位符
          { content: '', rowspan: 1, colspan: 1, width: 0, height: headerHeight / 2, isVertical: false }, // 占位符
          { content: '', rowspan: 1, colspan: 1, width: 0, height: headerHeight / 2, isVertical: false }  // 占位符
        ]
      ]

      // 准备数据行 - 确保每行都有8个单元格
      const dataRows = this.dataRows.map(row => {
        const processedRow = []
        for (let i = 0; i < 8; i++) {
          const cell = row[i] || { content: '', hasMath: false }
          processedRow.push({
            content: cell.content || '',
            hasMath: Boolean(cell.hasMath),
            mathML: cell.mathML || null, // 添加mathML字段
            width: this.getColumnWidth(i),
            height: 50
          })
        }
        return processedRow
      })

      // 过滤掉空行
      const filteredDataRows = dataRows.filter(row =>
        row.some(cell => cell.content && cell.content.trim() !== '')
      )

      console.log('开始处理数学公式转换...')
      console.log('转换前的数据行:', filteredDataRows)

      // 处理数学公式转换
      const processedTableData = await this.processMathFormulas({
        headers: headers,
        dataRows: filteredDataRows
      })

      console.log('转换后的数据行:', processedTableData.dataRows)

      // 检查是否有MathML数据
      let mathMLCount = 0
      processedTableData.dataRows.forEach((row, rowIndex) => {
        row.forEach((cell, cellIndex) => {
          if (cell.mathML) {
            mathMLCount++
            console.log(`发现MathML数据 [${rowIndex}, ${cellIndex}]:`, {
              content: cell.content,
              hasMath: cell.hasMath,
              mathML: cell.mathML.substring(0, 100) + '...'
            })
          }
        })
      })
      console.log(`总共找到 ${mathMLCount} 个MathML数据`)

      const exportData = {
        title: '检验记录表',
        tableWidth: Math.round(tableRect.width) || 1200,
        tableHeight: Math.round(tableRect.height) || 600,
        pageOrientation: 'LANDSCAPE', // 默认横向纸张
        tableData: processedTableData
      }

      return exportData
    },

    // 处理数学公式转换
    async processMathFormulas(tableData) {
      try {
        // 动态导入数学公式工具
        const mathFormulaUtils = await import('@/utils/math-formula-utils.js')
        const mathUtils = mathFormulaUtils.default

        console.log('开始处理表格中的数学公式...')

        // 处理表格数据中的数学公式
        const processedTableData = await mathUtils.processTableData(tableData)

        console.log('数学公式处理完成')
        return processedTableData

      } catch (error) {
        console.error('数学公式处理失败:', error)
        // 如果数学公式处理失败，返回原始数据
        console.warn('使用原始数据继续导出')
        return tableData
      }
    },

    // 获取列宽度
    getColumnWidth(columnIndex) {
      const tableContainer = this.$refs.tableContainer
      if (tableContainer && typeof tableContainer.getActualColumnWidth === 'function') {
        return tableContainer.getActualColumnWidth(columnIndex)
      }
      // 备用硬编码宽度（如果无法从组件获取）
      const fallbackWidths = [150, 200, 150, 80, 80, 120, 120, 120]
      return fallbackWidths[columnIndex] || 120
    },

    // 下载Word文件
    downloadWordFile(response, title) {
      try {
        if (!response || !response.data) {
          throw new Error('响应数据为空')
        }

        // 创建Blob对象
        const blob = new Blob([response.data], {
          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        })

        if (blob.size === 0) {
          throw new Error('生成的文件为空')
        }

        // 创建下载链接
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `${title || '检查记录表'}.docx`

        // 触发下载
        document.body.appendChild(link)
        link.click()

        // 清理
        document.body.removeChild(link)
        setTimeout(() => {
          window.URL.revokeObjectURL(url)
        }, 100)

      } catch (error) {
        console.error('文件下载失败:', error)
        throw error
      }
    },

    // 生成下载HTML
    generateDownloadHTML(table) {
      const currentDate = new Date().toLocaleString('zh-CN')

      return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格重构版本</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; }
        td { border: 1px solid #ddd; padding: 8px; text-align: center; }
        .header { text-align: center; margin-bottom: 20px; }
        .header-cell { background: #f5f7fa; font-weight: bold; }
        .editable-cell { background: white; }
    </style>
</head>
<body>
    <div class="header">
        <h2>表格重构版本</h2>
        <p>生成时间: ${currentDate}</p>
    </div>
    ${table.outerHTML}
</body>
</html>`
    }
  }
}
</script>

<style scoped>
.table-editor-page {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
  min-height: 100vh;
  padding: 10px;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

/* 控制面板样式 */
.control-panel {
  background: white;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e9ecef;
  max-height: 25vh;
  overflow-y: auto;
}

.control-section {
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid #f1f3f5;
}

.control-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.control-section h4 {
  margin: 0 0 6px 0;
  color: #495057;
  font-weight: 500;
  font-size: 12px;
}

.control-row-split {
  display: flex;
  gap: 15px;
  align-items: flex-start;
}

.control-column {
  flex: 1;
}

.control-row-compact {
  display: flex;
  gap: 10px;
  align-items: center;
}

.control-item-compact {
  display: flex;
  align-items: center;
  gap: 5px;
  min-width: 120px;
}

.control-item-compact label {
  font-size: 11px;
  color: #6c757d;
  white-space: nowrap;
  min-width: 30px;
}

.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.dimension-input {
  width: 80px;
  padding: 4px 25px 4px 6px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 11px;
  text-align: center;
}

.input-icon {
  position: absolute;
  right: 4px;
  font-size: 10px;
  color: #6c757d;
  pointer-events: none;
}

.info-display-compact {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 11px;
  color: #495057;
  background: #f8f9fa;
  padding: 6px 8px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.control-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  align-items: center;
}

.control-buttons button {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.uniform-size-button {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
}

.add-row-button {
  background: linear-gradient(135deg, #28a745, #1e7e34);
  color: white;
}

.math-help-button {
  background: linear-gradient(135deg, #17a2b8, #117a8b);
  color: white;
}

.reset-button {
  background: linear-gradient(135deg, #6c757d, #545b62);
  color: white;
}

.download-button {
  background: linear-gradient(135deg, #fd7e14, #e55a00);
  color: white;
}

.export-word-button {
  background: linear-gradient(135deg, #20c997, #17a085);
  color: white;
}

.test-button {
  background: linear-gradient(135deg, #e83e8c, #c2185b);
  color: white;
}

.control-buttons button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.button-icon {
  font-size: 14px;
}

/* 对话框样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.dialog {
  background: white;
  border-radius: 8px;
  padding: 20px;
  min-width: 300px;
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.dialog h3 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 18px;
}

.dialog-content {
  margin-bottom: 20px;
}

.dialog-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.btn-cancel, .btn-confirm {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-cancel {
  background: #6c757d;
  color: white;
}

.btn-confirm {
  background: #007bff;
  color: white;
}

.btn-cancel:hover {
  background: #5a6268;
}

.btn-confirm:hover {
  background: #0056b3;
}

/* 统一尺寸对话框样式 */
.uniform-size-dialog {
  min-width: 400px;
}

.uniform-size-section {
  margin-bottom: 20px;
}

.uniform-size-section h4 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 14px;
  font-weight: 600;
}

.current-settings {
  background: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-label {
  color: #6c757d;
  font-size: 12px;
}

.setting-value {
  color: #495057;
  font-weight: 500;
  font-size: 12px;
}

.uniform-inputs {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.uniform-input-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.uniform-input-group label {
  min-width: 80px;
  color: #495057;
  font-size: 14px;
}

.uniform-dialog-input {
  width: 100px;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

.input-unit {
  color: #6c757d;
  font-size: 12px;
}

.quick-presets {
  margin-top: 10px;
}

.preset-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.preset-btn {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.preset-btn:hover {
  background: #f8f9fa;
  border-color: #007bff;
}

.small-preset:hover {
  background: #e3f2fd;
}

.medium-preset:hover {
  background: #e8f5e9;
}

.large-preset:hover {
  background: #fff3e0;
}

/* 数学公式帮助对话框样式 */
.math-help-dialog {
  min-width: 500px;
}

.math-help-section {
  margin-bottom: 20px;
}

.math-help-section h4 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 14px;
  font-weight: 600;
}

.math-examples {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.math-example {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
}

.example-label {
  min-width: 80px;
  color: #6c757d;
  font-size: 12px;
}

.example-input {
  font-family: 'Courier New', monospace;
  background: white;
  padding: 4px 8px;
  border: 1px solid #e9ecef;
  border-radius: 3px;
  font-size: 12px;
}

.symbol-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 10px;
}

.symbol-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.symbol-display {
  font-size: 16px;
  margin-bottom: 5px;
}

.symbol-latex {
  font-family: 'Courier New', monospace;
  font-size: 11px;
  color: #6c757d;
}

.quick-examples {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.example-btn {
  padding: 6px 12px;
  border: 1px solid #007bff;
  border-radius: 4px;
  background: white;
  color: #007bff;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.example-btn:hover {
  background: #007bff;
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .control-row-split {
    flex-direction: column;
    gap: 12px;
  }

  .control-buttons {
    flex-direction: column;
  }

  .dialog {
    min-width: auto;
    width: 90vw;
  }

  .uniform-size-dialog,
  .math-help-dialog {
    min-width: auto;
  }
}
</style>
