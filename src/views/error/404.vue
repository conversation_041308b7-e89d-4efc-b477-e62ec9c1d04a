<template>
  <div class="error-page">
    <!-- 404 错误头部 -->
    <div class="error-header">
      <div class="error-icon">
        <i class="el-icon-warning-outline"></i>
      </div>
      <div class="error-content">
        <h1 class="error-title">404</h1>
        <h2 class="error-subtitle">页面未找到</h2>
        <p class="error-description">
          抱歉，您访问的页面不存在。请检查URL是否正确，或从下面的可用页面中选择一个。
        </p>
      </div>
    </div>

    <!-- 可用路由列表 -->
    <div class="routes-section">
      <!-- 路由卡片列表 -->
      <div class="routes-grid" v-if="filteredRoutes.length > 0">
        <div
            v-for="route in filteredRoutes"
            :key="route.path"
            class="route-card"
            @click="navigateToRoute(route)"
        >
          <div class="route-icon">
            <i :class="getRouteIcon(route)"></i>
          </div>
          <div class="route-info">
            <h4 class="route-title">{{ route.title }}</h4>
            <p class="route-path">{{ route.path }}</p>
            <p class="route-description" v-if="route.description">
              {{ route.description }}
            </p>
          </div>
          <div class="route-arrow">
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作 -->
    <div class="actions">
      <el-button type="primary" @click="goHome" icon="el-icon-house">
        返回首页
      </el-button>
      <el-button @click="goBack" icon="el-icon-back">
        返回上页
      </el-button>
      <el-button @click="refreshPage" icon="el-icon-refresh">
        刷新页面
      </el-button>
    </div>
  </div>
</template>

<script>
import {constantRoutes} from '@/router'

export default {
  name: 'Page404',
  data() {
    return {
      searchKeyword: '',
      filteredRoutes: [],
      allRoutes: []
    }
  },
  computed: {
    // 获取所有可用路由
    availableRoutes() {
      const routes = []

      // 从 constantRoutes 中提取非隐藏路由
      this.extractRoutes(constantRoutes, routes)

      // 从 store 中获取动态路由
      if (this.$store.state.permission && this.$store.state.permission.routes) {
        this.extractRoutes(this.$store.state.permission.routes, routes)
      }

      // 去重并排序
      const uniqueRoutes = routes.filter((route, index, self) =>
          index === self.findIndex(r => r.path === route.path)
      )

      return uniqueRoutes.sort((a, b) => a.title.localeCompare(b.title))
    }
  },
  mounted() {
    this.initializeRoutes()
  },
  methods: {
    // 初始化路由列表
    initializeRoutes() {
      this.allRoutes = this.availableRoutes
      this.filteredRoutes = [...this.allRoutes]
    },

    // 递归提取路由信息
    extractRoutes(routes, result, parentPath = '') {
      routes.forEach(route => {
        // 跳过隐藏路由和特殊路由
        if (route.hidden ||
            route.path === '*' ||
            route.path.includes('redirect') ||
            route.path.includes('404') ||
            route.path.includes('401') ||
            route.path.includes('login')) {
          return
        }

        const fullPath = this.buildFullPath(parentPath, route.path)

        // 如果有子路由，递归处理
        if (route.children && route.children.length > 0) {
          // 如果父路由有标题，也添加到结果中
          if (route.meta && route.meta.title) {
            result.push({
              path: fullPath,
              title: route.meta.title,
              icon: route.meta.icon,
              description: this.getRouteDescription(route)
            })
          }

          this.extractRoutes(route.children, result, fullPath)
        } else {
          // 叶子节点路由
          if (route.meta && route.meta.title) {
            result.push({
              path: fullPath,
              title: route.meta.title,
              icon: route.meta.icon,
              description: this.getRouteDescription(route)
            })
          }
        }
      })
    },

    // 构建完整路径
    buildFullPath(parentPath, currentPath) {
      if (currentPath.startsWith('/')) {
        return currentPath
      }

      if (parentPath) {
        return `${parentPath}/${currentPath}`.replace(/\/+/g, '/')
      }

      return `/${currentPath}`
    },

    // 获取路由描述
    getRouteDescription(route) {
      if (route.meta && route.meta.description) {
        return route.meta.description
      }

      // 根据路径生成描述
      const pathSegments = route.path.split('/').filter(Boolean)
      const lastSegment = pathSegments[pathSegments.length - 1]

      const descriptions = {
        'table': '表格管理页面',
        'word': '文档处理页面',
        'user': '用户管理',
        'system': '系统设置',
        'dashboard': '仪表盘',
        'index': '首页',
        'profile': '个人资料',
        'settings': '设置'
      }

      return descriptions[lastSegment] || '页面功能'
    },

    // 获取路由图标
    getRouteIcon(route) {
      if (route.icon) {
        return route.icon.startsWith('el-icon-') ? route.icon : `el-icon-${route.icon}`
      }

      // 根据路径或标题推断图标
      const path = route.path.toLowerCase()
      const title = route.title.toLowerCase()

      if (path.includes('table') || title.includes('表格')) {
        return 'el-icon-s-grid'
      } else if (path.includes('word') || title.includes('文档')) {
        return 'el-icon-document'
      } else if (path.includes('user') || title.includes('用户')) {
        return 'el-icon-user'
      } else if (path.includes('system') || title.includes('系统')) {
        return 'el-icon-setting'
      } else if (path.includes('dashboard') || title.includes('仪表盘')) {
        return 'el-icon-data-analysis'
      } else if (path.includes('index') || title.includes('首页')) {
        return 'el-icon-house'
      }

      return 'el-icon-menu'
    },

    // 过滤路由
    filterRoutes() {
      if (!this.searchKeyword.trim()) {
        this.filteredRoutes = [...this.allRoutes]
        return
      }

      const keyword = this.searchKeyword.toLowerCase()
      this.filteredRoutes = this.allRoutes.filter(route =>
          route.title.toLowerCase().includes(keyword) ||
          route.path.toLowerCase().includes(keyword) ||
          (route.description && route.description.toLowerCase().includes(keyword))
      )
    },

    // 导航到指定路由
    navigateToRoute(route) {
      try {
        this.$router.push(route.path)
      } catch (error) {
        console.error('导航失败:', error)
        this.$message.error('无法访问该页面')
      }
    },

    // 返回首页
    goHome() {
      this.$router.push('/')
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },

    // 刷新页面
    refreshPage() {
      window.location.reload()
    }
  }
}
</script>

<style lang="scss" scoped>
.error-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

// 错误头部
.error-header {
  text-align: center;
  margin-bottom: 40px;
  animation: fadeInDown 0.8s ease-out;

  .error-icon {
    margin-bottom: 20px;

    i {
      font-size: 80px;
      color: #fff;
      opacity: 0.9;
    }
  }

  .error-content {
    color: white;

    .error-title {
      font-size: 120px;
      font-weight: 700;
      margin: 0;
      text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      background: linear-gradient(45deg, #fff, #f0f0f0);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .error-subtitle {
      font-size: 32px;
      font-weight: 300;
      margin: 10px 0 20px 0;
      opacity: 0.9;
    }

    .error-description {
      font-size: 16px;
      line-height: 1.6;
      opacity: 0.8;
      max-width: 600px;
      margin: 0 auto;
    }
  }
}

// 路由部分
.routes-section {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 30px;
  width: 100%;
  max-width: 1200px;
  margin-bottom: 30px;
  animation: fadeInUp 0.8s ease-out 0.2s both;

  .section-header {
    text-align: center;
    margin-bottom: 30px;

    h3 {
      font-size: 24px;
      color: #333;
      margin: 0 0 10px 0;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;

      i {
        color: #409eff;
      }
    }

    .section-description {
      color: #666;
      margin: 0;
      font-size: 14px;
    }
  }

  .search-box {
    margin-bottom: 30px;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
  }
}

// 路由网格
.routes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;

  .route-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 15px;

    &:hover {
      background: #409eff;
      color: white;
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(64, 158, 255, 0.3);

      .route-icon i {
        color: white;
      }

      .route-arrow i {
        color: white;
        transform: translateX(5px);
      }
    }

    .route-icon {
      flex-shrink: 0;
      width: 50px;
      height: 50px;
      background: #409eff;
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;

      i {
        font-size: 24px;
        color: white;
      }
    }

    .route-info {
      flex: 1;

      .route-title {
        font-size: 16px;
        font-weight: 600;
        margin: 0 0 5px 0;
        color: #333;
      }

      .route-path {
        font-size: 12px;
        color: #666;
        margin: 0 0 5px 0;
        font-family: 'Monaco', 'Menlo', monospace;
        background: rgba(0, 0, 0, 0.05);
        padding: 2px 6px;
        border-radius: 4px;
        display: inline-block;
      }

      .route-description {
        font-size: 13px;
        color: #888;
        margin: 0;
        line-height: 1.4;
      }
    }

    .route-arrow {
      flex-shrink: 0;

      i {
        font-size: 18px;
        color: #ccc;
        transition: all 0.3s ease;
      }
    }
  }
}

// 无结果状态
.no-results,
.no-routes {
  text-align: center;
  padding: 60px 20px;
  color: #999;

  i {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
  }

  p {
    font-size: 16px;
    margin: 0;
  }
}

// 底部操作
.actions {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  justify-content: center;
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

// 动画
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .error-page {
    padding: 15px;
  }

  .error-header {
    .error-content {
      .error-title {
        font-size: 80px;
      }

      .error-subtitle {
        font-size: 24px;
      }

      .error-description {
        font-size: 14px;
      }
    }
  }

  .routes-section {
    padding: 20px;
  }

  .routes-grid {
    grid-template-columns: 1fr;
    gap: 15px;

    .route-card {
      padding: 15px;
    }
  }

  .actions {
    flex-direction: column;
    width: 100%;
    max-width: 300px;

    .el-button {
      width: 100%;
    }
  }
}
</style>
