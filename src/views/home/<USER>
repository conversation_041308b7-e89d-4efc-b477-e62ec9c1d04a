<template>
  <div class="home-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">检测数据管理系统</h1>
      <p class="page-subtitle">设备检测数据模板配置与管理平台</p>
    </div>

    <!-- 功能模块卡片 -->
    <div class="module-grid">
      <!-- 检测数据管理模块 -->
      <div class="module-card" @click="navigateToDataManagement">
        <div class="module-icon">
          <i class="el-icon-s-data"></i>
        </div>
        <div class="module-content">
          <h3 class="module-title">检测数据管理</h3>
          <p class="module-description">
            管理设备检测数据，支持数据上传、查看
          </p>
          <div class="module-features">
            <span class="feature-tag">数据查询</span>
            <span class="feature-tag">数据上传</span>
          </div>
        </div>
        <div class="module-arrow">
          <i class="el-icon-arrow-right"></i>
        </div>
      </div>

      <!-- 模板管理模块 -->
      <div class="module-card" @click="navigateToTemplateManagement">
        <div class="module-icon">
          <i class="el-icon-document-copy"></i>
        </div>
        <div class="module-content">
          <h3 class="module-title">模板管理</h3>
          <p class="module-description">
            设计和管理检测数据模板，支持Excel样式设计和设备绑定配置
          </p>
          <div class="module-features">
            <span class="feature-tag">模板列表</span>
            <span class="feature-tag">设备绑定</span>
          </div>
        </div>
        <div class="module-arrow">
          <i class="el-icon-arrow-right"></i>
        </div>
      </div>

    </div>

    <!-- 快速统计信息 -->
    <div class="stats-section">
      <div class="stats-card">
        <div class="stats-icon">
          <i class="el-icon-document"></i>
        </div>
        <div class="stats-content">
          <div class="stats-number">{{ totalDataCount }}</div>
          <div class="stats-label">检测数据总数</div>
        </div>
      </div>

      <div class="stats-card">
        <div class="stats-icon">
          <i class="el-icon-files"></i>
        </div>
        <div class="stats-content">
          <div class="stats-number">{{ totalTemplateCount }}</div>
          <div class="stats-label">模板总数</div>
        </div>
      </div>

      <div class="stats-card">
        <div class="stats-icon">
          <i class="el-icon-upload"></i>
        </div>
        <div class="stats-content">
          <div class="stats-number">{{ todayUploadCount }}</div>
          <div class="stats-label">今日上传</div>
        </div>
      </div>

    </div>
  </div>
</template>

<script>
export default {
  name: 'Home',
  data() {
    return {
      totalDataCount: 0,
      totalTemplateCount: 0,
      todayUploadCount: 0,
      activeTemplateCount: 0
    }
  },
  created() {
    this.loadStatistics()
  },
  methods: {
    // 导航到检测数据管理
    navigateToDataManagement() {
      this.$router.push('/detection/data/list')
    },

    // 导航到模板管理
    navigateToTemplateManagement() {
      this.$router.push('/template/list')
    },

    // 加载统计数据
    loadStatistics() {
      // 这里可以调用API获取实际统计数据
      // 暂时使用模拟数据
      this.totalDataCount = 1248
      this.totalTemplateCount = 23
      this.todayUploadCount = 15
      this.activeTemplateCount = 18
    }
  }
}
</script>

<style lang="scss" scoped>
.home-container {
  background: linear-gradient(180deg, #124B9A, #0D438D);
  min-height: 100vh;
  padding: 30px;
  color: #ffffff;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 36px;
  font-weight: bold;
  margin: 0 0 10px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.page-subtitle {
  font-size: 18px;
  margin: 0;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.module-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 25px;
  margin-bottom: 40px;
}

.module-card {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  backdrop-filter: blur(10px);
}

.module-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.15);
}

.module-icon {
  font-size: 48px;
  margin-right: 20px;
  opacity: 0.9;
  min-width: 60px;
}

.module-content {
  flex: 1;
}

.module-title {
  font-size: 20px;
  font-weight: bold;
  margin: 0 0 8px 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.module-description {
  font-size: 14px;
  margin: 0 0 15px 0;
  opacity: 0.8;
  line-height: 1.5;
}

.module-features {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.feature-tag {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.module-arrow {
  font-size: 24px;
  opacity: 0.7;
  margin-left: 15px;
}

.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stats-card {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.stats-card:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.stats-icon {
  font-size: 32px;
  margin-right: 15px;
  opacity: 0.8;
}

.stats-content {
  flex: 1;
}

.stats-number {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.stats-label {
  font-size: 14px;
  opacity: 0.8;
}
</style>
