<template>
  <div>
    <header class="d-flex a-center j-center font-14">系统切换</header>
    <div
      v-for="item in routers"
      :key="item.meta.title"
      :style="{ color: activeSystem === item.meta.title ? ' #409EFF' : '' }"
      class="systemItem"
      @click="systemSwitch(item)"
    >
      {{ item.meta.title }}
    </div>
    <div class="systemItem" @click="backHome">首页</div>
    <div class="systemItem" @click="logout">退出系统</div>
  </div>
</template>

<script>
import systemRoute from '@/mixin/systemRoute.js';
import { getPartiesLoginInfo } from '@/api/interfaces/interfaces';
export default {
  mixins: [systemRoute],
  data() {
    return {};
  },
  computed: {
    // 所有的路由信息
    routers() {
      return this.$store.state.permission.topbarRouters;
    },
    activeSystem() {
      try {
        return this.$route.matched.filter(
          (item) => item.meta && item.meta.title,
        )[0].meta.title;
      } catch (e) {
        //TODO handle the exception
        return '';
      }
    },
  },
  methods: {
    /**
     * 系统切换
     * @param {Object} item
     */
    systemSwitch(item) {
      if (item.meta.link) {
        item.meta.link = this.$string.handlerLink(item.meta.link);
        if (item.isSso === '1') {
          getPartiesLoginInfo(item.ssoName).then((res) => {
            console.log(
              `${item.meta.link}?username=${res.data.user_id}&password=${res.data.password}`,
            );
            window.open(
              res.data
                ? `${item.meta.link}?username=${res.data.user_id}&password=${res.data.password}`
                : item.meta.link,
              'href',
            );
          });
          return;
        }
        window.open(item.meta.link, 'href');
      } else {
        let arr = ['/workStation', '/schedule','/workshop'];
        let { path } = arr.includes(item.path)
          ? item
          : this.getSystemRoute(item);
        this.$store.dispatch('settings/changeSetting', {
          key: 'showSettings',
          value: false,
        });
        this.$router.push(path);
      }
    },
    /**
     * 返回首页
     */
    backHome() {
      this.$store.dispatch('settings/changeSetting', {
        key: 'showSettings',
        value: false,
      });
      this.$router.push('/index');
    },
    /**
     * 退出
     */
    async logout() {
      this.$store.dispatch('LogOut').then(() => {
        location.href = '/index';
      });
    },
  },
};
</script>

<style lang="scss" scoped>
header {
  height: 50px;
  font-weight: 600;
  // background: #eef5fd;
}
.systemItem {
  height: 40px;
  padding: 0 30px;
  display: flex;
  align-items: center;
  font-size: 16px;
  cursor: pointer;
  &:hover {
    color: #409eff;
    background-color: #f3f8fe;
  }
}
</style>
