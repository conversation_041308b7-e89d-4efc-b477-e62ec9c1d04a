<template>
  <div v-if="errorLog.length">
    <el-badge is-dot class="pointer" style="line-height: 25px;" @click.native="dialogVisible=true">
      <span class="iconfont font-23 pd-r5" style="color: #FFF;">&#xe606;</span>
    </el-badge>

    <el-dialog :visible.sync="dialogVisible" width="80%" append-to-body>
      <div slot="title">
        <span style="padding-right: 10px;">错误日志</span>
        <el-button size="mini" type="primary" icon="el-icon-delete" @click="clearAll">清空</el-button>
      </div>

      <el-scrollbar class="scrollbar" wrap-style="overflow-x:hidden;">
        <el-table :data="errorLog" class="lt-table">
          <el-table-column label="报错路由" width="220">
            <template slot-scope="{row}">
              {{ row.vm.$route.fullPath }}
            </template>
          </el-table-column>
          <!-- <el-table-column label="完整路径" width="300" prop="url" /> -->
          <el-table-column label="报错原因" show-overflow-tooltip>
            <template slot-scope="{row}">
              {{ row.vm.$vnode.tag }} error in {{ row.info }}
            </template>
          </el-table-column>
          <el-table-column label="报错信息" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span style="color: #FF0000;">{{ row.err.message }}</span>
            </template>
          </el-table-column>
          <el-table-column label="报错时间" prop="createdTime" width="170" />
        </el-table>
      </el-scrollbar>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: 'ErrorLog',
  data() {
    return {
      dialogVisible: false
    }
  },
  computed: {
    ...mapGetters([
      'errorLog'
    ])
  },

  methods: {
    clearAll() {
      this.dialogVisible = false
      this.$store.dispatch('errorLog/clearErrorLog')
    }
  }
}
</script>

<style lang="scss" scoped>
  .scrollbar {
    width: 100%;
    height: 500px;
  }
</style>
