<template>
  <div>
    <div class="siderbar">
      <logo v-if="showLogo" :collapse="isCollapse" />
      <el-scrollbar
        :class="settings.sideTheme"
        wrap-class="scrollbar-wrapper"
        style="height: calc(100vh - 83px)"
      >
        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapse"
          :unique-opened="true"
          :collapse-transition="false"
          mode="vertical"
        >
          <sidebar-item
            v-for="(route, index) in sidebarRouters"
            :key="route.path + index"
            :item="route"
            :base-path="route.path"
          />
        </el-menu>
      </el-scrollbar>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";
import Logo from "./Logo";
import SidebarItem from "./SidebarItem";
import variables from "@/assets/styles/variables.scss";
import { constantRoutes } from "@/router";

export default {
  components: { SidebarItem, Logo },
  computed: {
    ...mapState(["settings"]),
    ...mapGetters(["sidebar"]),
    sidebarRouters() {
      // 直接返回所有有meta.title的路由，排除隐藏的路由
      const visibleRoutes = constantRoutes.filter(route => 
        !route.hidden && 
        route.meta && 
        route.meta.title
      );
      return visibleRoutes;
    },
    activeMenu() {
      const route = this.$route;
      const { meta, path } = route;
      // if set path, the sidebar will highlight the path you set
      if (meta && meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo;
    },
    variables() {
      return variables;
    },
    isCollapse() {
      return !this.sidebar.opened;
    },
  },
  methods: {
    ishttp(url) {
      return url.indexOf("http://") !== -1 || url.indexOf("https://") !== -1;
    },
  },
};
</script>
<style scoped lang="scss">
.siderbar {
  background-image: url("../../../assets/images/菜单栏底部背景.png");
  background-repeat: no-repeat;
  background-position: center bottom;
  width: 100%;
  height: 100%;
}

</style>
