<script>
import { getIconImage } from '@/utils/index.js'
export default {
  name: 'MenuItem',
  functional: true,
  // data() {
  //   return {
  //     getIconImage
  //   }
  // },
  props: {
    icon: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    }
  },
  render(h, context) {
    const { icon, title } = context.props
    const vnodes = []
    if (icon && icon !== '#') {
      vnodes.push(parseInt(icon) > 0 ? <img src={getIconImage(icon)} style="width: 15px; height: 15px;margin-right: 16px; margin-left: 0.35em;" /> : <svg-icon icon-class={icon}/>)
    }

    if (title) {
      vnodes.push(<span slot='title'>{(title)}</span>)
    }
    return vnodes
  }
}
</script>
