<template>
  <div class="navbar">
    <img src="@/assets/images/首页顶部中间.png" class="light" alt="" />
    <div class="d-flex a-center">
      <hamburger
        id="hamburger-container"
        :is-active="sidebar.opened"
        class="hamburger-container"
        @toggleClick="toggleSideBar"
      />

      <breadcrumb
        v-if="!topNav || true"
        id="breadcrumb-container"
        class="breadcrumb-container"
      />
    </div>

    <div class="right-menu d-flex a-center">
      <el-tooltip class="item" effect="dark" content="返回" placement="bottom">
        <div class="function" @click="$router.back(-1)">
          <img src="@/assets/images/返回.png" />
        </div>
      </el-tooltip>

      <!-- <el-tooltip
        class="item"
        effect="dark"
        content="在线指南"
        placement="bottom"
      >
        <div class="function mr-r30" @click="toMapMarkdown">
          <img src="@/assets/images/在线指南.png" />
        </div>
      </el-tooltip> -->

      <el-dropdown
        class="avatar-container right-menu-item hover-effect"
        trigger="click"
      >
        <div class="avatar-wrapper d-flex a-center">
          <img :src="$store.state.app.uploadUrl + avatar" class="user-avatar" />
          <span style="color: #fff" class="pd-l10 font-14">{{
            $store.state.user.nickName
          }}</span>
          <!-- <i class="el-icon-caret-bottom" /> -->
        </div>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item>
            <span>修改密码</span>
          </el-dropdown-item>
          <el-dropdown-item @click.native="logout">
            <span>退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <div
        class="d-flex a-center j-center system mr-l20"
        style="color: #fff"
        @click.stop="setting = true"
      >
        <div>{{ activeSystem }} <i class="el-icon-arrow-down mr-l10" /></div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Breadcrumb from "@/components/Breadcrumb";
import Hamburger from "@/components/Hamburger";
import errorLog from "./errorLog/errorLog.vue";
import news from "./news";

export default {
  components: {
    Breadcrumb,
    Hamburger,
    errorLog,
    news,
  },
  inject:['reload'],
  data() {
    return {
      drawerFlag: false,
      appUrlIn: "",
      appUrlOut: "",
      obj: {},
    };
  },
  computed: {
    ...mapGetters(["sidebar", "avatar", "device", "user"]),
    setting: {
      get() {
        return this.$store.state.settings.showSettings;
      },
      set(val) {
        this.$store.dispatch("settings/changeSetting", {
          key: "showSettings",
          value: val,
        });
      },
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav;
      },
    },
    activeSystem() {
      // 修复刷新界面时的报错问题
      const matched = this.$route.matched.filter(
        (item) => item.meta && item.meta.title
      );
      return matched.length > 0 ? matched[0].meta.title : "首页";
    },
  },
  mounted() {
  },
  methods: {
    toAdminEditor() {
      window.open("http://" + window.location.host + "/adminEditor");
    },
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar");
      setTimeout(() => {
        this.reload()
      }, 300);

    },
    async logout() {
      this.$confirm("确定注销并退出系统吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$store.dispatch("LogOut").then(() => {
            location.href = "/index";
          });
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.navbar {
  height: 68px;
  overflow: hidden;
  position: relative;
  background: #0d438d;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .hamburger-container {
    line-height: 68px;
    height: 100%;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .right-menu {
    height: 100%;
    line-height: 68px;
    padding-right: 20px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      // padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      // margin-right: 30px;

      .avatar-wrapper {
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 50%;
        }
      }
    }
  }
  .light {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%);
    width: 340px;
    height: 140px;
    z-index: 9999;
  }
}

::v-depp .el-breadcrumb__item {
  line-height: 68px;
}

::v-depp .el-breadcrumb__inner {
  color: #fff;
}

.function {
  cursor: pointer;
  width: 40px;
  height: 40px;
  background: linear-gradient(0deg, #0096ff, #043475);
  border: 1px solid;
  border-image: radial-gradient(circle, #e3f3ff, #548ddb) 1 1;
  border-radius: 4px;
  img {
    width: 100%;
    height: 100%;
  }
}
.system {
  color: #fff;
  cursor: pointer;
  height: 40px;
  font-size: 16px;
  background: linear-gradient(0deg, #0096ff, #043475);
  border: 1px solid #548ddb;
  border-radius: 4px;
  padding: 0 15px;
}
</style>
