package com.logictrue.common.core.utils;

import com.logictrue.common.core.domain.ExcelData;
import com.logictrue.common.core.domain.ExcelLineData;
import com.logictrue.common.core.domain.ExcelSheetData;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class ExcelUtils {

    private static HSSFWorkbook wb;
    private static HSSFSheet sheet;
    private static HSSFRow row;
    private static XSSFWorkbook wbx;
    private static XSSFSheet sheetx;
    private static XSSFRow rowx;


    public static ExcelData readExcel(File file) throws Exception {

        InputStream is = null;
        try {
            is = new FileInputStream(file);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }

        ExcelData excelData = new ExcelData();
        try {
            wb = new HSSFWorkbook(is);
        } catch (IOException e) {
            e.printStackTrace();
        }
        Integer sheetNum = wb.getNumberOfSheets();
        excelData.setSheetSum(sheetNum);
        excelData.setFileName(file.getName());

        //循环获取所有sheet数据
        List<ExcelSheetData> sheetDatas = new ArrayList<>();
        for (int i = 0; i < sheetNum; i++) {
            ExcelSheetData sheetData = new ExcelSheetData();
            sheet = wb.getSheetAt(i);
            sheetData.setLineSum(sheet.getPhysicalNumberOfRows());
            sheetData.setSheetName(sheet.getSheetName());

            List<ExcelLineData> lineDatas = readExcelContentBySheet(sheet);
            sheetData.setLineData(lineDatas);
            sheetDatas.add(sheetData);
        }
        excelData.setSheetData(sheetDatas);
        return excelData;
    }

    /**
     * 获取多个sheetExcel表格数据
     * 注意：1、若单元格为空时，读出为""空字符串；列数为按最后一列计算；
     * 2、如果在最后一列前单元格合并还是数据个数还是按合并前列数算，但是合并后单元格只有左上单元格有数据；
     * 3、如果是最后一列合并单元格，则算最前面一列 ，后面列不计算 在其中，也没数据（不是""空串）
     *
     * @param
     * @return
     */
    public static ExcelData readExcel(MultipartFile file) throws Exception {
        File newFile = multipartFileToFile(file);
        return readExcel(newFile);
    }

    private static ExcelData readExcelx(XSSFWorkbook wbx, String fileName) {

        ExcelData excelData = new ExcelData();
        Integer sheetNum = wbx.getNumberOfSheets();
        excelData.setSheetSum(sheetNum);
        excelData.setFileName(fileName);

        //循环获取所有sheet数据
        List<ExcelSheetData> sheetDatas = new ArrayList<>();
        for (int i = 0; i < sheetNum; i++) {
            ExcelSheetData sheetData = new ExcelSheetData();
            sheetx = wbx.getSheetAt(i);
            sheetData.setSheetName(sheetx.getSheetName());
            sheetData.setLineSum(sheetx.getPhysicalNumberOfRows());
            List<ExcelLineData> lineDatas = readExcelContentBySheetx(sheetx);
            sheetData.setLineData(lineDatas);
            sheetDatas.add(sheetData);
        }
        excelData.setSheetData(sheetDatas);
        return excelData;
    }


    private static List<ExcelLineData> readExcelContentBySheet(HSSFSheet sheet) {
        List<ExcelLineData> lineDatas = new ArrayList<>();
        // 得到总行数
        int rowNum = sheet.getLastRowNum();

        //记录sheet总列数
        int sheetLine = 0;

        for (int i = 0; i <= rowNum; i++) {
            int j = 0;
            row = sheet.getRow(i);
            if (Objects.isNull(row)) {
                continue;
            }

            int colNum = row.getLastCellNum();
            ExcelLineData lineData = new ExcelLineData();
            List<String> colData = new ArrayList<>();
            lineData.setColSum(colNum);
            while (j < colNum) {
                String value = getCellValue(row.getCell(j)).trim();
                colData.add(value);
                j++;
            }

            //行数为1时 保存数据长度
            //行数不为1时 比对数据长度 进行追加
            if (i == 0) {
                sheetLine = colNum;
            } else {
                int caNum = sheetLine - colData.size();
                if (caNum > 0) {
                    List<String> nColData = new ArrayList<>(sheetLine);
                    for (String col : colData) {
                        nColData.add(col);
                    }
                    for (int k = 0; k < caNum; k++) {
                        nColData.add(colData.size() + k, "");
                    }
                    colData = nColData;
                }
            }

            lineData.setColData(colData);
            lineDatas.add(lineData);
        }

        return lineDatas;
    }

    private static List<ExcelLineData> readExcelContentBySheetx(XSSFSheet sheetx) {
        List<ExcelLineData> lineDatas = new ArrayList<>();
        // 得到总行数
        int rowNum = sheetx.getLastRowNum();
        for (int i = 0; i <= rowNum; i++) {
            int j = 0;
            rowx = sheetx.getRow(i);
            if (Objects.isNull(rowx)) {
                continue;
            }

            int colNum = rowx.getLastCellNum();
            ExcelLineData lineData = new ExcelLineData();
            List<String> colData = new ArrayList<>();
            lineData.setColSum(colNum);
            while (j < colNum) {
                String value = getCellValuex(rowx.getCell(j)).trim();
                colData.add(value);
                j++;
            }
            lineData.setColData(colData);
            lineDatas.add(lineData);
        }

        return lineDatas;
    }

    /**
     * 获取单元格数据
     *
     * @param cell Excel单元格
     * @return String 单元格数据内容
     */
    private static String getCellValue(HSSFCell cell) {
        if (Objects.isNull(cell)) {
            return "";
        }

        String value = "";
        switch (cell.getCellType().getCode()) {
            case 0: // 数字
                //如果为时间格式的内容
                if (DateUtil.isCellDateFormatted(cell)) {
                    //注：format格式 yyyy-MM-dd hh:mm:ss 中小时为12小时制，若要24小时制，则把小h变为H即可，yyyy-MM-dd HH:mm:ss
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
                    value = sdf.format(DateUtil.getJavaDate(cell.getNumericCellValue())).toString();
                    break;
                } else {
                    value = Double.toString(cell.getNumericCellValue());
                }
                break;
            case 1: // 字符串
                value = cell.getStringCellValue();
                break;
            case 4: // Boolean
                value = cell.getBooleanCellValue() + "";
                break;
            case 2: // 公式
                value = cell.getCellFormula() + "";
                break;
            case 3: // 空值
                value = "";
                break;
            case 5: // 故障
                value = "非法字符";
                break;
            default:
                value = "未知类型";
                break;
        }
        return value;
    }

    private static String getCellValuex(XSSFCell cellx) {
        if (Objects.isNull(cellx)) {
            return "";
        }

        String value = "";
        switch (cellx.getCellType().getCode()) {
            case 0: // 数字
                //如果为时间格式的内容
                if (DateUtil.isCellDateFormatted(cellx)) {
                    //注：format格式 yyyy-MM-dd hh:mm:ss 中小时为12小时制，若要24小时制，则把小h变为H即可，yyyy-MM-dd HH:mm:ss
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
                    value = sdf.format(DateUtil.getJavaDate(cellx.getNumericCellValue())).toString();
                    break;
                } else {
                    value =Double.toString(cellx.getNumericCellValue());
                }
                break;
            case 1: // 字符串
                value = cellx.getStringCellValue();
                break;
            case 4: // Boolean
                value = cellx.getBooleanCellValue() + "";
                break;
            case 2: // 公式
                value = cellx.getCellFormula() + "";
                break;
            case 3: // 空值
                value = "";
                break;
            case 5: // 故障
                value = "非法字符";
                break;
            default:
                value = "未知类型";
                break;
        }
        return value;
    }

    //流转换
    public static File multipartFileToFile(MultipartFile file) throws Exception {

        File toFile = null;
        if (file.equals("") || file.getSize() <= 0) {
            file = null;
        } else {
            InputStream ins = null;
            ins = file.getInputStream();
            toFile = new File(file.getOriginalFilename());
            inputStreamToFile(ins, toFile);
            ins.close();
        }
        return toFile;
    }

    //获取流文件
    private static void inputStreamToFile(InputStream ins, File file) {
        try {
            OutputStream os = new FileOutputStream(file);
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            ins.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public static void main(String[] args) {
//        ExcelData excelData = readExcel(file);
//        System.out.println(excelData.toString());
//        for(ExcelSheetData excelSheetData:excelData.getSheetData()){
//            System.out.println("**********************"+excelSheetData.getSheetName());
//            for(ExcelLineData excelLineData :excelSheetData.getLineData()){
//                System.out.println("----------------"+excelLineData.getColSum());
//                System.out.println(">>>>>>>"+excelLineData.getColData());
//            }
//        }
    }


}
