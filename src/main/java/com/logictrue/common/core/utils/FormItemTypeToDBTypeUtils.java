package com.logictrue.common.core.utils;

/**
 * <p>
 * 将表单项类型转换为数据库字段类型
 * 如：textarea转换为longtext
 * 暂时只对textarea做特殊处理，其他类型不做处理，默认返回varchar(255)
 * 如果需要对其他类型做特殊处理，可以在这里添加
 * 只适配mysql数据库
 * </p>
 *
 * <AUTHOR>
 */
public class FormItemTypeToDBTypeUtils {


    /**
     * 将表单项类型转换为数据库字段类型
     *
     * @param formItemType
     * @return 返回对应的数据库字段类型
     */
    public static String formItemTypeToDBType(String formItemType) {
        if ("textarea".equals(formItemType) || "data-table".equals(formItemType)) {
            return "longtext";
        }
        return "varchar(255)";
    }

}
