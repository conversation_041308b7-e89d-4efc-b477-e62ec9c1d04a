package com.logictrue.common.core.constant;

/**
 * 权限相关通用常量
 *
 * <AUTHOR>
 */
public class SecurityConstants
{
    /**
     * 令牌自定义标识
     */
    public static final String TOKEN_AUTHENTICATION = "Authorization";

    /**
     * 令牌前缀
     */
    public static final String TOKEN_PREFIX = "Bearer ";

    /**
     * 用户ID字段
     */
    public static final String DETAILS_USER_ID = "user_id";

    /**
     * 用户名字段
     */
    public static final String DETAILS_USERNAME = "username";

    /**
     * 用户昵称
     */
    public static final String DETAILS_NICKNAME = "nickname";

    /**
     * 授权信息字段
     */
    public static final String AUTHORIZATION_HEADER = "authorization";

    /**
     * 请求来源
     */
    public static final String FROM_SOURCE = "from-source";

    /**
     * 内部请求
     */
    public static final String INNER = "inner";
}
