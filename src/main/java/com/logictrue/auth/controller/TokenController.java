package com.logictrue.auth.controller;

import com.logictrue.auth.entity.LoginUser;
import com.logictrue.auth.form.LoginBody;
import com.logictrue.auth.form.RegisterBody;
import com.logictrue.auth.service.RedisService;
import com.logictrue.auth.service.SysLoginService;
import com.logictrue.auth.service.TokenService;
import com.logictrue.common.core.domain.R;
import com.logictrue.common.core.exception.BaseException;
import com.logictrue.common.core.utils.RSAUtils;
import com.logictrue.common.core.utils.StringUtils;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * token 控制
 *
 * <AUTHOR>
 */
@RestController
public class TokenController {
    @Autowired
    private TokenService tokenService;

    @Autowired
    private SysLoginService sysLoginService;

    @Autowired
    private RedisService redisService;


    /**
     * 获取公钥
     *
     * @param request    会话
     * @param isWebLogin 是否Web端登陆 1 是; 假如有值并为1的话 则根据username判断此用户是否已经登陆
     * @return
     */
    @ApiOperation(value = "获取公钥", httpMethod = "GET")
    @GetMapping("/publicKey")
    @ResponseBody
    public R<?> publicKey(HttpServletRequest request, Integer isWebLogin, String username) {
        boolean isLogin = false;
        if (null != isWebLogin && 1 == isWebLogin) {
            R<LoginUser> userInfo = sysLoginService.getUserInfoByName(username,null);
            if (StringUtils.isNull(userInfo) || StringUtils.isNull(userInfo.getData())) {
                throw new BaseException("登录用户：" + username + " 不存在");
            }
            isLogin = tokenService.checkUserIsLogin(userInfo.getData());
        }
        String publicKey = RSAUtils.generateKey(request);

        Map<String, Object> resMap = new HashMap<>();
        resMap.put("isLogin", isLogin);
        resMap.put("publicKey", publicKey);
        return R.ok(resMap, "操作成功！");
    }

    @PostMapping("login")
    public R<?> login(HttpServletRequest request, @RequestBody LoginBody form) {
        String password = getDecryptPassword(request, form);
        if (null == password) {
            return R.fail("鉴权失败，请刷新页面,重新登陆！");
        }
        try {
            // 用户登录
            LoginUser userInfo = sysLoginService.login(form.getUsername(), password,form.getType());
            // 获取登录token
            return R.ok(tokenService.createToken(userInfo));
        }catch (BaseException be){
            return R.fail(be.getDefaultMessage());
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @PostMapping("cardLogin")
    public R<?> cardLogin(@RequestBody LoginBody form) {
        // 用户登录
        LoginUser userInfo = sysLoginService.cardLogin(form.getUsername(), form.getUserNumber(), form.getUserId(),form.getType());
        // 获取登录token
        return R.ok(tokenService.createToken(userInfo));
    }

    private String getDecryptPassword(HttpServletRequest request, LoginBody form) {
        String privateKey = (String) request.getSession().getAttribute("privateKey");
        if (privateKey == null) {
            return null;
        }
        try {
            return RSAUtils.decryptDataOnJava(form.getPassword(), privateKey);
        } catch (Exception e) {
            return null;
        }
    }

    @DeleteMapping("logout")
    public R<?> logout(HttpServletRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (StringUtils.isNotNull(loginUser)) {
            String username = loginUser.getUsername();
            // 删除用户缓存记录
            tokenService.delLoginUser(loginUser.getToken());
            // 记录用户退出日志
            sysLoginService.logout(username);
        }
        return R.ok();
    }


    @PostMapping("refresh")
    public R<?> refresh(HttpServletRequest request) {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (StringUtils.isNotNull(loginUser)) {
            // 刷新令牌有效期
            tokenService.refreshToken(loginUser);
            return R.ok();
        }
        return R.ok();
    }

    @PostMapping("register")
    public R<?> register(@RequestBody RegisterBody registerBody) {
        // 用户注册
        sysLoginService.register(registerBody.getUsername(), registerBody.getPassword());
        return R.ok();
    }

}
