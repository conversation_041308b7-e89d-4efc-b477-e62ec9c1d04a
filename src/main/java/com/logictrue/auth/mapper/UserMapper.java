package com.logictrue.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logictrue.auth.entity.SysUser;
import org.apache.ibatis.annotations.Mapper;

/**
 * 用户表 数据层 (简化版用于iot-base模块)
 *
 * <AUTHOR>
 */
@Mapper
public interface UserMapper extends BaseMapper<SysUser> {
    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    public SysUser selectUserByUserName(String userName);

    /**
     * 通过企业用户ID查询用户
     *
     * @param enterpriseUserId 企业用户ID
     * @return 用户对象信息
     */
    public SysUser selectUserEnterpriseUserId(String enterpriseUserId);

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    public SysUser selectUserById(Long userId);


    /**
     * 新增用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    int insertUser(SysUser user);
}
