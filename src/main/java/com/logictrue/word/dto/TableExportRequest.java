package com.logictrue.word.dto;

import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * 表格导出请求DTO
 */
@Data
public class TableExportRequest {
    
    /**
     * 表格标题
     */
    private String title;
    
    /**
     * 表格宽度（像素）
     */
    private Integer tableWidth;
    
    /**
     * 表格高度（像素）
     */
    private Integer tableHeight;

    /**
     * 纸张方向 (PORTRAIT: 纵向, LANDSCAPE: 横向)
     */
    private String pageOrientation;

    /**
     * 表格数据
     */
    private TableData tableData;

    /**
     * 数据行合并单元格配置列表
     */
    private List<MergeCell> merges;

    /**
     * 表头合并单元格配置列表
     */
    private List<MergeCell> headerMerges;

    /**
     * 表头宽度配置
     */
    private HeaderWidthConfig headerWidthConfig;

    /**
     * 表头纵向文字配置
     */
    private List<Boolean> verticalHeadersConfig;

    /**
     * 元数据信息
     */
    private Metadata metadata;
    
    @Data
    public static class TableData {
        /**
         * 表头数据
         */
        private List<List<HeaderCell>> headers;
        
        /**
         * 数据行
         */
        private List<List<DataCell>> dataRows;
    }
    
    @Data
    public static class HeaderCell {
        /**
         * 单元格内容
         */
        private String content;

        /**
         * 行跨度
         */
        private Integer rowspan;

        /**
         * 列跨度
         */
        private Integer colspan;

        /**
         * 宽度（像素）
         */
        private Integer width;

        /**
         * 高度（像素）
         */
        private Integer height;

        /**
         * 是否纵向显示文字
         */
        private Boolean isVertical;
    }
    
    @Data
    public static class DataCell {
        /**
         * 单元格内容（可能包含占位符）
         */
        private String content;

        /**
         * 是否包含数学公式
         */
        private Boolean hasMath;

        /**
         * MathML字符串（前端LaTeX转换后的MathML，用于纯公式内容）
         */
        private String mathML;

        /**
         * 是否包含混合内容（公式+文本）
         */
        private Boolean hasMultipleContent;

        /**
         * 占位符到MathML的映射（用于混合内容）
         */
        private Map<String, String> mathMLMap;

        /**
         * 宽度（像素）
         */
        private Integer width;

        /**
         * 高度（像素）
         */
        private Integer height;

        /**
         * 行跨度（用于合并单元格）
         */
        private Integer rowspan;

        /**
         * 列跨度（用于合并单元格）
         */
        private Integer colspan;
    }

    /**
     * 合并单元格配置
     */
    @Data
    public static class MergeCell {
        /**
         * 起始行索引（从0开始）
         */
        private Integer startRow;

        /**
         * 起始列索引（从0开始）
         */
        private Integer startCol;

        /**
         * 结束行索引（从0开始）
         */
        private Integer endRow;

        /**
         * 结束列索引（从0开始）
         */
        private Integer endCol;

        /**
         * 行跨度
         */
        private Integer rowspan;

        /**
         * 列跨度
         */
        private Integer colspan;

        /**
         * 合并单元格的宽度（像素）
         */
        private Integer width;

        /**
         * 合并单元格的高度（像素）
         */
        private Integer height;

        /**
         * 合并后的内容
         */
        private String content;
    }

    /**
     * 元数据信息
     */
    @Data
    public static class Metadata {
        /**
         * 导出时间
         */
        private String exportTime;

        /**
         * 总行数
         */
        private Integer totalRows;

        /**
         * 总列数
         */
        private Integer totalColumns;

        /**
         * 是否包含合并单元格
         */
        private Boolean hasMergedCells;
    }

    /**
     * 表头宽度配置
     */
    @Data
    public static class HeaderWidthConfig {
        /**
         * 列宽数组（像素）
         */
        private List<Integer> columnWidths;

        /**
         * 表头行高数组（像素）
         */
        private List<Integer> headerHeights;
    }
}
