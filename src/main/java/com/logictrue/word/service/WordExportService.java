package com.logictrue.word.service;

import com.logictrue.word.dto.JsonTableExportRequest;
import com.logictrue.word.dto.TableExportRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.officeDocument.x2006.math.CTOMath;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.util.List;
import java.util.Map;

/**
 * Word导出服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WordExportService {

    // 像素到磅的转换比例（1磅 = 1.33像素）
    private static final double PIXEL_TO_POINT = 0.75;
    // 磅到Twips的转换比例（1磅 = 20 Twips）
    private static final int POINT_TO_TWIPS = 20;

    /**
     * 导出表格到Word文档
     */
    public byte[] exportTableToWord(TableExportRequest request) throws IOException {
        log.info("开始导出Word文档，表格标题: {}", request.getTitle());

        try (XWPFDocument document = new XWPFDocument();
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {

            // 设置页面方向
            setPageOrientation(document, request.getPageOrientation());

            // 添加标题
            if (request.getTitle() != null && !request.getTitle().trim().isEmpty()) {
                XWPFParagraph titleParagraph = document.createParagraph();
                titleParagraph.setAlignment(ParagraphAlignment.CENTER);
                XWPFRun titleRun = titleParagraph.createRun();
                titleRun.setText(request.getTitle());
                titleRun.setBold(true);
                titleRun.setFontSize(16);
                titleRun.setFontFamily("宋体");
            }

            // 创建表格
            createTable(document, request);

            document.write(out);
            byte[] result = out.toByteArray();

            log.info("Word文档导出完成，文件大小: {} bytes", result.length);
            return result;

        } catch (Exception e) {
            log.error("导出Word文档失败", e);
            throw new IOException("导出Word文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建表格
     */
    private void createTable(XWPFDocument document, TableExportRequest request) {
        TableExportRequest.TableData tableData = request.getTableData();
        if (tableData == null) {
            log.warn("表格数据为空，跳过创建表格");
            return;
        }

        // 调试：打印接收到的表头数据
        if (tableData.getHeaders() != null) {
            log.info("接收到表头数据，共{}行", tableData.getHeaders().size());
            for (int i = 0; i < tableData.getHeaders().size(); i++) {
                List<TableExportRequest.HeaderCell> row = tableData.getHeaders().get(i);
                log.info("第{}行表头，共{}列", i + 1, row.size());
                for (int j = 0; j < row.size(); j++) {
                    TableExportRequest.HeaderCell cell = row.get(j);
                    if (cell.getContent() != null && !cell.getContent().trim().isEmpty()) {
                        log.info("  列{}: content='{}', rowspan={}, colspan={}",
                                j, cell.getContent(), cell.getRowspan(), cell.getColspan());
                    }
                }
            }
        }

        // 计算总行数
        int headerRows = tableData.getHeaders() != null ? tableData.getHeaders().size() : 0;
        int dataRows = tableData.getDataRows() != null ? tableData.getDataRows().size() : 0;
        int totalRows = headerRows + dataRows;

        if (totalRows == 0) {
            log.warn("表格行数为0，跳过创建表格");
            return;
        }

        // 计算列数 - 使用固定的8列（根据前端表格结构）
        int cols = 8;

        log.info("创建表格: {}行 x {}列", totalRows, cols);

        // 创建表格
        XWPFTable table = document.createTable(totalRows, cols);

        // 设置表格宽度和列宽
        setTableWidthAndColumns(table, tableData, request.getTableWidth());

        // 设置表格样式
        table.getCTTbl().getTblPr().unsetTblBorders();

        int currentRow = 0;

        // 处理表头 - 支持单元格合并
        if (headerRows > 0) {
            currentRow = processHeadersWithMerge(table, tableData.getHeaders());
        }

        // 处理数据行
        if (dataRows > 0) {
            for (List<TableExportRequest.DataCell> dataRow : tableData.getDataRows()) {
                if (currentRow < table.getRows().size()) {
                    XWPFTableRow row = table.getRow(currentRow);
                    processDataRow(row, dataRow);
                } else {
                    // 如果行数不够，创建新行
                    XWPFTableRow newRow = table.createRow();
                    processDataRow(newRow, dataRow);
                }
                currentRow++;
            }
        }

        // 设置表格边框
        setTableBorders(table);

        log.info("表格创建完成");
    }

    /**
     * 处理支持单元格合并的表头
     */
    private int processHeadersWithMerge(XWPFTable table, List<List<TableExportRequest.HeaderCell>> headers) {
        if (headers == null || headers.isEmpty()) {
            return 0;
        }

        log.info("开始处理表头，共{}行", headers.size());

        int processedRows = 0;

        for (int rowIndex = 0; rowIndex < headers.size(); rowIndex++) {
            List<TableExportRequest.HeaderCell> headerRow = headers.get(rowIndex);
            log.info("处理第{}行表头，共{}列", rowIndex + 1, headerRow.size());

            if (rowIndex < table.getRows().size()) {
                XWPFTableRow row = table.getRow(rowIndex);
                processHeaderRowWithMerge(row, headerRow, rowIndex);
                processedRows++;
            }
        }

        log.info("表头处理完成，共处理{}行", processedRows);
        return processedRows;
    }

    /**
     * 处理单行表头，支持单元格合并
     */
    private void processHeaderRowWithMerge(XWPFTableRow row, List<TableExportRequest.HeaderCell> headerCells, int rowIndex) {
        for (int i = 0; i < headerCells.size() && i < row.getTableCells().size(); i++) {
            TableExportRequest.HeaderCell headerCell = headerCells.get(i);
            XWPFTableCell cell = row.getCell(i);

            // 处理有内容的单元格
            if (headerCell.getContent() != null && !headerCell.getContent().trim().isEmpty()) {
                // 添加调试日志
                log.debug("处理表头单元格: content='{}', isVertical={}", headerCell.getContent(), headerCell.getIsVertical());

                // 设置单元格内容（支持纵向显示）
                setHeaderCellContent(cell, headerCell.getContent(), headerCell.getIsVertical());

                // 设置表头单元格尺寸（包含宽度，用于合并单元格）
                setHeaderCellSize(cell, headerCell.getWidth(), headerCell.getHeight());

                // 处理列合并 (colspan)
                if (headerCell.getColspan() != null && headerCell.getColspan() > 1) {
                    mergeHorizontalCells(row, i, headerCell.getColspan());
                    log.debug("合并列: 起始列={}, 合并数={}", i, headerCell.getColspan());
                }

                // 处理行合并 (rowspan) - 需要在表格创建完成后处理
                if (headerCell.getRowspan() != null && headerCell.getRowspan() > 1) {
                    // 标记需要行合并的单元格，稍后处理
                    CTTcPr tcPr = cell.getCTTc().getTcPr();
                    if (tcPr == null) {
                        tcPr = cell.getCTTc().addNewTcPr();
                    }
                    tcPr.addNewVMerge().setVal(STMerge.RESTART);
                    markVerticalMergeCells(row.getTable(), rowIndex, i, headerCell.getRowspan());
                    log.debug("标记行合并: 行={}, 列={}, 合并数={}", rowIndex, i, headerCell.getRowspan());
                }
            } else {
                // 处理空的占位符单元格 - 清空内容但保持单元格结构
                cell.removeParagraph(0);
                cell.addParagraph();
                log.debug("处理占位符单元格: 行={}, 列={}", rowIndex, i);
            }
        }
    }

    /**
     * 合并水平单元格
     */
    private void mergeHorizontalCells(XWPFTableRow row, int startCol, int colspan) {
        try {
            for (int i = 0; i < colspan; i++) {
                if (startCol + i < row.getTableCells().size()) {
                    XWPFTableCell cell = row.getCell(startCol + i);
                    CTTcPr tcPr = cell.getCTTc().getTcPr();
                    if (tcPr == null) {
                        tcPr = cell.getCTTc().addNewTcPr();
                    }

                    CTHMerge hMerge = tcPr.getHMerge();
                    if (hMerge == null) {
                        hMerge = tcPr.addNewHMerge();
                    }

                    if (i == 0) {
                        hMerge.setVal(STMerge.RESTART);
                    } else {
                        hMerge.setVal(STMerge.CONTINUE);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("水平合并单元格失败: {}", e.getMessage());
        }
    }

    /**
     * 标记垂直合并单元格
     */
    private void markVerticalMergeCells(XWPFTable table, int startRow, int col, int rowspan) {
        try {
            for (int i = 1; i < rowspan; i++) {
                int targetRow = startRow + i;
                if (targetRow < table.getRows().size()) {
                    XWPFTableRow row = table.getRow(targetRow);
                    if (col < row.getTableCells().size()) {
                        XWPFTableCell cell = row.getCell(col);
                        CTTcPr tcPr = cell.getCTTc().getTcPr();
                        if (tcPr == null) {
                            tcPr = cell.getCTTc().addNewTcPr();
                        }
                        tcPr.addNewVMerge().setVal(STMerge.CONTINUE);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("垂直合并单元格失败: {}", e.getMessage());
        }
    }

    /**
     * 设置表头单元格内容
     */
    private void setHeaderCellContent(XWPFTableCell cell, String content) {
        setHeaderCellContent(cell, content, null);
    }

    /**
     * 设置表头单元格内容（支持纵向显示）
     */
    private void setHeaderCellContent(XWPFTableCell cell, String content, Boolean isVertical) {
        // 清除现有内容
        cell.removeParagraph(0);

        // 创建新段落
        XWPFParagraph paragraph = cell.addParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);

        // 处理纵向显示
        if (Boolean.TRUE.equals(isVertical)) {
            // 纵向显示：每个字符单独一行
            String cleanContent = cleanHtmlTags(content);
            char[] chars = cleanContent.toCharArray();

            for (int i = 0; i < chars.length; i++) {
                XWPFRun run = paragraph.createRun();
                run.setText(String.valueOf(chars[i]));
                run.setBold(true);
                run.setFontFamily("宋体");
                run.setFontSize(12);

                // 除了最后一个字符，都添加换行
                if (i < chars.length - 1) {
                    run.addBreak();
                }
            }

            log.info("设置纵向文字: '{}' -> '{}' ({}个字符，每字符一行)", content, cleanContent, chars.length);
        } else {
            // 横向显示：处理<br>标签为换行
            processTextWithBreaks(paragraph, content);
            log.debug("设置横向文字: '{}'", content);
        }

        // 设置单元格样式
        cell.setColor("F2F2F2"); // 浅灰色背景
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
    }

    /**
     * 处理包含<br>标签的文本，转换为Word换行
     */
    private void processTextWithBreaks(XWPFParagraph paragraph, String content) {
        if (content == null || content.trim().isEmpty()) {
            return;
        }

        // 处理各种<br>标签格式
        String[] parts = content.split("(?i)<br\\s*/?>");

        for (int i = 0; i < parts.length; i++) {
            String part = parts[i].trim();

            if (!part.isEmpty()) {
                XWPFRun run = paragraph.createRun();
                run.setText(part);
                run.setBold(true);
                run.setFontFamily("宋体");
                run.setFontSize(12);
            }

            // 除了最后一部分，都添加换行
            if (i < parts.length - 1) {
                XWPFRun breakRun = paragraph.createRun();
                breakRun.addBreak();
            }
        }

        log.debug("处理<br>标签: '{}' -> {}个文本段", content, parts.length);
    }

    /**
     * 清理HTML标签（用于纵向显示）
     */
    private String cleanHtmlTags(String text) {
        if (text == null) {
            return null;
        }

        // 移除所有HTML标签
        String cleaned = text.replaceAll("(?i)<br\\s*/?>", "")
                            .replaceAll("<[^>]+>", "")
                            .trim();

        log.debug("清理HTML标签: '{}' -> '{}'", text, cleaned);
        return cleaned;
    }

    /**
     * 将文字转换为纵向显示格式
     */
    private String convertToVerticalText(String text) {
        if (text == null || text.trim().isEmpty()) {
            log.debug("文字为空，返回原文字: '{}'", text);
            return text;
        }

        // 将每个字符用换行符分隔
        StringBuilder verticalText = new StringBuilder();
        char[] chars = text.toCharArray();

        log.debug("开始转换纵向文字，原文字: '{}', 字符数: {}", text, chars.length);

        for (int i = 0; i < chars.length; i++) {
            verticalText.append(chars[i]);
            if (i < chars.length - 1) {
                verticalText.append("\n");
            }
        }

        String result = verticalText.toString();
        log.debug("纵向文字转换完成: '{}' -> '{}'", text, result.replace("\n", "\\n"));
        return result;
    }

    /**
     * 处理表头行（保留原方法以兼容）
     */
    private void processHeaderRow(XWPFTableRow row, List<TableExportRequest.HeaderCell> headerCells) {
        for (int i = 0; i < headerCells.size() && i < row.getTableCells().size(); i++) {
            TableExportRequest.HeaderCell headerCell = headerCells.get(i);
            XWPFTableCell cell = row.getCell(i);

            // 添加调试日志
            String content = headerCell.getContent() != null ? headerCell.getContent() : "";
            log.debug("处理旧版表头单元格: content='{}', isVertical={}", content, headerCell.getIsVertical());

            setHeaderCellContent(cell, content, headerCell.getIsVertical());

            // 设置表头单元格尺寸（包含宽度，用于合并单元格）
            setHeaderCellSize(cell, headerCell.getWidth(), headerCell.getHeight());

            // 处理合并单元格
            if (headerCell.getColspan() != null && headerCell.getColspan() > 1) {
                log.debug("表头单元格列合并: {}", headerCell.getColspan());
            }

            if (headerCell.getRowspan() != null && headerCell.getRowspan() > 1) {
                log.debug("表头单元格行合并: {}", headerCell.getRowspan());
            }
        }
    }

    /**
     * 处理数据行
     */
    private void processDataRow(XWPFTableRow row, List<TableExportRequest.DataCell> dataCells) {
        for (int i = 0; i < dataCells.size() && i < row.getTableCells().size(); i++) {
            TableExportRequest.DataCell dataCell = dataCells.get(i);
            XWPFTableCell cell = row.getCell(i);

            // 设置单元格内容
            cell.removeParagraph(0); // 移除默认段落
            XWPFParagraph paragraph = cell.addParagraph();
            paragraph.setAlignment(ParagraphAlignment.CENTER);

            // 处理单元格内容
            log.debug("处理数据单元格 - hasMath: {}, hasMultipleContent: {}, content: {}",
                     dataCell.getHasMath(), dataCell.getHasMultipleContent(), dataCell.getContent());

            if (Boolean.TRUE.equals(dataCell.getHasMath())) {
                if (Boolean.TRUE.equals(dataCell.getHasMultipleContent()) && dataCell.getMathMLMap() != null) {
                    // 处理混合内容（公式+文本）
                    log.info("处理混合内容，mathMLMap大小: {}", dataCell.getMathMLMap().size());
                    insertMixedContent(paragraph, dataCell.getContent(), dataCell.getMathMLMap());
                } else if (dataCell.getMathML() != null) {
                    // 处理纯公式内容
                    log.info("处理纯公式内容");
                    insertMathFormula(paragraph, dataCell.getMathML());
                } else {
                    // 处理普通文本内容（兜底处理）
                    log.info("处理普通文本内容（兜底）");
                    String content = dataCell.getContent() != null ? dataCell.getContent() : "";
                    insertTextWithLineBreaks(paragraph, content);
                }
            } else {
                // 处理普通文本内容
                log.info("处理普通文本内容");
                String content = dataCell.getContent() != null ? dataCell.getContent() : "";
                insertTextWithLineBreaks(paragraph, content);
            }

            // 设置单元格样式
            cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);

            // 设置单元格尺寸
            setCellSize(cell, dataCell.getWidth(), dataCell.getHeight());
        }
    }

    /**
     * 设置表格宽度和列宽（使用headerWidthConfig配置）
     */
    private void setTableWidthAndColumnsWithConfig(XWPFTable table, TableExportRequest request) {
        try {
            TableExportRequest.HeaderWidthConfig headerWidthConfig = request.getHeaderWidthConfig();
            int[] columnWidths = null;
            boolean hasWidthInfo = false;

            // 优先使用headerWidthConfig中的列宽配置
            if (headerWidthConfig != null && headerWidthConfig.getColumnWidths() != null && !headerWidthConfig.getColumnWidths().isEmpty()) {
                List<Integer> configWidths = headerWidthConfig.getColumnWidths();
                columnWidths = new int[configWidths.size()];
                for (int i = 0; i < configWidths.size(); i++) {
                    columnWidths[i] = configWidths.get(i);
                }
                hasWidthInfo = true;
                log.info("使用headerWidthConfig中的列宽: {}", java.util.Arrays.toString(columnWidths));
            }

            // 如果没有headerWidthConfig，尝试从数据行获取
            if (!hasWidthInfo && request.getTableData() != null && request.getTableData().getDataRows() != null && !request.getTableData().getDataRows().isEmpty()) {
                List<TableExportRequest.DataCell> firstDataRow = request.getTableData().getDataRows().get(0);
                columnWidths = new int[firstDataRow.size()];
                for (int i = 0; i < firstDataRow.size(); i++) {
                    TableExportRequest.DataCell dataCell = firstDataRow.get(i);
                    if (dataCell.getWidth() != null && dataCell.getWidth() > 0) {
                        columnWidths[i] = dataCell.getWidth();
                        hasWidthInfo = true;
                    }
                }
                if (hasWidthInfo) {
                    log.info("从数据行获取列宽: {}", java.util.Arrays.toString(columnWidths));
                }
            }

            // 如果还是没有宽度信息，使用默认宽度
            if (!hasWidthInfo) {
                columnWidths = new int[]{150, 200, 150, 80, 80, 120, 120, 120};
                hasWidthInfo = true;
                log.info("使用默认列宽: {}", java.util.Arrays.toString(columnWidths));
            }

            if (hasWidthInfo && columnWidths != null) {
                // 计算总宽度
                int totalWidth = 0;
                for (int width : columnWidths) {
                    totalWidth += width;
                }

                // 设置表格总宽度
                if (totalWidth > 0) {
                    int totalWidthTwips = (int) (totalWidth * 15); // 像素转Twips
                    table.setWidth(String.valueOf(totalWidthTwips));
                    log.info("设置表格总宽度: {}px ({}twips)", totalWidth, totalWidthTwips);
                } else {
                    table.setWidth("100%");
                }

                // 设置所有行的列宽（包括表头行和数据行）
                setAllRowsColumnWidth(table, columnWidths);

                log.info("表格宽度和列宽设置完成: {}", java.util.Arrays.toString(columnWidths));
            } else {
                // 没有宽度信息，使用默认设置
                table.setWidth("100%");
                log.info("未找到列宽信息，使用默认宽度设置");
            }

        } catch (Exception e) {
            log.warn("设置表格宽度失败: {}", e.getMessage(), e);
            table.setWidth("100%");
        }
    }

    /**
     * 设置表格宽度和列宽（原有方法，保持兼容性）
     */
    private void setTableWidthAndColumns(XWPFTable table, TableExportRequest.TableData tableData, Integer tableWidthPx) {
        try {
            // 收集所有列的宽度信息（逻辑列，不是物理列）
            int[] columnWidths = new int[8]; // 固定8列
            boolean hasWidthInfo = false;

            // 从数据行获取列宽信息（数据行是最准确的，因为它们是1:1对应的）
            if (tableData.getDataRows() != null && !tableData.getDataRows().isEmpty()) {
                List<TableExportRequest.DataCell> firstDataRow = tableData.getDataRows().get(0);
                for (int i = 0; i < Math.min(firstDataRow.size(), columnWidths.length); i++) {
                    TableExportRequest.DataCell dataCell = firstDataRow.get(i);
                    if (dataCell.getWidth() != null && dataCell.getWidth() > 0) {
                        columnWidths[i] = dataCell.getWidth();
                        hasWidthInfo = true;
                    }
                }
                log.info("从数据行获取列宽: {}", java.util.Arrays.toString(columnWidths));
            }

            // 如果数据行没有宽度信息，尝试从表头获取
            if (!hasWidthInfo && tableData.getHeaders() != null && !tableData.getHeaders().isEmpty()) {
                // 使用默认宽度
                columnWidths = new int[]{150, 200, 150, 80, 80, 120, 120, 120};
                hasWidthInfo = true;
                log.info("使用默认列宽: {}", java.util.Arrays.toString(columnWidths));
            }

            if (hasWidthInfo) {
                // 计算总宽度
                int totalWidth = 0;
                for (int width : columnWidths) {
                    totalWidth += width;
                }

                // 设置表格总宽度
                if (totalWidth > 0) {
                    int totalWidthTwips = (int) (totalWidth * 15); // 像素转Twips
                    table.setWidth(String.valueOf(totalWidthTwips));
                    log.info("设置表格总宽度: {}px ({}twips)", totalWidth, totalWidthTwips);
                } else {
                    table.setWidth("100%");
                }

                // 只为数据行设置列宽（表头的合并单元格会自动处理）
                setDataRowsColumnWidth(table, columnWidths);

                log.info("设置列宽完成: {}", java.util.Arrays.toString(columnWidths));
            } else {
                // 没有宽度信息，使用默认设置
                table.setWidth("100%");
                log.info("未找到列宽信息，使用默认宽度设置");
            }

        } catch (Exception e) {
            log.warn("设置表格宽度失败: {}", e.getMessage(), e);
            table.setWidth("100%");
        }
    }

    /**
     * 为所有行设置列宽（包括表头行和数据行）
     */
    private void setAllRowsColumnWidth(XWPFTable table, int[] columnWidths) {
        try {
            log.info("开始为所有行设置列宽，列数: {}", columnWidths.length);

            for (int rowIndex = 0; rowIndex < table.getRows().size(); rowIndex++) {
                XWPFTableRow row = table.getRow(rowIndex);
                List<XWPFTableCell> cells = row.getTableCells();

                // 为每个单元格设置宽度
                for (int colIndex = 0; colIndex < Math.min(cells.size(), columnWidths.length); colIndex++) {
                    if (columnWidths[colIndex] > 0) {
                        XWPFTableCell cell = cells.get(colIndex);
                        setCellWidth(cell, columnWidths[colIndex]);
                        log.debug("设置行[{}]列[{}]宽度: {}px", rowIndex, colIndex, columnWidths[colIndex]);
                    }
                }
            }

            log.info("所有行列宽设置完成");
        } catch (Exception e) {
            log.warn("设置所有行列宽失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 为数据行设置列宽（保持原有方法兼容性）
     */
    private void setDataRowsColumnWidth(XWPFTable table, int[] columnWidths) {
        try {
            // 找到数据行的起始位置（跳过表头行）
            int headerRowCount = 2; // 表头有2行

            for (int i = headerRowCount; i < table.getRows().size(); i++) {
                XWPFTableRow row = table.getRow(i);
                List<XWPFTableCell> cells = row.getTableCells();

                // 为每个数据单元格设置宽度
                for (int j = 0; j < Math.min(cells.size(), columnWidths.length); j++) {
                    if (columnWidths[j] > 0) {
                        XWPFTableCell cell = cells.get(j);
                        int widthTwips = (int) (columnWidths[j] * 15);
                        cell.setWidth(String.valueOf(widthTwips));
                        log.debug("设置数据行[{}]列[{}]宽度: {}px ({}twips)", i, j, columnWidths[j], widthTwips);
                    }
                }
            }

            log.info("数据行列宽设置完成");
        } catch (Exception e) {
            log.warn("设置数据行列宽失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 设置单元格尺寸
     */
    private void setCellSize(XWPFTableCell cell, Integer widthPx, Integer heightPx) {
        // 只设置高度，不设置宽度（宽度由统一的列宽设置处理）
        if (heightPx != null && heightPx > 0) {
            // 设置行高（POI中行高设置相对复杂）
            try {
                XWPFTableRow row = cell.getTableRow();
                CTTrPr trPr = row.getCtRow().getTrPr();
                if (trPr == null) {
                    trPr = row.getCtRow().addNewTrPr();
                }

                // 设置行高
                CTHeight rowHeight = null;
                if (trPr.getTrHeightList().isEmpty()) {
                    rowHeight = trPr.addNewTrHeight();
                } else {
                    rowHeight = trPr.getTrHeightList().get(0);
                }

                // 将像素转换为Twips
                int heightTwips = (int) (heightPx * 15);
                rowHeight.setVal(BigInteger.valueOf(heightTwips));
                rowHeight.setHRule(STHeightRule.AT_LEAST);
                log.debug("设置单元格高度: {}px ({}twips)", heightPx, heightTwips);
            } catch (Exception e) {
                log.warn("设置单元格高度失败: {}", e.getMessage());
            }
        }
    }

    /**
     * 设置表头单元格尺寸（包含宽度设置，用于合并单元格）
     */
    private void setHeaderCellSize(XWPFTableCell cell, Integer widthPx, Integer heightPx) {
        if (widthPx != null && widthPx > 0) {
            // 将像素转换为Twips (1像素 ≈ 15 Twips)
            int widthTwips = (int) (widthPx * 15);

            // 使用与SimpleWordService相同的可靠方式设置单元格宽度
            try {
                CTTcPr tcPr = cell.getCTTc().getTcPr();
                if (tcPr == null) {
                    tcPr = cell.getCTTc().addNewTcPr();
                }

                CTTblWidth cellWidth = tcPr.getTcW();
                if (cellWidth == null) {
                    cellWidth = tcPr.addNewTcW();
                }

                cellWidth.setType(STTblWidth.DXA);
                cellWidth.setW(BigInteger.valueOf(widthTwips));

                log.debug("设置表头单元格宽度: {}px ({}twips)", widthPx, widthTwips);
            } catch (Exception e) {
                log.warn("设置表头单元格宽度失败: {}", e.getMessage());
                // 备选方案：使用简单的setWidth方法
                cell.setWidth(String.valueOf(widthTwips));
            }
        }

        if (heightPx != null && heightPx > 0) {
            // 设置行高
            try {
                XWPFTableRow row = cell.getTableRow();
                CTTrPr trPr = row.getCtRow().getTrPr();
                if (trPr == null) {
                    trPr = row.getCtRow().addNewTrPr();
                }

                // 设置行高
                CTHeight rowHeight = null;
                if (trPr.getTrHeightList().isEmpty()) {
                    rowHeight = trPr.addNewTrHeight();
                } else {
                    rowHeight = trPr.getTrHeightList().get(0);
                }

                // 将像素转换为Twips
                int heightTwips = (int) (heightPx * 15);
                rowHeight.setVal(BigInteger.valueOf(heightTwips));
                rowHeight.setHRule(STHeightRule.AT_LEAST);
                log.debug("设置表头单元格高度: {}px ({}twips)", heightPx, heightTwips);
            } catch (Exception e) {
                log.warn("设置表头单元格高度失败: {}", e.getMessage());
            }
        }
    }

    /**
     * 设置表格边框
     *//*
    private void setTableBorders(XWPFTable table) {
        // 设置表格边框样式
        table.setInsideHBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, "000000");
        table.setInsideVBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, "000000");
        table.setTopBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, "000000");
        table.setBottomBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, "000000");
        table.setLeftBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, "000000");
        table.setRightBorder(XWPFTable.XWPFBorderType.SINGLE, 4, 0, "000000");
    }*/

    /**
     * 插入数学公式到段落
     */
    private void insertMathFormula(XWPFParagraph paragraph, String mathML) {
        try {
            log.info("插入数学公式，MathML: {}", mathML);

            // 将MathML转换为OMML
            CTOMath ctOMath = MathMLToOMMLService.convertOMML(mathML);

            CTP ctp = paragraph.getCTP();
            ctp.setOMathArray(new CTOMath[]{ctOMath});

        } catch (Exception e) {
            log.error("插入数学公式失败: {}", e.getMessage(), e);
            // 插入错误提示文本
            XWPFRun run = paragraph.createRun();
            run.setText("[公式错误]");
            run.setFontFamily("宋体");
            run.setFontSize(11);
        }
    }

    /**
     * 插入混合内容（公式+文本）到段落
     */
    private void insertMixedContent(XWPFParagraph paragraph, String content, Map<String, String> mathMLMap) {
        try {
            log.info("插入混合内容，内容: {}, 公式映射: {}", content, mathMLMap);

            if (content == null || content.isEmpty()) {
                return;
            }

            // 创建一个列表来存储内容片段和它们的类型
            java.util.List<ContentPart> parts = new java.util.ArrayList<>();

            // 使用正则表达式找到所有占位符
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("(__MATH_FORMULA_\\d+__)");
            java.util.regex.Matcher matcher = pattern.matcher(content);

            int lastEnd = 0;

            while (matcher.find()) {
                // 添加占位符前的文本
                if (matcher.start() > lastEnd) {
                    String textPart = content.substring(lastEnd, matcher.start());
                    if (!textPart.isEmpty()) {
                        parts.add(new ContentPart(textPart, false));
                    }
                }

                // 添加占位符（公式）
                String placeholder = matcher.group(1);
                parts.add(new ContentPart(placeholder, true));

                lastEnd = matcher.end();
            }

            // 添加最后一个占位符后的文本
            if (lastEnd < content.length()) {
                String remainingText = content.substring(lastEnd);
                if (!remainingText.isEmpty()) {
                    parts.add(new ContentPart(remainingText, false));
                }
            }

            // 如果没有找到占位符，整个内容都是文本
            if (parts.isEmpty()) {
                parts.add(new ContentPart(content, false));
            }

            // 按顺序插入所有部分
            for (ContentPart part : parts) {
                if (part.isFormula) {
                    // 这是公式占位符
                    String mathML = mathMLMap.get(part.content);
                    if (mathML != null && !mathML.isEmpty()) {
                        try {
                            // 创建一个新的段落来插入公式
                            insertMathFormulaInline(paragraph, mathML);
                            log.debug("成功插入公式: {}", part.content);
                        } catch (Exception e) {
                            log.error("插入公式失败: {}, 错误: {}", part.content, e.getMessage());
                            XWPFRun errorRun = paragraph.createRun();
                            errorRun.setText("[公式错误:" + part.content + "]");
                            errorRun.setFontFamily("宋体");
                            errorRun.setFontSize(11);
                        }
                    } else {
                        log.warn("未找到占位符对应的MathML: {}", part.content);
                        XWPFRun placeholderRun = paragraph.createRun();
                        placeholderRun.setText("[缺失公式:" + part.content + "]");
                        placeholderRun.setFontFamily("宋体");
                        placeholderRun.setFontSize(11);
                    }
                } else {
                    // 这是普通文本
                    insertTextPartWithLineBreaks(paragraph, part.content);
                    log.debug("插入文本: {}", part.content);
                }
            }

        } catch (Exception e) {
            log.error("插入混合内容失败: {}", e.getMessage(), e);
            // 降级处理：插入原始内容
            String fallbackContent = content != null ? content : "[混合内容错误]";
            insertTextWithLineBreaks(paragraph, fallbackContent);
        }
    }

    /**
     * 内联插入数学公式（用于混合内容）
     */
    private void insertMathFormulaInline(XWPFParagraph paragraph, String mathML) throws Exception {
        // 将MathML转换为OMML
        CTOMath ctOMath = MathMLToOMMLService.convertOMML(mathML);

        paragraph.getCTP().addNewOMath().set(ctOMath);
    }

    /**
     * 内容片段类，用于区分文本和公式
     */
    private static class ContentPart {
        String content;
        boolean isFormula;

        ContentPart(String content, boolean isFormula) {
            this.content = content;
            this.isFormula = isFormula;
        }
    }


    /**
     * 设置文档纸张方向
     */
    private void setPageOrientation(XWPFDocument document, String orientation) {
        try {
            CTDocument1 doc = document.getDocument();
            CTBody body = doc.getBody();

            if (!body.isSetSectPr()) {
                body.addNewSectPr();
            }
            CTSectPr section = body.getSectPr();

            if (!section.isSetPgSz()) {
                section.addNewPgSz();
            }

            CTPageSz pageSize = section.getPgSz();

            // 默认为横向，除非明确指定为纵向
            boolean isLandscape = !"PORTRAIT".equalsIgnoreCase(orientation);

            if (isLandscape) {
                // 横向 (A4纸张: 宽297mm, 高210mm)
                // 1英寸 = 1440 twips, 1mm = 56.7 twips
                pageSize.setW(BigInteger.valueOf(16838)); // 297mm * 56.7 ≈ 16838 twips
                pageSize.setH(BigInteger.valueOf(11906)); // 210mm * 56.7 ≈ 11906 twips
                pageSize.setOrient(STPageOrientation.LANDSCAPE);
                log.info("已设置文档为横向纸张");
            } else {
                // 纵向 (A4纸张: 宽210mm, 高297mm)
                pageSize.setW(BigInteger.valueOf(11906)); // 210mm * 56.7 ≈ 11906 twips
                pageSize.setH(BigInteger.valueOf(16838)); // 297mm * 56.7 ≈ 16838 twips
                pageSize.setOrient(STPageOrientation.PORTRAIT);
                log.info("已设置文档为纵向纸张");
            }
        } catch (Exception e) {
            log.warn("设置纸张方向失败: {}", e.getMessage());
        }
    }

    /**
     * 插入包含换行符的文本到段落中
     * @param paragraph Word段落对象
     * @param text 包含换行符的文本
     */
    private void insertTextWithLineBreaks(XWPFParagraph paragraph, String text) {
        if (text == null || text.isEmpty()) {
            return;
        }

        // 按换行符分割文本
        String[] lines = text.split("\n");

        for (int i = 0; i < lines.length; i++) {
            XWPFRun run = paragraph.createRun();
            run.setText(lines[i]);
            run.setFontFamily("宋体");
            run.setFontSize(11);

            // 如果不是最后一行，添加换行符
            if (i < lines.length - 1) {
                run.addBreak();
            }
        }

        log.debug("插入包含换行符的文本，行数: {}", lines.length);
    }

    /**
     * 在混合内容中插入包含换行符的文本片段
     * @param paragraph Word段落对象
     * @param text 包含换行符的文本片段
     */
    private void insertTextPartWithLineBreaks(XWPFParagraph paragraph, String text) {
        if (text == null || text.isEmpty()) {
            return;
        }

        // 按换行符分割文本
        String[] lines = text.split("\n");

        for (int i = 0; i < lines.length; i++) {
            XWPFRun run = paragraph.createRun();
            run.setText(lines[i]);
            run.setFontFamily("宋体");
            run.setFontSize(11);

            // 添加换行符
            run.addBreak();
        }
    }

    /**
     * 导出包含合并单元格的表格到Word文档
     */
    public byte[] exportTableToWordWithJson(TableExportRequest request) throws IOException {
        log.info("开始导出Word文档，表格标题: {}", request.getTitle());
        log.info("合并单元格数量: {}", request.getMerges() != null ? request.getMerges().size() : 0);

        try (XWPFDocument document = new XWPFDocument();
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {

            // 设置页面方向
            setPageOrientation(document, request.getPageOrientation());

            // 添加标题
            if (request.getTitle() != null && !request.getTitle().trim().isEmpty()) {
                addTitle(document, request.getTitle());
            }

            // 创建表格
            if (request.getTableData() != null) {
                createTableWithMergesOptimized(document, request);
            }

            document.write(out);
            byte[] result = out.toByteArray();

            log.info("包含合并单元格的Word文档导出完成，文件大小: {} bytes", result.length);
            return result;
        }
    }

    /**
     * 添加标题（参考SimpleWordService实现）
     */
    private void addTitle(XWPFDocument document, String title) {
        XWPFParagraph titleParagraph = document.createParagraph();
        titleParagraph.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun titleRun = titleParagraph.createRun();
        titleRun.setText(title);
        titleRun.setBold(true);
        titleRun.setFontSize(16);
        titleRun.setFontFamily("宋体");
        titleParagraph.setSpacingAfter(300);
    }

    /**
     * 创建包含合并单元格的表格（优化版本，参考SimpleWordService）
     */
    private void createTableWithMergesOptimized(XWPFDocument document, TableExportRequest request) {
        TableExportRequest.TableData tableData = request.getTableData();
        List<TableExportRequest.MergeCell> dataMerges = request.getMerges(); // 数据行合并
        List<TableExportRequest.MergeCell> headerMerges = request.getHeaderMerges(); // 表头合并

        // 计算表格的总行数和列数
        int totalRows = 0;
        int totalCols = 0;

        if (tableData.getHeaders() != null && !tableData.getHeaders().isEmpty()) {
            totalRows += tableData.getHeaders().size();
            totalCols = Math.max(totalCols, tableData.getHeaders().get(0).size());
        }

        if (tableData.getDataRows() != null && !tableData.getDataRows().isEmpty()) {
            totalRows += tableData.getDataRows().size();
            totalCols = Math.max(totalCols, tableData.getDataRows().get(0).size());
        }

        log.info("创建表格，总行数: {}, 总列数: {}", totalRows, totalCols);

        // 创建表格
        XWPFTable table = document.createTable(totalRows, totalCols);

        // 设置表格样式（参考SimpleWordService）
        setTableStyleOptimized(table, request);

        int currentRowIndex = 0;

        // 处理表头
        if (tableData.getHeaders() != null && !tableData.getHeaders().isEmpty()) {
            for (List<TableExportRequest.HeaderCell> headerRow : tableData.getHeaders()) {
                XWPFTableRow row = table.getRow(currentRowIndex);
                processHeaderRowOptimized(row, headerRow, request, currentRowIndex);
                currentRowIndex++;
            }
        }

        // 处理数据行
        if (tableData.getDataRows() != null && !tableData.getDataRows().isEmpty()) {
            for (List<TableExportRequest.DataCell> dataRow : tableData.getDataRows()) {
                XWPFTableRow row = table.getRow(currentRowIndex);
                processDataRowOptimized(row, dataRow, request, currentRowIndex);
                currentRowIndex++;
            }
        }

        // 应用合并（参考SimpleWordService的实现）
        applyMergesOptimized(table, headerMerges, dataMerges, tableData.getHeaders() != null ? tableData.getHeaders().size() : 0);

        // 设置边框
        setTableBorders(table);
    }

    /**
     * 创建包含合并单元格的表格
     */
    private void createTableWithMerges(XWPFDocument document, TableExportRequest request) {
        TableExportRequest.TableData tableData = request.getTableData();
        List<TableExportRequest.MergeCell> dataMerges = request.getMerges(); // 数据行合并
        List<TableExportRequest.MergeCell> headerMerges = request.getHeaderMerges(); // 表头合并

        // 计算表格的总行数和列数
        int totalRows = 0;
        int totalCols = 0;

        if (tableData.getHeaders() != null && !tableData.getHeaders().isEmpty()) {
            totalRows += tableData.getHeaders().size();
            totalCols = Math.max(totalCols, tableData.getHeaders().get(0).size());
        }

        if (tableData.getDataRows() != null && !tableData.getDataRows().isEmpty()) {
            totalRows += tableData.getDataRows().size();
            totalCols = Math.max(totalCols, tableData.getDataRows().get(0).size());
        }

        log.info("创建表格，总行数: {}, 总列数: {}", totalRows, totalCols);

        // 创建表格
        XWPFTable table = document.createTable(totalRows, totalCols);

        // 根据headerWidthConfig动态设置表格总宽度
        setTableTotalWidth(table, request.getHeaderWidthConfig());

        // 设置表格样式
        setTableStyle(table);

        int currentRowIndex = 0;

        // 处理表头
        if (tableData.getHeaders() != null && !tableData.getHeaders().isEmpty()) {
            for (List<TableExportRequest.HeaderCell> headerRow : tableData.getHeaders()) {
                XWPFTableRow row = table.getRow(currentRowIndex);
                processHeaderRowWithMerges(row, headerRow, headerMerges, currentRowIndex);
                currentRowIndex++;
            }
        }

        // 处理数据行
        if (tableData.getDataRows() != null && !tableData.getDataRows().isEmpty()) {
            for (List<TableExportRequest.DataCell> dataRow : tableData.getDataRows()) {
                XWPFTableRow row = table.getRow(currentRowIndex);
                processDataRowWithMerges(row, dataRow, dataMerges, currentRowIndex);
                currentRowIndex++;
            }
        }

        // 应用表头合并单元格（表头合并不需要偏移）
        if (headerMerges != null && !headerMerges.isEmpty()) {
            log.info("应用表头合并单元格，数量: {}", headerMerges.size());
            applyMerges(table, headerMerges, 0); // 表头合并不需要偏移
        }

        // 应用数据行合并单元格（需要考虑表头偏移）
        if (dataMerges != null && !dataMerges.isEmpty()) {
            int headerRowCount = (tableData.getHeaders() != null && !tableData.getHeaders().isEmpty()) ?
                                tableData.getHeaders().size() : 0;
            log.info("应用数据行合并单元格，数量: {}，表头偏移: {}", dataMerges.size(), headerRowCount);
            applyMerges(table, dataMerges, headerRowCount);
        }
    }

    /**
     * 设置表格样式（优化版本，参考SimpleWordService）
     */
    private void setTableStyleOptimized(XWPFTable table, TableExportRequest request) {
        try {
            // 获取列宽配置
            List<Integer> columnWidths = null;
            if (request.getHeaderWidthConfig() != null && request.getHeaderWidthConfig().getColumnWidths() != null) {
                columnWidths = request.getHeaderWidthConfig().getColumnWidths();
            }

            if (columnWidths != null && !columnWidths.isEmpty()) {
                // 计算总宽度
                int totalWidth = columnWidths.stream().mapToInt(Integer::intValue).sum();
                int totalWidthTwips = totalWidth * 15; // 像素转Twips

                // 设置表格宽度
                CTTblPr tblPr = table.getCTTbl().getTblPr();
                if (tblPr == null) {
                    tblPr = table.getCTTbl().addNewTblPr();
                }

                CTTblWidth tblWidth = tblPr.getTblW();
                if (tblWidth == null) {
                    tblWidth = tblPr.addNewTblW();
                }
                tblWidth.setType(STTblWidth.DXA);
                tblWidth.setW(BigInteger.valueOf(totalWidthTwips));

                // 设置表格居中
                CTJcTable jcTable = tblPr.getJc();
                if (jcTable == null) {
                    jcTable = tblPr.addNewJc();
                }
                jcTable.setVal(STJcTable.CENTER);

                log.info("设置表格总宽度: {}px ({}twips)", totalWidth, totalWidthTwips);
            }
        } catch (Exception e) {
            log.warn("设置表格样式失败: {}", e.getMessage());
        }
    }

    /**
     * 处理表头行（优化版本，参考SimpleWordService）
     */
    private void processHeaderRowOptimized(XWPFTableRow row, List<TableExportRequest.HeaderCell> headerCells,
                                          TableExportRequest request, int rowIndex) {
        for (int colIndex = 0; colIndex < headerCells.size(); colIndex++) {
            TableExportRequest.HeaderCell headerCell = headerCells.get(colIndex);
            XWPFTableCell cell = row.getCell(colIndex);

            if (cell == null) {
                cell = row.createCell();
            }

            // 设置单元格内容
            if (headerCell.getContent() != null && !headerCell.getContent().trim().isEmpty()) {
                setCellContentOptimized(cell, headerCell.getContent(), true, headerCell.getIsVertical());
            }

            // 设置列宽
            if (request.getHeaderWidthConfig() != null && request.getHeaderWidthConfig().getColumnWidths() != null) {
                List<Integer> columnWidths = request.getHeaderWidthConfig().getColumnWidths();
                if (colIndex < columnWidths.size() && columnWidths.get(colIndex) != null) {
                    setCellWidthOptimized(cell, columnWidths.get(colIndex));
                }
            }

            // 设置行高
            if (request.getHeaderWidthConfig() != null && request.getHeaderWidthConfig().getHeaderHeights() != null) {
                List<Integer> headerHeights = request.getHeaderWidthConfig().getHeaderHeights();
                if (rowIndex < headerHeights.size() && headerHeights.get(rowIndex) != null) {
                    setCellHeightOptimized(row, headerHeights.get(rowIndex));
                }
            }
        }
    }

    /**
     * 处理数据行（优化版本，参考SimpleWordService）
     */
    private void processDataRowOptimized(XWPFTableRow row, List<TableExportRequest.DataCell> dataCells,
                                        TableExportRequest request, int rowIndex) {
        for (int colIndex = 0; colIndex < dataCells.size(); colIndex++) {
            TableExportRequest.DataCell dataCell = dataCells.get(colIndex);
            XWPFTableCell cell = row.getCell(colIndex);

            if (cell == null) {
                cell = row.createCell();
            }

            // 设置单元格内容
            if (dataCell.getContent() != null && !dataCell.getContent().trim().isEmpty()) {
                setCellContentOptimized(cell, dataCell.getContent(), false, false);
            }

            // 设置列宽
            if (request.getHeaderWidthConfig() != null && request.getHeaderWidthConfig().getColumnWidths() != null) {
                List<Integer> columnWidths = request.getHeaderWidthConfig().getColumnWidths();
                if (colIndex < columnWidths.size() && columnWidths.get(colIndex) != null) {
                    setCellWidthOptimized(cell, columnWidths.get(colIndex));
                }
            }
        }
    }

    /**
     * 处理包含合并信息的表头行
     */
    private void processHeaderRowWithMerges(XWPFTableRow row,
                                           List<TableExportRequest.HeaderCell> headerCells,
                                           List<TableExportRequest.MergeCell> merges,
                                           int rowIndex) {
        // 注意：表头行的列宽已经在setAllRowsColumnWidth中统一设置了
        // 这里主要处理内容和高度，宽度只在有特殊合并需求时才单独设置
        for (int colIndex = 0; colIndex < headerCells.size(); colIndex++) {
            TableExportRequest.HeaderCell headerCell = headerCells.get(colIndex);
            XWPFTableCell cell = row.getCell(colIndex);

            if (cell == null) {
                cell = row.createCell();
            }

            // 设置单元格内容（表头通常不包含公式）
            setCellContent(cell, headerCell.getContent(), false, null, null);

            // 设置表头样式
            setHeaderCellStyle(cell);

            // 处理单元格尺寸
            TableExportRequest.MergeCell mergeInfo = findMergeInfo(merges, rowIndex, colIndex);

            // 对于宽度：只有合并单元格才需要特殊处理宽度（因为它可能跨多列）
            // 普通单元格的宽度已经在setAllRowsColumnWidth中统一设置了
            if (mergeInfo != null && mergeInfo.getWidth() != null && mergeInfo.getWidth() > 0) {
                setCellWidth(cell, mergeInfo.getWidth());
                log.debug("设置表头合并单元格[{},{}]宽度: {}px (来源: 合并信息)",
                         rowIndex, colIndex, mergeInfo.getWidth());
            }

            // 对于高度：优先使用合并信息，其次使用单元格信息
            Integer cellHeight = (mergeInfo != null && mergeInfo.getHeight() != null) ?
                                mergeInfo.getHeight() : headerCell.getHeight();

            if (cellHeight != null && cellHeight > 0) {
                setCellHeight(cell, cellHeight);
                log.debug("设置表头单元格[{},{}]高度: {}px (来源: {})",
                         rowIndex, colIndex, cellHeight,
                         mergeInfo != null ? "合并信息" : "单元格信息");
            }
        }
    }

    /**
     * 处理包含合并信息的数据行
     */
    private void processDataRowWithMerges(XWPFTableRow row,
                                         List<TableExportRequest.DataCell> dataCells,
                                         List<TableExportRequest.MergeCell> merges,
                                         int rowIndex) {
        for (int colIndex = 0; colIndex < dataCells.size(); colIndex++) {
            TableExportRequest.DataCell dataCell = dataCells.get(colIndex);
            XWPFTableCell cell = row.getCell(colIndex);

            if (cell == null) {
                cell = row.createCell();
            }

            // 检查是否为合并单元格的主单元格
            boolean isMergedCell = isMergedMainCell(merges, rowIndex, colIndex);

            // 设置单元格内容（支持公式和混合内容）
            if (isMergedCell) {
                // 对于合并单元格，使用合并配置中的内容
                String mergedContent = getMergedCellContent(merges, rowIndex, colIndex);
                String finalContent = mergedContent != null ? mergedContent : dataCell.getContent();

                // 支持公式内容
                setCellContent(cell, finalContent,
                              dataCell.getHasMath() != null ? dataCell.getHasMath() : false,
                              dataCell.getMathML(),
                              dataCell.getMathMLMap());
            } else {
                // 普通单元格，支持完整的公式功能
                setCellContent(cell, dataCell.getContent(),
                              dataCell.getHasMath() != null ? dataCell.getHasMath() : false,
                              dataCell.getMathML(),
                              dataCell.getMathMLMap());
            }

            // 设置数据单元格样式
            setDataCellStyle(cell);

            // 处理单元格尺寸
            TableExportRequest.MergeCell mergeInfo = findMergeInfo(merges, rowIndex, colIndex);

            // 对于宽度：只有合并单元格才需要特殊处理宽度（因为它可能跨多列）
            // 普通单元格的宽度已经在setAllRowsColumnWidth中统一设置了
            if (mergeInfo != null && mergeInfo.getWidth() != null && mergeInfo.getWidth() > 0) {
                setCellWidth(cell, mergeInfo.getWidth());
                log.debug("设置数据合并单元格[{},{}]宽度: {}px (来源: 合并信息)",
                         rowIndex, colIndex, mergeInfo.getWidth());
            }

            // 对于高度：优先使用合并信息，其次使用单元格信息
            Integer cellHeight = (mergeInfo != null && mergeInfo.getHeight() != null) ?
                                mergeInfo.getHeight() : dataCell.getHeight();

            if (cellHeight != null && cellHeight > 0) {
                setCellHeight(cell, cellHeight);
                log.debug("设置数据单元格[{},{}]高度: {}px (来源: {})",
                         rowIndex, colIndex, cellHeight,
                         mergeInfo != null ? "合并信息" : "单元格信息");
            }
        }
    }

    /**
     * 应用合并单元格
     */
    private void applyMerges(XWPFTable table, List<TableExportRequest.MergeCell> merges, int headerRowCount) {
        if (merges == null || merges.isEmpty()) {
            return;
        }

        log.info("开始应用 {} 个合并单元格，表头行数: {}", merges.size(), headerRowCount);

        for (TableExportRequest.MergeCell merge : merges) {
            try {
                applyMerge(table, merge, headerRowCount);
                log.debug("应用合并单元格: ({},{}) -> ({},{})，表头偏移: {}",
                         merge.getStartRow(), merge.getStartCol(),
                         merge.getEndRow(), merge.getEndCol(), headerRowCount);
            } catch (Exception e) {
                log.error("应用合并单元格失败: {}", merge, e);
            }
        }
    }

    /**
     * 应用单个合并单元格
     */
    private void applyMerge(XWPFTable table, TableExportRequest.MergeCell merge, int headerRowCount) {
        int startRow = merge.getStartRow();
        int startCol = merge.getStartCol();
        int endRow = merge.getEndRow();
        int endCol = merge.getEndCol();

        // 调整行索引，考虑表头偏移
        int actualStartRow = startRow + headerRowCount;
        int actualEndRow = endRow + headerRowCount;

        // 验证合并范围
        if (actualStartRow < 0 || startCol < 0 || actualEndRow >= table.getRows().size() ||
            endCol >= table.getRow(0).getTableCells().size()) {
            log.warn("合并单元格范围无效: 原始({},{}) -> ({},{}) 调整后({},{}) -> ({},{})",
                    startRow, startCol, endRow, endCol, actualStartRow, startCol, actualEndRow, endCol);
            return;
        }

        // 获取主单元格
        XWPFTableCell mainCell = table.getRow(actualStartRow).getCell(startCol);

        log.debug("应用合并单元格: 原始({},{}) -> ({},{}) 调整后({},{}) -> ({},{})，宽度: {}px，高度: {}px",
                 startRow, startCol, endRow, endCol, actualStartRow, startCol, actualEndRow, endCol,
                 merge.getWidth(), merge.getHeight());

        // 设置合并单元格的尺寸
        if (merge.getWidth() != null && merge.getWidth() > 0) {
            setCellWidth(mainCell, merge.getWidth());
            log.debug("设置合并单元格宽度: {}px", merge.getWidth());
        }

        if (merge.getHeight() != null && merge.getHeight() > 0) {
            setCellHeight(mainCell, merge.getHeight());
            log.debug("设置合并单元格高度: {}px", merge.getHeight());
        }

        // 纵向合并
        if (actualEndRow > actualStartRow) {
            CTVMerge vMerge = CTVMerge.Factory.newInstance();
            vMerge.setVal(STMerge.RESTART);

            // 确保主单元格有TcPr属性
            CTTcPr mainTcPr = mainCell.getCTTc().getTcPr();
            if (mainTcPr == null) {
                mainTcPr = mainCell.getCTTc().addNewTcPr();
            }
            mainTcPr.setVMerge(vMerge);

            // 设置被合并的单元格
            for (int row = actualStartRow + 1; row <= actualEndRow; row++) {
                XWPFTableCell mergedCell = table.getRow(row).getCell(startCol);
                CTVMerge vMergeContinue = CTVMerge.Factory.newInstance();
                vMergeContinue.setVal(STMerge.CONTINUE);

                CTTcPr mergedTcPr = mergedCell.getCTTc().getTcPr();
                if (mergedTcPr == null) {
                    mergedTcPr = mergedCell.getCTTc().addNewTcPr();
                }
                mergedTcPr.setVMerge(vMergeContinue);
            }
        }

        // 横向合并
        if (endCol > startCol) {
            CTHMerge hMerge = CTHMerge.Factory.newInstance();
            hMerge.setVal(STMerge.RESTART);

            // 确保主单元格有TcPr属性
            CTTcPr mainTcPr = mainCell.getCTTc().getTcPr();
            if (mainTcPr == null) {
                mainTcPr = mainCell.getCTTc().addNewTcPr();
            }
            mainTcPr.setHMerge(hMerge);

            // 设置被合并的单元格
            for (int col = startCol + 1; col <= endCol; col++) {
                XWPFTableCell mergedCell = table.getRow(actualStartRow).getCell(col);
                CTHMerge hMergeContinue = CTHMerge.Factory.newInstance();
                hMergeContinue.setVal(STMerge.CONTINUE);

                CTTcPr mergedTcPr = mergedCell.getCTTc().getTcPr();
                if (mergedTcPr == null) {
                    mergedTcPr = mergedCell.getCTTc().addNewTcPr();
                }
                mergedTcPr.setHMerge(hMergeContinue);
            }
        }

        // 如果是既有纵向又有横向的合并，需要处理所有相关单元格
        if (actualEndRow > actualStartRow && endCol > startCol) {
            for (int row = actualStartRow + 1; row <= actualEndRow; row++) {
                for (int col = startCol + 1; col <= endCol; col++) {
                    XWPFTableCell mergedCell = table.getRow(row).getCell(col);

                    // 确保单元格有TcPr属性
                    CTTcPr mergedTcPr = mergedCell.getCTTc().getTcPr();
                    if (mergedTcPr == null) {
                        mergedTcPr = mergedCell.getCTTc().addNewTcPr();
                    }

                    // 设置纵向合并
                    CTVMerge vMergeContinue = CTVMerge.Factory.newInstance();
                    vMergeContinue.setVal(STMerge.CONTINUE);
                    mergedTcPr.setVMerge(vMergeContinue);

                    // 设置横向合并
                    CTHMerge hMergeContinue = CTHMerge.Factory.newInstance();
                    hMergeContinue.setVal(STMerge.CONTINUE);
                    mergedTcPr.setHMerge(hMergeContinue);
                }
            }
        }
    }

    /**
     * 设置单元格内容（优化版本，参考SimpleWordService）
     */
    private void setCellContentOptimized(XWPFTableCell cell, String content, boolean isHeader, Boolean isVertical) {
        // 清除默认段落
        cell.removeParagraph(0);

        XWPFParagraph paragraph = cell.addParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);

        // 处理纵向显示
        if (Boolean.TRUE.equals(isVertical)) {
            // 纵向显示：每个字符单独一行
            String cleanContent = cleanHtmlTags(content);
            char[] chars = cleanContent.toCharArray();

            for (int i = 0; i < chars.length; i++) {
                XWPFRun run = paragraph.createRun();
                run.setText(String.valueOf(chars[i]));
                run.setFontFamily("宋体");
                run.setFontSize(isHeader ? 12 : 11);
                if (isHeader) {
                    run.setBold(true);
                }

                // 除了最后一个字符，都添加换行
                if (i < chars.length - 1) {
                    run.addBreak();
                }
            }
        } else {
            // 横向显示：处理换行符
            XWPFRun run = paragraph.createRun();
            run.setText(content);
            run.setFontFamily("宋体");
            run.setFontSize(isHeader ? 12 : 11);
            if (isHeader) {
                run.setBold(true);
            }
        }

        // 设置单元格样式
        if (isHeader) {
            cell.setColor("F2F2F2"); // 浅灰色背景
        }
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
    }

    /**
     * 设置单元格宽度（优化版本，参考SimpleWordService）
     */
    private void setCellWidthOptimized(XWPFTableCell cell, int widthPx) {
        int widthTwips = widthPx * 15;

        CTTcPr tcPr = cell.getCTTc().getTcPr();
        if (tcPr == null) {
            tcPr = cell.getCTTc().addNewTcPr();
        }

        CTTblWidth cellWidth = tcPr.getTcW();
        if (cellWidth == null) {
            cellWidth = tcPr.addNewTcW();
        }

        cellWidth.setType(STTblWidth.DXA);
        cellWidth.setW(BigInteger.valueOf(widthTwips));
    }

    /**
     * 设置行高（优化版本，参考SimpleWordService）
     */
    private void setCellHeightOptimized(XWPFTableRow row, int heightPx) {
        int heightTwips = heightPx * 15;

        CTTrPr trPr = row.getCtRow().getTrPr();
        if (trPr == null) {
            trPr = row.getCtRow().addNewTrPr();
        }

        CTHeight rowHeight = trPr.getTrHeightList().isEmpty() ?
                trPr.addNewTrHeight() : trPr.getTrHeightList().get(0);

        rowHeight.setVal(BigInteger.valueOf(heightTwips));
        rowHeight.setHRule(STHeightRule.AT_LEAST);
    }

    /**
     * 应用合并（优化版本，参考SimpleWordService）
     */
    private void applyMergesOptimized(XWPFTable table, List<TableExportRequest.MergeCell> headerMerges,
                                     List<TableExportRequest.MergeCell> dataMerges, int headerRowCount) {
        // 应用表头合并
        if (headerMerges != null && !headerMerges.isEmpty()) {
            log.info("应用表头合并单元格，数量: {}", headerMerges.size());
            for (TableExportRequest.MergeCell merge : headerMerges) {
                applyMergeOptimized(table, merge, 0); // 表头合并不需要偏移
            }
        }

        // 应用数据行合并
        if (dataMerges != null && !dataMerges.isEmpty()) {
            log.info("应用数据行合并单元格，数量: {}，表头偏移: {}", dataMerges.size(), headerRowCount);
            for (TableExportRequest.MergeCell merge : dataMerges) {
                applyMergeOptimized(table, merge, headerRowCount);
            }
        }
    }

    /**
     * 应用单个合并（优化版本，参考SimpleWordService）
     */
    private void applyMergeOptimized(XWPFTable table, TableExportRequest.MergeCell merge, int headerRowCount) {
        // 验证合并信息的有效性
        if (!isValidMergeOptimized(table, merge, headerRowCount)) {
            log.warn("跳过无效的合并信息: {}", merge);
            return;
        }

        int actualStartRow = merge.getStartRow() + headerRowCount;
        int actualEndRow = merge.getEndRow() + headerRowCount;

        // 获取主单元格
        XWPFTableCell mainCell = table.getRow(actualStartRow).getCell(merge.getStartCol());

        // 设置主单元格内容
        if (merge.getContent() != null && !merge.getContent().trim().isEmpty()) {
            setCellContentOptimized(mainCell, merge.getContent(), actualStartRow < headerRowCount, false);
        }

        // 水平合并
        if (merge.getColspan() != null && merge.getColspan() > 1) {
            applyHorizontalMerge(table, actualStartRow, merge.getStartCol(), merge.getEndCol());
        }

        // 垂直合并
        if (merge.getRowspan() != null && merge.getRowspan() > 1) {
            applyVerticalMerge(table, actualStartRow, actualEndRow, merge.getStartCol());
        }
    }

    /**
     * 检查是否为合并单元格的主单元格
     */
    private boolean isMergedMainCell(List<TableExportRequest.MergeCell> merges, int rowIndex, int colIndex) {
        if (merges == null || merges.isEmpty()) {
            return false;
        }

        for (TableExportRequest.MergeCell merge : merges) {
            if (merge.getStartRow().equals(rowIndex) && merge.getStartCol().equals(colIndex)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取合并单元格的内容
     */
    private String getMergedCellContent(List<TableExportRequest.MergeCell> merges, int rowIndex, int colIndex) {
        if (merges == null || merges.isEmpty()) {
            return null;
        }

        for (TableExportRequest.MergeCell merge : merges) {
            if (merge.getStartRow().equals(rowIndex) && merge.getStartCol().equals(colIndex)) {
                return merge.getContent();
            }
        }
        return null;
    }

    /**
     * 应用水平合并（参考SimpleWordService）
     */
    private void applyHorizontalMerge(XWPFTable table, int row, int startCol, int endCol) {
        XWPFTableRow tableRow = table.getRow(row);
        XWPFTableCell mainCell = tableRow.getCell(startCol);

        // 设置主单元格的gridSpan
        CTTcPr tcPr = mainCell.getCTTc().getTcPr();
        if (tcPr == null) {
            tcPr = mainCell.getCTTc().addNewTcPr();
        }

        CTDecimalNumber gridSpan = tcPr.getGridSpan();
        if (gridSpan == null) {
            gridSpan = tcPr.addNewGridSpan();
        }
        gridSpan.setVal(BigInteger.valueOf(endCol - startCol + 1));

        // 设置被合并的单元格为合并继续
        for (int col = startCol + 1; col <= endCol; col++) {
            XWPFTableCell cell = tableRow.getCell(col);
            if (cell != null) {
                CTTcPr cellTcPr = cell.getCTTc().getTcPr();
                if (cellTcPr == null) {
                    cellTcPr = cell.getCTTc().addNewTcPr();
                }

                CTHMerge hMerge = cellTcPr.getHMerge();
                if (hMerge == null) {
                    hMerge = cellTcPr.addNewHMerge();
                }
                hMerge.setVal(STMerge.CONTINUE);
            }
        }

        // 设置主单元格的水平合并开始
        CTHMerge mainHMerge = tcPr.getHMerge();
        if (mainHMerge == null) {
            mainHMerge = tcPr.addNewHMerge();
        }
        mainHMerge.setVal(STMerge.RESTART);
    }

    /**
     * 应用垂直合并（参考SimpleWordService）
     */
    private void applyVerticalMerge(XWPFTable table, int startRow, int endRow, int col) {
        // 设置主单元格为合并开始
        XWPFTableCell mainCell = table.getRow(startRow).getCell(col);
        CTTcPr mainTcPr = mainCell.getCTTc().getTcPr();
        if (mainTcPr == null) {
            mainTcPr = mainCell.getCTTc().addNewTcPr();
        }
        CTVMerge vMerge = mainTcPr.getVMerge();
        if (vMerge == null) {
            vMerge = mainTcPr.addNewVMerge();
        }
        vMerge.setVal(STMerge.RESTART);

        // 设置其他单元格为合并继续
        for (int row = startRow + 1; row <= endRow; row++) {
            XWPFTableRow tableRow = table.getRow(row);
            if (tableRow != null) {
                XWPFTableCell cell = tableRow.getCell(col);
                if (cell != null) {
                    CTTcPr tcPr = cell.getCTTc().getTcPr();
                    if (tcPr == null) {
                        tcPr = cell.getCTTc().addNewTcPr();
                    }
                    CTVMerge cellVMerge = tcPr.getVMerge();
                    if (cellVMerge == null) {
                        cellVMerge = tcPr.addNewVMerge();
                    }
                    cellVMerge.setVal(STMerge.CONTINUE);
                }
            }
        }
    }

    /**
     * 验证合并信息是否有效（参考SimpleWordService）
     */
    private boolean isValidMergeOptimized(XWPFTable table, TableExportRequest.MergeCell merge, int headerRowCount) {
        if (merge == null) {
            log.warn("合并信息为null");
            return false;
        }

        int tableRows = table.getRows().size();
        int tableCols = table.getRow(0).getTableCells().size();

        int actualStartRow = merge.getStartRow() + headerRowCount;
        int actualEndRow = merge.getEndRow() + headerRowCount;

        // 检查行索引
        if (actualStartRow < 0 || actualStartRow >= tableRows) {
            log.warn("起始行索引超出范围: {} (表格行数: {})", actualStartRow, tableRows);
            return false;
        }
        if (actualEndRow < 0 || actualEndRow >= tableRows) {
            log.warn("结束行索引超出范围: {} (表格行数: {})", actualEndRow, tableRows);
            return false;
        }

        // 检查列索引
        if (merge.getStartCol() < 0 || merge.getStartCol() >= tableCols) {
            log.warn("起始列索引超出范围: {} (表格列数: {})", merge.getStartCol(), tableCols);
            return false;
        }
        if (merge.getEndCol() < 0 || merge.getEndCol() >= tableCols) {
            log.warn("结束列索引超出范围: {} (表格列数: {})", merge.getEndCol(), tableCols);
            return false;
        }

        // 检查合并范围的逻辑性
        if (actualStartRow > actualEndRow) {
            log.warn("起始行大于结束行: {} > {}", actualStartRow, actualEndRow);
            return false;
        }
        if (merge.getStartCol() > merge.getEndCol()) {
            log.warn("起始列大于结束列: {} > {}", merge.getStartCol(), merge.getEndCol());
            return false;
        }

        return true;
    }

    /**
     * 设置表格边框（参考SimpleWordService）
     */
    private void setTableBorders(XWPFTable table) {
        try {
            CTTblPr tblPr = table.getCTTbl().getTblPr();
            if (tblPr == null) {
                tblPr = table.getCTTbl().addNewTblPr();
            }

            CTTblBorders borders = tblPr.getTblBorders();
            if (borders == null) {
                borders = tblPr.addNewTblBorders();
            }

            // 创建边框样式
            CTBorder border = CTBorder.Factory.newInstance();
            border.setVal(STBorder.SINGLE);
            border.setSz(BigInteger.valueOf(4));
            border.setColor("000000");

            // 设置所有边框
            if (borders.getTop() == null) borders.addNewTop().set(border);
            if (borders.getBottom() == null) borders.addNewBottom().set(border);
            if (borders.getLeft() == null) borders.addNewLeft().set(border);
            if (borders.getRight() == null) borders.addNewRight().set(border);
            if (borders.getInsideH() == null) borders.addNewInsideH().set(border);
            if (borders.getInsideV() == null) borders.addNewInsideV().set(border);

        } catch (Exception e) {
            log.warn("表格边框设置失败: {}", e.getMessage());
            // 使用简单的边框设置作为备选方案
            try {
                table.setTableAlignment(TableRowAlign.CENTER);
            } catch (Exception ex) {
                log.warn("简单边框设置也失败，使用默认样式");
            }
        }
    }

    /**
     * 生成表格预览HTML
     */
    public String generateTablePreview(TableExportRequest request) {
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html>");
        html.append("<html><head>");
        html.append("<meta charset='UTF-8'>");
        html.append("<title>表格预览</title>");
        html.append("<style>");
        html.append("body { font-family: '宋体', SimSun, serif; margin: 20px; }");
        html.append("h1 { text-align: center; font-size: 18px; margin-bottom: 20px; }");
        html.append("table { border-collapse: collapse; width: 100%; margin: 0 auto; }");
        html.append("th, td { border: 1px solid #000; padding: 8px; text-align: center; vertical-align: middle; }");
        html.append("th { background-color: #f0f0f0; font-weight: bold; }");
        html.append(".merged-cell { background-color: #e6f3ff; }");
        html.append("</style>");
        html.append("</head><body>");

        // 添加标题
        if (request.getTitle() != null && !request.getTitle().trim().isEmpty()) {
            html.append("<h1>").append(request.getTitle()).append("</h1>");
        }

        // 创建表格
        html.append("<table>");

        TableExportRequest.TableData tableData = request.getTableData();
        List<TableExportRequest.MergeCell> merges = request.getMerges();

        // 处理表头
        if (tableData.getHeaders() != null && !tableData.getHeaders().isEmpty()) {
            for (List<TableExportRequest.HeaderCell> headerRow : tableData.getHeaders()) {
                html.append("<tr>");
                for (TableExportRequest.HeaderCell headerCell : headerRow) {
                    html.append("<th>").append(headerCell.getContent() != null ? headerCell.getContent() : "").append("</th>");
                }
                html.append("</tr>");
            }
        }

        // 处理数据行
        if (tableData.getDataRows() != null && !tableData.getDataRows().isEmpty()) {
            for (int rowIndex = 0; rowIndex < tableData.getDataRows().size(); rowIndex++) {
                List<TableExportRequest.DataCell> dataRow = tableData.getDataRows().get(rowIndex);
                html.append("<tr>");
                for (int colIndex = 0; colIndex < dataRow.size(); colIndex++) {
                    TableExportRequest.DataCell dataCell = dataRow.get(colIndex);
                    boolean isMerged = isMergedMainCell(merges, rowIndex, colIndex);

                    String cellClass = isMerged ? " class='merged-cell'" : "";
                    String content = isMerged ? getMergedCellContent(merges, rowIndex, colIndex) : dataCell.getContent();

                    html.append("<td").append(cellClass).append(">")
                        .append(content != null ? content : "")
                        .append("</td>");
                }
                html.append("</tr>");
            }
        }

        html.append("</table>");
        html.append("</body></html>");

        return html.toString();
    }

    /**
     * 设置表格样式
     */
    private void setTableStyle(XWPFTable table) {
        // 设置表格边框
        CTTblBorders borders = CTTblBorders.Factory.newInstance();

        // 设置上边框
        CTBorder topBorder = borders.addNewTop();
        topBorder.setVal(STBorder.SINGLE);
        topBorder.setSz(BigInteger.valueOf(4));
        topBorder.setColor("000000");

        // 设置下边框
        CTBorder bottomBorder = borders.addNewBottom();
        bottomBorder.setVal(STBorder.SINGLE);
        bottomBorder.setSz(BigInteger.valueOf(4));
        bottomBorder.setColor("000000");

        // 设置左边框
        CTBorder leftBorder = borders.addNewLeft();
        leftBorder.setVal(STBorder.SINGLE);
        leftBorder.setSz(BigInteger.valueOf(4));
        leftBorder.setColor("000000");

        // 设置右边框
        CTBorder rightBorder = borders.addNewRight();
        rightBorder.setVal(STBorder.SINGLE);
        rightBorder.setSz(BigInteger.valueOf(4));
        rightBorder.setColor("000000");

        // 设置内部水平边框
        CTBorder insideHBorder = borders.addNewInsideH();
        insideHBorder.setVal(STBorder.SINGLE);
        insideHBorder.setSz(BigInteger.valueOf(4));
        insideHBorder.setColor("000000");

        // 设置内部垂直边框
        CTBorder insideVBorder = borders.addNewInsideV();
        insideVBorder.setVal(STBorder.SINGLE);
        insideVBorder.setSz(BigInteger.valueOf(4));
        insideVBorder.setColor("000000");

        // 应用边框到表格
        table.getCTTbl().getTblPr().setTblBorders(borders);

        // 设置表格对齐方式为居中
        CTJcTable jcTable = CTJcTable.Factory.newInstance();
        jcTable.setVal(STJcTable.CENTER);
        table.getCTTbl().getTblPr().setJc(jcTable);

        // 设置表格宽度为绝对值（与列宽保持一致）
        // 注意：这里暂时使用默认宽度，实际宽度会在设置列宽时动态计算
        CTTblWidth tblWidth = CTTblWidth.Factory.newInstance();
        tblWidth.setType(STTblWidth.DXA);
        tblWidth.setW(BigInteger.valueOf(12000)); // 默认宽度，会被动态调整
        table.getCTTbl().getTblPr().setTblW(tblWidth);

        log.debug("表格样式设置完成");
    }

    /**
     * 设置单元格内容（支持公式和混合内容）
     */
    private void setCellContent(XWPFTableCell cell, String content, boolean hasMath) {
        setCellContent(cell, content, hasMath, null, null);
    }

    /**
     * 设置单元格内容（完整版本，支持公式、混合内容和MathML）
     */
    private void setCellContent(XWPFTableCell cell, String content, boolean hasMath, String mathML, Map<String, String> mathMLMap) {
        // 清除单元格现有内容
        cell.removeParagraph(0);

        if (content == null || content.trim().isEmpty()) {
            // 创建空段落
            XWPFParagraph paragraph = cell.addParagraph();
            paragraph.setAlignment(ParagraphAlignment.CENTER);
            return;
        }

        XWPFParagraph paragraph = cell.addParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);

        try {
            if (mathMLMap != null && !mathMLMap.isEmpty()) {
                // 处理混合内容（文本 + 公式）
                log.debug("处理混合内容，mathMLMap大小: {}", mathMLMap.size());
                insertMixedContent(paragraph, content, mathMLMap);
            } else if (mathML != null && !mathML.trim().isEmpty()) {
                // 处理纯公式内容
                log.debug("处理纯公式内容");
                insertMathFormula(paragraph, mathML);
            } else if (hasMath) {
                // 处理LaTeX格式的数学公式（兼容旧版本）
                processMathContent(cell, content);
            } else {
                // 处理普通文本内容
                insertTextWithLineBreaks(paragraph, content);
            }
        } catch (Exception e) {
            log.error("设置单元格内容失败: {}", e.getMessage(), e);
            // 如果处理失败，回退到普通文本
            paragraph.getRuns().clear();
            insertTextWithLineBreaks(paragraph, content);
        }
    }

    /**
     * 处理数学公式内容
     */
    private void processMathContent(XWPFTableCell cell, String content) {
        XWPFParagraph paragraph = cell.addParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);

        try {
            // 检查是否为纯数学公式（被$包围）
            if (content.startsWith("$") && content.endsWith("$") && content.length() > 2) {
                String mathContent = content.substring(1, content.length() - 1);
                addMathFormula(paragraph, mathContent);
            } else {
                // 混合内容：文本 + 数学公式
                processMixedContent(paragraph, content);
            }
        } catch (Exception e) {
            log.warn("处理数学公式失败，使用普通文本: {}", content, e);
            // 如果数学公式处理失败，回退到普通文本
            processTextContent(cell, content);
        }
    }

    /**
     * 处理混合内容（文本 + 数学公式）
     */
    private void processMixedContent(XWPFParagraph paragraph, String content) {
        // 使用正则表达式分割文本和数学公式
        String[] parts = content.split("\\$");
        boolean isMath = false;

        for (String part : parts) {
            if (part.isEmpty()) {
                isMath = !isMath;
                continue;
            }

            if (isMath) {
                // 数学公式部分
                try {
                    addMathFormula(paragraph, part);
                } catch (Exception e) {
                    log.warn("处理数学公式部分失败: {}", part, e);
                    // 如果公式处理失败，作为普通文本处理
                    XWPFRun run = paragraph.createRun();
                    run.setText("$" + part + "$");
                    run.setFontFamily("宋体");
                    run.setFontSize(11);
                }
            } else {
                // 普通文本部分
                XWPFRun run = paragraph.createRun();
                run.setText(part);
                run.setFontFamily("宋体");
                run.setFontSize(11);
            }

            isMath = !isMath;
        }
    }

    /**
     * 处理普通文本内容
     */
    private void processTextContent(XWPFTableCell cell, String content) {
        XWPFParagraph paragraph = cell.addParagraph();
        paragraph.setAlignment(ParagraphAlignment.CENTER);

        // 处理多行文本
        String[] lines = content.split("\\n");
        for (int i = 0; i < lines.length; i++) {
            XWPFRun run = paragraph.createRun();
            run.setText(lines[i]);
            run.setFontFamily("宋体");
            run.setFontSize(11);

            // 除了最后一行，其他行都添加换行符
            if (i < lines.length - 1) {
                run.addBreak();
            }
        }
    }

    /**
     * 设置表头单元格样式
     */
    private void setHeaderCellStyle(XWPFTableCell cell) {
        // 设置单元格背景色为浅灰色
        cell.setColor("F0F0F0");

        // 设置单元格边框
        setCellBorders(cell);

        // 设置单元格对齐方式
        setCellAlignment(cell, ParagraphAlignment.CENTER, XWPFTableCell.XWPFVertAlign.CENTER);

        // 设置单元格内边距
        setCellMargins(cell, 100, 100, 100, 100); // 上右下左各100 twips

        log.debug("表头单元格样式设置完成");
    }

    /**
     * 设置数据单元格样式
     */
    private void setDataCellStyle(XWPFTableCell cell) {
        // 设置单元格背景色为白色
        cell.setColor("FFFFFF");

        // 设置单元格边框
        setCellBorders(cell);

        // 设置单元格对齐方式
        setCellAlignment(cell, ParagraphAlignment.CENTER, XWPFTableCell.XWPFVertAlign.CENTER);

        // 设置单元格内边距
        setCellMargins(cell, 100, 100, 100, 100); // 上右下左各100 twips

        log.debug("数据单元格样式设置完成");
    }

    /**
     * 设置单元格边框
     */
    private void setCellBorders(XWPFTableCell cell) {
        CTTcBorders borders = CTTcBorders.Factory.newInstance();

        // 设置上边框
        CTBorder topBorder = borders.addNewTop();
        topBorder.setVal(STBorder.SINGLE);
        topBorder.setSz(BigInteger.valueOf(4));
        topBorder.setColor("000000");

        // 设置下边框
        CTBorder bottomBorder = borders.addNewBottom();
        bottomBorder.setVal(STBorder.SINGLE);
        bottomBorder.setSz(BigInteger.valueOf(4));
        bottomBorder.setColor("000000");

        // 设置左边框
        CTBorder leftBorder = borders.addNewLeft();
        leftBorder.setVal(STBorder.SINGLE);
        leftBorder.setSz(BigInteger.valueOf(4));
        leftBorder.setColor("000000");

        // 设置右边框
        CTBorder rightBorder = borders.addNewRight();
        rightBorder.setVal(STBorder.SINGLE);
        rightBorder.setSz(BigInteger.valueOf(4));
        rightBorder.setColor("000000");

        // 应用边框到单元格
        cell.getCTTc().getTcPr().setTcBorders(borders);
    }

    /**
     * 设置单元格对齐方式
     */
    private void setCellAlignment(XWPFTableCell cell, ParagraphAlignment paragraphAlignment, XWPFTableCell.XWPFVertAlign verticalAlignment) {
        // 设置垂直对齐
        cell.setVerticalAlignment(verticalAlignment);

        // 设置段落水平对齐
        for (XWPFParagraph paragraph : cell.getParagraphs()) {
            paragraph.setAlignment(paragraphAlignment);
        }
    }

    /**
     * 设置单元格内边距
     */
    private void setCellMargins(XWPFTableCell cell, int top, int right, int bottom, int left) {
        CTTcMar margins = CTTcMar.Factory.newInstance();

        // 设置上边距
        CTTblWidth topMargin = margins.addNewTop();
        topMargin.setType(STTblWidth.DXA);
        topMargin.setW(BigInteger.valueOf(top));

        // 设置右边距
        CTTblWidth rightMargin = margins.addNewRight();
        rightMargin.setType(STTblWidth.DXA);
        rightMargin.setW(BigInteger.valueOf(right));

        // 设置下边距
        CTTblWidth bottomMargin = margins.addNewBottom();
        bottomMargin.setType(STTblWidth.DXA);
        bottomMargin.setW(BigInteger.valueOf(bottom));

        // 设置左边距
        CTTblWidth leftMargin = margins.addNewLeft();
        leftMargin.setType(STTblWidth.DXA);
        leftMargin.setW(BigInteger.valueOf(left));

        // 应用边距到单元格
        cell.getCTTc().getTcPr().setTcMar(margins);
    }

    /**
     * 设置单元格宽度
     */
    private void setCellWidth(XWPFTableCell cell, int widthInPixels) {
        // 将像素转换为Twips (1像素 ≈ 15 Twips)
        int widthInTwips = (int) (widthInPixels * 15);

        CTTblWidth cellWidth = CTTblWidth.Factory.newInstance();
        cellWidth.setType(STTblWidth.DXA);
        cellWidth.setW(BigInteger.valueOf(widthInTwips));

        cell.getCTTc().getTcPr().setTcW(cellWidth);

        log.debug("设置单元格宽度: {}像素 -> {}Twips", widthInPixels, widthInTwips);
    }

    /**
     * 设置单元格高度
     */
    private void setCellHeight(XWPFTableCell cell, int heightInPixels) {
        // 将像素转换为Twips (1像素 ≈ 15 Twips)
        int heightInTwips = (int) (heightInPixels * 15);

        // 获取单元格所在的行
        XWPFTableRow row = cell.getTableRow();
        if (row != null) {
            CTTrPr rowProperties = row.getCtRow().getTrPr();
            if (rowProperties == null) {
                rowProperties = row.getCtRow().addNewTrPr();
            }

            // 设置行高
            CTHeight rowHeight = null;
            if (rowProperties.getTrHeightList().isEmpty()) {
                rowHeight = rowProperties.addNewTrHeight();
            } else {
                rowHeight = rowProperties.getTrHeightList().get(0);
            }

            rowHeight.setVal(BigInteger.valueOf(heightInTwips));
            rowHeight.setHRule(STHeightRule.AT_LEAST); // 至少为指定高度

            log.debug("设置单元格高度: {}像素 -> {}Twips", heightInPixels, heightInTwips);
        }
    }

    /**
     * 添加数学公式到段落
     */
    private void addMathFormula(XWPFParagraph paragraph, String latexContent) {
        try {
            log.debug("添加数学公式: {}", latexContent);

            // 这里应该将LaTeX转换为MathML，然后转换为OMML
            // 由于没有LaTeX到MathML的转换器，我们先用简单的文本替代
            // 在实际项目中，可以集成MathJax或其他LaTeX转换库

            // 临时实现：将LaTeX公式作为特殊格式的文本显示
            XWPFRun run = paragraph.createRun();
            run.setText("[" + latexContent + "]");
            run.setFontFamily("Times New Roman");
            run.setFontSize(11);
            run.setItalic(true);

            log.debug("数学公式添加完成（临时文本格式）");

        } catch (Exception e) {
            log.error("添加数学公式失败: {}", latexContent, e);
            // 如果处理失败，添加错误提示
            XWPFRun run = paragraph.createRun();
            run.setText("[公式错误: " + latexContent + "]");
            run.setFontFamily("宋体");
            run.setFontSize(11);
            run.setColor("FF0000"); // 红色
        }
    }

    /**
     * 查找指定位置的合并信息
     */
    private TableExportRequest.MergeCell findMergeInfo(List<TableExportRequest.MergeCell> merges, int rowIndex, int colIndex) {
        if (merges == null || merges.isEmpty()) {
            return null;
        }

        for (TableExportRequest.MergeCell merge : merges) {
            if (merge.getStartRow().equals(rowIndex) && merge.getStartCol().equals(colIndex)) {
                return merge;
            }
        }
        return null;
    }

    /**
     * 根据列宽配置动态设置表格总宽度
     */
    private void setTableTotalWidth(XWPFTable table, TableExportRequest.HeaderWidthConfig headerWidthConfig) {
        if (headerWidthConfig == null || headerWidthConfig.getColumnWidths() == null) {
            return;
        }

        try {
            // 计算所有列宽的总和
            int totalWidthPx = 0;
            for (Integer columnWidth : headerWidthConfig.getColumnWidths()) {
                if (columnWidth != null && columnWidth > 0) {
                    totalWidthPx += columnWidth;
                }
            }

            if (totalWidthPx > 0) {
                // 将像素转换为Twips (1像素 ≈ 15 Twips)
                int totalWidthTwips = (int) (totalWidthPx * 15);

                // 设置表格总宽度
                CTTblWidth tblWidth = table.getCTTbl().getTblPr().getTblW();
                if (tblWidth == null) {
                    tblWidth = table.getCTTbl().getTblPr().addNewTblW();
                }

                tblWidth.setType(STTblWidth.DXA);
                tblWidth.setW(BigInteger.valueOf(totalWidthTwips));

                log.info("设置表格总宽度: {}px ({}twips)，基于列宽配置: {}",
                        totalWidthPx, totalWidthTwips, headerWidthConfig.getColumnWidths());
            }
        } catch (Exception e) {
            log.warn("设置表格总宽度失败: {}", e.getMessage());
        }
    }

    /**
     * 设置单元格宽度
     */
    private void setCellWidth(XWPFTableCell cell, Integer widthPx) {
        if (widthPx == null || widthPx <= 0) {
            return;
        }

        try {
            // 将像素转换为Twips (1像素 ≈ 15 Twips)
            int widthTwips = (int) (widthPx * 15);

            // 设置单元格宽度
            CTTcPr tcPr = cell.getCTTc().getTcPr();
            if (tcPr == null) {
                tcPr = cell.getCTTc().addNewTcPr();
            }

            CTTblWidth cellWidth = tcPr.getTcW();
            if (cellWidth == null) {
                cellWidth = tcPr.addNewTcW();
            }

            cellWidth.setType(STTblWidth.DXA);
            cellWidth.setW(BigInteger.valueOf(widthTwips));

            log.debug("设置单元格宽度: {}px ({}twips)", widthPx, widthTwips);
        } catch (Exception e) {
            log.warn("设置单元格宽度失败: {}", e.getMessage());
        }
    }

    /**
     * 设置单元格高度
     */
    private void setCellHeight(XWPFTableCell cell, Integer heightPx) {
        if (heightPx == null || heightPx <= 0) {
            return;
        }

        try {
            // 将像素转换为Twips (1像素 ≈ 15 Twips)
            int heightTwips = (int) (heightPx * 15);

            // 设置行高
            XWPFTableRow row = cell.getTableRow();
            CTTrPr trPr = row.getCtRow().getTrPr();
            if (trPr == null) {
                trPr = row.getCtRow().addNewTrPr();
            }

            // 设置行高
            CTHeight rowHeight = null;
            if (trPr.getTrHeightList().isEmpty()) {
                rowHeight = trPr.addNewTrHeight();
            } else {
                rowHeight = trPr.getTrHeightList().get(0);
            }

            rowHeight.setVal(BigInteger.valueOf(heightTwips));
            rowHeight.setHRule(STHeightRule.AT_LEAST);

            log.debug("设置单元格高度: {}px ({}twips)", heightPx, heightTwips);
        } catch (Exception e) {
            log.warn("设置单元格高度失败: {}", e.getMessage());
        }
    }

    /**
     * 导出新JSON格式的表格到Word文档
     * 处理示例JSON数据格式：
     * {
     *   "rows": [["智能手机\n（多功能检测）", "A001", "2024-01-15", "张三", "李四", "王五"]],
     *   "merges": [{"startRow": 2, "startCol": 0, "endRow": 3, "endCol": 0, "rowspan": 2, "colspan": 1, "width": 280, "height": 100, "content": "智能手机\n（多功能检测）"}],
     *   "headers": [["产品名称", "生产信息", "", "责任人员", "", ""], ["", "批次号", "日期", "检验员", "审核员", "负责人"]],
     *   "headerMerges": [...],
     *   "headerWidthConfig": {"columnWidths": [280, 100, 100, 80, 80, 80], "headerHeights": [60, 40]},
     *   "metadata": {"totalRows": 2, "totalColumns": 6, "headerRows": 2, "title": "检验记录表"}
     * }
     */
    public byte[] exportNewJsonFormatToWord(JsonTableExportRequest jsonRequest) throws IOException {
        log.info("开始导出新JSON格式Word文档，表格标题: {}",
                jsonRequest.getMetadata() != null ? jsonRequest.getMetadata().getTitle() : "未知标题");

        try (XWPFDocument document = new XWPFDocument();
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {

            // 设置页面方向为横向（根据示例数据）
            setPageOrientation(document, "LANDSCAPE");

            // 添加标题
            String title = jsonRequest.getMetadata() != null ? jsonRequest.getMetadata().getTitle() : jsonRequest.getTitle();
            if (title != null && !title.trim().isEmpty()) {
                addTitle(document, title);
            }

            // 创建表格
            createTableFromNewJsonFormat(document, jsonRequest);

            document.write(out);
            byte[] result = out.toByteArray();

            log.info("新JSON格式Word文档导出完成，文件大小: {} bytes", result.length);
            return result;
        }
    }

    /**
     * 从新JSON格式创建表格
     */
    private void createTableFromNewJsonFormat(XWPFDocument document, JsonTableExportRequest jsonRequest) {
        // 计算表格的总行数和列数
        int headerRows = jsonRequest.getHeaders() != null ? jsonRequest.getHeaders().size() : 0;
        int dataRows = jsonRequest.getRows() != null ? jsonRequest.getRows().size() : 0;
        int totalRows = headerRows + dataRows;

        int totalCols = 0;
        if (jsonRequest.getMetadata() != null && jsonRequest.getMetadata().getTotalColumns() != null) {
            totalCols = jsonRequest.getMetadata().getTotalColumns();
        } else if (jsonRequest.getHeaderWidthConfig() != null && jsonRequest.getHeaderWidthConfig().getColumnWidths() != null) {
            totalCols = jsonRequest.getHeaderWidthConfig().getColumnWidths().size();
        } else if (jsonRequest.getHeaders() != null && !jsonRequest.getHeaders().isEmpty()) {
            totalCols = jsonRequest.getHeaders().get(0).size();
        }

        log.info("创建新JSON格式表格，总行数: {}, 总列数: {}", totalRows, totalCols);

        // 创建表格
        XWPFTable table = document.createTable(totalRows, totalCols);

        // 设置表格样式
        setTableStyleFromJsonConfig(table, jsonRequest);

        int currentRowIndex = 0;

        // 处理表头
        if (jsonRequest.getHeaders() != null && !jsonRequest.getHeaders().isEmpty()) {
            for (int i = 0; i < jsonRequest.getHeaders().size(); i++) {
                List<String> headerRow = jsonRequest.getHeaders().get(i);
                XWPFTableRow row = table.getRow(currentRowIndex);
                processHeaderRowFromJson(row, headerRow, jsonRequest, i);
                currentRowIndex++;
            }
        }

        // 处理数据行
        if (jsonRequest.getRows() != null && !jsonRequest.getRows().isEmpty()) {
            for (List<String> dataRow : jsonRequest.getRows()) {
                XWPFTableRow row = table.getRow(currentRowIndex);
                processDataRowFromJson(row, dataRow, jsonRequest);
                currentRowIndex++;
            }
        }

        // 应用合并
        applyMergesFromJsonFormat(table, jsonRequest, headerRows);

        // 设置边框
        setTableBorders(table);
    }

    /**
     * 从JSON配置设置表格样式
     */
    private void setTableStyleFromJsonConfig(XWPFTable table, JsonTableExportRequest jsonRequest) {
        try {
            List<Integer> columnWidths = null;
            if (jsonRequest.getHeaderWidthConfig() != null && jsonRequest.getHeaderWidthConfig().getColumnWidths() != null) {
                columnWidths = jsonRequest.getHeaderWidthConfig().getColumnWidths();
            }

            if (columnWidths != null && !columnWidths.isEmpty()) {
                // 计算总宽度
                int totalWidth = columnWidths.stream().mapToInt(Integer::intValue).sum();
                int totalWidthTwips = totalWidth * 15; // 像素转Twips

                // 设置表格宽度
                CTTblPr tblPr = table.getCTTbl().getTblPr();
                if (tblPr == null) {
                    tblPr = table.getCTTbl().addNewTblPr();
                }

                CTTblWidth tblWidth = tblPr.getTblW();
                if (tblWidth == null) {
                    tblWidth = tblPr.addNewTblW();
                }
                tblWidth.setType(STTblWidth.DXA);
                tblWidth.setW(BigInteger.valueOf(totalWidthTwips));

                // 设置表格居中
                CTJcTable jcTable = tblPr.getJc();
                if (jcTable == null) {
                    jcTable = tblPr.addNewJc();
                }
                jcTable.setVal(STJcTable.CENTER);

                log.info("设置表格总宽度: {}px ({}twips)", totalWidth, totalWidthTwips);
            }
        } catch (Exception e) {
            log.warn("设置表格样式失败: {}", e.getMessage());
        }
    }

    /**
     * 处理JSON格式的表头行
     */
    private void processHeaderRowFromJson(XWPFTableRow row, List<String> headerRow, JsonTableExportRequest jsonRequest, int rowIndex) {
        for (int colIndex = 0; colIndex < headerRow.size(); colIndex++) {
            String content = headerRow.get(colIndex);
            XWPFTableCell cell = row.getCell(colIndex);

            if (cell == null) {
                cell = row.createCell();
            }

            // 设置内容
            if (content != null && !content.trim().isEmpty()) {
                // 检查是否需要纵向显示
                Boolean isVertical = false;
                if (jsonRequest.getVerticalHeadersConfig() != null && colIndex < jsonRequest.getVerticalHeadersConfig().size()) {
                    isVertical = jsonRequest.getVerticalHeadersConfig().get(colIndex);
                }
                setCellContentOptimized(cell, content, true, isVertical);
            }

            // 设置列宽
            if (jsonRequest.getHeaderWidthConfig() != null && jsonRequest.getHeaderWidthConfig().getColumnWidths() != null) {
                List<Integer> columnWidths = jsonRequest.getHeaderWidthConfig().getColumnWidths();
                if (colIndex < columnWidths.size() && columnWidths.get(colIndex) != null) {
                    setCellWidthOptimized(cell, columnWidths.get(colIndex));
                }
            }

            // 设置行高
            if (jsonRequest.getHeaderWidthConfig() != null && jsonRequest.getHeaderWidthConfig().getHeaderHeights() != null) {
                List<Integer> headerHeights = jsonRequest.getHeaderWidthConfig().getHeaderHeights();
                if (rowIndex < headerHeights.size() && headerHeights.get(rowIndex) != null) {
                    setCellHeightOptimized(row, headerHeights.get(rowIndex));
                }
            }
        }
    }

    /**
     * 处理JSON格式的数据行
     */
    private void processDataRowFromJson(XWPFTableRow row, List<String> dataRow, JsonTableExportRequest jsonRequest) {
        for (int colIndex = 0; colIndex < dataRow.size(); colIndex++) {
            String content = dataRow.get(colIndex);
            XWPFTableCell cell = row.getCell(colIndex);

            if (cell == null) {
                cell = row.createCell();
            }

            // 设置内容
            if (content != null && !content.trim().isEmpty()) {
                setCellContentOptimized(cell, content, false, false);
            }

            // 设置列宽
            if (jsonRequest.getHeaderWidthConfig() != null && jsonRequest.getHeaderWidthConfig().getColumnWidths() != null) {
                List<Integer> columnWidths = jsonRequest.getHeaderWidthConfig().getColumnWidths();
                if (colIndex < columnWidths.size() && columnWidths.get(colIndex) != null) {
                    setCellWidthOptimized(cell, columnWidths.get(colIndex));
                }
            }
        }
    }

    /**
     * 应用JSON格式的合并
     */
    private void applyMergesFromJsonFormat(XWPFTable table, JsonTableExportRequest jsonRequest, int headerRows) {
        // 应用表头合并
        if (jsonRequest.getHeaderMerges() != null && !jsonRequest.getHeaderMerges().isEmpty()) {
            log.info("应用JSON格式表头合并单元格，数量: {}", jsonRequest.getHeaderMerges().size());
            for (JsonTableExportRequest.MergeConfig merge : jsonRequest.getHeaderMerges()) {
                applyJsonMerge(table, merge, 0); // 表头合并不需要偏移
            }
        }

        // 应用数据行合并
        if (jsonRequest.getMerges() != null && !jsonRequest.getMerges().isEmpty()) {
            log.info("应用JSON格式数据行合并单元格，数量: {}，表头偏移: {}", jsonRequest.getMerges().size(), headerRows);
            for (JsonTableExportRequest.MergeConfig merge : jsonRequest.getMerges()) {
                applyJsonMerge(table, merge, headerRows);
            }
        }
    }

    /**
     * 应用JSON格式的单个合并
     */
    private void applyJsonMerge(XWPFTable table, JsonTableExportRequest.MergeConfig merge, int headerRowCount) {
        // 验证合并信息的有效性
        if (!isValidJsonMerge(table, merge, headerRowCount)) {
            log.warn("跳过无效的JSON合并信息: {}", merge);
            return;
        }

        int actualStartRow = merge.getStartRow() + headerRowCount;
        int actualEndRow = merge.getEndRow() + headerRowCount;

        // 获取主单元格
        XWPFTableCell mainCell = table.getRow(actualStartRow).getCell(merge.getStartCol());

        // 设置主单元格内容
        if (merge.getContent() != null && !merge.getContent().trim().isEmpty()) {
            setCellContentOptimized(mainCell, merge.getContent(), actualStartRow < headerRowCount, false);
        }

        // 计算跨度
        int colspan = merge.getEndCol() - merge.getStartCol() + 1;
        int rowspan = merge.getEndRow() - merge.getStartRow() + 1;

        // 水平合并
        if (colspan > 1) {
            applyHorizontalMerge(table, actualStartRow, merge.getStartCol(), merge.getEndCol());
        }

        // 垂直合并
        if (rowspan > 1) {
            applyVerticalMerge(table, actualStartRow, actualEndRow, merge.getStartCol());
        }
    }

    /**
     * 验证JSON合并信息是否有效
     */
    private boolean isValidJsonMerge(XWPFTable table, JsonTableExportRequest.MergeConfig merge, int headerRowCount) {
        if (merge == null) {
            log.warn("JSON合并信息为null");
            return false;
        }

        int tableRows = table.getRows().size();
        int tableCols = table.getRow(0).getTableCells().size();

        int actualStartRow = merge.getStartRow() + headerRowCount;
        int actualEndRow = merge.getEndRow() + headerRowCount;

        // 检查行索引
        if (actualStartRow < 0 || actualStartRow >= tableRows) {
            log.warn("JSON合并起始行索引超出范围: {} (表格行数: {})", actualStartRow, tableRows);
            return false;
        }
        if (actualEndRow < 0 || actualEndRow >= tableRows) {
            log.warn("JSON合并结束行索引超出范围: {} (表格行数: {})", actualEndRow, tableRows);
            return false;
        }

        // 检查列索引
        if (merge.getStartCol() < 0 || merge.getStartCol() >= tableCols) {
            log.warn("JSON合并起始列索引超出范围: {} (表格列数: {})", merge.getStartCol(), tableCols);
            return false;
        }
        if (merge.getEndCol() < 0 || merge.getEndCol() >= tableCols) {
            log.warn("JSON合并结束列索引超出范围: {} (表格列数: {})", merge.getEndCol(), tableCols);
            return false;
        }

        // 检查合并范围的逻辑性
        if (actualStartRow > actualEndRow) {
            log.warn("JSON合并起始行大于结束行: {} > {}", actualStartRow, actualEndRow);
            return false;
        }
        if (merge.getStartCol() > merge.getEndCol()) {
            log.warn("JSON合并起始列大于结束列: {} > {}", merge.getStartCol(), merge.getEndCol());
            return false;
        }

        return true;
    }
}
