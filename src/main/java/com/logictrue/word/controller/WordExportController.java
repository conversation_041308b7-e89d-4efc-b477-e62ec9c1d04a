package com.logictrue.word.controller;

import com.logictrue.word.dto.JsonTableExportRequest;
import com.logictrue.word.dto.TableExportRequest;
import com.logictrue.word.service.WordExportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * Word导出控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class WordExportController {

    private final WordExportService wordExportService;

    /**
     * 导出表格到Word文档
     */
    @PostMapping("/exportTable")
    public ResponseEntity<byte[]> exportTable(@RequestBody TableExportRequest request) {
        try {
            log.info("接收到表格导出请求，标题: {}", request.getTitle());

            // 导出Word文档
            byte[] wordBytes = wordExportService.exportTableToWord(request);

            // 生成文件名
            String fileName = generateFileName(request.getTitle());

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentLength(wordBytes.length);

            log.info("表格导出成功，文件名: {}, 大小: {} bytes", fileName, wordBytes.length);

            return new ResponseEntity<>(wordBytes, headers, HttpStatus.OK);

        } catch (IOException e) {
            log.error("导出Word文档失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(("导出失败: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("处理导出请求时发生未知错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(("系统错误: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        }
    }

    /**
     * 导出包含合并单元格的表格到Word文档
     */
    @PostMapping("/exportTableWithMerges")
    public ResponseEntity<byte[]> exportTableWithMerges(@RequestBody JsonTableExportRequest jsonRequest) {
        try {
            log.info("接收到包含合并单元格的表格导出请求，标题: {}", jsonRequest.getTitle());
            log.info("合并单元格数量: {}", jsonRequest.getMerges() != null ? jsonRequest.getMerges().size() : 0);

            // 转换JSON格式的请求为内部格式
            TableExportRequest request = convertJsonRequestToTableRequest(jsonRequest);

            // 导出Word文档
            byte[] wordBytes = wordExportService.exportTableToWordWithJson(request);

            // 生成文件名
            String fileName = generateFileName(jsonRequest.getTitle());

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentLength(wordBytes.length);

            log.info("包含合并单元格的表格导出成功，文件名: {}, 大小: {} bytes", fileName, wordBytes.length);

            return new ResponseEntity<>(wordBytes, headers, HttpStatus.OK);

        } catch (IOException e) {
            log.error("导出包含合并单元格的Word文档失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(("导出失败: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("处理包含合并单元格的导出请求时发生未知错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(("系统错误: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        }
    }

    /**
     * 预览表格导出效果（返回HTML预览）
     */
    @PostMapping("/previewTable")
    public ResponseEntity<String> previewTable(@RequestBody JsonTableExportRequest jsonRequest) {
        try {
            log.info("接收到表格预览请求，标题: {}", jsonRequest.getTitle());

            // 转换JSON格式的请求为内部格式
            TableExportRequest request = convertJsonRequestToTableRequest(jsonRequest);

            // 生成HTML预览
            String htmlPreview = wordExportService.generateTablePreview(request);

            log.info("表格预览生成成功");

            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_HTML)
                    .body(htmlPreview);

        } catch (Exception e) {
            log.error("生成表格预览失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("<html><body><h1>预览生成失败</h1><p>" + e.getMessage() + "</p></body></html>");
        }
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String title) {
        try {
            String baseFileName = (title != null && !title.trim().isEmpty())
                    ? title.trim()
                    : "表格导出";

            // 添加时间戳
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String fileName = baseFileName + "_" + timestamp + ".docx";

            // URL编码文件名以支持中文
            return URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString())
                    .replaceAll("\\+", "%20");

        } catch (Exception e) {
            log.warn("生成文件名失败，使用默认文件名", e);
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            return "table_export_" + timestamp + ".docx";
        }
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/word/wordExport/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("Word导出服务运行正常");
    }

    /**
     * GET方式测试导出接口 - 生成一个简单的测试Word文档
     */
    @GetMapping("/word/wordExport/testExport")
    public ResponseEntity<byte[]> testExport() {
        try {
            log.info("开始测试Word导出...");

            // 创建测试数据
            TableExportRequest testRequest = createTestRequest();

            // 导出Word文档
            byte[] wordBytes = wordExportService.exportTableToWord(testRequest);

            // 生成文件名
            String fileName = generateFileName("测试导出");

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentLength(wordBytes.length);

            log.info("测试导出成功，文件名: {}, 大小: {} bytes", fileName, wordBytes.length);

            return new ResponseEntity<>(wordBytes, headers, HttpStatus.OK);

        } catch (Exception e) {
            log.error("测试导出失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(("测试导出失败: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        }
    }

    /**
     * 创建测试请求数据
     */
    private TableExportRequest createTestRequest() {
        TableExportRequest request = new TableExportRequest();
        request.setTitle("测试检验记录表");
        request.setTableWidth(1200);
        request.setTableHeight(600);
        request.setPageOrientation("LANDSCAPE"); // 默认横向

        // 创建表格数据
        TableExportRequest.TableData tableData = new TableExportRequest.TableData();

        // 创建表头
        List<List<TableExportRequest.HeaderCell>> headers = new ArrayList<>();

        // 第一行表头
        List<TableExportRequest.HeaderCell> headerRow1 = new ArrayList<>();
        headerRow1.add(createHeaderCell("检查工序名称", 2, 1, 120, 50));
        headerRow1.add(createHeaderCell("检查项目及技术条件", 2, 1, 200, 50));
        headerRow1.add(createHeaderCell("实际检查结果", 2, 1, 150, 50));
        headerRow1.add(createHeaderCell("完工", 1, 2, 120, 25));
        headerRow1.add(createHeaderCell("", 1, 1, 0, 25)); // 占位符
        headerRow1.add(createHeaderCell("操作员", 2, 1, 100, 50));
        headerRow1.add(createHeaderCell("班组长", 2, 1, 100, 50));
        headerRow1.add(createHeaderCell("检验员", 2, 1, 100, 50));
        headers.add(headerRow1);

        // 第二行表头
        List<TableExportRequest.HeaderCell> headerRow2 = new ArrayList<>();
        headerRow2.add(createHeaderCell("", 1, 1, 0, 25)); // 占位符
        headerRow2.add(createHeaderCell("", 1, 1, 0, 25)); // 占位符
        headerRow2.add(createHeaderCell("", 1, 1, 0, 25)); // 占位符
        headerRow2.add(createHeaderCell("月", 1, 1, 60, 25));
        headerRow2.add(createHeaderCell("日", 1, 1, 60, 25));
        headerRow2.add(createHeaderCell("", 1, 1, 0, 25)); // 占位符
        headerRow2.add(createHeaderCell("", 1, 1, 0, 25)); // 占位符
        headerRow2.add(createHeaderCell("", 1, 1, 0, 25)); // 占位符
        headers.add(headerRow2);

        tableData.setHeaders(headers);

        // 创建数据行
        List<List<TableExportRequest.DataCell>> dataRows = new ArrayList<>();

        // 测试数据行1
        List<TableExportRequest.DataCell> dataRow1 = new ArrayList<>();
        dataRow1.add(createDataCell("外观检查", false, 120, 50));
        dataRow1.add(createDataCell("表面无划痕、无变形", false, 200, 50));
        dataRow1.add(createDataCell("合格", false, 150, 50));
        dataRow1.add(createDataCell("12", false, 60, 50));
        dataRow1.add(createDataCell("25", false, 60, 50));
        dataRow1.add(createDataCell("张三", false, 100, 50));
        dataRow1.add(createDataCell("李四", false, 100, 50));
        dataRow1.add(createDataCell("王五", false, 100, 50));
        dataRows.add(dataRow1);

        // 测试数据行2 - 包含数学公式
        List<TableExportRequest.DataCell> dataRow2 = new ArrayList<>();
        dataRow2.add(createDataCell("尺寸检查", false, 120, 50));
        dataRow2.add(createDataCell("长度: $L = 100 \\pm 0.1$ mm", true, 200, 50));
        dataRow2.add(createDataCell("$L = 99.95$ mm", true, 150, 50));
        dataRow2.add(createDataCell("12", false, 60, 50));
        dataRow2.add(createDataCell("25", false, 60, 50));
        dataRow2.add(createDataCell("张三", false, 100, 50));
        dataRow2.add(createDataCell("李四", false, 100, 50));
        dataRow2.add(createDataCell("王五", false, 100, 50));
        dataRows.add(dataRow2);

        // 测试数据行3 - 混合内容测试
        List<TableExportRequest.DataCell> dataRow3 = new ArrayList<>();
        dataRow3.add(createDataCell("温度检查", false, 120, 50));
        dataRow3.add(createMixedContentDataCell("温度范围: $T = 25 \\pm 2$ ℃，湿度: $H < 60\\%$", 200, 50));
        dataRow3.add(createMixedContentDataCell("实测: $T = 24.5$ ℃, $H = 55\\%$", 150, 50));
        dataRow3.add(createDataCell("12", false, 60, 50));
        dataRow3.add(createDataCell("26", false, 60, 50));
        dataRow3.add(createDataCell("赵六", false, 100, 50));
        dataRow3.add(createDataCell("钱七", false, 100, 50));
        dataRow3.add(createDataCell("孙八", false, 100, 50));
        dataRows.add(dataRow3);

        // 测试数据行4
        List<TableExportRequest.DataCell> dataRow4 = new ArrayList<>();
        dataRow4.add(createDataCell("功能测试", false, 120, 50));
        dataRow4.add(createDataCell("按规定程序操作", false, 200, 50));
        dataRow4.add(createDataCell("正常", false, 150, 50));
        dataRow4.add(createDataCell("12", false, 60, 50));
        dataRow4.add(createDataCell("27", false, 60, 50));
        dataRow4.add(createDataCell("周九", false, 100, 50));
        dataRow4.add(createDataCell("吴十", false, 100, 50));
        dataRow4.add(createDataCell("郑一", false, 100, 50));
        dataRows.add(dataRow4);

        tableData.setDataRows(dataRows);
        request.setTableData(tableData);

        return request;
    }

    /**
     * 创建表头单元格
     */
    private TableExportRequest.HeaderCell createHeaderCell(String content, Integer rowspan, Integer colspan, Integer width, Integer height) {
        TableExportRequest.HeaderCell cell = new TableExportRequest.HeaderCell();
        cell.setContent(content);
        cell.setRowspan(rowspan);
        cell.setColspan(colspan);
        cell.setWidth(width);
        cell.setHeight(height);
        return cell;
    }

    /**
     * 创建数据单元格
     */
    private TableExportRequest.DataCell createDataCell(String content, Boolean hasMath, Integer width, Integer height) {
        TableExportRequest.DataCell cell = new TableExportRequest.DataCell();
        cell.setContent(content);
        cell.setHasMath(hasMath);
        cell.setWidth(width);
        cell.setHeight(height);
        return cell;
    }

    /**
     * 创建混合内容数据单元格（用于测试）
     * 注意：实际使用中，混合内容的处理应该由前端完成
     */
    private TableExportRequest.DataCell createMixedContentDataCell(String content, Integer width, Integer height) {
        TableExportRequest.DataCell cell = new TableExportRequest.DataCell();

        // 模拟前端处理后的混合内容
        // 实际场景中，这些数据应该由前端的math-formula-utils.js处理后传递过来
        cell.setContent("温度范围: __MATH_FORMULA_0__ ℃，湿度: __MATH_FORMULA_1__"); // 示例占位符内容
        cell.setHasMath(true);
        cell.setHasMultipleContent(true);

        // 模拟占位符到MathML的映射
        java.util.Map<String, String> mathMLMap = new java.util.HashMap<>();
        mathMLMap.put("__MATH_FORMULA_0__", "<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>T</mi><mo>=</mo><mn>25</mn><mo>&#177;</mo><mn>2</mn></math>");
        mathMLMap.put("__MATH_FORMULA_1__", "<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>H</mi><mo>&lt;</mo><mn>60</mn><mi>%</mi></math>");
        cell.setMathMLMap(mathMLMap);

        cell.setWidth(width);
        cell.setHeight(height);
        return cell;
    }

    /**
     * 将JSON格式的请求转换为内部TableExportRequest格式
     */
    private TableExportRequest convertJsonRequestToTableRequest(JsonTableExportRequest jsonRequest) {
        TableExportRequest request = new TableExportRequest();

        // 设置基本信息
        request.setTitle(jsonRequest.getTitle());
        request.setTableWidth(1200); // 默认宽度
        request.setTableHeight(600);  // 默认高度
        request.setPageOrientation("LANDSCAPE"); // 默认横向

        // 转换数据行合并单元格信息
        if (jsonRequest.getMerges() != null && !jsonRequest.getMerges().isEmpty()) {
            List<TableExportRequest.MergeCell> merges = new ArrayList<>();
            for (JsonTableExportRequest.MergeConfig mergeConfig : jsonRequest.getMerges()) {
                TableExportRequest.MergeCell mergeCell = new TableExportRequest.MergeCell();
                mergeCell.setStartRow(mergeConfig.getStartRow());
                mergeCell.setStartCol(mergeConfig.getStartCol());
                mergeCell.setEndRow(mergeConfig.getEndRow());
                mergeCell.setEndCol(mergeConfig.getEndCol());
                mergeCell.setContent(mergeConfig.getContent());
                merges.add(mergeCell);
            }
            request.setMerges(merges);
        }

        // 转换表头合并单元格信息
        if (jsonRequest.getHeaderMerges() != null && !jsonRequest.getHeaderMerges().isEmpty()) {
            List<TableExportRequest.MergeCell> headerMerges = new ArrayList<>();
            for (JsonTableExportRequest.MergeConfig mergeConfig : jsonRequest.getHeaderMerges()) {
                TableExportRequest.MergeCell mergeCell = new TableExportRequest.MergeCell();
                mergeCell.setStartRow(mergeConfig.getStartRow());
                mergeCell.setStartCol(mergeConfig.getStartCol());
                mergeCell.setEndRow(mergeConfig.getEndRow());
                mergeCell.setEndCol(mergeConfig.getEndCol());
                mergeCell.setContent(mergeConfig.getContent());
                headerMerges.add(mergeCell);
            }
            request.setHeaderMerges(headerMerges);
        }

        // 转换表头宽度配置
        if (jsonRequest.getHeaderWidthConfig() != null) {
            TableExportRequest.HeaderWidthConfig headerWidthConfig = new TableExportRequest.HeaderWidthConfig();

            // 转换列宽数组
            if (jsonRequest.getHeaderWidthConfig().getColumnWidths() != null) {
                headerWidthConfig.setColumnWidths(jsonRequest.getHeaderWidthConfig().getColumnWidths());
            }

            // 转换表头行高数组
            if (jsonRequest.getHeaderWidthConfig().getHeaderHeights() != null) {
                headerWidthConfig.setHeaderHeights(jsonRequest.getHeaderWidthConfig().getHeaderHeights());
            }

            request.setHeaderWidthConfig(headerWidthConfig);
            log.debug("转换表头宽度配置: 列宽={}, 行高={}",
                     headerWidthConfig.getColumnWidths(),
                     headerWidthConfig.getHeaderHeights());
        }

        // 转换表头纵向文字配置
        if (jsonRequest.getVerticalHeadersConfig() != null) {
            request.setVerticalHeadersConfig(jsonRequest.getVerticalHeadersConfig());
            log.debug("转换表头纵向文字配置: {}", jsonRequest.getVerticalHeadersConfig());
        }

        // 转换元数据
        if (jsonRequest.getMetadata() != null) {
            TableExportRequest.Metadata metadata = new TableExportRequest.Metadata();
            metadata.setExportTime(jsonRequest.getMetadata().getExportTime());
            metadata.setTotalRows(jsonRequest.getMetadata().getTotalRows());
            metadata.setTotalColumns(jsonRequest.getMetadata().getTotalColumns());
            metadata.setHasMergedCells(jsonRequest.getMetadata().getHasMergedCells());
            request.setMetadata(metadata);
        }

        // 创建表格数据
        TableExportRequest.TableData tableData = new TableExportRequest.TableData();

        // 转换表头（支持多行表头）
        if (jsonRequest.getHeaders() != null && !jsonRequest.getHeaders().isEmpty()) {
            List<List<TableExportRequest.HeaderCell>> headers = new ArrayList<>();

            // 遍历每一行表头
            for (int rowIndex = 0; rowIndex < jsonRequest.getHeaders().size(); rowIndex++) {
                List<String> headerRowData = jsonRequest.getHeaders().get(rowIndex);
                List<TableExportRequest.HeaderCell> headerRow = new ArrayList<>();

                // 遍历每一列
                for (int colIndex = 0; colIndex < headerRowData.size(); colIndex++) {
                    String headerContent = headerRowData.get(colIndex);
                    TableExportRequest.HeaderCell headerCell = new TableExportRequest.HeaderCell();
                    headerCell.setContent(headerContent);
                    headerCell.setWidth(150); // 默认宽度
                    headerCell.setHeight(50); // 默认高度
                    headerCell.setIsVertical(false);

                    // 检查是否为表头合并单元格，设置rowspan和colspan
                    setHeaderCellMergeInfo(headerCell, jsonRequest.getHeaderMerges(), rowIndex, colIndex);

                    headerRow.add(headerCell);
                }
                headers.add(headerRow);
            }
            tableData.setHeaders(headers);
        }

        // 转换数据行（优先使用复杂格式，回退到简单格式）
        if (jsonRequest.getCellRows() != null && !jsonRequest.getCellRows().isEmpty()) {
            // 使用复杂格式（支持公式和宽度配置）
            List<List<TableExportRequest.DataCell>> dataRows = new ArrayList<>();

            for (int rowIndex = 0; rowIndex < jsonRequest.getCellRows().size(); rowIndex++) {
                List<JsonTableExportRequest.CellData> jsonRow = jsonRequest.getCellRows().get(rowIndex);
                List<TableExportRequest.DataCell> dataRow = new ArrayList<>();

                for (int colIndex = 0; colIndex < jsonRow.size(); colIndex++) {
                    JsonTableExportRequest.CellData cellData = jsonRow.get(colIndex);
                    TableExportRequest.DataCell dataCell = new TableExportRequest.DataCell();

                    // 设置基本内容
                    dataCell.setContent(cellData.getContent());
                    dataCell.setHasMath(cellData.getHasMath() != null ? cellData.getHasMath() : false);

                    // 设置公式相关信息
                    dataCell.setMathML(cellData.getMathML());
                    dataCell.setHasMultipleContent(cellData.getHasMultipleContent() != null ? cellData.getHasMultipleContent() : false);
                    dataCell.setMathMLMap(cellData.getMathMLMap());

                    // 设置尺寸（使用传入的值或默认值）
                    dataCell.setWidth(cellData.getWidth() != null ? cellData.getWidth() : 150);
                    dataCell.setHeight(cellData.getHeight() != null ? cellData.getHeight() : 50);

                    // 检查是否为合并单元格，设置rowspan和colspan
                    setCellMergeInfo(dataCell, jsonRequest.getMerges(), rowIndex, colIndex);

                    dataRow.add(dataCell);
                }
                dataRows.add(dataRow);
            }
            tableData.setDataRows(dataRows);

        } else if (jsonRequest.getRows() != null && !jsonRequest.getRows().isEmpty()) {
            // 使用简单格式（向后兼容）
            List<List<TableExportRequest.DataCell>> dataRows = new ArrayList<>();

            for (int rowIndex = 0; rowIndex < jsonRequest.getRows().size(); rowIndex++) {
                List<String> jsonRow = jsonRequest.getRows().get(rowIndex);
                List<TableExportRequest.DataCell> dataRow = new ArrayList<>();

                for (int colIndex = 0; colIndex < jsonRow.size(); colIndex++) {
                    String cellContent = jsonRow.get(colIndex);
                    TableExportRequest.DataCell dataCell = new TableExportRequest.DataCell();
                    dataCell.setContent(cellContent);
                    dataCell.setHasMath(containsMathFormula(cellContent));
                    dataCell.setWidth(150); // 默认宽度
                    dataCell.setHeight(50); // 默认高度

                    // 检查是否为合并单元格，设置rowspan和colspan
                    setCellMergeInfo(dataCell, jsonRequest.getMerges(), rowIndex, colIndex);

                    dataRow.add(dataCell);
                }
                dataRows.add(dataRow);
            }
            tableData.setDataRows(dataRows);
        }

        request.setTableData(tableData);

        log.info("JSON请求转换完成，表头数量: {}, 数据行数量: {}, 合并单元格数量: {}",
                jsonRequest.getHeaders() != null ? jsonRequest.getHeaders().size() : 0,
                jsonRequest.getRows() != null ? jsonRequest.getRows().size() : 0,
                jsonRequest.getMerges() != null ? jsonRequest.getMerges().size() : 0);

        return request;
    }

    /**
     * 检查内容是否包含数学公式
     */
    private boolean containsMathFormula(String content) {
        if (content == null || content.trim().isEmpty()) {
            return false;
        }
        // 简单检查是否包含LaTeX数学公式标记
        return content.contains("$") || content.contains("\\") ||
               content.contains("^") || content.contains("_");
    }

    /**
     * 为数据单元格设置合并信息
     */
    private void setCellMergeInfo(TableExportRequest.DataCell dataCell,
                                  List<JsonTableExportRequest.MergeConfig> merges,
                                  int rowIndex, int colIndex) {
        if (merges == null || merges.isEmpty()) {
            return;
        }

        for (JsonTableExportRequest.MergeConfig merge : merges) {
            if (merge.getStartRow().equals(rowIndex) && merge.getStartCol().equals(colIndex)) {
                // 这是合并单元格的主单元格
                int rowspan = merge.getEndRow() - merge.getStartRow() + 1;
                int colspan = merge.getEndCol() - merge.getStartCol() + 1;
                dataCell.setRowspan(rowspan);
                dataCell.setColspan(colspan);

                // 如果合并单元格有内容，使用合并单元格的内容
                if (merge.getContent() != null && !merge.getContent().trim().isEmpty()) {
                    dataCell.setContent(merge.getContent());
                    dataCell.setHasMath(containsMathFormula(merge.getContent()));
                }
                break;
            }
        }
    }

    /**
     * 为表头单元格设置合并信息
     */
    private void setHeaderCellMergeInfo(TableExportRequest.HeaderCell headerCell,
                                       List<JsonTableExportRequest.MergeConfig> headerMerges,
                                       int rowIndex, int colIndex) {
        if (headerMerges == null || headerMerges.isEmpty()) {
            // 没有合并配置，设置默认值
            headerCell.setRowspan(1);
            headerCell.setColspan(1);
            return;
        }

        for (JsonTableExportRequest.MergeConfig merge : headerMerges) {
            if (merge.getStartRow().equals(rowIndex) && merge.getStartCol().equals(colIndex)) {
                // 这是合并单元格的主单元格
                int rowspan = merge.getEndRow() - merge.getStartRow() + 1;
                int colspan = merge.getEndCol() - merge.getStartCol() + 1;
                headerCell.setRowspan(rowspan);
                headerCell.setColspan(colspan);

                // 如果合并单元格有内容，使用合并单元格的内容
                if (merge.getContent() != null && !merge.getContent().trim().isEmpty()) {
                    headerCell.setContent(merge.getContent());
                }

                log.debug("设置表头合并单元格: ({},{}) rowspan={} colspan={} content={}",
                         rowIndex, colIndex, rowspan, colspan, headerCell.getContent());
                return;
            }
        }

        // 检查是否被其他合并单元格覆盖
        for (JsonTableExportRequest.MergeConfig merge : headerMerges) {
            if (rowIndex >= merge.getStartRow() && rowIndex <= merge.getEndRow() &&
                colIndex >= merge.getStartCol() && colIndex <= merge.getEndCol() &&
                !(merge.getStartRow().equals(rowIndex) && merge.getStartCol().equals(colIndex))) {
                // 这个单元格被其他合并单元格覆盖，设置为隐藏
                headerCell.setRowspan(0);
                headerCell.setColspan(0);
                headerCell.setContent(""); // 清空内容
                log.debug("表头单元格被合并覆盖: ({},{})", rowIndex, colIndex);
                return;
            }
        }

        // 普通单元格，设置默认值
        headerCell.setRowspan(1);
        headerCell.setColspan(1);
    }
}
