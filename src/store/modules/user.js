import {
  login,
  logout,
  getInfo,
  refreshToken,
  getPublicKey,
  cardLogin,
  getImageBase64,
} from '@/api/login';
import { getToken, setToken, setExpiresIn, removeToken } from '@/utils/auth';
import { encrypt } from '@/utils/jsencrypt';
import Cookies from "js-cookie";
const user = {
  state: {
    token: getToken(),
    name: '',
    nickName: '',
    userId: '',
    avatar: '',
    roles: [],
    permissions: [],
    dept: {
      deptId: '',
      deptName: '',
    },
    messageTime: null,
    postIds: [],
    age: '',
    userNumber: '',
    post: '',
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token;
    },
    SET_AGE: (state, age) => {
      state.age = age;
    },
    SET_POST: (state, post) => {
      state.post = post;
    },
    SET_USER_Number: (state, userNumber) => {
      state.userNumber = userNumber;
    },
    SET_EXPIRES_IN: (state, time) => {
      state.expires_in = time;
    },
    SET_NAME: (state, name) => {
      state.name = name;
    },
    SET_NICK_NAME: (state, name) => {
      state.nickName = name;
    },
    SET_USER_ID: (state, name) => {
      state.userId = name;
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar;
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles;
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions;
    },
    SET_DEPT: (state, { deptId, deptName }) => {
      state.dept.deptId = deptId;
      state.dept.deptName = deptName;
    },
    SET_POSTIDS: (state, postIds) => {
      state.postIds = postIds;
    },
  },

  actions: {
    Login({ commit }, userInfo) {
      const username = userInfo.username.trim();
      return new Promise((resolve, reject) => {
        // 后台请求时 密码通过 base64加密
        const password = encrypt(userInfo.password, userInfo.publicKey);
        const code = userInfo.code;
        const uuid = userInfo.uuid;
        const type = userInfo.type;
        login(username, password, code, uuid, type)
          .then((res) => {
            let data = res.data;

            setToken(data.access_token);
            commit('SET_TOKEN', data.access_token);
            // setToken('fa72237e-6c4f-4bf2-b237-c4f2e629a829');
            // commit('SET_TOKEN', 'fa72237e-6c4f-4bf2-b237-c4f2e629a829');
            setExpiresIn(data.expires_in);
            commit('SET_EXPIRES_IN', data.expires_in);
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    cardLogin({ commit }, userInfo) {
      return new Promise((resolve, reject) => {
        cardLogin(userInfo)
          .then((res) => {
            let data = res.data;

            setToken(data.access_token);
            commit('SET_TOKEN', data.access_token);
            // setToken('fa72237e-6c4f-4bf2-b237-c4f2e629a829');
            // commit('SET_TOKEN', 'fa72237e-6c4f-4bf2-b237-c4f2e629a829');
            setExpiresIn(data.expires_in);
            commit('SET_EXPIRES_IN', data.expires_in);
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 获取用户信息
    GetInfo({ commit, rootState }) {
      let cookieMenuType = Cookies.get("type")
      return new Promise((resolve, reject) => {
        getInfo(cookieMenuType)
          .then((res) => {
            const user = res.data.user;
            const avatar = user.avatar;
            if (res.roles && res.roles.length > 0) {
              // 验证返回的roles是否是一个非空数组
              commit('SET_ROLES', res.roles);
              commit('SET_PERMISSIONS', res.permissions);
            } else {
              commit('SET_ROLES', ['ROLE_DEFAULT']);
            }
            commit('SET_NAME', user.userName);
            commit('SET_NICK_NAME', user.nickName);
            commit('SET_USER_ID', user.userId);
            commit('SET_AVATAR', avatar);

            // commit('SET_DEPT', user.dept);
            commit('SET_USER_Number', user.userNumber);
            commit('SET_AGE', user.age);
            commit('SET_POST', user.post);

            commit('SET_POSTIDS', user.postIds || []);

            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 刷新token
    RefreshToken({ commit, state }) {
      return new Promise((resolve, reject) => {
        refreshToken(state.token)
          .then((res) => {
            setExpiresIn(res.data);
            commit('SET_EXPIRES_IN', res.data);
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token)
          .then(() => {
            commit('SET_TOKEN', '');
            commit('SET_ROLES', []);
            commit('SET_PERMISSIONS', []);
            removeToken();
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise((resolve) => {
        commit('SET_TOKEN', '');
        removeToken();
        resolve();
      });
    },
  },
};

export default user;
