import { constantRoutes } from '@/router'
import Cookies from "js-cookie";


// 过滤路由的函数
const filterRoutes = (routes) => {
  return routes.filter(route => {
    // 如果有子路由，递归过滤
    if (route.children) {
      route.children = filterRoutes(route.children);
    }

    // 只保留同时具有 meta.title 和 name 的路由
    return route.name && route.meta && route.meta.title;
  });
};

// 过滤constantRoutes
const filteredRoutes = filterRoutes(constantRoutes);


const permission = {
  state: {
    routes: filteredRoutes,
    addRoutes: [],
    defaultRoutes: filteredRoutes,
    topbarRouters: filteredRoutes,
    sidebarRouters: filteredRoutes,
    indexRouters: filteredRoutes,
    requestUrl:"127.0.0.1"
  },
  mutations: {
    SET_ROUTES: (state, routes) => {
      state.addRoutes = routes
      state.routes = filteredRoutes.concat(routes)
    },
    SET_DEFAULT_ROUTES: (state, routes) => {
      state.defaultRoutes = filteredRoutes
    },
    SET_TOPBAR_ROUTERS: (state, routes) => {
      state.topbarRouters = filteredRoutes
    },
    SET_SIDEBAR_ROUTERS: (state, routes) => {
      state.sidebarRouters = filteredRoutes
    },
    SET_INDEX_ROUTERS: (state, routes) => {
      state.indexRouters = filteredRoutes
    }
  },
  actions: {
    // 生成路由 - 直接使用路由配置，不从后端获取
    GenerateRoutes({ commit }) {
      return new Promise(resolve => {
        const routes = filteredRoutes
        commit('SET_ROUTES', routes)
        commit('SET_DEFAULT_ROUTES', routes)
        commit('SET_TOPBAR_ROUTERS', routes)
        commit('SET_SIDEBAR_ROUTERS', routes)
        resolve(routes)
      })
    }
  }
}

export default permission
