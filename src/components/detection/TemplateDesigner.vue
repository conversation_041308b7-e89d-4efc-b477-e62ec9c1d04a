<template>
  <div class="template-designer">
    <div class="designer-header">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form :model="templateInfo" :rules="templateRules" ref="templateForm" label-width="100px" size="small">
            <el-form-item label="模板名称" prop="name">
              <el-input v-model="templateInfo.name" placeholder="请输入模板名称"></el-input>
            </el-form-item>
            <el-form-item label="模板编码" prop="code">
              <el-input v-model="templateInfo.code" placeholder="请输入模板编码"></el-input>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="12">
          <el-form :model="templateInfo" label-width="100px" size="small">
            <el-form-item label="设备类型" prop="deviceTypeId">
              <el-select v-model="templateInfo.deviceTypeId" placeholder="请选择设备类型" style="width: 100%">
                <el-option
                  v-for="item in (deviceTypeOptions || [])"
                  :key="item.id"
                  :label="item.typeName"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="模板描述">
              <el-input v-model="templateInfo.description" type="textarea" :rows="2" placeholder="请输入模板描述"></el-input>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>

    <div class="designer-body">
      <el-row :gutter="20">
        <!-- 字段库 -->
        <el-col :span="6">
          <div class="field-library">
            <div class="library-header">
              <h4>字段库</h4>
            </div>
            <div class="library-content">
              <el-collapse v-model="activeLibrary">
                <el-collapse-item title="基础字段" name="basic">
                  <div class="field-list">
                    <div
                      v-for="field in basicFieldLibrary"
                      :key="field.type"
                      class="field-item"
                      draggable="true"
                      @dragstart="handleDragStart($event, { ...field, category: 'basic' }, 'basic')"
                    >
                      <i :class="field.icon"></i>
                      <span>{{ field.name }}</span>
                    </div>
                  </div>
                </el-collapse-item>
                <el-collapse-item title="表格字段" name="table">
                  <div class="field-list">
                    <div
                      v-for="field in tableFieldLibrary"
                      :key="field.type"
                      class="field-item"
                      draggable="true"
                      @dragstart="handleDragStart($event, { ...field, category: 'table' }, 'table')"
                    >
                      <i :class="field.icon"></i>
                      <span>{{ field.name }}</span>
                    </div>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
        </el-col>

        <!-- 设计区域 -->
        <el-col :span="12">
          <div class="design-area">
            <el-tabs v-model="activeTab" type="card">
              <el-tab-pane label="基础数据配置" name="basic">
                <div class="basic-fields-container">
                  <div class="container-header">
                    <span>基础数据字段</span>
                    <el-button type="text" size="mini" @click="clearBasicFields">清空</el-button>
                  </div>
                  <div
                    class="drop-zone"
                    :class="{ 'drag-over': dragOverBasic }"
                    @drop="handleDrop($event, 'basic')"
                    @dragover.prevent="handleDragOver($event, 'basic')"
                    @dragenter.prevent="handleDragEnter($event, 'basic')"
                    @dragleave="handleDragLeave($event, 'basic')"
                  >
                    <draggable
                      v-model="templateConfig.basicFields"
                      group="fields"
                      @start="drag = true"
                      @end="drag = false"
                      item-key="code"
                    >
                      <template #item="{ element, index }">
                        <div class="field-config-item" @click="selectField(element, 'basic', index)">
                          <div class="field-header">
                            <span class="field-label">{{ element.name }}</span>
                            <div class="field-actions">
                              <el-button type="text" size="mini" @click.stop="editField(element, 'basic', index)">
                                <i class="el-icon-edit"></i>
                              </el-button>
                              <el-button type="text" size="mini" @click.stop="removeField('basic', index)">
                                <i class="el-icon-delete"></i>
                              </el-button>
                            </div>
                          </div>
                          <div class="field-preview">
                            <component
                              :is="getFieldComponent(element.type)"
                              v-bind="getFieldProps(element)"
                              :disabled="true"
                            />
                          </div>
                        </div>
                      </template>
                    </draggable>
                    <div v-if="templateConfig.basicFields.length === 0" class="empty-drop-zone">
                      <i class="el-icon-plus"></i>
                      <p>拖拽字段到此处配置基础数据</p>
                    </div>
                  </div>
                </div>
              </el-tab-pane>

              <el-tab-pane label="表格数据配置" name="table">
                <div class="table-fields-container">
                  <div class="container-header">
                    <span>表格字段</span>
                    <el-button type="text" size="mini" @click="clearTableFields">清空</el-button>
                  </div>
                  <div
                    class="drop-zone"
                    :class="{ 'drag-over': dragOverTable }"
                    @drop="handleDrop($event, 'table')"
                    @dragover.prevent="handleDragOver($event, 'table')"
                    @dragenter.prevent="handleDragEnter($event, 'table')"
                    @dragleave="handleDragLeave($event, 'table')"
                  >
                    <div class="table-preview">
                      <el-table :data="[{}]" border size="mini">
                        <el-table-column
                          v-for="(element, index) in templateConfig.tableFields"
                          :key="element.code"
                          :prop="element.code"
                          :label="element.name"
                          :width="element.width || 150"
                        >
                          <template #header>
                            <div class="table-header-cell" @click="selectField(element, 'table', index)">
                              <span>{{ element.name }}</span>
                              <div class="field-actions">
                                <el-button type="text" size="mini" @click.stop="editField(element, 'table', index)">
                                  <i class="el-icon-edit"></i>
                                </el-button>
                                <el-button type="text" size="mini" @click.stop="removeField('table', index)">
                                  <i class="el-icon-delete"></i>
                                </el-button>
                              </div>
                            </div>
                          </template>
                          <template #default>
                            <component
                              :is="getFieldComponent(element.type)"
                              v-bind="getFieldProps(element)"
                              :disabled="true"
                              size="mini"
                            />
                          </template>
                        </el-table-column>
                      </el-table>

                      <!-- 表格字段拖拽区域 -->
                      <div class="table-fields-drag-area">
                        <draggable
                          v-model="templateConfig.tableFields"
                          group="fields"
                          @start="drag = true"
                          @end="drag = false"
                          item-key="code"
                          class="table-fields-list"
                        >
                          <template #item="{ element, index }">
                            <div class="table-field-item" @click="selectField(element, 'table', index)">
                              <div class="field-header">
                                <span class="field-label">{{ element.name }}</span>
                                <div class="field-actions">
                                  <el-button type="text" size="mini" @click.stop="editField(element, 'table', index)">
                                    <i class="el-icon-edit"></i>
                                  </el-button>
                                  <el-button type="text" size="mini" @click.stop="removeField('table', index)">
                                    <i class="el-icon-delete"></i>
                                  </el-button>
                                </div>
                              </div>
                              <div class="field-info">
                                <span class="field-type">{{ element.type }}</span>
                                <span class="field-width">宽度: {{ element.width || 150 }}px</span>
                              </div>
                            </div>
                          </template>
                        </draggable>
                      </div>
                    </div>
                    <div v-if="templateConfig.tableFields.length === 0" class="empty-drop-zone">
                      <i class="el-icon-plus"></i>
                      <p>拖拽字段到此处配置表格列</p>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-col>

        <!-- 属性配置 -->
        <el-col :span="6">
          <div class="property-panel">
            <div class="panel-header">
              <h4>属性配置</h4>
            </div>
            <div class="panel-content">
              <field-property-editor
                v-if="selectedField"
                :field="selectedField"
                :field-type="selectedFieldType"
                @update="handleFieldUpdate"
              />
              <div v-else class="no-selection">
                <p>请选择一个字段进行配置</p>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <div class="designer-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button @click="handlePreview">预览</el-button>
      <el-button type="primary" @click="handleSave">保存模板</el-button>
    </div>

    <!-- 字段编辑对话框 -->
    <field-edit-dialog
      :visible.sync="fieldEditVisible"
      :field="editingField"
      :field-type="editingFieldType"
      @confirm="handleFieldEditConfirm"
    />
  </div>
</template>

<script>
import draggable from 'vuedraggable'
import FieldPropertyEditor from './FieldPropertyEditor.vue'
import FieldEditDialog from './FieldEditDialog.vue'
import { getDeviceTypeOptions } from '@/api/detection/deviceTypes'

export default {
  name: 'TemplateDesigner',
  components: {
    draggable,
    FieldPropertyEditor,
    FieldEditDialog
  },
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    mode: {
      type: String,
      default: 'create' // create, edit
    }
  },
  data() {
    return {
      drag: false,
      dragOverBasic: false,
      dragOverTable: false,
      activeTab: 'basic',
      activeLibrary: ['basic', 'table'],
      selectedField: null,
      selectedFieldType: null,
      selectedFieldIndex: -1,
      fieldEditVisible: false,
      editingField: null,
      editingFieldType: null,
      editingFieldIndex: -1,
      deviceTypeOptions: [],
      templateInfo: {
        name: '',
        code: '',
        deviceTypeId: null,
        description: ''
      },
      templateConfig: {
        version: '1.0',
        basicFields: [],
        tableFields: [],
        excelConfig: {
          sheetName: '检测数据',
          startRow: 1,
          basicDataStartRow: 1,
          tableDataStartRow: 5
        }
      },
      templateRules: {
        name: [
          { required: true, message: '请输入模板名称', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入模板编码', trigger: 'blur' }
        ]
      },
      basicFieldLibrary: [
        { type: 'text', name: '单行文本', icon: 'el-icon-edit-outline' },
        { type: 'textarea', name: '多行文本', icon: 'el-icon-document' },
        { type: 'number', name: '数字', icon: 'el-icon-s-data' },
        { type: 'date', name: '日期', icon: 'el-icon-date' },
        { type: 'datetime', name: '日期时间', icon: 'el-icon-time' },
        { type: 'select', name: '下拉选择', icon: 'el-icon-arrow-down' },
        { type: 'radio', name: '单选', icon: 'el-icon-success' },
        { type: 'checkbox', name: '多选', icon: 'el-icon-check' }
      ],
      tableFieldLibrary: [
        { type: 'text', name: '文本列', icon: 'el-icon-edit-outline' },
        { type: 'number', name: '数字列', icon: 'el-icon-s-data' },
        { type: 'select', name: '选择列', icon: 'el-icon-arrow-down' },
        { type: 'date', name: '日期列', icon: 'el-icon-date' },
        { type: 'textarea', name: '文本域列', icon: 'el-icon-document' }
      ]
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val && Object.keys(val).length > 0) {
          this.initTemplate(val)
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.loadDeviceTypes()
  },
  methods: {
    // 初始化模板数据
    initTemplate(template) {
      if (!template) {
        return
      }
      this.templateInfo = {
        name: template.templateName || '',
        code: template.templateCode || '',
        deviceTypeId: template.deviceTypeId || null,
        description: template.description || ''
      }
      if (template.templateConfig) {
        this.templateConfig = { ...this.templateConfig, ...template.templateConfig }
      }
    },

    // 加载设备类型
    async loadDeviceTypes() {
      try {
        const response = await getDeviceTypeOptions()
        this.deviceTypeOptions = response || []
      } catch (error) {
        console.error('加载设备类型失败:', error)
        this.deviceTypeOptions = []
        this.$message.error('加载设备类型失败')
      }
    },

    // 拖拽开始
    handleDragStart(event, field, category) {
      console.log('拖拽开始:', field, category)
      event.dataTransfer.effectAllowed = 'copy'
      const dragData = { ...field, category }
      event.dataTransfer.setData('text/plain', JSON.stringify(dragData))
      event.dataTransfer.setData('field', JSON.stringify(dragData))
      console.log('拖拽数据:', dragData)
    },

    // 拖拽放置
    handleDrop(event, targetCategory) {
      event.preventDefault()
      event.stopPropagation()
      console.log('拖拽放置:', targetCategory)

      // 重置拖拽状态
      this.dragOverBasic = false
      this.dragOverTable = false

      let fieldData
      try {
        fieldData = JSON.parse(event.dataTransfer.getData('field') || event.dataTransfer.getData('text/plain'))
      } catch (error) {
        console.error('解析拖拽数据失败:', error)
        this.$message.error('拖拽数据格式错误')
        return
      }

      if (!fieldData || !fieldData.type) {
        console.error('拖拽数据无效:', fieldData)
        this.$message.error('拖拽数据无效')
        return
      }

      if (fieldData.category !== targetCategory) {
        this.$message.warning('字段类型不匹配')
        return
      }

      console.log('添加字段:', fieldData, targetCategory)
      this.addField(fieldData, targetCategory)
    },

    // 添加字段
    addField(fieldTemplate, category) {
      console.log('开始添加字段:', fieldTemplate, category)

      const field = {
        code: `${fieldTemplate.type}_${Date.now()}`,
        name: fieldTemplate.name,
        type: fieldTemplate.type,
        required: false,
        defaultValue: '',
        placeholder: `请输入${fieldTemplate.name}`,
        order: category === 'basic' ? this.templateConfig.basicFields.length + 1 : this.templateConfig.tableFields.length + 1
      }

      if (category === 'table') {
        field.width = 150
      }

      if (fieldTemplate.type === 'select' || fieldTemplate.type === 'radio' || fieldTemplate.type === 'checkbox') {
        field.options = ['选项1', '选项2']
      }

      if (category === 'basic') {
        this.templateConfig.basicFields.push(field)
        console.log('添加基础字段成功:', field)
        console.log('当前基础字段列表:', this.templateConfig.basicFields)
      } else {
        this.templateConfig.tableFields.push(field)
        console.log('添加表格字段成功:', field)
        console.log('当前表格字段列表:', this.templateConfig.tableFields)
      }

      // 强制更新视图
      this.$forceUpdate()
    },

    // 拖拽进入
    handleDragEnter(event, targetCategory) {
      event.preventDefault()
      if (targetCategory === 'basic') {
        this.dragOverBasic = true
      } else {
        this.dragOverTable = true
      }
    },

    // 拖拽悬停
    handleDragOver(event, targetCategory) {
      event.preventDefault()
      event.dataTransfer.dropEffect = 'copy'
    },

    // 拖拽离开
    handleDragLeave(event, targetCategory) {
      // 检查是否真的离开了拖拽区域
      const rect = event.currentTarget.getBoundingClientRect()
      const x = event.clientX
      const y = event.clientY

      if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
        if (targetCategory === 'basic') {
          this.dragOverBasic = false
        } else {
          this.dragOverTable = false
        }
      }
    },

    // 选择字段
    selectField(field, type, index) {
      this.selectedField = { ...field }
      this.selectedFieldType = type
      this.selectedFieldIndex = index
    },

    // 编辑字段
    editField(field, type, index) {
      this.editingField = { ...field }
      this.editingFieldType = type
      this.editingFieldIndex = index
      this.fieldEditVisible = true
    },

    // 删除字段
    removeField(type, index) {
      if (type === 'basic') {
        this.templateConfig.basicFields.splice(index, 1)
      } else {
        this.templateConfig.tableFields.splice(index, 1)
      }
      
      // 清除选中状态
      if (this.selectedFieldIndex === index && this.selectedFieldType === type) {
        this.selectedField = null
        this.selectedFieldType = null
        this.selectedFieldIndex = -1
      }
    },

    // 清空基础字段
    clearBasicFields() {
      this.$confirm('确定要清空所有基础字段吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.templateConfig.basicFields = []
        this.selectedField = null
        this.selectedFieldType = null
        this.selectedFieldIndex = -1
      })
    },

    // 清空表格字段
    clearTableFields() {
      this.$confirm('确定要清空所有表格字段吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.templateConfig.tableFields = []
        this.selectedField = null
        this.selectedFieldType = null
        this.selectedFieldIndex = -1
      })
    },

    // 字段属性更新
    handleFieldUpdate(updatedField) {
      if (this.selectedFieldType === 'basic') {
        this.$set(this.templateConfig.basicFields, this.selectedFieldIndex, updatedField)
      } else {
        this.$set(this.templateConfig.tableFields, this.selectedFieldIndex, updatedField)
      }
      this.selectedField = { ...updatedField }
    },

    // 字段编辑确认
    handleFieldEditConfirm(updatedField) {
      if (this.editingFieldType === 'basic') {
        this.$set(this.templateConfig.basicFields, this.editingFieldIndex, updatedField)
      } else {
        this.$set(this.templateConfig.tableFields, this.editingFieldIndex, updatedField)
      }
      this.fieldEditVisible = false
    },

    // 获取字段组件
    getFieldComponent(type) {
      const componentMap = {
        text: 'el-input',
        textarea: 'el-input',
        number: 'el-input-number',
        date: 'el-date-picker',
        datetime: 'el-date-picker',
        select: 'el-select',
        radio: 'el-radio-group',
        checkbox: 'el-checkbox-group'
      }
      return componentMap[type] || 'el-input'
    },

    // 获取字段属性
    getFieldProps(field) {
      const props = {
        placeholder: field.placeholder || `请输入${field.name}`,
        disabled: true
      }

      if (field.type === 'textarea') {
        props.type = 'textarea'
        props.rows = 2
      } else if (field.type === 'number') {
        props.min = field.min
        props.max = field.max
        props.step = field.step || 1
      } else if (field.type === 'date') {
        props.type = 'date'
        props.format = 'yyyy-MM-dd'
        props.valueFormat = 'yyyy-MM-dd'
      } else if (field.type === 'datetime') {
        props.type = 'datetime'
        props.format = 'yyyy-MM-dd HH:mm:ss'
        props.valueFormat = 'yyyy-MM-dd HH:mm:ss'
      }

      return props
    },

    // 预览
    handlePreview() {
      this.$emit('preview', this.getTemplateData())
    },

    // 保存
    handleSave() {
      this.$refs.templateForm.validate((valid) => {
        if (valid) {
          this.$emit('save', this.getTemplateData())
        }
      })
    },

    // 取消
    handleCancel() {
      this.$emit('cancel')
    },

    // 获取设备类型名称
    getDeviceTypeName() {
      if (!this.deviceTypeOptions || !this.templateInfo.deviceTypeId) {
        return ''
      }
      const deviceType = this.deviceTypeOptions.find(item => item.id === this.templateInfo.deviceTypeId)
      return deviceType ? deviceType.typeName : ''
    },

    // 获取模板数据
    getTemplateData() {
      return {
        templateName: this.templateInfo.name,
        templateCode: this.templateInfo.code,
        deviceTypeId: this.templateInfo.deviceTypeId,
        description: this.templateInfo.description,
        templateConfig: {
          ...this.templateConfig,
          templateInfo: {
            name: this.templateInfo.name,
            code: this.templateInfo.code,
            deviceType: this.getDeviceTypeName(),
            description: this.templateInfo.description
          }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.template-designer {
  height: 100%;
  display: flex;
  flex-direction: column;

  .designer-header {
    padding: 20px;
    background: #f5f5f5;
    border-bottom: 1px solid #e6e6e6;
  }

  .designer-body {
    flex: 1;
    padding: 20px;
    overflow: hidden;

    .field-library {
      height: 100%;
      border: 1px solid #e6e6e6;
      border-radius: 4px;

      .library-header {
        padding: 10px 15px;
        background: #fafafa;
        border-bottom: 1px solid #e6e6e6;

        h4 {
          margin: 0;
          font-size: 14px;
          color: #333;
        }
      }

      .library-content {
        height: calc(100% - 45px);
        overflow-y: auto;

        .field-list {
          padding: 10px;

          .field-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            margin-bottom: 8px;
            background: #fff;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
            cursor: grab;
            transition: all 0.3s;

            &:hover {
              border-color: #409eff;
              box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
            }

            &:active {
              cursor: grabbing;
            }

            i {
              margin-right: 8px;
              color: #409eff;
            }

            span {
              font-size: 13px;
              color: #333;
            }
          }
        }
      }
    }

    .design-area {
      height: 100%;
      border: 1px solid #e6e6e6;
      border-radius: 4px;

      .el-tabs {
        height: 100%;

        ::v-deep .el-tabs__content {
          height: calc(100% - 55px);
          overflow-y: auto;
        }
      }

      .basic-fields-container,
      .table-fields-container {
        height: 100%;
        padding: 15px;

        .container-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 15px;
          padding-bottom: 10px;
          border-bottom: 1px solid #e6e6e6;

          span {
            font-weight: 500;
            color: #333;
          }
        }

        .drop-zone {
          height: calc(100% - 50px);
          min-height: 300px;
          border: 2px dashed #d9d9d9;
          border-radius: 4px;
          position: relative;
          overflow-y: auto;
          padding: 10px;

          &:hover {
            border-color: #409eff;
          }

          &.drag-over {
            border-color: #67c23a;
            background-color: #f0f9ff;
          }

          .empty-drop-zone {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #999;

            i {
              font-size: 48px;
              margin-bottom: 10px;
              display: block;
            }

            p {
              margin: 0;
              font-size: 14px;
            }
          }

          .field-config-item {
            margin: 10px;
            padding: 15px;
            background: #fff;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
              border-color: #409eff;
              box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
            }

            .field-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 10px;

              .field-label {
                font-weight: 500;
                color: #333;
              }

              .field-actions {
                opacity: 0;
                transition: opacity 0.3s;

                .el-button {
                  padding: 0;
                  margin-left: 5px;
                }
              }
            }

            &:hover .field-actions {
              opacity: 1;
            }

            .field-preview {
              ::v-deep .el-input,
              ::v-deep .el-select,
              ::v-deep .el-date-editor {
                width: 100%;
              }
            }
          }

          .table-preview {
            padding: 10px;

            .table-header-cell {
              display: flex;
              justify-content: space-between;
              align-items: center;

              .field-actions {
                opacity: 0;
                transition: opacity 0.3s;

                .el-button {
                  padding: 0;
                  margin-left: 5px;
                }
              }

              &:hover .field-actions {
                opacity: 1;
              }
            }

            .table-fields-drag-area {
              margin-top: 15px;
              padding: 10px;
              border: 1px solid #e6e6e6;
              border-radius: 4px;
              background: #fafafa;

              .table-fields-list {
                min-height: 50px;
              }

              .table-field-item {
                padding: 10px;
                margin-bottom: 8px;
                background: #fff;
                border: 1px solid #e6e6e6;
                border-radius: 4px;
                cursor: move;
                transition: all 0.3s;

                &:hover {
                  border-color: #409eff;
                  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
                }

                .field-header {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  margin-bottom: 5px;

                  .field-label {
                    font-weight: 500;
                    color: #333;
                  }

                  .field-actions {
                    opacity: 0;
                    transition: opacity 0.3s;

                    .el-button {
                      padding: 0;
                      margin-left: 5px;
                    }
                  }
                }

                &:hover .field-actions {
                  opacity: 1;
                }

                .field-info {
                  display: flex;
                  gap: 15px;
                  font-size: 12px;
                  color: #666;

                  .field-type {
                    padding: 2px 6px;
                    background: #f0f0f0;
                    border-radius: 2px;
                  }
                }
              }
            }
          }
        }
      }
    }

    .property-panel {
      height: 100%;
      border: 1px solid #e6e6e6;
      border-radius: 4px;

      .panel-header {
        padding: 10px 15px;
        background: #fafafa;
        border-bottom: 1px solid #e6e6e6;

        h4 {
          margin: 0;
          font-size: 14px;
          color: #333;
        }
      }

      .panel-content {
        height: calc(100% - 45px);
        overflow-y: auto;

        .no-selection {
          padding: 50px 20px;
          text-align: center;
          color: #999;

          p {
            margin: 0;
            font-size: 14px;
          }
        }
      }
    }
  }

  .designer-footer {
    padding: 15px 20px;
    background: #fafafa;
    border-top: 1px solid #e6e6e6;
    text-align: right;

    .el-button {
      margin-left: 10px;
    }
  }
}
</style>
