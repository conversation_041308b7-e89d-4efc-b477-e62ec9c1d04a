<template>
  <div class="data-detail">
    <div class="detail-header">
      <el-descriptions :column="3" size="small" border>
        <el-descriptions-item label="数据编码">{{ data.dataCode }}</el-descriptions-item>
        <el-descriptions-item label="模板名称">{{ data.templateName }}</el-descriptions-item>
        <el-descriptions-item label="设备类型">{{ data.deviceTypeName }}</el-descriptions-item>
        <el-descriptions-item label="检测日期">{{ data.detectionDate }}</el-descriptions-item>
        <el-descriptions-item label="检测人员">{{ data.detectionPerson }}</el-descriptions-item>
        <el-descriptions-item label="车号">{{ data.vehicleNumber || '-' }}</el-descriptions-item>
        <el-descriptions-item label="文件名">{{ data.fileName }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ data.createTime }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ data.updateTime }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <div class="detail-content">
      <el-tabs v-model="activeTab" type="card">
        <!-- 基础数据 -->
        <el-tab-pane label="基础数据" name="basic" v-if="basicData && Object.keys(basicData).length > 0">
          <div class="basic-data">
            <el-descriptions :column="2" size="small" border>
              <el-descriptions-item
                v-for="field in basicFields"
                :key="field.code"
                :label="field.name"
              >
                <span v-if="field.type === 'date' || field.type === 'datetime'">
                  {{ formatDate(basicData[field.code], field.type) }}
                </span>
                <span v-else-if="field.type === 'number'">
                  {{ formatNumber(basicData[field.code], field) }}
                </span>
                <span v-else>
                  {{ basicData[field.code] || '-' }}
                </span>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-tab-pane>

        <!-- 表格数据 -->
        <el-tab-pane label="表格数据" name="table" v-if="tableData && tableData.length > 0">
          <div class="table-data">
            <el-table :data="tableData" border size="small">
              <el-table-column
                v-for="field in tableFields"
                :key="field.code"
                :prop="field.code"
                :label="field.name"
                :width="field.width"
                :show-overflow-tooltip="true"
              >
                <template slot-scope="scope">
                  <span v-if="field.type === 'date' || field.type === 'datetime'">
                    {{ formatDate(scope.row[field.code], field.type) }}
                  </span>
                  <span v-else-if="field.type === 'number'">
                    {{ formatNumber(scope.row[field.code], field) }}
                  </span>
                  <el-tag
                    v-else-if="field.type === 'select' && field.code.includes('result')"
                    :type="getResultTagType(scope.row[field.code])"
                    size="mini"
                  >
                    {{ scope.row[field.code] }}
                  </el-tag>
                  <span v-else>
                    {{ scope.row[field.code] || '-' }}
                  </span>
                </template>
              </el-table-column>
            </el-table>

            <!-- 表格统计 -->
            <div class="table-summary" v-if="tableSummary">
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-statistic title="总计" :value="tableSummary.total" />
                </el-col>
                <el-col :span="6">
                  <el-statistic title="合格" :value="tableSummary.qualified" />
                </el-col>
                <el-col :span="6">
                  <el-statistic title="不合格" :value="tableSummary.unqualified" />
                </el-col>
                <el-col :span="6">
                  <el-statistic
                    title="合格率"
                    :value="tableSummary.qualifiedRate"
                    suffix="%"
                    :precision="1"
                  />
                </el-col>
              </el-row>
            </div>
          </div>
        </el-tab-pane>

        <!-- 原始数据 -->
        <el-tab-pane label="原始数据" name="raw">
          <div class="raw-data">
            <el-row :gutter="20">
              <el-col :span="12">
                <h4>基础数据</h4>
                <el-input
                  :value="JSON.stringify(basicData, null, 2)"
                  type="textarea"
                  :rows="10"
                  readonly
                  class="json-textarea"
                />
              </el-col>
              <el-col :span="12">
                <h4>表格数据</h4>
                <el-input
                  :value="JSON.stringify(tableData, null, 2)"
                  type="textarea"
                  :rows="10"
                  readonly
                  class="json-textarea"
                />
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DataDetail',
  props: {
    data: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      activeTab: 'basic'
    }
  },
  computed: {
    basicData() {
      return this.data.basicData || {}
    },
    tableData() {
      return this.data.tableData || []
    },
    basicFields() {
      return this.data.templateConfig?.basicFields || []
    },
    tableFields() {
      return this.data.templateConfig?.tableFields || []
    },
    tableSummary() {
      if (!this.tableData.length) return null
      
      const resultField = this.tableFields.find(field => 
        field.code.includes('result') || field.name.includes('结果')
      )
      
      if (!resultField) return null
      
      const total = this.tableData.length
      const qualified = this.tableData.filter(row => 
        row[resultField.code] === '合格' || row[resultField.code] === '通过'
      ).length
      const unqualified = total - qualified
      const qualifiedRate = total > 0 ? (qualified / total) * 100 : 0
      
      return {
        total,
        qualified,
        unqualified,
        qualifiedRate
      }
    }
  },
  mounted() {
    // 根据数据内容设置默认标签页
    if (this.basicData && Object.keys(this.basicData).length > 0) {
      this.activeTab = 'basic'
    } else if (this.tableData && this.tableData.length > 0) {
      this.activeTab = 'table'
    } else {
      this.activeTab = 'raw'
    }
  },
  methods: {
    formatDate(value, type) {
      if (!value) return '-'
      
      const date = new Date(value)
      if (isNaN(date)) return value
      
      if (type === 'datetime') {
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        })
      } else {
        return date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        })
      }
    },

    formatNumber(value, field) {
      if (value === null || value === undefined || value === '') return '-'
      
      const num = Number(value)
      if (isNaN(num)) return value
      
      if (field.precision !== undefined) {
        return num.toFixed(field.precision)
      }
      
      return num.toString()
    },

    getResultTagType(value) {
      if (!value) return 'info'
      
      const lowerValue = value.toLowerCase()
      if (lowerValue.includes('合格') || lowerValue.includes('通过') || lowerValue === 'pass') {
        return 'success'
      } else if (lowerValue.includes('不合格') || lowerValue.includes('失败') || lowerValue === 'fail') {
        return 'danger'
      } else {
        return 'warning'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.data-detail {
  .detail-header {
    margin-bottom: 20px;
  }

  .detail-content {
    .basic-data {
      padding: 20px;
      background: #f9f9f9;
      border-radius: 4px;
    }

    .table-data {
      .table-summary {
        margin-top: 20px;
        padding: 20px;
        background: #f9f9f9;
        border-radius: 4px;
      }
    }

    .raw-data {
      h4 {
        margin: 0 0 15px 0;
        color: #333;
      }

      .json-textarea {
        font-family: 'Courier New', monospace;
        font-size: 12px;
      }
    }
  }
}
</style>
