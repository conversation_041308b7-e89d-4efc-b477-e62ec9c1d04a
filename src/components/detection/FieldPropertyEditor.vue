<template>
  <div class="field-property-editor">
    <el-form :model="fieldData" label-width="80px" size="small">
      <!-- 基础属性 -->
      <el-form-item label="字段名称">
        <el-input v-model="fieldData.name" placeholder="请输入字段名称" @input="handleUpdate" />
      </el-form-item>
      
      <el-form-item label="字段编码">
        <el-input v-model="fieldData.code" placeholder="请输入字段编码" @input="handleUpdate" />
      </el-form-item>
      
      <el-form-item label="字段类型">
        <el-select v-model="fieldData.type" placeholder="请选择字段类型" @change="handleTypeChange" style="width: 100%">
          <el-option
            v-for="option in fieldTypeOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="是否必填">
        <el-switch v-model="fieldData.required" @change="handleUpdate" />
      </el-form-item>
      
      <!-- 文本类型属性 -->
      <template v-if="fieldData.type === 'text' || fieldData.type === 'textarea'">
        <el-form-item label="占位符">
          <el-input v-model="fieldData.placeholder" placeholder="请输入占位符" @input="handleUpdate" />
        </el-form-item>
        
        <el-form-item label="默认值">
          <el-input v-model="fieldData.defaultValue" placeholder="请输入默认值" @input="handleUpdate" />
        </el-form-item>
        
        <el-form-item v-if="fieldData.type === 'text'" label="最大长度">
          <el-input-number v-model="fieldData.maxLength" :min="1" :max="1000" @change="handleUpdate" />
        </el-form-item>
        
        <el-form-item v-if="fieldData.type === 'textarea'" label="行数">
          <el-input-number v-model="fieldData.rows" :min="2" :max="10" @change="handleUpdate" />
        </el-form-item>
      </template>
      
      <!-- 数字类型属性 -->
      <template v-if="fieldData.type === 'number'">
        <el-form-item label="最小值">
          <el-input-number v-model="fieldData.min" @change="handleUpdate" />
        </el-form-item>
        
        <el-form-item label="最大值">
          <el-input-number v-model="fieldData.max" @change="handleUpdate" />
        </el-form-item>
        
        <el-form-item label="步长">
          <el-input-number v-model="fieldData.step" :min="0.01" :step="0.01" @change="handleUpdate" />
        </el-form-item>
        
        <el-form-item label="精度">
          <el-input-number v-model="fieldData.precision" :min="0" :max="10" @change="handleUpdate" />
        </el-form-item>
      </template>
      
      <!-- 日期类型属性 -->
      <template v-if="fieldData.type === 'date' || fieldData.type === 'datetime'">
        <el-form-item label="日期格式">
          <el-select v-model="fieldData.format" @change="handleUpdate" style="width: 100%">
            <el-option
              v-for="format in dateFormats"
              :key="format.value"
              :label="format.label"
              :value="format.value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="默认值">
          <el-select v-model="fieldData.defaultValue" @change="handleUpdate" style="width: 100%">
            <el-option label="无" value="" />
            <el-option label="当前日期" value="today" />
            <el-option label="当前时间" value="now" />
          </el-select>
        </el-form-item>
      </template>
      
      <!-- 选择类型属性 -->
      <template v-if="fieldData.type === 'select' || fieldData.type === 'radio' || fieldData.type === 'checkbox'">
        <el-form-item label="选项配置">
          <div class="options-config">
            <div
              v-for="(option, index) in fieldData.options"
              :key="index"
              class="option-item"
            >
              <el-input
                v-model="fieldData.options[index]"
                placeholder="请输入选项值"
                @input="handleUpdate"
                size="mini"
              />
              <el-button
                type="text"
                icon="el-icon-delete"
                @click="removeOption(index)"
                size="mini"
              />
            </div>
            <el-button
              type="text"
              icon="el-icon-plus"
              @click="addOption"
              size="mini"
            >
              添加选项
            </el-button>
          </div>
        </el-form-item>
        
        <el-form-item v-if="fieldData.type === 'select'" label="允许清空">
          <el-switch v-model="fieldData.clearable" @change="handleUpdate" />
        </el-form-item>
        
        <el-form-item v-if="fieldData.type === 'select'" label="可搜索">
          <el-switch v-model="fieldData.filterable" @change="handleUpdate" />
        </el-form-item>
      </template>
      
      <!-- 表格字段特有属性 -->
      <template v-if="fieldType === 'table'">
        <el-form-item label="列宽">
          <el-input-number v-model="fieldData.width" :min="80" :max="500" @change="handleUpdate" />
        </el-form-item>
        
        <el-form-item label="固定列">
          <el-select v-model="fieldData.fixed" @change="handleUpdate" style="width: 100%">
            <el-option label="不固定" value="" />
            <el-option label="左侧固定" value="left" />
            <el-option label="右侧固定" value="right" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="排序">
          <el-switch v-model="fieldData.sortable" @change="handleUpdate" />
        </el-form-item>
      </template>
      
      <!-- 验证规则 -->
      <el-form-item label="验证规则">
        <div class="validation-rules">
          <el-checkbox
            v-model="fieldData.validationRules.required"
            @change="handleValidationChange"
          >
            必填验证
          </el-checkbox>
          
          <template v-if="fieldData.type === 'text' || fieldData.type === 'textarea'">
            <el-checkbox
              v-model="fieldData.validationRules.minLength.enabled"
              @change="handleValidationChange"
            >
              最小长度
            </el-checkbox>
            <el-input-number
              v-if="fieldData.validationRules.minLength.enabled"
              v-model="fieldData.validationRules.minLength.value"
              :min="1"
              size="mini"
              @change="handleValidationChange"
            />
            
            <el-checkbox
              v-model="fieldData.validationRules.maxLength.enabled"
              @change="handleValidationChange"
            >
              最大长度
            </el-checkbox>
            <el-input-number
              v-if="fieldData.validationRules.maxLength.enabled"
              v-model="fieldData.validationRules.maxLength.value"
              :min="1"
              size="mini"
              @change="handleValidationChange"
            />
          </template>
          
          <template v-if="fieldData.type === 'text'">
            <el-checkbox
              v-model="fieldData.validationRules.pattern.enabled"
              @change="handleValidationChange"
            >
              正则验证
            </el-checkbox>
            <el-input
              v-if="fieldData.validationRules.pattern.enabled"
              v-model="fieldData.validationRules.pattern.value"
              placeholder="请输入正则表达式"
              size="mini"
              @input="handleValidationChange"
            />
          </template>
        </div>
      </el-form-item>
      
      <el-form-item label="排序">
        <el-input-number v-model="fieldData.order" :min="1" @change="handleUpdate" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'FieldPropertyEditor',
  props: {
    field: {
      type: Object,
      required: true
    },
    fieldType: {
      type: String,
      required: true // 'basic' or 'table'
    }
  },
  data() {
    return {
      fieldData: {},
      fieldTypeOptions: [
        { label: '单行文本', value: 'text' },
        { label: '多行文本', value: 'textarea' },
        { label: '数字', value: 'number' },
        { label: '日期', value: 'date' },
        { label: '日期时间', value: 'datetime' },
        { label: '下拉选择', value: 'select' },
        { label: '单选', value: 'radio' },
        { label: '多选', value: 'checkbox' }
      ],
      dateFormats: [
        { label: 'YYYY-MM-DD', value: 'yyyy-MM-dd' },
        { label: 'YYYY/MM/DD', value: 'yyyy/MM/dd' },
        { label: 'YYYY-MM-DD HH:mm:ss', value: 'yyyy-MM-dd HH:mm:ss' },
        { label: 'YYYY/MM/DD HH:mm:ss', value: 'yyyy/MM/dd HH:mm:ss' }
      ]
    }
  },
  watch: {
    field: {
      handler(val) {
        this.initFieldData(val)
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    initFieldData(field) {
      this.fieldData = {
        ...field,
        validationRules: {
          required: field.required || false,
          minLength: { enabled: false, value: 1 },
          maxLength: { enabled: false, value: 100 },
          pattern: { enabled: false, value: '' },
          ...(field.validationRules || {})
        },
        options: field.options || [],
        rows: field.rows || 3,
        step: field.step || 1,
        precision: field.precision || 0,
        format: field.format || 'yyyy-MM-dd',
        clearable: field.clearable !== false,
        filterable: field.filterable || false,
        width: field.width || 150,
        fixed: field.fixed || '',
        sortable: field.sortable || false
      }
    },

    handleUpdate() {
      this.$emit('update', { ...this.fieldData })
    },

    handleTypeChange() {
      // 重置类型相关的属性
      if (this.fieldData.type === 'select' || this.fieldData.type === 'radio' || this.fieldData.type === 'checkbox') {
        if (!this.fieldData.options || this.fieldData.options.length === 0) {
          this.fieldData.options = ['选项1', '选项2']
        }
      }
      this.handleUpdate()
    },

    handleValidationChange() {
      this.handleUpdate()
    },

    addOption() {
      if (!this.fieldData.options) {
        this.fieldData.options = []
      }
      this.fieldData.options.push(`选项${this.fieldData.options.length + 1}`)
      this.handleUpdate()
    },

    removeOption(index) {
      this.fieldData.options.splice(index, 1)
      this.handleUpdate()
    }
  }
}
</script>

<style lang="scss" scoped>
.field-property-editor {
  padding: 15px;

  .options-config {
    .option-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .el-input {
        flex: 1;
        margin-right: 8px;
      }
    }
  }

  .validation-rules {
    .el-checkbox {
      display: block;
      margin-bottom: 8px;
    }

    .el-input-number,
    .el-input {
      margin-top: 5px;
      margin-bottom: 10px;
      width: 100%;
    }
  }
}
</style>
