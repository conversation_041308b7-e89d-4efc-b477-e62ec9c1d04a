/**
 * MathJax 本地字体样式
 * 确保MathJax使用本地字体文件而不依赖CDN
 */

/* MathJax 字体预加载 */
@font-face {
  font-family: 'MJXc-TeX-main-R';
  src: url('/fonts/mathjax/mjx-ncm-n.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'MJXc-TeX-main-B';
  src: url('/fonts/mathjax/mjx-ncm-b.woff') format('woff');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'MJXc-TeX-main-I';
  src: url('/fonts/mathjax/mjx-ncm-i.woff') format('woff');
  font-weight: normal;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'MJXc-TeX-math-I';
  src: url('/fonts/mathjax/mjx-ncm-mi.woff') format('woff');
  font-weight: normal;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'MJXc-TeX-size1-R';
  src: url('/fonts/mathjax/mjx-ncm-s.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'MJXc-TeX-size2-R';
  src: url('/fonts/mathjax/mjx-ncm-s.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'MJXc-TeX-size3-R';
  src: url('/fonts/mathjax/mjx-ncm-s3.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'MJXc-TeX-size4-R';
  src: url('/fonts/mathjax/mjx-ncm-s4.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

/* MathJax 容器样式优化 */
.MathJax {
  font-family: 'MJXc-TeX-main-R', serif !important;
}

.MathJax_Display {
  text-align: center;
  margin: 1em 0;
}

/* 确保数学公式在各种背景下都能正常显示 */
.MathJax, .MathJax_Display {
  color: inherit;
  background: transparent;
}

/* 针对表格中的数学公式优化 */
table .MathJax {
  margin: 0;
  padding: 2px;
}

/* 行内公式样式 */
.MathJax_Preview {
  color: #888;
  font-size: smaller;
}

/* 加载状态样式 */
.MathJax_Processing {
  visibility: hidden;
}

.MathJax_Processed {
  visibility: visible;
}
