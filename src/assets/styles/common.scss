

// flex布局 
.d-flex{ display: flex; }
.d-block{ display: block; }
.d-inline-block{ display: inline-block; }
.flex-1{ flex: 1; }
.flex-2{ flex: 2; }
.flex-column{ flex-direction: column; }
.flex-row{ flex-direction: row; }
.flex-row-reverse{flex-direction:row-reverse; }
.flex-wrap{ flex-wrap: wrap; }
.flex-nowrap{ flex-wrap: nowrap; }
.flex-shrink{flex-shrink: 0;}
.j-start{ justify-content: flex-start; }
.j-center{ justify-content: center; }
.j-end{ justify-content: flex-end; }
.j-se{ justify-content: space-evenly; }
.j-sb{ justify-content: space-between; }
.j-sa{ justify-content: space-around; }
.a-center{ align-items:center; }
.a-start{ align-items: flex-start; }
.a-end{ align-items:flex-end; }
.a-stretch{ align-items: stretch; }
.a-self-start{ align-self: flex-start; }
.a-self-auto{ align-self: auto; }
.a-self-end{ align-self: flex-end; }
.a-self-stretch{ align-self:stretch; }
.a-self-baseline{ align-self:baseline; }

// 内外边距
@for $i from 0 through 200 {
  .mr-t#{$i} { margin-top:  (1px * $i) !important; }
  .mr-r#{$i} { margin-right: $i*1px !important; }
  .mr-l#{$i} { margin-left: (1px * $i) !important; }
  .mr-b#{$i} { margin-bottom: (1px * $i) !important; }
  .mr-tb#{$i} { 
    margin-top: $i*1px !important;
    margin-bottom: $i*1px !important;
  }
  .mr-lr#{$i} { 
    margin-left: $i*1px !important;
    margin-right: $i*1px !important;
  }
  .mr-#{$i} { 
    margin-top: (1px * $i) !important;
    margin-bottom: (1px * $i) !important;
    margin-left: (1px * $i) !important;
    margin-right: (1px * $i) !important;
    margin: (1px * $i) !important; 
  }
  
  .pd-t#{$i} { padding-top:  (1px * $i) !important; }
  .pd-r#{$i} { padding-right: $i*1px !important; }
  .pd-l#{$i} { padding-left: (1px * $i) !important; }
  .pd-b#{$i} { padding-bottom: (1px * $i) !important; }
  .pd-tb#{$i} { 
    padding-top: $i*1px !important;
    padding-bottom: $i*1px !important;
  }
  .pd-lr#{$i} { 
    padding-left: $i*1px !important;
    padding-right: $i*1px !important;
  }
  .pd-#{$i} { 
    padding-top: (1px * $i) !important;
    padding-bottom: (1px * $i) !important;
    padding-left: (1px * $i) !important;
    padding-right: (1px * $i) !important;
  }
}

// 字体
@for $i from 12 through 40 {
  .font-#{$i} { font-size:  (1px * $i) !important; }
}

// 圆角
@for $i from 0 through 40 {
  .radius-#{$i} { border-radius:  (1px * $i) !important; }
}

.text-center { text-align: center !important;}
.text-left { text-align: left !important;}
.text-right { text-align: right !important;}
// 禁止换行
.text-nowrap { white-space: nowrap !important; }
// 文字溢出显示省略号
.text-ellipsis{
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
}

.tableHieght{
	height: 170px !important;
  .el-table__body-wrapper{
    height: 130px;
  }
}

// 移入指针变成一只手
.pointer{
	cursor: pointer;
}
