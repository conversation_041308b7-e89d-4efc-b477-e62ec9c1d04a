import request from '@/utils/request'

// 查询参数列表
export function listConfig(query) {
  return request({
    url: '/iot/config/list',
    method: 'get',
    params: query
  })
}

// 查询参数详细
export function getConfig(configId) {
  return request({
    url: '/iot/config/' + configId,
    method: 'get'
  })
}

// 根据参数键名查询参数值
export function getConfigKey(configKey) {
  return request({
    url: '/iot/config/configKey/' + configKey,
    method: 'get'
  })
}

// 刷新参数缓存
export function refreshCache() {
  return request({
    url: '/iot/config/refreshCache',
    method: 'delete'
  })
}


// 导出json
export function syncTableExport(data) {
  return request({
    url: '/iot/syncTable/exportFile',
    method: 'post',
    data
  })
}

// 导入json
export function syncTableRead(data) {
  return request({
    url: '/iot/syncTable/read',
    method: 'post',
    data
  })
}
