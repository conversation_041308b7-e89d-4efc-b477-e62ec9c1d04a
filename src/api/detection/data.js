import request from '@/api/request'

// 获取检测数据列表
export function getDetectionDataList(params) {
  return request.send('/api/detection-data', params, {
    method: 'get'
  })
}

// 获取检测数据详情
export function getDetectionData(id) {
  return request.send(`/api/detection-data/${id}`, {}, {
    method: 'get'
  })
}

// 上传并解析Excel数据
export function uploadExcelData(formData) {
  return request.send('/api/detection-data/upload-excel', formData, {
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 确认保存解析数据
export function confirmSaveData(data) {
  return request.send('/api/detection-data/confirm-save', data, {
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 手动新增检测数据
export function addDetectionData(data) {
  return request.send('/api/detection-data', data, {
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 更新检测数据
export function updateDetectionData(id, data) {
  return request.send(`/api/detection-data/${id}`, data, {
    method: 'put',
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 删除检测数据
export function deleteDetectionData(id) {
  return request.send(`/api/detection-data/${id}`, {}, {
    method: 'delete'
  })
}

// 批量删除检测数据
export function batchDeleteDetectionData(ids) {
  return request.send('/api/detection-data/batch', { ids }, {
    method: 'delete',
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 导出检测数据
export function exportDetectionData(params) {
  return request.send('/api/detection-data/export', params, {
    method: 'get',
    responseType: 'blob'
  })
}

// 下载原始文件
export function downloadOriginalFile(id) {
  return request.send(`/api/detection-data/${id}/download`, {}, {
    method: 'get',
    responseType: 'blob'
  })
}

// 获取检测趋势数据
export function getDetectionTrend(params) {
  return request.send('/api/detection-data/trend', params, {
    method: 'get'
  })
}

// 获取检测结果分布
export function getDetectionResultDistribution(params) {
  return request.send('/api/detection-data/result-distribution', params, {
    method: 'get'
  })
}

// 验证检测数据
export function validateDetectionData(data) {
  return request.send('/api/detection-data/validate', data, {
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 批量导入检测数据
export function batchImportData(formData) {
  return request.send('/api/detection-data/batch-import', formData, {
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取导入模板
export function getImportTemplate(templateId) {
  return request.send(`/api/detection-data/import-template/${templateId}`, {}, {
    method: 'get',
    responseType: 'blob'
  })
}
