import request from '@/api/request'

// 获取设备类型列表
export function getDeviceTypeList(params) {
  return request.send('/api/device-types', params, {
    method: 'get'
  })
}

// 获取设备类型详情
export function getDeviceType(id) {
  return request.send(`/api/device-types/${id}`, {}, {
    method: 'get'
  })
}

// 新增设备类型
export function addDeviceType(data) {
  return request.send('/api/device-types', data, {
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 更新设备类型
export function updateDeviceType(id, data) {
  return request.send(`/api/device-types/${id}`, data, {
    method: 'put',
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 删除设备类型
export function deleteDeviceType(id) {
  return request.send(`/api/device-types/${id}`, {}, {
    method: 'delete'
  })
}

// 批量删除设备类型
export function batchDeleteDeviceTypes(ids) {
  return request.send('/api/device-types/batch', { ids }, {
    method: 'delete',
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 启用/禁用设备类型
export function toggleDeviceTypeStatus(id, status) {
  return request.send(`/api/device-types/${id}/status`, { status }, {
    method: 'put',
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 获取启用的设备类型选项
export function getDeviceTypeOptions() {
  return request.send('/api/device-types/options', {}, {
    method: 'get'
  })
}
