import request from '@/api/request'

// 获取检测模板列表
export function getTemplateList(params) {
  return request.send('/api/detection-templates', params, {
    method: 'get'
  })
}

// 获取模板详情
export function getTemplate(id) {
  return request.send(`/api/detection-templates/${id}`, {}, {
    method: 'get'
  })
}

// 新增模板
export function addTemplate(data) {
  return request.send('/api/detection-templates', data, {
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 更新模板
export function updateTemplate(id, data) {
  return request.send(`/api/detection-templates/${id}`, data, {
    method: 'put',
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 删除模板
export function deleteTemplate(id) {
  return request.send(`/api/detection-templates/${id}`, {}, {
    method: 'delete'
  })
}

// 批量删除模板
export function batchDeleteTemplates(ids) {
  return request.send('/api/detection-templates/batch', { ids }, {
    method: 'delete',
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 设置默认模板
export function setDefaultTemplate(id) {
  return request.send(`/api/detection-templates/${id}/set-default`, {}, {
    method: 'put'
  })
}

// 复制模板
export function copyTemplate(id, data) {
  return request.send(`/api/detection-templates/${id}/copy`, data, {
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 导出Excel模板
export function exportExcelTemplate(id) {
  return request.send(`/api/detection-templates/${id}/export-excel`, {}, {
    method: 'get',
    responseType: 'blob'
  })
}

// 预览模板
export function previewTemplate(data) {
  return request.send('/api/detection-templates/preview', data, {
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 验证模板配置
export function validateTemplate(data) {
  return request.send('/api/detection-templates/validate', data, {
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 获取模板选项（用于下拉选择）
export function getTemplateOptions(deviceTypeId) {
  return request.send('/api/detection-templates/options', { deviceTypeId }, {
    method: 'get'
  })
}

// 获取默认模板
export function getDefaultTemplate(deviceTypeId) {
  return request.send('/api/detection-templates/default', { deviceTypeId }, {
    method: 'get'
  })
}

// 启用/禁用模板
export function toggleTemplateStatus(id, status) {
  return request.send(`/api/detection-templates/${id}/status`, { status }, {
    method: 'put',
    headers: {
      'Content-Type': 'application/json'
    }
  })
}
