import request from '@/utils/request'

/**
 * 开始导出设备数据
 * @param {Object} data 导出请求参数
 */
export function startDeviceExport(data) {
  return request({
    url: '/iot/device/data/export/start',
    method: 'post',
    data: data
  })
}

/**
 * 获取导出进度
 * @param {String} taskId 任务ID
 */
export function getExportProgress(taskId) {
  return request({
    url: `/iot/device/data/export/progress/${taskId}`,
    method: 'get'
  })
}

/**
 * 下载导出文件
 * @param {String} taskId 任务ID
 */
export function downloadExportFile(taskId) {
  return request({
    url: `/iot/device/data/export/download/${taskId}`,
    method: 'get',
    responseType: 'blob'
  })
}

/**
 * 取消导出任务
 * @param {String} taskId 任务ID
 */
export function cancelDeviceExport(taskId) {
  return request({
    url: `/iot/device/data/export/cancel/${taskId}`,
    method: 'post'
  })
}

/**
 * 获取导出历史记录
 * @param {Object} query 查询参数
 */
export function getExportHistory(query) {
  return request({
    url: '/iot/device/data/export/history',
    method: 'get',
    params: query
  })
}

/**
 * 清理过期的导出文件
 * @param {Number} expireDays 过期天数
 */
export function cleanupExpiredExports(expireDays = 7) {
  return request({
    url: '/iot/device/data/export/cleanup',
    method: 'post',
    params: { expireDays }
  })
}
