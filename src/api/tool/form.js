import request from '@/utils/request'

// 查询单模板信息列表
export function listInfo(query) {
  return request({
    url: '/flows/formTemplateInfo/list',
    method: 'get',
    params: query
  })
}

// 查询单模板信息详细
export function getInfo(formId, params) {
  return request({
    url: '/flows/formTemplateInfo/' + formId,
    method: 'get',
    params
  })
}

export function getByTemplateIdInfo(templateId, params) {
  return request({
    url: '/flows/formTemplateInfo/template/' + templateId,
    method: 'get',
    params
  })
}

export function updateTemplateInfoJson(data) {
  return request({
    url: '/flows/formTemplateInfo/updateTemplateInfoJson',
    method: 'post',
    data: data
  })
}

export function formRelease(data) {
  return request({
    url: '/flows/formTemplateInfo/pushForm/' + data,
    method: 'put'
  })
}

// 修改单模板信息
export function updateInfo(data) {
  return request({
    url: '/flows/formTemplateInfo',
    method: 'put',
    data: data
  })
}

// 删除单模板信息
export function delInfo(formId) {
  return request({
    url: '/flows/formTemplateInfo/' + formId,
    method: 'delete'
  })
}

// 通过taskId 和definitionId获取流程当前审批或者查看表单
export function keyTaskIdForm(params) {
  return request({
    url: '/flows/form/keyTaskIdForm',
    method: 'get',
    params: params
  })
}

/**
 * 获取key的开始提交表单
 * @param {Object} params
 */
export function keyStartForm(params) {
  return request({
    url: '/flows/form/keyStartForm',
    method: 'get',
    params: params
  })
}

/**
 * 获取流程审批日志
 * @param {Object} params
 */
export function getFlowApproveLog(params) {
  return request({
    url: '/flows/processFormTable/flowApproveLog',
    method: 'get',
    params: params
  })
}

/**
 * 获取流程数据表列表
 * */
export function queryFlowDataTable() {
  return request({
    url: '/flows/queryFlowDataTable',
    method: 'get'
  })
}
/**
 * 获取数据表字段
 * @param {String} tableName 表名称
 * */
export function queryTableColumn(tableName) {
  return request({
    url: '/flows/queryTableColumn/' + tableName,
    method: 'get'
  })
}


//获取表单模板
 export function getFormTempalte() {
  return request({
    url: '/flows/formTemplateStandardInfo/list',
    method: 'get'
  })
}


//获取历史列表
export function getFormTempalteHistory(data) {
  return request({
    url: '/flows/formTemplateInfo/versionHis',
    method: 'post',
    data
  })
}


//获取历史列表
export function updateFormTempalteHistory(data) {
  return request({
    url: '/flows/formTemplateInfo/updateVersionHis',
    method: 'put',
    data
  })
}



export function getFormTree() {
  return request({
    url: '/flows/formTemplateInfo/getFormTree',
    method: 'get'
  })
}

export function colsUpdate(data) {
  return request({
    url: '/flows/formTemplateInfo/colsUpdate',
    method: 'put',
    data
  })
}


export function addOrUpdateStructure(data) {
  return request({
    url: '/flows/formTemplateInfo/addOrUpdateStructure',
    method: 'put',
    data
  })
}


export function deleteStructure(formId) {
  return request({
    url: '/flows/formTemplateInfo/deleteStructure/' + formId,
    method: 'delete'
  })
}


export function moveNode(params) {
  return request({
    url: '/flows/formTemplateInfo/moveNode',
    method: 'get',
    params
  })
}


export function moveTemplate(params) {
  return request({
    url: '/flows/formTemplateInfo/moveTemplate',
    method: 'get',
    params
  })
}


