import request from '@/utils/request'
import { download } from '@/utils/request'

/**
 * 保存Excel模板设计
 * @param {Object} data 模板数据
 */
export function saveExcelTemplate(data) {
  return request({
    url: '/iot/excel-template/save',
    method: 'post',
    data: data
  })
}

/**
 * 根据ID查询模板详情
 * @param {String} id 模板ID
 */
export function getExcelTemplate(id) {
  return request({
    url: `/iot/excel-template/${id}`,
    method: 'get'
  })
}

/**
 * 根据模板编码查询模板
 * @param {String} templateCode 模板编码
 */
export function getExcelTemplateByCode(templateCode) {
  return request({
    url: `/iot/excel-template/code/${templateCode}`,
    method: 'get'
  })
}

/**
 * 根据设备类型查询模板列表
 * @param {String} deviceType 设备类型
 */
export function getExcelTemplatesByDeviceType(deviceType) {
  return request({
    url: `/iot/excel-template/device-type/${deviceType}`,
    method: 'get'
  })
}

/**
 * 分页查询模板列表
 * @param {Object} query 查询参数
 */
export function listExcelTemplate(query) {
  return request({
    url: '/iot/excel-template/pageList',
    method: 'get',
    params: query
  })
}

/**
 * 删除模板
 * @param {String} id 模板ID
 */
export function deleteExcelTemplate(id) {
  return request({
    url: `/iot/excel-template/${id}`,
    method: 'delete'
  })
}

/**
 * 复制模板
 * @param {String} id 源模板ID
 * @param {String} newTemplateName 新模板名称
 * @param {String} newTemplateCode 新模板编码
 */
export function copyExcelTemplate(id, newTemplateName, newTemplateCode) {
  return request({
    url: `/iot/excel-template/copy/${id}`,
    method: 'post',
    params: {
      newTemplateName: newTemplateName,
      newTemplateCode: newTemplateCode
    }
  })
}

/**
 * 导出Excel模板（带数据）
 * @param {String} id 模板ID
 * @param {Array} data 填充数据
 */
export function exportExcelTemplate(id, data) {
  return request({
    url: `/iot/excel-template/export/${id}`,
    method: 'post',
    data: data || [],
    responseType: 'blob'
  })
}

/**
 * 导出空白Excel模板
 * @param {String} id 模板ID
 */
export function exportBlankExcelTemplate(id) {
  return request({
    url: `/iot/excel-template/export-blank/${id}`,
    method: 'get',
    responseType: 'blob'
  })
}

/**
 * 验证模板编码唯一性
 * @param {String} templateCode 模板编码
 * @param {String} excludeId 排除的ID
 */
export function checkTemplateCodeUnique(templateCode, excludeId) {
  return request({
    url: '/iot/excel-template/check-code',
    method: 'get',
    params: {
      templateCode: templateCode,
      excludeId: excludeId
    }
  })
}

/**
 * 启用/禁用模板
 * @param {String} id 模板ID
 * @param {Number} status 状态
 */
export function updateExcelTemplateStatus(id, status) {
  return request({
    url: `/iot/excel-template/status/${id}`,
    method: 'put',
    params: {
      status: status
    }
  })
}

/**
 * 下载Excel模板文件
 * @param {String} id 模板ID
 * @param {String} filename 文件名
 * @param {Array} data 填充数据（可选）
 */
export function downloadExcelTemplate(id, filename, data = null) {
  if (data) {
    // 带数据导出
    return request({
      url: `/iot/excel-template/export/${id}`,
      method: 'post',
      data: data,
      responseType: 'blob'
    }).then(response => {
      const blob = new Blob([response], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })

      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    })
  } else {
    // 空白模板导出
    return download(`/iot/excel-template/export-blank/${id}`, {}, filename)
  }
}
