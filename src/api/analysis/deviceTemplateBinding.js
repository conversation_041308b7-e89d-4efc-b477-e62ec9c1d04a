import request from '@/utils/request'

/**
 * 分页查询设备模板绑定列表
 * @param {Object} query 查询参数
 */
export function listDeviceTemplateBinding(query) {
  return request({
    url: '/iot/deviceTemplateBinding/pageList',
    method: 'get',
    params: query
  })
}

/**
 * 根据设备编码查询绑定的模板ID
 * @param {String} deviceCode 设备编码
 */
export function getTemplateIdByDeviceCode(deviceCode) {
  return request({
    url: `/iot/deviceTemplateBinding/template-id/${deviceCode}`,
    method: 'get'
  })
}

/**
 * 根据模板ID查询绑定的设备编码列表
 * @param {Number} templateId 模板ID
 */
export function getDeviceCodesByTemplateId(templateId) {
  return request({
    url: `/iot/deviceTemplateBinding/device-codes/${templateId}`,
    method: 'get'
  })
}

/**
 * 创建设备模板绑定
 * @param {Object} data 绑定数据
 */
export function createDeviceTemplateBinding(data) {
  return request({
    url: '/iot/deviceTemplateBinding',
    method: 'post',
    data: data
  })
}

/**
 * 更新设备模板绑定
 * @param {Object} data 绑定数据
 */
export function updateDeviceTemplateBinding(data) {
  return request({
    url: '/iot/deviceTemplateBinding',
    method: 'put',
    data: data
  })
}

/**
 * 删除设备模板绑定
 * @param {Number} id 绑定ID
 */
export function deleteDeviceTemplateBinding(id) {
  return request({
    url: `/iot/deviceTemplateBinding/${id}`,
    method: 'delete'
  })
}

/**
 * 批量删除设备模板绑定
 * @param {Array} ids 绑定ID列表
 */
export function deleteBatchDeviceTemplateBinding(ids) {
  return request({
    url: '/iot/deviceTemplateBinding/batch',
    method: 'delete',
    data: ids
  })
}

/**
 * 启用/禁用绑定
 * @param {Number} id 绑定ID
 * @param {Number} status 状态
 */
export function updateDeviceTemplateBindingStatus(id, status) {
  return request({
    url: `/iot/deviceTemplateBinding/status/${id}`,
    method: 'put',
    params: { status }
  })
}

/**
 * 检查设备编码是否已绑定
 * @param {String} deviceCode 设备编码
 * @param {Number} excludeId 排除的ID
 */
export function checkDeviceCodeExists(deviceCode, excludeId) {
  return request({
    url: '/iot/deviceTemplateBinding/check-device-code',
    method: 'get',
    params: { deviceCode, excludeId }
  })
}

/**
 * 获取所有可用的设备编码列表（未绑定的设备）
 */
export function getAvailableDeviceCodes() {
  return request({
    url: '/iot/deviceTemplateBinding/availableDeviceCodes',
    method: 'get'
  })
}

/**
 * 获取可用设备选项列表（设备名称-设备编码格式）
 */
export function getAvailableDeviceOptions() {
  return request({
    url: '/iot/device/availableOptions',
    method: 'get'
  })
}

export function getBindOptions() {
  return request({
    url: '/iot/device/bindOptions',
    method: 'get'
  })
}

/**
 * 解绑设备模板
 * @param {String} deviceCode 设备编码
 */
export function unbindTemplate(deviceCode) {
  return request({
    url: `/iot/deviceTemplateBinding/unbind/${deviceCode}`,
    method: 'delete'
  })
}
