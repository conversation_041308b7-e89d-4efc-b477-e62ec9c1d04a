<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>检查表格</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
            color: #333;
        }
        
        .table-container {
            background-color: white;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            min-width: 800px;
            max-width: 1800px;
            width: auto;
            position: relative;
        }
        
        table {
            border-collapse: collapse;
            width: auto;
            min-width: 600px;
            max-width: 1800px;
            min-height: 300px;
            table-layout: fixed;
        }
        
        th, td {
            border: 1px solid #dee2e6;
            padding: 12px 15px;
            text-align: center;
            vertical-align: middle;
            font-size: 14px;
            position: relative;
            overflow: hidden;
            transition: background-color 0.2s ease;
            min-width: 80px;
            min-height: 40px;
            background-color: white;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        
        tr:hover td {
            background-color: #f8f9fa;
        }
        
        /* Resizable handles */
        .resize-handle {
            position: absolute;
            background-color: transparent;
            z-index: 10;
            transition: background-color 0.2s ease;
        }
        
        .resize-handle:hover {
            background-color: rgba(0, 123, 255, 0.3);
        }
        
        .resize-handle-right {
            top: 0;
            right: -2px;
            width: 4px;
            height: 100%;
            cursor: col-resize;
        }
        
        .resize-handle-left {
            top: 0;
            left: -2px;
            width: 4px;
            height: 100%;
            cursor: col-resize;
        }
        
        .resize-handle-bottom {
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 4px;
            cursor: row-resize;
        }
        
        .resize-handle-corner {
            bottom: -2px;
            right: -2px;
            width: 12px;
            height: 12px;
            cursor: nwse-resize;
            border-radius: 50%;
            background-color: rgba(0, 123, 255, 0.2);
        }
        
        .resize-handle-corner:hover {
            background-color: rgba(0, 123, 255, 0.5);
        }
        
        .add-button {
            position: absolute;
            background: #007bff;
            border: none;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 18px;
            color: white;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            transition: all 0.2s ease;
            z-index: 20;
        }
        
        .add-button:hover {
            background-color: #0056b3;
            transform: scale(1.1);
        }
        
        .top-left-add {
            top: -14px;
            left: -14px;
        }
        
        .bottom-add {
            bottom: -14px;
            left: 50%;
            transform: translateX(-50%);
        }
        
        .right-add {
            top: 50%;
            right: -14px;
            transform: translateY(-50%);
        }
        
        /* Make cells resizable */
        .resizable-cell {
            position: relative;
        }
        
        .resizable-cell:hover .resize-handle {
            background-color: rgba(0, 123, 255, 0.2);
        }
        
        .download-button {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 24px;
            margin-bottom: 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 2px 5px rgba(0, 123, 255, 0.3);
            transition: all 0.2s ease;
            z-index: 1000;
        }
        
        .download-button:hover {
            background: linear-gradient(135deg, #0056b3, #004085);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
        }
        
        /* Context Menu Styles */
        .context-menu {
            position: fixed;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            padding: 8px 0;
            z-index: 1000;
            min-width: 200px;
            font-size: 14px;
            display: none;
            backdrop-filter: blur(5px);
        }
        
        .context-menu-item {
            padding: 10px 16px;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        
        .context-menu-item:hover {
            background-color: #f8f9fa;
            color: #007bff;
        }
        
        .context-menu-info {
            padding: 10px 16px;
            color: #6c757d;
            font-size: 12px;
            border-bottom: 1px solid #f1f3f5;
            margin-bottom: 5px;
        }
        
        .input-dialog {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
            padding: 24px;
            z-index: 1001;
            min-width: 320px;
            display: none;
            animation: fadeIn 0.3s ease;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translate(-50%, -50%) scale(0.9); }
            to { opacity: 1; transform: translate(-50%, -50%) scale(1); }
        }
        
        .input-dialog h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-weight: 600;
        }
        
        .input-group {
            margin-bottom: 16px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 8px;
            color: #495057;
            font-weight: 500;
        }
        
        .input-group input {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
        }
        
        .input-group input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        }
        
        .dialog-buttons {
            text-align: right;
            margin-top: 24px;
        }
        
        .dialog-button {
            padding: 10px 16px;
            margin-left: 8px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .dialog-button.primary {
            background: #007bff;
            color: white;
        }
        
        .dialog-button.primary:hover {
            background: #0056b3;
        }
        
        .dialog-button.secondary {
            background: #6c757d;
            color: white;
        }
        
        .dialog-button.secondary:hover {
            background: #545b62;
        }
        
        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: none;
            backdrop-filter: blur(2px);
        }
        
        /* Visual feedback for resizing */
        .resizing {
            opacity: 0.7;
        }
        
        .resize-preview {
            position: absolute;
            background-color: rgba(0, 123, 255, 0.1);
            border: 1px dashed #007bff;
            pointer-events: none;
            z-index: 5;
        }
    </style>
</head>
<body>
    <button class="download-button" onclick="downloadStaticTable()">下载静态表格</button>
    
    <div style="position: relative;">
        <div class="add-button top-left-add">+</div>
        
        <div class="table-container">
            <table id="resizableTable">
                <tr>
                    <td class="resizable-cell" rowspan="2">
                        检查工序名称
                        <div class="resize-handle resize-handle-left"></div>
                        <div class="resize-handle resize-handle-right"></div>
                        <div class="resize-handle resize-handle-bottom"></div>
                        <div class="resize-handle resize-handle-corner"></div>
                    </td>
                    <td class="resizable-cell" rowspan="2">
                        检查项目及技术条件
                        <div class="resize-handle resize-handle-left"></div>
                        <div class="resize-handle resize-handle-right"></div>
                        <div class="resize-handle resize-handle-bottom"></div>
                        <div class="resize-handle resize-handle-corner"></div>
                    </td>
                    <td class="resizable-cell" rowspan="2">
                        实际检查结果
                        <div class="resize-handle resize-handle-left"></div>
                        <div class="resize-handle resize-handle-right"></div>
                        <div class="resize-handle resize-handle-bottom"></div>
                        <div class="resize-handle resize-handle-corner"></div>
                    </td>
                    <td class="resizable-cell" colspan="2">
                        完工
                        <div class="resize-handle resize-handle-left"></div>
                        <div class="resize-handle resize-handle-right"></div>
                        <div class="resize-handle resize-handle-bottom"></div>
                        <div class="resize-handle resize-handle-corner"></div>
                    </td>
                    <td class="resizable-cell" rowspan="2">
                        操作员
                        <div class="resize-handle resize-handle-left"></div>
                        <div class="resize-handle resize-handle-right"></div>
                        <div class="resize-handle resize-handle-bottom"></div>
                        <div class="resize-handle resize-handle-corner"></div>
                    </td>
                    <td class="resizable-cell" rowspan="2">
                        班组长
                        <div class="resize-handle resize-handle-left"></div>
                        <div class="resize-handle resize-handle-right"></div>
                        <div class="resize-handle resize-handle-bottom"></div>
                        <div class="resize-handle resize-handle-corner"></div>
                    </td>
                    <td class="resizable-cell" rowspan="2">
                        检验员
                        <div class="resize-handle resize-handle-left"></div>
                        <div class="resize-handle resize-handle-right"></div>
                        <div class="resize-handle resize-handle-bottom"></div>
                        <div class="resize-handle resize-handle-corner"></div>
                    </td>
                </tr>
                <tr>
                    <td class="resizable-cell">
                        月
                        <div class="resize-handle resize-handle-left"></div>
                        <div class="resize-handle resize-handle-right"></div>
                        <div class="resize-handle resize-handle-bottom"></div>
                        <div class="resize-handle resize-handle-corner"></div>
                    </td>
                    <td class="resizable-cell">
                        日
                        <div class="resize-handle resize-handle-left"></div>
                        <div class="resize-handle resize-handle-right"></div>
                        <div class="resize-handle resize-handle-bottom"></div>
                        <div class="resize-handle resize-handle-corner"></div>
                    </td>
                </tr>
                <tr>
                    <td class="date-columns resizable-cell">
                        <div class="resize-handle resize-handle-left"></div>
                        <div class="resize-handle resize-handle-right"></div>
                        <div class="resize-handle resize-handle-bottom"></div>
                        <div class="resize-handle resize-handle-corner"></div>
                    </td>
                    <td class="date-columns resizable-cell">
                        <div class="resize-handle resize-handle-left"></div>
                        <div class="resize-handle resize-handle-right"></div>
                        <div class="resize-handle resize-handle-bottom"></div>
                        <div class="resize-handle resize-handle-corner"></div>
                    </td>
                    <td class="date-columns resizable-cell">
                        <div class="resize-handle resize-handle-left"></div>
                        <div class="resize-handle resize-handle-right"></div>
                        <div class="resize-handle resize-handle-bottom"></div>
                        <div class="resize-handle resize-handle-corner"></div>
                    </td>
                    <td class="date-columns resizable-cell">
                        <div class="resize-handle resize-handle-left"></div>
                        <div class="resize-handle resize-handle-right"></div>
                        <div class="resize-handle resize-handle-bottom"></div>
                        <div class="resize-handle resize-handle-corner"></div>
                    </td>
                    <td class="date-columns resizable-cell">
                        <div class="resize-handle resize-handle-left"></div>
                        <div class="resize-handle resize-handle-right"></div>
                        <div class="resize-handle resize-handle-bottom"></div>
                        <div class="resize-handle resize-handle-corner"></div>
                    </td>
                    <td class="date-columns resizable-cell">
                        <div class="resize-handle resize-handle-left"></div>
                        <div class="resize-handle resize-handle-right"></div>
                        <div class="resize-handle resize-handle-bottom"></div>
                        <div class="resize-handle resize-handle-corner"></div>
                    </td>
                    <td class="date-columns resizable-cell">
                        <div class="resize-handle resize-handle-left"></div>
                        <div class="resize-handle resize-handle-right"></div>
                        <div class="resize-handle resize-handle-bottom"></div>
                        <div class="resize-handle resize-handle-corner"></div>
                    </td>
                    <td class="date-columns resizable-cell">
                        <div class="resize-handle resize-handle-left"></div>
                        <div class="resize-handle resize-handle-right"></div>
                        <div class="resize-handle resize-handle-bottom"></div>
                        <div class="resize-handle resize-handle-corner"></div>
                    </td>
                </tr>
            </table>
        </div>
        
        <div class="add-button bottom-add">+</div>
        <div class="add-button right-add">+</div>
    </div>

    <!-- Context Menu -->
    <div id="contextMenu" class="context-menu">
        <div class="context-menu-info">
            <div>当前宽度: <span id="currentWidth">-</span>px</div>
            <div>当前高度: <span id="currentHeight">-</span>px</div>
        </div>
        <div class="context-menu-item" onclick="adjustCellSize()">调整尺寸</div>
        <div class="context-menu-item" onclick="resetCellSize()">重置尺寸</div>
    </div>

    <!-- Input Dialog -->
    <div id="overlay" class="overlay"></div>
    <div id="inputDialog" class="input-dialog">
        <h3>调整单元格尺寸</h3>
        <div class="input-group">
            <label for="widthInput">宽度 (px):</label>
            <input type="number" id="widthInput" min="80" step="1">
        </div>
        <div class="input-group">
            <label for="heightInput">高度 (px):</label>
            <input type="number" id="heightInput" min="40" step="1">
        </div>
        <div class="dialog-buttons">
            <button class="dialog-button secondary" onclick="closeDialog()">取消</button>
            <button class="dialog-button primary" onclick="applyCellSize()">确定</button>
        </div>
    </div>

    <script>
        let currentContextCell = null;
        const MIN_CELL_WIDTH = 80;  // 最小单元格宽度
        const MIN_CELL_HEIGHT = 40; // 最小单元格高度
        const MAX_TABLE_WIDTH = 1800; // 最大表格宽度

        // Context menu functionality
        document.addEventListener('contextmenu', function(e) {
            const cell = e.target.closest('td, th');
            if (cell && cell.classList.contains('resizable-cell')) {
                e.preventDefault();
                currentContextCell = cell;
                
                const rect = cell.getBoundingClientRect();
                const contextMenu = document.getElementById('contextMenu');
                
                // Update current dimensions display
                document.getElementById('currentWidth').textContent = Math.round(rect.width);
                document.getElementById('currentHeight').textContent = Math.round(rect.height);
                
                // Position context menu
                contextMenu.style.left = e.pageX + 'px';
                contextMenu.style.top = e.pageY + 'px';
                contextMenu.style.display = 'block';
                
                // Adjust position if menu goes off screen
                setTimeout(() => {
                    const menuRect = contextMenu.getBoundingClientRect();
                    if (menuRect.right > window.innerWidth) {
                        contextMenu.style.left = (e.pageX - menuRect.width) + 'px';
                    }
                    if (menuRect.bottom > window.innerHeight) {
                        contextMenu.style.top = (e.pageY - menuRect.height) + 'px';
                    }
                }, 0);
            }
        });

        // Hide context menu when clicking elsewhere
        document.addEventListener('click', function(e) {
            const contextMenu = document.getElementById('contextMenu');
            if (!contextMenu.contains(e.target)) {
                contextMenu.style.display = 'none';
            }
        });

        // Adjust cell size function
        function adjustCellSize() {
            if (!currentContextCell) return;
            
            const rect = currentContextCell.getBoundingClientRect();
            document.getElementById('widthInput').value = Math.round(rect.width);
            document.getElementById('heightInput').value = Math.round(rect.height);
            
            document.getElementById('overlay').style.display = 'block';
            document.getElementById('inputDialog').style.display = 'block';
            document.getElementById('contextMenu').style.display = 'none';
            
            // Focus on width input
            setTimeout(() => {
                document.getElementById('widthInput').focus();
                document.getElementById('widthInput').select();
            }, 100);
        }

        // Reset cell size function
        function resetCellSize() {
            if (!currentContextCell) return;
            
            // Remove inline styles to reset to CSS defaults
            currentContextCell.style.width = '';
            currentContextCell.style.height = '';
            
            document.getElementById('contextMenu').style.display = 'none';
        }

        // Apply cell size function
        function applyCellSize() {
            if (!currentContextCell) return;
            
            const width = parseInt(document.getElementById('widthInput').value);
            const height = parseInt(document.getElementById('heightInput').value);
            
            if (width >= MIN_CELL_WIDTH) {
                const currentRect = currentContextCell.getBoundingClientRect();
                const widthDelta = width - currentRect.width;
                
                // Set new width for current cell
                currentContextCell.style.width = width + 'px';
                
                // Get all cells to the right of current cell
                const cells = Array.from(currentContextCell.parentElement.cells);
                const cellIndex = cells.indexOf(currentContextCell);
                const rightCells = cells.slice(cellIndex + 1);
                
                // Check if this is the rightmost cell
                if (rightCells.length === 0) {
                    // This is the rightmost cell - update table container size based on cells
                    updateTableContainerSize();
                } else {
                    // This is not the rightmost cell - maintain table width by adjusting right cells
                    const widthChangePerCell = -widthDelta / rightCells.length;
                    
                    rightCells.forEach((rightCell) => {
                        const rightRect = rightCell.getBoundingClientRect();
                        const newRightWidth = Math.max(MIN_CELL_WIDTH, rightRect.width + widthChangePerCell);
                        rightCell.style.width = newRightWidth + 'px';
                    });
                }
            }
            
            if (height >= MIN_CELL_HEIGHT) {
                currentContextCell.style.height = height + 'px';
            }
            
            closeDialog();
        }

        // Close dialog function
        function closeDialog() {
            document.getElementById('overlay').style.display = 'none';
            document.getElementById('inputDialog').style.display = 'none';
        }

        // Handle Enter key in input dialog
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && document.getElementById('inputDialog').style.display === 'block') {
                applyCellSize();
            } else if (e.key === 'Escape') {
                closeDialog();
                document.getElementById('contextMenu').style.display = 'none';
            }
        });

        document.addEventListener('DOMContentLoaded', function() {
            let isResizing = false;
            let currentHandle = null;
            let currentCell = null;
            let adjacentCell = null;
            let startX = 0;
            let startY = 0;
            let startWidth = 0;
            let startHeight = 0;
            let adjacentStartWidth = 0;
            let adjacentStartHeight = 0;
            let resizePreview = null;
            
            // Function to update table container width based on cells
            function updateTableContainerWidth() {
                const table = document.getElementById('resizableTable');
                
                // Calculate total width of all cells in the first row
                const firstRow = table.rows[0];
                let totalWidth = 0;
                
                for (let i = 0; i < firstRow.cells.length; i++) {
                    const cell = firstRow.cells[i];
                    const cellWidth = cell.getBoundingClientRect().width;
                    totalWidth += cellWidth;
                }
                
                // Set table width to total width
                table.style.width = totalWidth + 'px';
                
                // Also update table container width
                const tableContainer = document.querySelector('.table-container');
                tableContainer.style.width = totalWidth + 'px';
            }
            
            // Function to update table container height based on cells
            function updateTableContainerHeight() {
                const table = document.getElementById('resizableTable');
                
                // Calculate total height of all rows
                let totalHeight = 0;
                for (let i = 0; i < table.rows.length; i++) {
                    const row = table.rows[i];
                    const rowHeight = row.getBoundingClientRect().height;
                    totalHeight += rowHeight;
                }
                
                table.style.height = totalHeight + 'px';
            }
            
            // Function to update both table container width and height
            function updateTableContainerSize() {
                updateTableContainerWidth();
                updateTableContainerHeight();
            }

            // Function to create resize preview
            function createResizePreview() {
                if (resizePreview) {
                    document.body.removeChild(resizePreview);
                }
                
                resizePreview = document.createElement('div');
                resizePreview.className = 'resize-preview';
                document.body.appendChild(resizePreview);
                
                return resizePreview;
            }

            // Function to update resize preview position and size
            function updateResizePreview(cell, width, height) {
                if (!resizePreview) return;
                
                const rect = cell.getBoundingClientRect();
                resizePreview.style.left = rect.left + 'px';
                resizePreview.style.top = rect.top + 'px';
                resizePreview.style.width = width + 'px';
                resizePreview.style.height = height + 'px';
            }

            // Add event listeners to all resize handles
            document.querySelectorAll('.resize-handle').forEach(handle => {
                handle.addEventListener('mousedown', function(e) {
                    e.preventDefault();
                    isResizing = true;
                    currentHandle = this;
                    currentCell = this.parentElement;
                    
                    // Add visual feedback
                    currentCell.classList.add('resizing');
                    if (adjacentCell) {
                        adjacentCell.classList.add('resizing');
                    }
                    
                    startX = e.clientX;
                    startY = e.clientY;
                    
                    const rect = currentCell.getBoundingClientRect();
                    startWidth = rect.width;
                    startHeight = rect.height;
                    
                    // Create resize preview
                    createResizePreview();
                    
                    // Find adjacent cell for width/height adjustment
                    if (currentHandle.classList.contains('resize-handle-right') ||
                        currentHandle.classList.contains('resize-handle-corner')) {
                        adjacentCell = getAdjacentCell(currentCell, 'right');
                        if (adjacentCell) {
                            const adjacentRect = adjacentCell.getBoundingClientRect();
                            adjacentStartWidth = adjacentRect.width;
                            adjacentCell.classList.add('resizing');
                        }
                    }
                    
                    if (currentHandle.classList.contains('resize-handle-left')) {
                        adjacentCell = getAdjacentCell(currentCell, 'left');
                        if (adjacentCell) {
                            const adjacentRect = adjacentCell.getBoundingClientRect();
                            adjacentStartWidth = adjacentRect.width;
                            adjacentCell.classList.add('resizing');
                        }
                    }
                    
                    if (currentHandle.classList.contains('resize-handle-bottom') || 
                        currentHandle.classList.contains('resize-handle-corner')) {
                        const bottomAdjacentCell = getAdjacentCell(currentCell, 'bottom');
                        if (bottomAdjacentCell) {
                            const adjacentRect = bottomAdjacentCell.getBoundingClientRect();
                            adjacentStartHeight = adjacentRect.height;
                            bottomAdjacentCell.classList.add('resizing');
                        }
                    }
                    
                    document.addEventListener('mousemove', handleMouseMove);
                    document.addEventListener('mouseup', handleMouseUp);
                    
                    document.body.style.cursor = this.style.cursor;
                    document.body.style.userSelect = 'none';
                });
            });

            function getAdjacentCell(cell, direction) {
                const table = document.getElementById('resizableTable');
                const rows = Array.from(table.rows);
                const cells = Array.from(cell.parentElement.cells);
                const cellIndex = cells.indexOf(cell);
                const rowIndex = rows.indexOf(cell.parentElement);
                
                if (direction === 'right') {
                    // Find next cell in the same row
                    if (cellIndex < cells.length - 1) {
                        return cells[cellIndex + 1];
                    }
                } else if (direction === 'left') {
                    // Find previous cell in the same row
                    if (cellIndex > 0) {
                        return cells[cellIndex - 1];
                    }
                } else if (direction === 'bottom') {
                    // Find cell in the same column of next row
                    if (rowIndex < rows.length - 1) {
                        const nextRow = rows[rowIndex + 1];
                        if (nextRow.cells[cellIndex]) {
                            return nextRow.cells[cellIndex];
                        }
                    }
                }
                return null;
            }

            function handleMouseMove(e) {
                if (!isResizing) return;
                
                const deltaX = e.clientX - startX;
                const deltaY = e.clientY - startY;
                
                if (currentHandle.classList.contains('resize-handle-right')) {
                    // Right border dragging logic
                    const rightCell = getAdjacentCell(currentCell, 'right');
                    
                    if (rightCell) {
                        // When dragging right border, only affect current cell and right neighbor
                        if (deltaX < 0) {
                            // Dragging left - decrease current cell width, increase right cell width
                            const newCurrentWidth = Math.max(MIN_CELL_WIDTH, startWidth + deltaX);
                            const newRightWidth = Math.max(MIN_CELL_WIDTH, adjacentStartWidth - deltaX);
                            
                            // Update preview
                            updateResizePreview(currentCell, newCurrentWidth, currentCell.getBoundingClientRect().height);
                            
                            // Only apply changes if both cells can be resized
                            if (newCurrentWidth >= MIN_CELL_WIDTH && newRightWidth >= MIN_CELL_WIDTH) {
                                currentCell.style.width = newCurrentWidth + 'px';
                                rightCell.style.width = newRightWidth + 'px';
                            }
                        } else if (deltaX > 0) {
                            // Dragging right - increase current cell width, decrease right cell width
                            const newCurrentWidth = Math.max(MIN_CELL_WIDTH, startWidth + deltaX);
                            const newRightWidth = Math.max(MIN_CELL_WIDTH, adjacentStartWidth - deltaX);
                            
                            // Update preview
                            updateResizePreview(currentCell, newCurrentWidth, currentCell.getBoundingClientRect().height);
                            
                            // Only apply changes if both cells can be resized
                            if (newCurrentWidth >= MIN_CELL_WIDTH && newRightWidth >= MIN_CELL_WIDTH) {
                                currentCell.style.width = newCurrentWidth + 'px';
                                rightCell.style.width = newRightWidth + 'px';
                            }
                        }
                    } else {
                        // This is the rightmost cell - only increase current cell width and table width
                        // Do not compress other cells
                        const newWidth = Math.max(MIN_CELL_WIDTH, startWidth + deltaX);
                        
                        // Update preview
                        updateResizePreview(currentCell, newWidth, currentCell.getBoundingClientRect().height);
                        
                        // Set the new width for the current cell
                        currentCell.style.width = newWidth + 'px';
                        
                        // Update table width directly to prevent compression of other cells
                        const table = document.getElementById('resizableTable');
                        const currentTableWidth = table.getBoundingClientRect().width;
                        let newTableWidth = currentTableWidth + deltaX;
                        
                        // Ensure table width doesn't exceed maximum
                        newTableWidth = Math.min(newTableWidth, MAX_TABLE_WIDTH);
                        
                        table.style.width = newTableWidth + 'px';
                        
                        // Also update table container width
                        const tableContainer = document.querySelector('.table-container');
                        tableContainer.style.width = newTableWidth + 'px';
                    }
                }
                
                if (currentHandle.classList.contains('resize-handle-left')) {
                    // Left border dragging logic
                    const leftCell = getAdjacentCell(currentCell, 'left');
                    
                    if (leftCell) {
                        // When dragging left border, only affect current cell and left neighbor
                        if (deltaX < 0) {
                            // Dragging left - decrease left cell width, increase current cell width
                            const newLeftWidth = Math.max(MIN_CELL_WIDTH, adjacentStartWidth + deltaX);
                            const newCurrentWidth = Math.max(MIN_CELL_WIDTH, startWidth - deltaX);
                            
                            // Update preview
                            updateResizePreview(currentCell, newCurrentWidth, currentCell.getBoundingClientRect().height);
                            
                            // Only apply changes if both cells can be resized
                            if (newLeftWidth >= MIN_CELL_WIDTH && newCurrentWidth >= MIN_CELL_WIDTH) {
                                leftCell.style.width = newLeftWidth + 'px';
                                currentCell.style.width = newCurrentWidth + 'px';
                            }
                        } else if (deltaX > 0) {
                            // Dragging right - increase left cell width, decrease current cell width
                            const newLeftWidth = Math.max(MIN_CELL_WIDTH, adjacentStartWidth + deltaX);
                            const newCurrentWidth = Math.max(MIN_CELL_WIDTH, startWidth - deltaX);
                            
                            // Update preview
                            updateResizePreview(currentCell, newCurrentWidth, currentCell.getBoundingClientRect().height);
                            
                            // Only apply changes if both cells can be resized
                            if (newLeftWidth >= MIN_CELL_WIDTH && newCurrentWidth >= MIN_CELL_WIDTH) {
                                leftCell.style.width = newLeftWidth + 'px';
                                currentCell.style.width = newCurrentWidth + 'px';
                            }
                        }
                    } else {
                        // This is the leftmost cell - only adjust current cell
                        const newWidth = Math.max(MIN_CELL_WIDTH, startWidth - deltaX);
                        
                        // Update preview
                        updateResizePreview(currentCell, newWidth, currentCell.getBoundingClientRect().height);
                        
                        currentCell.style.width = newWidth + 'px';
                    }
                }
                
                if (currentHandle.classList.contains('resize-handle-corner')) {
                    // Corner dragging - adjust both width and height
                    // Width adjustment (same as right border)
                    const rightCell = getAdjacentCell(currentCell, 'right');
                    
                    if (rightCell) {
                        if (deltaX < 0) {
                            // Dragging left - decrease current cell width, increase right cell width
                            const newCurrentWidth = Math.max(MIN_CELL_WIDTH, startWidth + deltaX);
                            const newRightWidth = Math.max(MIN_CELL_WIDTH, adjacentStartWidth - deltaX);
                            
                            // Update preview
                            updateResizePreview(currentCell, newCurrentWidth, startHeight + deltaY);
                            
                            // Only apply changes if both cells can be resized
                            if (newCurrentWidth >= MIN_CELL_WIDTH && newRightWidth >= MIN_CELL_WIDTH) {
                                currentCell.style.width = newCurrentWidth + 'px';
                                rightCell.style.width = newRightWidth + 'px';
                            }
                        } else if (deltaX > 0) {
                            // Dragging right - increase current cell width, decrease right cell width
                            const newCurrentWidth = Math.max(MIN_CELL_WIDTH, startWidth + deltaX);
                            const newRightWidth = Math.max(MIN_CELL_WIDTH, adjacentStartWidth - deltaX);
                            
                            // Update preview
                            updateResizePreview(currentCell, newCurrentWidth, startHeight + deltaY);
                            
                            // Only apply changes if both cells can be resized
                            if (newCurrentWidth >= MIN_CELL_WIDTH && newRightWidth >= MIN_CELL_WIDTH) {
                                currentCell.style.width = newCurrentWidth + 'px';
                                rightCell.style.width = newRightWidth + 'px';
                            }
                        }
                    } else {
                        // This is the rightmost cell - only increase current cell width and table width
                        // Do not compress other cells
                        const newWidth = Math.max(MIN_CELL_WIDTH, startWidth + deltaX);
                        
                        // Update preview
                        updateResizePreview(currentCell, newWidth, startHeight + deltaY);
                        
                        // Set the new width for the current cell
                        currentCell.style.width = newWidth + 'px';
                        
                        // Update table width directly to prevent compression of other cells
                        const table = document.getElementById('resizableTable');
                        const currentTableWidth = table.getBoundingClientRect().width;
                        let newTableWidth = currentTableWidth + deltaX;
                        
                        // Ensure table width doesn't exceed maximum
                        newTableWidth = Math.min(newTableWidth, MAX_TABLE_WIDTH);
                        
                        table.style.width = newTableWidth + 'px';
                        
                        // Also update table container width
                        const tableContainer = document.querySelector('.table-container');
                        tableContainer.style.width = newTableWidth + 'px';
                        
                        // Handle height adjustment
                        const newHeight = Math.max(MIN_CELL_HEIGHT, startHeight + deltaY);
                        currentCell.style.height = newHeight + 'px';
                        
                        // Adjust adjacent cell height in opposite direction
                        const bottomAdjacentCell = getAdjacentCell(currentCell, 'bottom');
                        if (bottomAdjacentCell) {
                            const adjacentNewHeight = Math.max(MIN_CELL_HEIGHT, adjacentStartHeight - deltaY);
                            bottomAdjacentCell.style.height = adjacentNewHeight + 'px';
                        }
                    }
                    
                    // Height adjustment
                    const newHeight = Math.max(MIN_CELL_HEIGHT, startHeight + deltaY);
                    
                    // Update preview
                    updateResizePreview(currentCell, currentCell.getBoundingClientRect().width, newHeight);
                    
                    currentCell.style.height = newHeight + 'px';
                    
                    // Adjust adjacent cell height in opposite direction
                    const bottomAdjacentCell = getAdjacentCell(currentCell, 'bottom');
                    if (bottomAdjacentCell) {
                        const adjacentNewHeight = Math.max(MIN_CELL_HEIGHT, adjacentStartHeight - deltaY);
                        bottomAdjacentCell.style.height = adjacentNewHeight + 'px';
                    }
                }
                
                if (currentHandle.classList.contains('resize-handle-bottom')) {
                    const newHeight = Math.max(MIN_CELL_HEIGHT, startHeight + deltaY);
                    
                    // Update preview
                    updateResizePreview(currentCell, currentCell.getBoundingClientRect().width, newHeight);
                    
                    currentCell.style.height = newHeight + 'px';
                    
                    // Adjust adjacent cell height in opposite direction
                    const bottomAdjacentCell = getAdjacentCell(currentCell, 'bottom');
                    if (bottomAdjacentCell) {
                        const adjacentNewHeight = Math.max(MIN_CELL_HEIGHT, adjacentStartHeight - deltaY);
                        bottomAdjacentCell.style.height = adjacentNewHeight + 'px';
                    } else {
                        // This is the bottommost cell - update table container height
                        updateTableContainerSize();
                    }
                }
            }

            function handleMouseUp() {
                if (!isResizing) return;
                
                isResizing = false;
                
                // Remove visual feedback
                if (currentCell) {
                    currentCell.classList.remove('resizing');
                }
                if (adjacentCell) {
                    adjacentCell.classList.remove('resizing');
                }
                
                // Remove resize preview
                if (resizePreview) {
                    document.body.removeChild(resizePreview);
                    resizePreview = null;
                }
                
                currentHandle = null;
                currentCell = null;
                adjacentCell = null;
                
                document.removeEventListener('mousemove', handleMouseMove);
                document.removeEventListener('mouseup', handleMouseUp);
                
                document.body.style.cursor = '';
                document.body.style.userSelect = '';
            }

            // Prevent text selection during resize
            document.addEventListener('selectstart', function(e) {
                if (isResizing) {
                    e.preventDefault();
                }
            });
        });

        // Make functions global
        window.adjustCellSize = adjustCellSize;
        window.resetCellSize = resetCellSize;
        window.applyCellSize = applyCellSize;
        window.closeDialog = closeDialog;
        window.updateTableContainerWidth = updateTableContainerWidth;
        window.updateTableContainerHeight = updateTableContainerHeight;
        window.updateTableContainerSize = updateTableContainerSize;

        // Download static table function
        function downloadStaticTable() {
            // Get the current table
            const table = document.getElementById('resizableTable');
            const tableContainer = document.querySelector('.table-container');
            
            // Create a clone of the table
            const clonedTable = table.cloneNode(true);
            const clonedContainer = document.createElement('div');
            clonedContainer.className = 'table-container';
            
            // Remove all resize handles and interactive elements from the clone
            const resizeHandles = clonedTable.querySelectorAll('.resize-handle');
            resizeHandles.forEach(handle => handle.remove());
            
            // Get current computed styles for all cells and apply them as inline styles
            const originalCells = table.querySelectorAll('td, th');
            const clonedCells = clonedTable.querySelectorAll('td, th');
            
            originalCells.forEach((cell, index) => {
                const computedStyle = window.getComputedStyle(cell);
                const clonedCell = clonedCells[index];
                
                // Apply current dimensions as fixed inline styles
                clonedCell.style.width = computedStyle.width;
                clonedCell.style.height = computedStyle.height;
                clonedCell.style.minWidth = computedStyle.width;
                clonedCell.style.minHeight = computedStyle.height;
                clonedCell.style.maxWidth = computedStyle.width;
                clonedCell.style.maxHeight = computedStyle.height;
                
                // Remove resizable class and resize property
                clonedCell.classList.remove('resizable-cell');
                clonedCell.style.resize = 'none';
                clonedCell.style.position = 'static';
            });
            
            // Apply table container dimensions
            const containerStyle = window.getComputedStyle(tableContainer);
            clonedContainer.style.width = containerStyle.width;
            clonedContainer.style.height = containerStyle.height;
            
            // Append cloned table to container
            clonedContainer.appendChild(clonedTable);
            
            // Create static HTML content
            const staticHTML = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>静态检查表格</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f8f9fa;
            color: #333;
        }
        
        .table-container {
            background-color: white;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            display: inline-block;
            position: relative;
        }
        
        table {
            border-collapse: collapse;
            width: 100%;
            table-layout: fixed;
        }
        
        th, td {
            border: 1px solid #dee2e6;
            padding: 12px 15px;
            text-align: center;
            vertical-align: middle;
            font-size: 14px;
            overflow: hidden;
            word-wrap: break-word;
            background-color: white;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        
        @media print {
            body {
                margin: 0;
                background-color: white;
            }
            
            .table-container {
                border: 2px solid #000;
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <div style="text-align: center; margin-bottom: 20px;">
        <h2>检查表格 - 静态版本</h2>
        <p>生成时间: ${new Date().toLocaleString('zh-CN')}</p>
    </div>
    
    ${clonedContainer.outerHTML}
    
    <div style="margin-top: 20px; text-align: center; color: #6c757d; font-size: 12px;">
        <p>此表格为静态版本，尺寸已固定</p>
    </div>
</body>
</html>`;
            
            // Create and download the file
            const blob = new Blob([staticHTML], { type: 'text/html;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `静态检查表格_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.html`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            // Show success message
            const originalText = document.querySelector('.download-button').textContent;
            document.querySelector('.download-button').textContent = '下载成功!';
            document.querySelector('.download-button').style.background = '#28a745';
            
            setTimeout(() => {
                document.querySelector('.download-button').textContent = originalText;
                document.querySelector('.download-button').style.background = '';
            }, 2000);
        }
    </script>
</body>
</html>